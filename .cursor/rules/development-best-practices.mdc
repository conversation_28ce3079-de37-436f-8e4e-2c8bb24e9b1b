---
description:
globs:
alwaysApply: false
---
# 开发最佳实践与编码规范

## 🎯 开发原则

本项目遵循的核心开发原则，确保代码质量和团队协作效率。

## 📝 编码规范

### JavaScript/React规范

#### 1. 文件组织规范

```javascript
// ✅ 推荐的文件结构
src/
├── components/
│   ├── Common/           # 通用组件
│   ├── Editor/          # 编辑器组件
│   └── Preview/         # 预览组件
├── hooks/               # 自定义Hook
├── services/           # 服务层
│   ├── api/            # API相关
│   └── agents/         # Agent相关
└── utils/              # 工具函数
```

#### 2. 命名约定

```javascript
// ✅ 组件使用PascalCase
const OutlineNavigation = () => { ... };

// ✅ Hook使用camelCase，以use开头
const useAICards = () => { ... };

// ✅ 常量使用UPPER_CASE
const AGENT_TYPE = {
  ANALYSIS: 'analysis-agent'
};

// ✅ 文件名使用camelCase或PascalCase
// useAICards.js, OutlineNavigation.js
```

#### 3. 导入顺序

```javascript
// ✅ 推荐的导入顺序
// 1. React相关
import React, { useState, useEffect, useCallback } from 'react';

// 2. 第三方库
import axios from 'axios';
import lodash from 'lodash';

// 3. 本地组件
import OutlineNavigation from './components/Common/OutlineNavigation';

// 4. 服务层
import { agentClient } from './services/agents/AgentClient';

// 5. 工具函数
import { formatDate } from './utils/dateUtils';

// 6. 样式文件
import './App.css';
```

### 状态管理最佳实践

#### 1. Hook设计原则

参考 [useAICards.js](mdc:src/hooks/useAICards.js) 的设计模式：

```javascript
// ✅ 清晰的Hook接口设计
export const useAICards = () => {
  // 状态定义
  const [cards, setCards] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  
  // 使用useCallback优化性能
  const handleAIInteraction = useCallback(async (selectedText, analysisType) => {
    // 实现逻辑
  }, []);
  
  // 返回结构化接口
  return {
    // 状态
    cards,
    isLoading,
    // 操作
    handleAIInteraction,
    adoptCard,
    ignoreCard,
    // 工具方法
    getStats
  };
};
```

#### 2. 状态更新模式

```javascript
// ✅ 使用函数式更新
setCards(prev => prev.map(card => 
  card.id === cardId 
    ? { ...card, status: 'adopted' }
    : card
));

// ❌ 避免直接修改状态
cards[0].status = 'adopted'; // 错误
setCards(cards); // 错误
```

### 组件设计原则

#### 1. 组件职责单一

```javascript
// ✅ 单一职责的组件
const CardHeader = ({ card, onExpand }) => (
  <div onClick={onExpand}>
    <span className="card-type">{card.type}</span>
    <span className="card-title">{card.title}</span>
  </div>
);

// ❌ 避免过于复杂的组件
const MonolithicCard = () => {
  // 混合了UI、状态管理、API调用等多种职责
};
```

#### 2. Props验证

```javascript
// ✅ 使用PropTypes或TypeScript
import PropTypes from 'prop-types';

OutlineNavigation.propTypes = {
  outline: PropTypes.array.isRequired,
  activeSection: PropTypes.string,
  onItemClick: PropTypes.func.isRequired
};
```

## 🔧 服务层架构

### API客户端设计

参考 [httpClient.js](mdc:src/services/api/httpClient.js) 的设计模式：

#### 1. 错误处理

```javascript
// ✅ 统一错误处理
const handleError = (error) => {
  const normalizedError = {
    message: error.message || '请求失败',
    status: error.status || 0,
    code: error.code || 'UNKNOWN_ERROR'
  };
  
  // 根据错误类型处理
  switch (error.status) {
    case 401:
      localStorage.removeItem('auth_token');
      break;
    case 500:
      // 服务器错误处理
      break;
  }
  
  throw normalizedError;
};
```

#### 2. 重试机制

```javascript
// ✅ 智能重试
const shouldRetry = (error) => {
  return (
    error.name === 'TypeError' ||        // 网络错误
    error.name === 'AbortError' ||       // 超时
    (error.status >= 500 && error.status < 600) // 服务器错误
  );
};
```

### Agent系统设计

参考 [AgentClient.js](mdc:src/services/agents/AgentClient.js) 的设计原则：

#### 1. 事件驱动架构

```javascript
// ✅ 使用EventEmitter进行解耦
class AgentClient extends EventEmitter {
  executeTask(task) {
    this.emit('task:started', task);
    // 执行任务
    this.emit('task:completed', result);
  }
}

// 使用方式
agentClient.on('task:completed', (task) => {
  updateUI(task);
});
```

#### 2. 负载均衡策略

```javascript
// ✅ 策略模式实现负载均衡
class LoadBalancer {
  select(agents, task) {
    switch (this.strategy) {
      case 'round-robin':
        return this.roundRobin(agents);
      case 'least-connections':
        return this.leastConnections(agents);
      default:
        return this.roundRobin(agents);
    }
  }
}
```

## 🧪 开发环境配置

### Mock服务设计

参考 [MockAgentService.js](mdc:src/services/agents/MockAgentService.js) 的设计：

#### 1. 环境检测

```javascript
// ✅ 基于环境自动切换
const config = getCurrentConfig();

if (config.enableMockAgent) {
  result = await mockAgentService.executeTask(agentType, taskConfig);
} else {
  result = await agentClient.executeTask(taskConfig);
}
```

#### 2. 模拟真实场景

```javascript
// ✅ 模拟网络延迟和错误
async executeTask(agentType, taskConfig) {
  // 模拟网络延迟
  await this.delay(1000 + Math.random() * 2000);
  
  // 模拟偶发错误
  if (Math.random() < 0.1) {
    throw new Error('模拟网络错误');
  }
  
  return this.generateMockResponse(agentType, taskConfig);
}
```

## 📊 性能优化

### React性能优化

#### 1. 避免不必要的重渲染

```javascript
// ✅ 使用React.memo优化组件
const CardItem = React.memo(({ card, onAction }) => {
  return <div>{card.title}</div>;
});

// ✅ 使用useCallback稳定引用
const handleCardAction = useCallback((cardId, action) => {
  // 处理逻辑
}, []);
```

#### 2. 状态优化

```javascript
// ✅ 合理拆分状态
const useAICards = () => {
  const [cards, setCards] = useState([]);
  const [loadingStates, setLoadingStates] = useState({});
  
  // 避免将所有状态放在一个对象中
};
```

### 网络请求优化

#### 1. 请求合并

```javascript
// ✅ 合并并发请求
const executeMultiAgentAnalysis = async (tasks) => {
  const results = await Promise.allSettled(
    tasks.map(task => agentClient.executeTask(task))
  );
  return combineResults(results);
};
```

#### 2. 缓存策略

```javascript
// ✅ 实现适当的缓存
const cache = new Map();

const getCachedResult = (key) => {
  if (cache.has(key)) {
    const { data, timestamp } = cache.get(key);
    if (Date.now() - timestamp < 5 * 60 * 1000) { // 5分钟缓存
      return data;
    }
  }
  return null;
};
```

## 🔍 调试与测试

### 日志记录

```javascript
// ✅ 结构化日志
const logger = {
  info: (message, meta = {}) => {
    console.log({
      level: 'info',
      message,
      timestamp: new Date().toISOString(),
      ...meta
    });
  }
};

// 使用方式
logger.info('Agent task completed', {
  taskId: task.id,
  agentType: task.type,
  duration: task.duration
});
```

### 错误边界

```javascript
// ✅ 实现错误边界组件
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error) {
    return { hasError: true };
  }
  
  componentDidCatch(error, errorInfo) {
    logger.error('Component error:', { error, errorInfo });
  }
  
  render() {
    if (this.state.hasError) {
      return <div>Something went wrong.</div>;
    }
    return this.props.children;
  }
}
```

## 🔐 安全最佳实践

### 前端安全

```javascript
// ✅ 输入验证
const validateInput = (input) => {
  if (!input || typeof input !== 'string') {
    throw new Error('Invalid input');
  }
  return input.trim();
};

// ✅ XSS防护
const sanitizeContent = (content) => {
  return content.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
};
```

### API安全

参考 [server.js](mdc:backend-server/src/server.js) 的安全配置：

```javascript
// ✅ 请求验证
const validateRequest = (req, res, next) => {
  const { error } = schema.validate(req.body);
  if (error) {
    return res.status(400).json({ error: error.details[0].message });
  }
  next();
};
```

## 📚 文档规范

### 代码注释

```javascript
/**
 * 执行多Agent协同分析
 * @param {string} selectedText - 用户选择的文本内容
 * @param {Object} context - 上下文信息
 * @param {string} context.documentId - 文档ID
 * @returns {Promise<Object>} 分析结果
 */
const executeMultiAgentAnalysis = async (selectedText, context = {}) => {
  // 实现逻辑
};
```

### README文档

```markdown
## 快速开始

### 前端开发
\`\`\`bash
npm install
npm start
\`\`\`

### 后端开发
\`\`\`bash
cd backend-server
npm install
cp env.example .env
npm run dev
\`\`\`
```

## 🔧 工具配置

### ESLint配置

```json
{
  "extends": ["react-app", "react-app/jest"],
  "rules": {
    "no-unused-vars": "warn",
    "no-console": "warn",
    "prefer-const": "error"
  }
}
```

### Git提交规范

```
feat: 新增功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 重构代码
test: 测试相关
chore: 构建过程或辅助工具变动
```

参考 [STAGE2_ARCHITECTURE.md](mdc:STAGE2_ARCHITECTURE.md) 了解项目架构设计。




