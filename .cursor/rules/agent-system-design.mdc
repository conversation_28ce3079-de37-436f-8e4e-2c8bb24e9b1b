---
description:
globs:
alwaysApply: false
---
# Agent系统设计与实现

## 🤖 Agent架构概述

本项目实现了基于插件化的多Agent协同系统，支持动态注册、负载均衡和任务编排。

## 📋 Agent类型定义

### 核心Agent类型

在 [config.js](mdc:src/services/api/config.js) 中定义的Agent类型：

```javascript
AGENT_TYPES: {
  ANALYSIS: 'analysis-agent',      // 需求分析Agent
  KNOWLEDGE: 'knowledge-agent',    // 知识补充Agent  
  CASES: 'cases-agent',           // 案例参考Agent
  INSPIRATION: 'inspiration-agent', // 灵感激发Agent
  ORCHESTRATOR: 'orchestrator-agent' // 编排协调Agent
}
```

### Agent能力映射

在 [config.js](mdc:src/services/api/config.js) 中的能力定义：

```javascript
CAPABILITIES: {
  'analysis-agent': ['evaluate', 'inspect', 'validate'],
  'knowledge-agent': ['supplement', 'reference', 'standards'],
  'cases-agent': ['examples', 'patterns', 'best-practices'],
  'inspiration-agent': ['creative', 'brainstorm', 'innovative']
}
```

## 🔧 Agent客户端实现

### AgentClient核心功能

[AgentClient.js](mdc:src/services/agents/AgentClient.js) 实现的核心功能：

1. **服务发现**: 自动发现和注册可用Agent
2. **健康检查**: 定期监控Agent状态
3. **负载均衡**: 支持多种策略选择最优Agent
4. **任务分发**: 将任务分配给合适的Agent执行

### 任务状态管理

```javascript
TASK_STATUS: {
  PENDING: 'pending',      // 等待执行
  RUNNING: 'running',      // 正在执行
  COMPLETED: 'completed',  // 执行完成
  FAILED: 'failed',        // 执行失败
  CANCELLED: 'cancelled'   // 已取消
}
```

## 🎯 负载均衡策略

### 策略类型

在 [AgentClient.js](mdc:src/services/agents/AgentClient.js) 中实现的负载均衡：

1. **轮询(round-robin)**: 按顺序分配任务
2. **最少连接(least-connections)**: 选择任务数最少的Agent
3. **加权(weighted)**: 基于成功率的加权选择

### 实现示例

```javascript
// 轮询策略
roundRobin(agents) {
  const agent = agents[this.roundRobinIndex % agents.length];
  this.roundRobinIndex++;
  return agent;
}

// 最少连接策略
leastConnections(agents) {
  return agents.reduce((prev, current) => 
    prev.taskCount < current.taskCount ? prev : current
  );
}
```

## 🤝 多Agent协同

### 协同模式

[useAICards.js](mdc:src/hooks/useAICards.js) 支持的协同模式：

1. **并行执行**: `executeMultiAgentAnalysis()` - 多个Agent同时处理同一任务
2. **顺序执行**: `executeSequentialTasks()` - 串行处理，传递上下文
3. **结果聚合**: `combineMultiAgentResults()` - 统一展示多Agent结果

### 并行执行示例

```javascript
const executeMultiAgentAnalysis = async (selectedText, context = {}) => {
  const tasks = [
    { type: AGENT_TYPE.ANALYSIS, input: { selectedText }, context: { analysisType: 'evaluate' }},
    { type: AGENT_TYPE.KNOWLEDGE, input: { selectedText }, context: { analysisType: 'supplement' }},
    { type: AGENT_TYPE.CASES, input: { selectedText }, context: { analysisType: 'examples' }}
  ];
  
  const results = await Promise.allSettled(
    tasks.map(task => agentClient.executeTask(task))
  );
  
  return combineMultiAgentResults(results);
};
```

## 🧪 Mock服务实现

### MockAgentService设计

[MockAgentService.js](mdc:src/services/agents/MockAgentService.js) 为开发环境提供：

1. **模拟Agent响应**: 根据Agent类型生成不同风格的分析结果
2. **网络延迟模拟**: 模拟真实网络环境
3. **错误场景测试**: 支持失败场景模拟
4. **开发调试**: 提供详细的日志和状态信息

### Mock响应生成

```javascript
async generateMockResponse(agentType, taskConfig) {
  const { input, context } = taskConfig;
  const selectedText = input.selectedText || '';
  
  switch (agentType) {
    case AGENT_TYPE.ANALYSIS:
      return this.generateAnalysisResponse(selectedText, context.analysisType);
    case AGENT_TYPE.KNOWLEDGE:
      return this.generateKnowledgeResponse(selectedText, context.analysisType);
    // ... 其他Agent类型
  }
}
```

## 📊 监控与管理

### Agent状态监控

在 [App.js](mdc:src/App.js) 中集成的Agent状态监控：

```javascript
// 获取Agent统计信息
const aiStats = getStats();

// 显示可用Agent列表
{availableAgents.map((agent) => (
  <div key={agent.id}>
    <span>{agent.metadata?.name || agent.id}</span>
    <span className={agent.status === 'active' ? 'text-green-700' : 'text-red-700'}>
      {agent.status}
    </span>
  </div>
))}
```

### 健康检查机制

[AgentClient.js](mdc:src/services/agents/AgentClient.js) 中的健康检查：

```javascript
startHealthCheck() {
  setInterval(async () => {
    for (const [agentId, agent] of this.agents) {
      try {
        const response = await httpClient.get(`${agent.endpoint}/health`, { timeout: 5000 });
        if (response.status === 200) {
          agent.lastHeartbeat = Date.now();
          agent.status = 'active';
        }
      } catch (error) {
        agent.status = 'inactive';
        this.emit('agent:unhealthy', { agentId, error });
      }
    }
  }, AGENT_DISCOVERY.HEALTH_CHECK_INTERVAL);
}
```

## 🔮 扩展开发

### 添加新Agent类型

1. **定义Agent类型**: 在 [config.js](mdc:src/services/api/config.js) 中添加新的AGENT_TYPE
2. **添加能力定义**: 在CAPABILITIES中定义Agent的具体能力
3. **实现Mock响应**: 在 [MockAgentService.js](mdc:src/services/agents/MockAgentService.js) 中添加生成逻辑
4. **更新前端映射**: 在 [useAICards.js](mdc:src/hooks/useAICards.js) 中添加类型映射

### Agent开发最佳实践

1. **能力单一**: 每个Agent专注特定领域
2. **接口标准**: 统一的输入输出格式
3. **状态管理**: 及时上报健康状态
4. **错误处理**: 优雅处理异常情况
5. **性能优化**: 异步处理和结果缓存

参考 [frontend-backend-architecture.mdc](mdc:.cursor/rules/frontend-backend-architecture.mdc) 了解整体架构设计。




