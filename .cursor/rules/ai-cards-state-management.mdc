---
description:
globs:
alwaysApply: false
---
# AI卡片状态管理系统

## 🎯 概述

AI卡片系统是用户与Agent交互的核心界面，通过 [useAICards.js](mdc:src/hooks/useAICards.js) Hook 实现状态管理和生命周期控制。

## 📊 状态管理架构

### 核心状态结构

```javascript
const {
  cards,                    // AI卡片列表
  isLoading,               // 全局加载状态
  availableAgents,         // 可用Agent列表
  taskQueue,               // 任务队列
  handleAIInteraction,     // AI交互处理
  executeMultiAgentAnalysis, // 多Agent协同
  adoptCard,               // 采纳卡片
  ignoreCard,              // 忽略卡片
  deleteCard,              // 删除卡片
  markAsRead,              // 标记已读
  getStats                 // 获取统计
} = useAICards();
```

### 卡片数据模型

每个AI卡片包含以下字段：

```javascript
{
  id: 'card-timestamp-random',     // 唯一标识
  type: 'analysis|knowledge|collaboration|error', // 卡片类型
  title: 'AI分析标题',             // 显示标题
  timestamp: '2024-01-01 12:00',   // 创建时间
  selectedText: '用户选择的文本',   // 原始文本
  analysisType: 'evaluate',        // 分析类型
  status: 'loading|completed|failed|adopted|ignored', // 状态
  hasUnread: true,                 // 是否未读
  aiResponse: 'AI分析内容',        // AI响应内容
  tags: ['标签1', '标签2'],        // 标签列表
  metadata: {                      // 元数据
    agentType: 'analysis-agent',
    confidence: 0.89,
    processingTime: 2500
  }
}
```

## 🔄 卡片生命周期

### 1. 卡片创建

通过 `handleAIInteraction()` 触发：

```javascript
const handleAIInteraction = async (selectedText, analysisType, context) => {
  // 1. 创建预加载卡片
  const newCard = createPendingCard(selectedText, analysisType, context);
  setCards(prev => [newCard, ...prev]);
  
  // 2. 执行AI任务
  const result = await agentClient.executeTask(taskConfig);
  
  // 3. 更新卡片内容
  updateCardContent(newCard.id, result, 'completed');
};
```

### 2. 状态转换

卡片状态转换流程：

```
loading → completed → adopted/ignored
       → failed → (可重试)
```

### 3. 用户操作

- **adoptCard()**: 采纳卡片建议，标记为已采用
- **ignoreCard()**: 忽略卡片，标记为已忽略
- **deleteCard()**: 永久删除卡片
- **markAsRead()**: 标记为已读，移除未读标识

## 🤝 多Agent协同

### 协同卡片创建

`executeMultiAgentAnalysis()` 创建特殊的协同卡片：

```javascript
const executeMultiAgentAnalysis = async (selectedText, context) => {
  // 创建协同卡片
  const collaborationCard = createCollaborationCard(selectedText, tasks.length);
  
  // 并行执行多个Agent任务
  const results = await Promise.allSettled(
    tasks.map(task => agentClient.executeTask(task))
  );
  
  // 合并结果显示
  const combinedResult = combineMultiAgentResults(results);
  updateCardContent(collaborationCard.id, combinedResult, 'completed');
};
```

### 结果聚合策略

`combineMultiAgentResults()` 实现智能结果合并：

1. **筛选成功结果**: 过滤失败的Agent响应
2. **内容整合**: 将多个分析结果组织成统一格式
3. **置信度计算**: 基于各Agent置信度计算综合置信度
4. **标签合并**: 合并所有相关标签

## 🎨 UI集成模式

### 在App.js中的集成

[App.js](mdc:src/App.js) 中展示了完整的集成模式：

```javascript
// 1. Hook使用
const {
  cards, isLoading, availableAgents, handleAIInteraction, 
  executeMultiAgentAnalysis, adoptCard, ignoreCard, deleteCard, 
  markAsRead, getStats
} = useAICards();

// 2. 统计信息显示
const aiStats = getStats();

// 3. 卡片列表渲染
{cards.map((card) => (
  <div key={card.id} className={card.hasUnread ? 'ring-2 ring-blue-200' : ''}>
    {/* 卡片内容 */}
  </div>
))}
```

### 卡片展示组件

卡片UI组件包含：

1. **头部信息**: 类型标识、时间戳、未读标识
2. **内容区域**: AI分析结果、标签展示
3. **操作按钮**: 采纳/忽略/删除按钮
4. **对话框**: 继续与AI对话的输入框

## 📈 性能优化

### 状态优化策略

1. **useCallback优化**: 所有事件处理函数使用useCallback包装
2. **条件渲染**: 基于卡片状态进行条件渲染
3. **虚拟滚动**: 对大量卡片使用虚拟滚动（待实现）
4. **缓存策略**: 对AI响应结果进行适当缓存

### 内存管理

```javascript
// 定期清理已完成的任务
setInterval(() => {
  const cutoff = Date.now() - 24 * 60 * 60 * 1000; // 24小时前
  for (const [taskId, task] of this.tasks) {
    if (task.completedAt && task.completedAt < cutoff) {
      this.tasks.delete(taskId);
    }
  }
}, 60 * 60 * 1000); // 每小时清理一次
```

## 🔧 环境适配

### Mock模式支持

开发环境中，系统自动切换到Mock模式：

```javascript
// 根据环境决定使用真实Agent还是Mock服务
let result;
if (config.enableMockAgent) {
  result = await executeMockTask(agentType, taskConfig);
} else {
  result = await agentClient.executeTask(taskConfig);
}
```

### 错误处理

完善的错误处理机制：

1. **网络错误**: 自动重试机制
2. **Agent不可用**: 降级到Mock服务
3. **任务超时**: 自动取消并提示
4. **异常响应**: 创建错误卡片展示

## 📊 监控与统计

### 统计信息

`getStats()` 提供的统计数据：

```javascript
{
  totalCards: 10,           // 总卡片数
  unreadCards: 3,           // 未读卡片数
  adoptedCards: 5,          // 已采纳卡片数
  ignoredCards: 2,          // 已忽略卡片数
  runningTasks: 1,          // 运行中任务数
  availableAgentCount: 4    // 可用Agent数量
}
```

### 实时监控

通过事件监听实现实时状态更新：

```javascript
// 监听Agent事件
agentClient.on('agent:registered', handleAgentRegistered);
agentClient.on('task:completed', handleTaskCompleted);
agentClient.on('task:failed', handleTaskFailed);
```

## 🔮 扩展指南

### 添加新卡片类型

1. **定义类型**: 在卡片数据模型中添加新type
2. **创建函数**: 实现对应的create函数
3. **UI样式**: 添加对应的CSS类和图标
4. **操作逻辑**: 实现特定的操作处理逻辑

### 自定义分析类型

1. **映射关系**: 在 `getAgentTypeByAnalysis()` 中添加映射
2. **显示名称**: 在 `getAnalysisDisplayName()` 中添加显示名
3. **能力要求**: 在 `getRequiredCapabilities()` 中定义能力
4. **Mock响应**: 在 [MockAgentService.js](mdc:src/services/agents/MockAgentService.js) 中添加响应逻辑

参考 [agent-system-design.mdc](mdc:.cursor/rules/agent-system-design.mdc) 了解Agent系统设计。




