---
description:
globs:
alwaysApply: false
---
# 后端API设计模式与开发指南

## 🚀 后端架构概述

基于Node.js + Express的RESTful API服务，支持WebSocket实时通信和Agent编排管理。

## 📂 项目结构

### 核心文件组织

```
backend-server/
├── [src/server.js](mdc:backend-server/src/server.js)                 # Express服务器主体
├── [src/services/AgentOrchestrator.js](mdc:backend-server/src/services/AgentOrchestrator.js) # Agent编排器
├── [src/routes/](mdc:backend-server/src/routes/)                     # API路由
├── [src/middleware/](mdc:backend-server/src/middleware/)             # 中间件
├── [src/utils/](mdc:backend-server/src/utils/)                       # 工具函数
├── [package.json](mdc:backend-server/package.json)                   # 项目依赖
└── [env.example](mdc:backend-server/env.example)                     # 环境配置模板
```

## 🔧 核心服务器配置

### Express服务器设置

[server.js](mdc:backend-server/src/server.js) 中的关键配置：

1. **安全中间件**:
   - `helmet()` - 安全头设置
   - `cors()` - 跨域请求配置
   - `rateLimit()` - 请求频率限制

2. **请求处理**:
   - JSON解析（10MB限制）
   - URL编码支持
   - 请求压缩

3. **日志记录**:
   - Morgan请求日志
   - Winston结构化日志

### 环境配置管理

使用 [env.example](mdc:backend-server/env.example) 作为配置模板：

```bash
# 服务器配置
NODE_ENV=development
PORT=8000
FRONTEND_URL=http://localhost:3000

# Agent配置
AGENT_DISCOVERY_INTERVAL=30000
AGENT_HEALTH_CHECK_INTERVAL=10000
AGENT_LOAD_BALANCE_STRATEGY=round-robin

# 安全配置
JWT_SECRET=your-super-secret-jwt-key
RATE_LIMIT_MAX_REQUESTS=1000
```

## 🛠️ Agent编排器架构

### AgentOrchestrator核心功能

[AgentOrchestrator.js](mdc:backend-server/src/services/AgentOrchestrator.js) 实现：

1. **服务发现**: 动态注册和发现Agent服务
2. **健康监控**: 定期检查Agent健康状态
3. **任务调度**: 智能分发任务到最优Agent
4. **负载均衡**: 支持多种负载均衡策略

### Agent注册机制

```javascript
// Agent注册示例
async registerAgent(agentInfo) {
  const agent = {
    id: agentInfo.id,
    type: agentInfo.type,
    endpoint: agentInfo.endpoint,
    capabilities: agentInfo.capabilities,
    status: 'active',
    lastHeartbeat: Date.now(),
    taskCount: 0
  };
  
  this.agents.set(agent.id, agent);
  this.emit('agent:registered', agent);
}
```

### 任务调度策略

```javascript
// 选择最优Agent
selectOptimalAgent(task) {
  const availableAgents = this.getHealthyAgents(task.type);
  
  switch (this.loadBalanceStrategy) {
    case 'round-robin':
      return this.roundRobinSelect(availableAgents);
    case 'least-connections':
      return this.leastConnectionsSelect(availableAgents);
    case 'weighted':
      return this.weightedSelect(availableAgents);
  }
}
```

## 🌐 API路由设计

### RESTful端点结构

```
/api/v1/
├── /agents
│   ├── GET    /discovery     # Agent发现
│   ├── POST   /register      # Agent注册
│   ├── GET    /:id/health    # 健康检查
│   └── POST   /collaborate   # 多Agent协同
├── /tasks
│   ├── POST   /              # 创建任务
│   ├── GET    /:id           # 获取任务状态
│   └── DELETE /:id           # 取消任务
└── /analysis
    ├── POST   /              # 执行分析
    └── GET    /history       # 分析历史
```

### 标准响应格式

```javascript
// 成功响应
{
  "success": true,
  "data": { ... },
  "timestamp": "2024-01-01T12:00:00Z",
  "requestId": "req-123456"
}

// 错误响应
{
  "success": false,
  "error": {
    "code": "AGENT_NOT_FOUND",
    "message": "指定的Agent不存在",
    "details": { ... }
  },
  "timestamp": "2024-01-01T12:00:00Z",
  "requestId": "req-123456"
}
```

## 🔒 安全与认证

### 中间件安全配置

```javascript
// CORS配置
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID']
}));

// 速率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 每IP最多1000请求
  message: { error: '请求过于频繁，请稍后再试' }
});
```

### 请求追踪

每个请求自动分配唯一ID用于追踪：

```javascript
app.use((req, res, next) => {
  req.requestId = req.headers['x-request-id'] || 
                  `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  res.setHeader('X-Request-ID', req.requestId);
  next();
});
```

## 📡 WebSocket实时通信

### WebSocket服务配置

```javascript
const wss = new WebSocketServer({ 
  server,
  path: '/ws'
});

// WebSocket管理器
wsManager = new WebSocketManager(wss, orchestrator);
```

### 实时事件推送

支持的WebSocket事件：

- `agent:registered` - Agent注册通知
- `agent:status_changed` - Agent状态变更
- `task:created` - 任务创建通知
- `task:completed` - 任务完成通知
- `task:failed` - 任务失败通知

## 🔄 错误处理机制

### 统一错误处理中间件

```javascript
// 错误处理中间件
const errorHandler = (err, req, res, next) => {
  const errorResponse = {
    success: false,
    error: {
      code: err.code || 'INTERNAL_ERROR',
      message: err.message || '服务器内部错误',
      ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    },
    timestamp: new Date().toISOString(),
    requestId: req.requestId
  };
  
  res.status(err.statusCode || 500).json(errorResponse);
};
```

### 自定义错误类型

```javascript
class AgentError extends Error {
  constructor(message, code = 'AGENT_ERROR', statusCode = 400) {
    super(message);
    this.name = 'AgentError';
    this.code = code;
    this.statusCode = statusCode;
  }
}
```

## 📊 监控与健康检查

### 健康检查端点

```javascript
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.APP_VERSION || '1.0.0',
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    agents: {
      total: orchestrator.getTotalAgents(),
      active: orchestrator.getActiveAgents().length
    }
  });
});
```

### 性能监控

```javascript
// 请求性能监控
app.use((req, res, next) => {
  const startTime = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    logger.info('Request completed', {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration,
      requestId: req.requestId
    });
  });
  
  next();
});
```

## 🚀 部署与优雅关闭

### 服务器启动

```javascript
server.listen(PORT, '0.0.0.0', async () => {
  logger.info(`🚀 服务器启动成功！`);
  logger.info(`📍 地址: http://0.0.0.0:${PORT}`);
  logger.info(`🔗 WebSocket: ws://0.0.0.0:${PORT}/ws`);
  
  // 初始化服务
  await initializeServices();
});
```

### 优雅关闭处理

```javascript
const gracefulShutdown = async (signal) => {
  logger.info(`收到 ${signal} 信号，开始优雅关闭...`);
  
  server.close(async () => {
    try {
      // 关闭WebSocket连接
      if (wsManager) await wsManager.close();
      
      // 关闭Agent编排器
      if (orchestrator) await orchestrator.shutdown();
      
      logger.info('服务器已优雅关闭');
      process.exit(0);
    } catch (error) {
      logger.error('关闭过程中出错:', error);
      process.exit(1);
    }
  });
};

// 信号处理
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
```

## 🔮 扩展开发指南

### 添加新API端点

1. **创建路由文件**: 在 `src/routes/` 中创建新的路由模块
2. **定义中间件**: 在 `src/middleware/` 中添加验证和处理逻辑
3. **注册路由**: 在 [server.js](mdc:backend-server/src/server.js) 中注册新路由
4. **更新配置**: 在前端 [config.js](mdc:src/services/api/config.js) 中添加端点

### Agent服务开发

1. **实现健康检查**: 提供 `/health` 端点
2. **任务执行接口**: 实现 `/execute` 端点
3. **注册机制**: 启动时向编排器注册
4. **错误处理**: 统一的错误响应格式

### 数据库集成

```javascript
// MongoDB连接示例
import { MongoClient } from 'mongodb';

const client = new MongoClient(process.env.MONGODB_URI);
await client.connect();
const db = client.db('req-ai-agent');
```

参考 [frontend-backend-architecture.mdc](mdc:.cursor/rules/frontend-backend-architecture.mdc) 了解整体架构设计。




