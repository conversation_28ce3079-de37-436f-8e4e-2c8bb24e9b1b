---
description:
globs:
alwaysApply: false
---
# 项目总览 - AI智能需求编辑助手

## 🎯 项目概述

**REQ AI Edit Agent** 是一个基于React + Node.js的AI智能需求文档编辑助手，采用前后端分离的多Agent协同架构，支持实时预览、智能分析和多Agent协作。

## 📋 当前实施状态

### ✅ 已完成功能

#### 阶段1：大纲导航系统
- **增强的大纲导航**: [OutlineNavigation.js](mdc:src/components/Common/OutlineNavigation.js) - 层级展示、智能高亮、平滑滚动
- **预览同步**: [useOutlineSync.js](mdc:src/hooks/useOutlineSync.js) - 滚动监听、当前段落跟踪
- **UI优化**: 预览模式CSS修复、视觉层次优化

#### 阶段2：前后端分离AI服务架构 ⭐ 
- **前端服务层**: 
  - [API配置管理](mdc:src/services/api/config.js) - 环境适配、Agent端点管理
  - [HTTP客户端](mdc:src/services/api/httpClient.js) - 请求拦截、自动重试、错误处理
  - [Agent客户端](mdc:src/services/agents/AgentClient.js) - 服务发现、负载均衡、任务调度
  - [Mock服务](mdc:src/services/agents/MockAgentService.js) - 开发环境仿真

- **AI卡片系统**: 
  - [useAICards Hook](mdc:src/hooks/useAICards.js) - 状态管理、生命周期控制
  - 实时卡片生成、多Agent协同、统计监控

- **后端架构**:
  - [Express服务器](mdc:backend-server/src/server.js) - RESTful API、WebSocket支持
  - [Agent编排器](mdc:backend-server/src/services/AgentOrchestrator.js) - 服务发现、健康监控、任务分发
  - [环境配置](mdc:backend-server/env.example) - 多环境支持、安全配置

- **4种专业Agent**:
  - **Analysis Agent**: 需求分析和评估
  - **Knowledge Agent**: 行业知识和标准规范  
  - **Cases Agent**: 案例参考和最佳实践
  - **Inspiration Agent**: 创新思路和灵感激发

## 🏗️ 核心架构

### 前端架构
```
src/
├── components/          # UI组件层
│   ├── Common/         # 通用组件
│   ├── Editor/         # 编辑器组件
│   └── Preview/        # 预览渲染器
├── hooks/              # 状态管理层
│   ├── useAICards.js   # AI卡片管理
│   ├── useContentState.js # 内容状态
│   └── useOutlineSync.js  # 大纲同步
├── services/           # 服务适配层
│   ├── api/           # API客户端
│   └── agents/        # Agent服务
└── utils/             # 工具函数层
```

### 后端架构
```
backend-server/
├── src/
│   ├── server.js         # Express主服务
│   ├── services/         # 核心服务
│   │   └── AgentOrchestrator.js # Agent编排
│   ├── routes/          # API路由
│   └── middleware/      # 中间件
└── env.example         # 配置模板
```

## 🚀 技术栈

### 前端技术
- **React 18** - 用户界面框架
- **Tailwind CSS** - 原子化CSS框架
- **Milkdown** - 富文本编辑器
- **EventEmitter** - 事件驱动通信

### 后端技术
- **Node.js + Express** - 服务器框架
- **WebSocket** - 实时通信
- **Winston** - 结构化日志
- **Helmet + CORS** - 安全中间件

### 开发工具
- **Mock服务** - 开发环境仿真
- **环境配置** - 多环境支持
- **错误处理** - 统一错误机制
- **性能监控** - 实时状态追踪

## 🔄 数据流架构

```
用户操作 → PreviewRenderer → useAICards → AgentClient → 
HTTP/WS → AgentOrchestrator → Agent服务 → 结果返回 → 
卡片更新 → UI展示
```

### 核心数据流

1. **用户交互**: [PreviewRenderer.js](mdc:src/components/Preview/PreviewRenderer.js) 处理文本选择和AI工具触发
2. **状态管理**: [useAICards.js](mdc:src/hooks/useAICards.js) 管理AI卡片的完整生命周期
3. **Agent通信**: [AgentClient.js](mdc:src/services/agents/AgentClient.js) 负责与后端Agent服务通信
4. **任务编排**: [AgentOrchestrator.js](mdc:backend-server/src/services/AgentOrchestrator.js) 智能分发任务到最优Agent
5. **实时更新**: WebSocket推送任务状态变更，前端实时更新UI

## 📊 核心功能特性

### 🤖 AI智能分析
- **多维度分析**: 需求评估、知识补充、案例参考、创意激发
- **实时生成**: 选择文本即可触发AI分析，生成智能卡片
- **协同工作**: 多Agent并行处理，结果智能聚合
- **状态追踪**: 完整的任务生命周期管理

### 🎨 交互体验
- **智能卡片**: 可展开折叠、状态标识、操作按钮
- **实时预览**: 编辑器与预览同步，大纲导航跟踪
- **响应式设计**: 三栏布局，适配不同屏幕尺寸
- **操作反馈**: 加载状态、错误提示、成功确认

### 🔧 开发体验
- **Hot Reload**: 前后端开发服务器支持热重载
- **Mock服务**: 完整的开发环境仿真，无需真实Agent
- **环境切换**: 一键在开发/生产环境间切换
- **错误处理**: 完善的错误边界和恢复机制

## 📁 关键文件导航

### 🎯 入口文件
- **[App.js](mdc:src/App.js)** - 主应用组件，集成所有功能模块
- **[server.js](mdc:backend-server/src/server.js)** - 后端服务器入口

### 🔧 核心服务
- **[useAICards.js](mdc:src/hooks/useAICards.js)** - AI卡片状态管理核心
- **[AgentClient.js](mdc:src/services/agents/AgentClient.js)** - 前端Agent客户端
- **[AgentOrchestrator.js](mdc:backend-server/src/services/AgentOrchestrator.js)** - 后端Agent编排器

### 📋 配置文件
- **[config.js](mdc:src/services/api/config.js)** - 前端API配置
- **[env.example](mdc:backend-server/env.example)** - 后端环境配置模板
- **[package.json](mdc:backend-server/package.json)** - 后端依赖配置

### 🎨 UI组件
- **[OutlineNavigation.js](mdc:src/components/Common/OutlineNavigation.js)** - 大纲导航组件
- **[PreviewRenderer.js](mdc:src/components/Preview/PreviewRenderer.js)** - 预览渲染器

## 🚀 快速开始

### 前端启动
```bash
# 在项目根目录
npm install
npm start  # 启动在 http://localhost:3000
```

### 后端启动
```bash
# 进入后端目录
cd backend-server
npm install
cp env.example .env  # 复制环境配置
npm run dev  # 启动在 http://localhost:8000
```

### 开发模式
- 前端自动使用Mock服务，无需启动后端即可体验完整功能
- 后端提供健康检查: `http://localhost:8000/health`
- WebSocket服务: `ws://localhost:8000/ws`

## 🔧 开发指南

### 添加新功能
1. 参考 [开发最佳实践](mdc:.cursor/rules/development-best-practices.mdc)
2. 查看 [Agent系统设计](mdc:.cursor/rules/agent-system-design.mdc)
3. 了解 [前后端架构](mdc:.cursor/rules/frontend-backend-architecture.mdc)

### 扩展Agent类型
1. 在 [config.js](mdc:src/services/api/config.js) 中定义新Agent类型
2. 在 [MockAgentService.js](mdc:src/services/agents/MockAgentService.js) 中添加Mock响应
3. 在 [useAICards.js](mdc:src/hooks/useAICards.js) 中添加类型映射

### API开发
1. 参考 [后端API模式](mdc:.cursor/rules/backend-api-patterns.mdc)
2. 在 `backend-server/src/routes/` 中创建新路由
3. 在前端 [config.js](mdc:src/services/api/config.js) 中添加端点

## 📈 未来发展路线

### 🎯 下一阶段计划
- **阶段3**: 文档管理系统 - 多文档支持、版本控制、协作功能
- **阶段4**: 高级AI功能 - 深度学习、上下文记忆、个性化推荐

### 🔮 技术演进
- **容器化部署**: Docker + Kubernetes
- **Agent生态**: 插件市场、自定义Agent
- **智能化升级**: 机器学习、自适应优化

## 📚 相关资源

### 📖 架构文档
- **[完整架构文档](mdc:STAGE2_ARCHITECTURE.md)** - 详细的技术架构说明
- **[前后端分离架构](mdc:.cursor/rules/frontend-backend-architecture.mdc)** - 架构设计详解
- **[Agent系统设计](mdc:.cursor/rules/agent-system-design.mdc)** - Agent架构说明

### 🛠️ 开发指南
- **[开发最佳实践](mdc:.cursor/rules/development-best-practices.mdc)** - 编码规范和最佳实践
- **[后端API模式](mdc:.cursor/rules/backend-api-patterns.mdc)** - API设计模式
- **[AI卡片状态管理](mdc:.cursor/rules/ai-cards-state-management.mdc)** - 状态管理详解

这个项目代表了现代Web应用的最佳实践，结合了React生态、Node.js后端、AI Agent架构和现代化的开发工具链，为AI驱动的文档编辑提供了完整的解决方案。




