#!/bin/bash

# 简化版按功能执行测试脚本
# 兼容更多bash版本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TESTS_DIR="$PROJECT_ROOT/tests"
REPORTS_DIR="$TESTS_DIR/reports"

# 创建报告目录
mkdir -p "$REPORTS_DIR/by-feature"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_feature() {
    echo -e "${PURPLE}[FEATURE]${NC} $1"
}

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 运行单个测试
run_test() {
    local test_name="$1"
    local test_file="$2"
    local feature="$3"
    
    log_info "运行测试: $test_name"
    
    if [ ! -f "$test_file" ]; then
        log_warning "测试文件不存在: $test_file"
        return 1
    fi
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    # 创建功能特定的报告目录
    local feature_report_dir="$REPORTS_DIR/by-feature/${feature}-reports"
    mkdir -p "$feature_report_dir"
    
    if node "$test_file"; then
        log_success "✅ $test_name 测试通过"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        log_error "❌ $test_name 测试失败"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 运行编辑器表格功能测试
run_editor_table_tests() {
    log_feature "📊 开始编辑器表格功能测试"
    
    local feature_dir="$TESTS_DIR/features/editor-table"
    
    if [ ! -d "$feature_dir" ]; then
        log_warning "功能测试目录不存在: $feature_dir"
        return 1
    fi
    
    # 运行表格系统测试
    if [ -f "$feature_dir/table-system-test.js" ]; then
        run_test "表格系统综合测试" "$feature_dir/table-system-test.js" "editor-table"
    fi
    
    # 运行表格编辑测试
    if [ -f "$feature_dir/table-editing-test.js" ]; then
        run_test "表格编辑功能测试" "$feature_dir/table-editing-test.js" "editor-table"
    fi
    
    # 运行其他表格相关测试
    if [ -f "$TESTS_DIR/table-editing-flow-fix-test.js" ]; then
        run_test "表格编辑流程修复测试" "$TESTS_DIR/table-editing-flow-fix-test.js" "editor-table"
    fi
    
    if [ -f "$TESTS_DIR/enhanced-table-functionality-test.js" ]; then
        run_test "增强表格功能测试" "$TESTS_DIR/enhanced-table-functionality-test.js" "editor-table"
    fi
}

# 运行编辑器工具箱测试
run_editor_toolbox_tests() {
    log_feature "🧰 开始编辑器工具箱测试"
    
    local feature_dir="$TESTS_DIR/features/editor-toolbox"
    
    if [ ! -d "$feature_dir" ]; then
        log_warning "功能测试目录不存在: $feature_dir"
        return 1
    fi
    
    # 运行AI辅助功能测试
    if [ -f "$feature_dir/ai-assistance-test.js" ]; then
        run_test "AI辅助功能测试" "$feature_dir/ai-assistance-test.js" "editor-toolbox"
    fi
    
    # 运行智能卡片测试
    if [ -f "$TESTS_DIR/smart-card-menu-integration-test.js" ]; then
        run_test "智能卡片菜单集成测试" "$TESTS_DIR/smart-card-menu-integration-test.js" "editor-toolbox"
    fi
}

# 运行导航功能测试
run_navigation_tests() {
    log_feature "🧭 开始导航功能测试"
    
    local feature_dir="$TESTS_DIR/features/navigation"
    
    if [ ! -d "$feature_dir" ]; then
        log_warning "功能测试目录不存在: $feature_dir"
        return 1
    fi
    
    # 运行大纲导航测试
    if [ -f "$feature_dir/outline-navigation-test.js" ]; then
        run_test "大纲导航测试" "$feature_dir/outline-navigation-test.js" "navigation"
    fi
}

# 运行其他测试
run_other_tests() {
    log_feature "🔧 开始其他功能测试"
    
    # 运行错误处理测试
    if [ -f "$TESTS_DIR/error-handling-test.js" ]; then
        run_test "错误处理测试" "$TESTS_DIR/error-handling-test.js" "other"
    fi
    
    # 运行性能优化测试
    if [ -f "$TESTS_DIR/performance-optimization-test.js" ]; then
        run_test "性能优化测试" "$TESTS_DIR/performance-optimization-test.js" "other"
    fi
    
    # 运行手动反馈修复测试
    if [ -f "$TESTS_DIR/manual-feedback-fixes-test.js" ]; then
        run_test "手动反馈修复测试" "$TESTS_DIR/manual-feedback-fixes-test.js" "other"
    fi
}

# 生成测试报告
generate_test_report() {
    local output_file="$REPORTS_DIR/feature-tests-summary-$(date +%Y%m%d_%H%M%S).json"
    
    log_info "📊 生成测试报告: $output_file"
    
    cat > "$output_file" << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "testSuite": "Feature-based Tests",
  "summary": {
    "total": $TOTAL_TESTS,
    "passed": $PASSED_TESTS,
    "failed": $FAILED_TESTS,
    "successRate": $(echo "scale=2; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc -l 2>/dev/null || echo "0")
  },
  "features": {
    "editor-table": "编辑器表格功能",
    "editor-toolbox": "编辑器工具箱",
    "navigation": "导航功能",
    "other": "其他功能"
  }
}
EOF
    
    log_success "测试报告已生成: $output_file"
}

# 显示帮助信息
show_help() {
    echo "简化版按功能特性执行测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -a, --all               运行所有测试 (默认)"
    echo "  --editor-table          运行编辑器表格测试"
    echo "  --editor-toolbox        运行编辑器工具箱测试"
    echo "  --navigation            运行导航功能测试"
    echo "  --other                 运行其他功能测试"
    echo "  -r, --report            生成测试报告"
    echo ""
    echo "示例:"
    echo "  $0                      # 运行所有测试"
    echo "  $0 --editor-table       # 运行表格功能测试"
    echo "  $0 --all --report       # 运行所有测试并生成报告"
}

# 主函数
main() {
    log_info "🧪 智能PRD编辑器功能测试开始"
    log_info "项目根目录: $PROJECT_ROOT"
    log_info "测试目录: $TESTS_DIR"
    
    # 解析命令行参数
    local run_all=true
    local run_editor_table=false
    local run_editor_toolbox=false
    local run_navigation=false
    local run_other=false
    local generate_report=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -a|--all)
                run_all=true
                shift
                ;;
            --editor-table)
                run_all=false
                run_editor_table=true
                shift
                ;;
            --editor-toolbox)
                run_all=false
                run_editor_toolbox=true
                shift
                ;;
            --navigation)
                run_all=false
                run_navigation=true
                shift
                ;;
            --other)
                run_all=false
                run_other=true
                shift
                ;;
            -r|--report)
                generate_report=true
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 运行测试
    if [ "$run_all" = true ]; then
        run_editor_table_tests
        run_editor_toolbox_tests
        run_navigation_tests
        run_other_tests
    else
        [ "$run_editor_table" = true ] && run_editor_table_tests
        [ "$run_editor_toolbox" = true ] && run_editor_toolbox_tests
        [ "$run_navigation" = true ] && run_navigation_tests
        [ "$run_other" = true ] && run_other_tests
    fi
    
    # 生成测试报告
    if [ "$generate_report" = true ]; then
        generate_test_report
    fi
    
    # 显示测试结果
    echo ""
    log_info "📊 测试结果汇总:"
    log_info "总测试数: $TOTAL_TESTS"
    log_success "通过测试: $PASSED_TESTS"
    log_error "失败测试: $FAILED_TESTS"
    
    if [ $TOTAL_TESTS -gt 0 ]; then
        local success_rate=$(echo "scale=1; $PASSED_TESTS * 100 / $TOTAL_TESTS" | bc -l 2>/dev/null || echo "0")
        log_info "成功率: ${success_rate}%"
        
        if [ $FAILED_TESTS -eq 0 ]; then
            log_success "🎉 所有功能测试通过！"
            exit 0
        else
            log_error "❌ 部分测试失败，请检查错误信息"
            exit 1
        fi
    else
        log_warning "⚠️ 没有运行任何测试"
        exit 1
    fi
}

# 运行主函数
main "$@"
