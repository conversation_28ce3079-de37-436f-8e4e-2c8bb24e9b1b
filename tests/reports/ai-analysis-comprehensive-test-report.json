{"timestamp": "2025-07-20T04:16:35.729Z", "suite": "AI Analysis Comprehensive", "testResults": [{"timestamp": "2025-07-20T04:16:24.261Z", "type": "info", "message": "🚀 开始AI分析功能综合测试", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.267Z", "type": "test", "message": "开始测试: SmartCardMenu组件测试", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.267Z", "type": "info", "message": "测试SmartCardMenu组件...", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.267Z", "type": "error", "message": "❌ 测试失败: SmartCardMenu组件测试 - SmartCardMenu组件测试失败: ENOENT: no such file or directory, open '/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/src/components/PRDEditor/SmartCardMenu.js'", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.267Z", "type": "test", "message": "开始测试: AI服务集成测试", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.267Z", "type": "info", "message": "测试AI服务集成...", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.267Z", "type": "info", "message": "AI服务文件不存在，检查内联实现...", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.269Z", "type": "info", "message": "AI功能在PRDEditor中实现", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.269Z", "type": "info", "message": "AI服务集成测试通过", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.269Z", "type": "success", "message": "✅ 测试通过: AI服务集成测试", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.269Z", "type": "test", "message": "开始测试: 多代理分析功能测试", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.269Z", "type": "info", "message": "测试多代理分析功能...", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.270Z", "type": "info", "message": "发现多代理分析功能", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.270Z", "type": "info", "message": "多代理分析功能测试通过", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.270Z", "type": "success", "message": "✅ 测试通过: 多代理分析功能测试", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.270Z", "type": "test", "message": "开始测试: 智能卡片生成测试", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.270Z", "type": "info", "message": "测试智能卡片生成...", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.270Z", "type": "error", "message": "❌ 测试失败: 智能卡片生成测试 - 智能卡片生成测试失败: ENOENT: no such file or directory, open '/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/src/components/PRDEditor/SmartCardMenu.js'", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.270Z", "type": "test", "message": "开始测试: 需求分析功能测试", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.270Z", "type": "info", "message": "测试需求分析功能...", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.271Z", "type": "error", "message": "❌ 测试失败: 需求分析功能测试 - 需求分析功能测试失败: 未找到需求分析功能", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.271Z", "type": "test", "message": "开始测试: PRDEditor AI集成测试", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.271Z", "type": "info", "message": "测试PRDEditor中的AI集成...", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.271Z", "type": "error", "message": "❌ 测试失败: PRDEditor AI集成测试 - PRDEditor AI集成测试失败: PRDEditor未集成SmartCardMenu", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.271Z", "type": "test", "message": "开始测试: 卡片模板和数据测试", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.271Z", "type": "info", "message": "测试卡片模板和数据...", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.271Z", "type": "info", "message": "卡片模板文件不存在，检查内联定义...", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.272Z", "type": "error", "message": "❌ 测试失败: 卡片模板和数据测试 - 卡片模板和数据测试失败: ENOENT: no such file or directory, open '/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/src/components/PRDEditor/SmartCardMenu.js'", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.272Z", "type": "test", "message": "开始测试: AI工作流程测试", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.272Z", "type": "info", "message": "测试AI工作流程...", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.272Z", "type": "info", "message": "AI工作流程测试通过", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.272Z", "type": "success", "message": "✅ 测试通过: AI工作流程测试", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.272Z", "type": "test", "message": "开始测试: 应用编译测试", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:24.272Z", "type": "info", "message": "测试应用编译...", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:35.728Z", "type": "info", "message": "应用编译成功", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:35.728Z", "type": "success", "message": "✅ 测试通过: 应用编译测试", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:35.728Z", "type": "info", "message": "\n📊 测试结果汇总:", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:35.728Z", "type": "info", "message": "总测试数: 9", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:35.728Z", "type": "info", "message": "通过测试: 4", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:35.728Z", "type": "info", "message": "失败测试: 5", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:35.728Z", "type": "info", "message": "成功率: 44.4%", "suite": "AI Analysis Comprehensive"}, {"timestamp": "2025-07-20T04:16:35.728Z", "type": "error", "message": "❌ 部分测试失败，请检查错误信息", "suite": "AI Analysis Comprehensive"}], "summary": {"total": 9, "passed": 4, "failed": 6}}