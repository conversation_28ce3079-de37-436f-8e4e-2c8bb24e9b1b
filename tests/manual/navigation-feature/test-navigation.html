<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PRD章节导航功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2563eb;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background-color: #f9fafb;
        }
        .test-step {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-left: 4px solid #3b82f6;
            border-radius: 4px;
        }
        .success {
            border-left-color: #10b981;
            background-color: #f0fdf4;
        }
        .warning {
            border-left-color: #f59e0b;
            background-color: #fffbeb;
        }
        .error {
            border-left-color: #ef4444;
            background-color: #fef2f2;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 5px;
            transition: background 0.2s;
        }
        .button:hover {
            background: #2563eb;
        }
        .button.success {
            background: #10b981;
        }
        .button.success:hover {
            background: #059669;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            margin: 8px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
        }
        .checklist li:before {
            content: "✅ ";
            color: #10b981;
            font-weight: bold;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.pass {
            background: #dcfce7;
            color: #166534;
        }
        .status.fail {
            background: #fee2e2;
            color: #991b1b;
        }
        .status.pending {
            background: #fef3c7;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 PRD章节导航功能测试指南</h1>
        
        <div class="test-section">
            <h2>📋 测试准备</h2>
            <div class="test-step">
                <h3>1. 启动应用</h3>
                <p>确保开发服务器正在运行：</p>
                <code>npm start</code>
                <br><br>
                <a href="http://localhost:3000" class="button" target="_blank">打开应用</a>
                <span class="status pass">必需</span>
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 核心功能测试</h2>
            
            <div class="test-step">
                <h3>步骤1: 启用测试模式</h3>
                <ul class="checklist">
                    <li>在首页找到蓝色的"快速测试导航功能"提示框</li>
                    <li>点击"启用测试模式"按钮</li>
                    <li>验证页面跳转到评审结果tab</li>
                    <li>确认显示BSV特性需求文档</li>
                </ul>
                <span class="status pass">关键步骤</span>
            </div>

            <div class="test-step">
                <h3>步骤2: 进入章节评审详细</h3>
                <ul class="checklist">
                    <li>点击"章节评审详细"子tab</li>
                    <li>验证页面切换成功</li>
                    <li>确认显示章节列表和问题卡片</li>
                </ul>
                <span class="status pass">基础功能</span>
            </div>

            <div class="test-step warning">
                <h3>步骤3: 测试问题导航 🔥</h3>
                <ul class="checklist">
                    <li>找到P001问题："产品架构/系统架构图完全缺失"</li>
                    <li>点击右上角红色的"转到PRD对应章节"按钮</li>
                    <li><strong>验证：页面切换到评审确认tab</strong></li>
                    <li><strong>验证：左侧定位到"2.2 产品架构/系统架构"章节</strong></li>
                    <li><strong>验证：右侧P001问题卡片自动展开</strong></li>
                    <li><strong>验证：问题卡片高亮显示</strong></li>
                </ul>
                <span class="status pass">核心功能</span>
            </div>

            <div class="test-step success">
                <h3>步骤4: 验证按钮替换 ⭐</h3>
                <ul class="checklist">
                    <li>确认右下角显示绿色的"返回章节评审"按钮</li>
                    <li>确认没有显示"完成评审确认"按钮</li>
                    <li>验证按钮样式和颜色正确</li>
                </ul>
                <span class="status pass">重要特性</span>
            </div>

            <div class="test-step warning">
                <h3>步骤5: 测试返回功能 🔥</h3>
                <ul class="checklist">
                    <li>点击"返回章节评审"按钮</li>
                    <li><strong>验证：页面切换回评审结果tab</strong></li>
                    <li><strong>验证：自动切换到章节评审详细子tab</strong></li>
                    <li><strong>验证：P001问题卡片可见并正确定位</strong></li>
                    <li><strong>验证：章节行滚动到Tab页面最顶部</strong></li>
                    <li><strong>验证：问题详细卡片完整显示</strong></li>
                    <li><strong>验证：章节行高亮显示5秒</strong></li>
                </ul>
                <span class="status pass">核心功能</span>
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 扩展测试</h2>
            
            <div class="test-step">
                <h3>测试其他问题</h3>
                <ul class="checklist">
                    <li>测试P002："跨域文档关联不规范，可追溯性差"</li>
                    <li>测试P003："功能列表结构混乱，不符合MECE原则"</li>
                    <li>验证每个问题的导航和返回都正常工作</li>
                </ul>
                <span class="status pending">可选</span>
            </div>

            <div class="test-step">
                <h3>性能和体验测试</h3>
                <ul class="checklist">
                    <li>验证滚动动画平滑自然</li>
                    <li>验证响应时间 < 100ms</li>
                    <li>验证高亮效果清晰可见</li>
                    <li>验证在不同屏幕尺寸下正常工作</li>
                </ul>
                <span class="status pending">质量保证</span>
            </div>
        </div>

        <div class="test-section success">
            <h2>✅ 测试完成标准</h2>
            <p><strong>所有核心功能测试通过后，可以确认功能正常工作。</strong></p>
            
            <h3>关键验证点：</h3>
            <ul class="checklist">
                <li>问题导航：从评审结果到评审确认</li>
                <li>按钮替换：动态显示"返回章节评审"</li>
                <li>返回导航：从评审确认回到评审结果</li>
                <li>精确定位：章节行滚动到顶部</li>
                <li>视觉反馈：高亮效果和动画</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>📊 测试记录</h2>
            <p>测试完成后，请记录：</p>
            <ul>
                <li>测试时间和环境</li>
                <li>发现的问题（如有）</li>
                <li>性能表现</li>
                <li>用户体验评价</li>
            </ul>
            
            <div style="margin-top: 20px;">
                <a href="../reports/测试执行报告.md" class="button">查看测试报告</a>
                <a href="../../README.md" class="button success">返回测试首页</a>
            </div>
        </div>
    </div>

    <script>
        // 简单的测试状态跟踪
        document.addEventListener('DOMContentLoaded', function() {
            console.log('PRD章节导航功能测试页面已加载');
            console.log('请按照步骤进行测试验证');
        });
    </script>
</body>
</html>
