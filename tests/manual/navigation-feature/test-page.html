<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PRD导航功能测试页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .test-button {
            @apply bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm transition-colors mr-2 mb-2;
        }
        .success { @apply text-green-600; }
        .error { @apply text-red-600; }
        .info { @apply text-blue-600; }
        .warning { @apply text-yellow-600; }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">🧪 PRD导航功能测试页面</h1>
        
        <!-- 测试状态 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">📊 测试状态</h2>
            <div id="test-status" class="space-y-2">
                <div class="info">等待测试开始...</div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">⚡ 快速操作</h2>
            <div class="flex flex-wrap">
                <button onclick="quickTest()" class="test-button">
                    🚀 快速测试
                </button>
                <button onclick="quickCleanup()" class="test-button">
                    🧹 快速清理
                </button>
                <button onclick="runFullSuite()" class="test-button">
                    🔄 完整测试套件
                </button>
                <button onclick="openMainApp()" class="test-button">
                    🌐 打开主应用
                </button>
            </div>
        </div>

        <!-- 手动控制 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">🎮 手动控制</h2>
            <div class="flex flex-wrap">
                <button onclick="enableTestMode()" class="test-button">
                    🔧 启用测试模式
                </button>
                <button onclick="injectTestBar()" class="test-button">
                    💉 注入测试Bar
                </button>
                <button onclick="removeTestBar()" class="test-button">
                    🗑️ 移除测试Bar
                </button>
                <button onclick="disableTestMode()" class="test-button">
                    ❌ 禁用测试模式
                </button>
            </div>
        </div>

        <!-- 测试日志 -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">📝 测试日志</h2>
            <div id="test-log" class="bg-gray-100 p-4 rounded-md h-64 overflow-y-auto font-mono text-sm">
                <div class="info">测试日志将在这里显示...</div>
            </div>
            <div class="mt-4">
                <button onclick="clearLog()" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm">
                    清空日志
                </button>
                <button onclick="exportLog()" class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm ml-2">
                    导出日志
                </button>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-6">
            <h3 class="text-lg font-semibold text-blue-800 mb-3">💡 使用说明</h3>
            <div class="text-blue-700 space-y-2">
                <p><strong>1. 快速测试</strong>: 点击"快速测试"按钮，然后在新窗口中测试导航功能</p>
                <p><strong>2. 完整测试</strong>: 点击"完整测试套件"运行自动化测试</p>
                <p><strong>3. 手动控制</strong>: 使用手动控制按钮逐步执行测试步骤</p>
                <p><strong>4. 查看日志</strong>: 所有操作都会记录在测试日志中</p>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let testUtils = null;
        let autoTestRunner = null;

        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;

            // 更新状态
            updateStatus(message, type);
        }

        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('test-status');
            statusElement.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            log('日志已清空');
        }

        function exportLog() {
            const logContent = document.getElementById('test-log').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `test-log-${new Date().toISOString().slice(0, 19)}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            log('日志已导出');
        }

        // 测试函数
        async function quickTest() {
            log('🚀 开始快速测试...');
            try {
                await loadTestUtils();
                if (testUtils) {
                    testUtils.enableTestMode();
                    testUtils.injectTestBar();
                    log('✅ 快速测试环境已准备就绪', 'success');
                    log('💡 请在主应用中测试导航功能', 'info');
                }
            } catch (error) {
                log(`❌ 快速测试失败: ${error.message}`, 'error');
            }
        }

        async function quickCleanup() {
            log('🧹 开始快速清理...');
            try {
                if (testUtils) {
                    testUtils.removeTestBar();
                    testUtils.disableTestMode();
                    log('✅ 快速清理完成', 'success');
                } else {
                    log('⚠️ testUtils未加载', 'warning');
                }
            } catch (error) {
                log(`❌ 快速清理失败: ${error.message}`, 'error');
            }
        }

        async function runFullSuite() {
            log('🔄 开始运行完整测试套件...');
            try {
                await loadAutoTestRunner();
                if (autoTestRunner) {
                    await autoTestRunner.runFullTestSuite();
                    log('✅ 完整测试套件运行完成', 'success');
                }
            } catch (error) {
                log(`❌ 完整测试套件失败: ${error.message}`, 'error');
            }
        }

        function openMainApp() {
            log('🌐 打开主应用...');
            window.open('http://localhost:3000', '_blank');
            log('✅ 主应用已在新窗口中打开', 'success');
        }

        // 手动控制函数
        async function enableTestMode() {
            log('🔧 启用测试模式...');
            try {
                await loadTestUtils();
                if (testUtils) {
                    testUtils.enableTestMode();
                    log('✅ 测试模式已启用', 'success');
                }
            } catch (error) {
                log(`❌ 启用测试模式失败: ${error.message}`, 'error');
            }
        }

        async function injectTestBar() {
            log('💉 注入测试Bar...');
            try {
                if (testUtils) {
                    testUtils.injectTestBar();
                    log('✅ 测试Bar已注入', 'success');
                } else {
                    log('⚠️ 请先启用测试模式', 'warning');
                }
            } catch (error) {
                log(`❌ 注入测试Bar失败: ${error.message}`, 'error');
            }
        }

        async function removeTestBar() {
            log('🗑️ 移除测试Bar...');
            try {
                if (testUtils) {
                    testUtils.removeTestBar();
                    log('✅ 测试Bar已移除', 'success');
                } else {
                    log('⚠️ testUtils未加载', 'warning');
                }
            } catch (error) {
                log(`❌ 移除测试Bar失败: ${error.message}`, 'error');
            }
        }

        async function disableTestMode() {
            log('❌ 禁用测试模式...');
            try {
                if (testUtils) {
                    testUtils.disableTestMode();
                    log('✅ 测试模式已禁用', 'success');
                } else {
                    log('⚠️ testUtils未加载', 'warning');
                }
            } catch (error) {
                log(`❌ 禁用测试模式失败: ${error.message}`, 'error');
            }
        }

        // 工具加载函数
        async function loadTestUtils() {
            if (testUtils) return testUtils;
            
            try {
                // 这里需要根据实际的模块路径调整
                log('📦 加载testUtils...');
                // 由于跨域限制，这里只是示例
                // 实际使用时需要在主应用的控制台中运行
                log('💡 请在主应用控制台中运行: testUtils.enableTestMode()', 'info');
                return null;
            } catch (error) {
                log(`❌ 加载testUtils失败: ${error.message}`, 'error');
                return null;
            }
        }

        async function loadAutoTestRunner() {
            if (autoTestRunner) return autoTestRunner;
            
            try {
                log('📦 加载autoTestRunner...');
                // 同样，这里需要在主应用中运行
                log('💡 请在主应用控制台中运行: autoTestRunner.runFullTestSuite()', 'info');
                return null;
            } catch (error) {
                log(`❌ 加载autoTestRunner失败: ${error.message}`, 'error');
                return null;
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🎯 PRD导航功能测试页面已加载');
            log('💡 点击上方按钮开始测试');
        });
    </script>
</body>
</html>
