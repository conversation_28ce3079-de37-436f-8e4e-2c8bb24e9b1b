# 文档大纲和智能卡片定位功能测试指南

## 🎯 功能概述

本次实现了两个重要的定位功能：

1. **文档大纲章节点击定位** - 点击左边文档大纲的具体章节时，中间文档内容区域（预览模式下）页面定位到具体章节
2. **智能卡片定位按钮** - 每张智能卡片都有定位按钮，点击后定位到文档中对应的章节段落

## 🔧 技术实现要点

### 文档大纲章节点击定位
- **实现位置**: `src/components/PRDEvaluation/PRDReviewTab.js` 中的 `scrollToSection` 函数
- **核心技术**: 
  - 使用 `offsetTop` 计算精确位置
  - 平滑滚动到目标位置 (`scrollTo` with `behavior: 'smooth'`)
  - 添加视觉高亮效果 (`.outline-section-highlight` 和 `.highlight-pulse`)
- **CSS样式**: `src/components/PRDEvaluation/PRDReviewTab.css` 中的章节高亮样式

### 智能卡片定位按钮
- **实现位置**: 
  - `src/components/AICards/AICard.js` - 添加定位按钮UI
  - `src/components/PRDEditor/PRDEditor.js` - `handleCardLocate` 函数实现
- **核心技术**:
  - 多层级搜索算法：精确匹配→模糊匹配→关键词搜索→分行匹配→中文关键词提取
  - 支持 `selectedText` 字段
  - DOM树遍历定位到具体文本节点
  - 高亮显示目标文本

## 🧪 测试步骤

### 前置条件
1. 启动开发服务器：`npm start`
2. 打开 http://localhost:3000
3. 确保应用正常加载

### 测试1: 文档大纲章节点击定位

#### 在PRD编辑器中测试
1. **进入PRD编辑器模块**
2. **切换到预览模式** (点击右上角的"预览"按钮)
3. **展开左侧大纲** (如果大纲未显示，点击文档标题旁的大纲图标)
4. **点击大纲中的任意章节**

**预期结果**:
- [ ] 中间预览区域平滑滚动到对应章节
- [ ] 目标章节有蓝色高亮效果
- [ ] 高亮效果持续约3秒后消失
- [ ] 控制台显示定位日志

#### 在PRD评审确认页面测试
1. **进入PRD智能评估模块**
2. **切换到"评审确认"tab**
3. **点击左上角大纲图标展开文档大纲**
4. **点击大纲中的任意章节**

**预期结果**:
- [ ] 中间文档内容区域滚动到对应章节
- [ ] 章节有蓝色高亮效果
- [ ] 滚动平滑自然

### 测试2: 智能卡片定位按钮

#### 创建测试卡片
1. **进入PRD编辑器模块**
2. **切换到预览模式**
3. **选择一段文本** (例如选择某个章节标题或段落内容)
4. **点击AI工具** (选择任意分析类型，如"评估分析")
5. **等待AI卡片生成完成**

#### 测试定位功能
1. **在右侧AI助手面板中找到生成的卡片**
2. **展开卡片** (点击卡片头部)
3. **查找定位按钮** (应该在卡片展开内容中，显示为"📍 定位"按钮)
4. **点击定位按钮**

**预期结果**:
- [ ] 中间预览区域滚动到对应的文本位置
- [ ] 目标文本有黄色高亮效果
- [ ] 高亮效果持续约3秒后消失
- [ ] 控制台显示定位成功日志

## 🔍 验证要点

### 功能验证
- ✅ **响应性**: 点击后立即有响应，无明显延迟
- ✅ **准确性**: 能准确定位到目标章节/文本
- ✅ **平滑性**: 滚动动画平滑自然
- ✅ **视觉反馈**: 高亮效果清晰可见

### 用户体验验证
- ✅ **直观性**: 用户能够直观理解功能作用
- ✅ **一致性**: 两个定位功能的行为保持一致
- ✅ **容错性**: 在找不到目标时有合理的降级处理

### 技术验证
- ✅ **兼容性**: 在不同浏览器中正常工作
- ✅ **性能**: 定位操作不影响页面性能
- ✅ **稳定性**: 多次操作后功能依然正常

## 🐛 常见问题排查

### 问题1: 大纲点击没有反应
- **检查**: 是否在预览模式下
- **确认**: 大纲是否正确展开
- **验证**: 浏览器控制台是否有错误信息

### 问题2: 智能卡片没有定位按钮
- **检查**: 卡片是否有 `selectedText` 字段
- **确认**: 卡片是否完全展开
- **验证**: `onLocate` 函数是否正确传递

### 问题3: 定位不准确
- **检查**: 目标文本是否在当前文档中
- **确认**: 搜索算法是否找到匹配文本
- **验证**: 控制台日志显示的搜索策略

### 问题4: 高亮效果不显示
- **检查**: CSS样式是否正确加载
- **确认**: 高亮元素是否成功创建
- **验证**: DOM结构是否被正确修改

## 📊 测试记录

### 测试环境
- **浏览器**: Chrome/Safari/Firefox
- **操作系统**: macOS/Windows/Linux
- **屏幕分辨率**: 1920x1080 及以上

### 测试结果记录
| 功能 | 测试状态 | 备注 |
|------|----------|------|
| 大纲章节定位 | ⏳ 待测试 | |
| 智能卡片定位 | ⏳ 待测试 | |
| 高亮效果 | ⏳ 待测试 | |
| 滚动平滑性 | ⏳ 待测试 | |

---

**测试版本**: v1.0  
**功能状态**: ✅ 已实现  
**测试状态**: 待验证  
**更新时间**: 2025-01-18
