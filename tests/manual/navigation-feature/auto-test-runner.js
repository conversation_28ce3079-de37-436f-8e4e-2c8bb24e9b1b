/**
 * 自动化测试运行器
 * 用于自动注入测试功能、执行测试、清理环境
 */

class AutoTestRunner {
  constructor() {
    this.testUtils = null;
    this.testResults = [];
    this.isRunning = false;
  }

  /**
   * 初始化测试环境
   */
  async init() {
    console.log('🚀 初始化测试环境...');
    
    // 动态导入testUtils
    try {
      const { default: testUtils } = await import('../../../src/utils/testUtils.js');
      this.testUtils = testUtils;
      console.log('✅ testUtils加载成功');
    } catch (error) {
      console.error('❌ testUtils加载失败:', error);
      return false;
    }

    return true;
  }

  /**
   * 运行完整的测试套件
   */
  async runFullTestSuite() {
    if (this.isRunning) {
      console.warn('⚠️ 测试已在运行中');
      return;
    }

    this.isRunning = true;
    console.log('🧪 开始运行完整测试套件...');

    try {
      // 1. 初始化
      const initSuccess = await this.init();
      if (!initSuccess) {
        throw new Error('初始化失败');
      }

      // 2. 启用测试模式
      await this.enableTestMode();

      // 3. 注入测试bar
      await this.injectTestBar();

      // 4. 等待用户交互或自动测试
      await this.waitForUserInteraction();

      // 5. 清理环境
      await this.cleanup();

      console.log('✅ 测试套件运行完成');
      this.generateFinalReport();

    } catch (error) {
      console.error('❌ 测试套件运行失败:', error);
      await this.cleanup();
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * 启用测试模式
   */
  async enableTestMode() {
    console.log('🔧 启用测试模式...');
    this.testUtils.enableTestMode();
    this.addTestResult('enableTestMode', true, '测试模式启用成功');
    await this.delay(500);
  }

  /**
   * 注入测试bar
   */
  async injectTestBar() {
    console.log('💉 注入测试导航bar...');
    this.testUtils.injectTestBar();
    
    // 验证注入是否成功
    const barExists = !!document.getElementById('test-navigation-bar');
    this.addTestResult('injectTestBar', barExists, barExists ? '测试bar注入成功' : '测试bar注入失败');
    
    if (barExists) {
      console.log('✅ 测试导航bar已成功注入到页面');
      this.highlightTestBar();
    } else {
      throw new Error('测试bar注入失败');
    }
    
    await this.delay(1000);
  }

  /**
   * 高亮显示测试bar
   */
  highlightTestBar() {
    const testBar = document.getElementById('test-navigation-bar');
    if (testBar) {
      testBar.style.animation = 'pulse 2s infinite';
      testBar.style.boxShadow = '0 0 20px rgba(59, 130, 246, 0.5)';
      
      setTimeout(() => {
        testBar.style.animation = '';
        testBar.style.boxShadow = '';
      }, 4000);
    }
  }

  /**
   * 等待用户交互
   */
  async waitForUserInteraction() {
    console.log('⏳ 等待用户交互...');
    console.log('💡 请点击页面上的蓝色测试bar来测试导航功能');
    console.log('💡 或者在控制台运行 autoTestRunner.simulateUserClick() 来自动测试');

    // 监听测试bar的点击事件
    const testBar = document.getElementById('test-navigation-bar');
    if (testBar) {
      const enableBtn = testBar.querySelector('#enable-test-mode-btn');
      if (enableBtn) {
        enableBtn.addEventListener('click', () => {
          this.addTestResult('userInteraction', true, '用户点击了启用测试模式按钮');
          console.log('✅ 检测到用户交互');
        });
      }
    }

    // 等待5秒，给用户时间进行交互
    await this.delay(5000);
  }

  /**
   * 模拟用户点击（用于自动化测试）
   */
  simulateUserClick() {
    console.log('🤖 模拟用户点击...');
    const testBar = document.getElementById('test-navigation-bar');
    if (testBar) {
      const enableBtn = testBar.querySelector('#enable-test-mode-btn');
      if (enableBtn) {
        enableBtn.click();
        this.addTestResult('simulateClick', true, '自动点击测试按钮成功');
        console.log('✅ 自动点击执行成功');
      } else {
        this.addTestResult('simulateClick', false, '未找到测试按钮');
      }
    } else {
      this.addTestResult('simulateClick', false, '未找到测试bar');
    }
  }

  /**
   * 清理测试环境
   */
  async cleanup() {
    console.log('🧹 清理测试环境...');
    
    if (this.testUtils) {
      this.testUtils.removeTestBar();
      this.testUtils.disableTestMode();
      this.addTestResult('cleanup', true, '测试环境清理完成');
    }
    
    console.log('✅ 测试环境已清理');
    await this.delay(500);
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, message) {
    const result = {
      testName,
      success,
      message,
      timestamp: new Date().toISOString()
    };
    this.testResults.push(result);
    
    const status = success ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  /**
   * 生成最终测试报告
   */
  generateFinalReport() {
    console.log('\n📊 测试报告');
    console.log('='.repeat(50));
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests}`);
    console.log(`失败: ${failedTests}`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    console.log('\n详细结果:');
    console.table(this.testResults);
    
    // 返回报告对象
    return {
      summary: {
        total: totalTests,
        passed: passedTests,
        failed: failedTests,
        successRate: (passedTests / totalTests) * 100
      },
      details: this.testResults
    };
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 快速测试（一键运行）
   */
  async quickTest() {
    console.log('⚡ 快速测试模式');
    
    await this.init();
    this.testUtils.enableTestMode();
    this.testUtils.injectTestBar();
    
    console.log('✅ 测试环境已准备就绪');
    console.log('💡 测试bar已注入，请手动测试导航功能');
    console.log('💡 测试完成后运行: autoTestRunner.quickCleanup()');
  }

  /**
   * 快速清理
   */
  quickCleanup() {
    console.log('🧹 快速清理...');
    if (this.testUtils) {
      this.testUtils.removeTestBar();
      this.testUtils.disableTestMode();
    }
    console.log('✅ 清理完成');
  }
}

// 创建全局实例
const autoTestRunner = new AutoTestRunner();

// 暴露到全局作用域，方便在控制台使用
if (typeof window !== 'undefined') {
  window.autoTestRunner = autoTestRunner;
}

// 自动检测环境并提供使用提示
if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
  console.log('🧪 自动化测试运行器已加载');
  console.log('💡 使用方法:');
  console.log('   autoTestRunner.quickTest()     - 快速测试');
  console.log('   autoTestRunner.quickCleanup()  - 快速清理');
  console.log('   autoTestRunner.runFullTestSuite() - 完整测试套件');
}

export default autoTestRunner;
