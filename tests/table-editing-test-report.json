{"timestamp": "2025-07-20T15:10:41.436Z", "testResults": [{"timestamp": "2025-07-20T15:10:28.923Z", "type": "info", "message": "🚀 开始表格编辑功能测试"}, {"timestamp": "2025-07-20T15:10:28.934Z", "type": "test", "message": "开始测试: 表格编辑器组件检查"}, {"timestamp": "2025-07-20T15:10:28.934Z", "type": "info", "message": "检查表格编辑器组件..."}, {"timestamp": "2025-07-20T15:10:28.936Z", "type": "info", "message": "表格编辑器组件结构正确"}, {"timestamp": "2025-07-20T15:10:28.936Z", "type": "success", "message": "✅ 测试通过: 表格编辑器组件检查"}, {"timestamp": "2025-07-20T15:10:28.936Z", "type": "test", "message": "开始测试: 表格工具函数检查"}, {"timestamp": "2025-07-20T15:10:28.936Z", "type": "info", "message": "检查表格工具函数..."}, {"timestamp": "2025-07-20T15:10:28.937Z", "type": "info", "message": "表格工具函数结构正确"}, {"timestamp": "2025-07-20T15:10:28.937Z", "type": "success", "message": "✅ 测试通过: 表格工具函数检查"}, {"timestamp": "2025-07-20T15:10:28.937Z", "type": "test", "message": "开始测试: 表格模板系统检查"}, {"timestamp": "2025-07-20T15:10:28.937Z", "type": "info", "message": "检查表格模板系统..."}, {"timestamp": "2025-07-20T15:10:28.938Z", "type": "info", "message": "表格模板系统结构正确"}, {"timestamp": "2025-07-20T15:10:28.938Z", "type": "success", "message": "✅ 测试通过: 表格模板系统检查"}, {"timestamp": "2025-07-20T15:10:28.938Z", "type": "test", "message": "开始测试: 表格模板选择器检查"}, {"timestamp": "2025-07-20T15:10:28.938Z", "type": "info", "message": "检查表格模板选择器..."}, {"timestamp": "2025-07-20T15:10:28.938Z", "type": "info", "message": "表格模板选择器结构正确"}, {"timestamp": "2025-07-20T15:10:28.938Z", "type": "success", "message": "✅ 测试通过: 表格模板选择器检查"}, {"timestamp": "2025-07-20T15:10:28.938Z", "type": "test", "message": "开始测试: 数据验证功能测试"}, {"timestamp": "2025-07-20T15:10:28.938Z", "type": "info", "message": "测试数据验证功能..."}, {"timestamp": "2025-07-20T15:10:28.938Z", "type": "info", "message": "数据验证功能检查通过"}, {"timestamp": "2025-07-20T15:10:28.939Z", "type": "success", "message": "✅ 测试通过: 数据验证功能测试"}, {"timestamp": "2025-07-20T15:10:28.939Z", "type": "test", "message": "开始测试: CSV处理功能测试"}, {"timestamp": "2025-07-20T15:10:28.939Z", "type": "info", "message": "测试CSV处理功能..."}, {"timestamp": "2025-07-20T15:10:28.939Z", "type": "info", "message": "CSV处理功能检查通过"}, {"timestamp": "2025-07-20T15:10:28.939Z", "type": "success", "message": "✅ 测试通过: CSV处理功能测试"}, {"timestamp": "2025-07-20T15:10:28.939Z", "type": "test", "message": "开始测试: 应用编译测试"}, {"timestamp": "2025-07-20T15:10:28.939Z", "type": "info", "message": "测试应用编译..."}, {"timestamp": "2025-07-20T15:10:41.435Z", "type": "info", "message": "应用编译成功"}, {"timestamp": "2025-07-20T15:10:41.436Z", "type": "success", "message": "✅ 测试通过: 应用编译测试"}, {"timestamp": "2025-07-20T15:10:41.436Z", "type": "info", "message": "\n📊 测试结果汇总:"}, {"timestamp": "2025-07-20T15:10:41.436Z", "type": "info", "message": "总测试数: 7"}, {"timestamp": "2025-07-20T15:10:41.436Z", "type": "info", "message": "通过测试: 7"}, {"timestamp": "2025-07-20T15:10:41.436Z", "type": "info", "message": "失败测试: 0"}, {"timestamp": "2025-07-20T15:10:41.436Z", "type": "info", "message": "成功率: 100.0%"}, {"timestamp": "2025-07-20T15:10:41.436Z", "type": "success", "message": "🎉 所有表格编辑功能测试通过！"}], "summary": {"total": 7, "passed": 8, "failed": 0}}