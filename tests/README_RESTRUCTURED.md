# 🧪 测试系统 - 重构版

## 📋 测试组织结构

测试系统按功能特性重新组织，与文档结构保持一致，便于查找和维护。

## 📁 目录结构

```
tests/
├── README_RESTRUCTURED.md             # 重构版测试指南 (本文档)
├── run-tests-by-feature.sh           # 按功能执行测试脚本
├── test-runner-enhanced.js           # 增强测试运行器
│
├── features/                          # 按功能特性组织的测试
│   ├── README.md                      # 功能测试总览
│   ├── editor-table/                  # 编辑器表格功能测试
│   │   ├── README.md                  # 表格测试总览
│   │   ├── table-system-test.js       # 表格系统综合测试
│   │   ├── table-editing-test.js      # 表格编辑功能测试
│   │   ├── table-templates-test.js    # 表格模板测试
│   │   └── table-interaction-test.js  # 表格交互测试
│   ├── prd-import/                    # PRD导入功能测试
│   │   ├── README.md                  # PRD导入测试总览
│   │   ├── upload-test.js             # 上传功能测试
│   │   ├── parsing-test.js            # 解析功能测试
│   │   └── evaluation-test.js         # 评估功能测试
│   ├── editor-toolbox/                # 编辑器工具箱测试
│   │   ├── README.md                  # 工具箱测试总览
│   │   ├── toolbar-test.js            # 工具栏功能测试
│   │   ├── smart-cards-test.js        # 智能卡片测试
│   │   └── ai-assistance-test.js      # AI辅助功能测试
│   ├── navigation/                    # 导航功能测试
│   │   ├── README.md                  # 导航测试总览
│   │   ├── outline-navigation-test.js # 大纲导航测试
│   │   └── text-selection-test.js     # 文本选择测试
│   └── content-management/            # 内容管理测试
│       ├── README.md                  # 内容管理测试总览
│       ├── section-management-test.js # 章节管理测试
│       ├── content-rendering-test.js  # 内容渲染测试
│       └── save-sync-test.js          # 保存同步测试
│
├── integration/                       # 集成测试
│   ├── README.md                      # 集成测试总览
│   ├── cross-feature/                 # 跨功能集成测试
│   │   ├── table-ai-integration.js   # 表格与AI集成测试
│   │   ├── editor-navigation.js      # 编辑器与导航集成
│   │   └── content-sync.js           # 内容同步集成测试
│   └── system-integration.js         # 系统级集成测试
│
├── e2e/                              # 端到端测试
│   ├── README.md                     # E2E测试总览
│   ├── user-workflows/               # 用户工作流测试
│   │   ├── complete-editing.js      # 完整编辑流程
│   │   ├── table-management.js      # 表格管理流程
│   │   └── ai-assisted-editing.js   # AI辅助编辑流程
│   └── browser-compatibility.js     # 浏览器兼容性测试
│
├── performance/                      # 性能测试
│   ├── README.md                     # 性能测试总览
│   ├── load-testing/                 # 负载测试
│   │   ├── large-document.js        # 大文档性能测试
│   │   ├── multiple-tables.js       # 多表格性能测试
│   │   └── concurrent-users.js      # 并发用户测试
│   └── memory-testing.js            # 内存测试
│
├── manual/                           # 手动测试
│   ├── README.md                     # 手动测试总览
│   ├── feature-testing/              # 功能手动测试
│   │   ├── table-features.md        # 表格功能测试清单
│   │   ├── ai-features.md           # AI功能测试清单
│   │   └── navigation-features.md   # 导航功能测试清单
│   └── user-acceptance/              # 用户验收测试
│       ├── scenarios.md             # 测试场景
│       └── checklists.md            # 验收清单
│
├── reports/                          # 测试报告
│   ├── README.md                     # 报告说明
│   ├── by-feature/                   # 按功能分类的报告
│   │   ├── editor-table-reports/    # 表格功能测试报告
│   │   ├── ai-analysis-reports/     # AI分析测试报告
│   │   └── navigation-reports/      # 导航功能测试报告
│   ├── daily/                        # 日常测试报告
│   ├── release/                      # 发布测试报告
│   └── performance/                  # 性能测试报告
│
└── legacy/                           # 历史测试文件
    ├── README.md                     # 历史文件说明
    ├── old-structure/                # 旧结构测试文件
    └── deprecated-tests/             # 废弃的测试文件
```

## 🎯 测试分类说明

### 1. 功能测试 (features/)
**目标**: 按功能特性组织的专项测试

#### 编辑器表格 (editor-table/)
- **table-system-test.js** - 表格系统综合测试
- **table-editing-test.js** - 表格编辑功能测试
- **table-templates-test.js** - 表格模板测试
- **table-interaction-test.js** - 表格交互测试

#### PRD导入 (prd-import/)
- **upload-test.js** - 文档上传功能测试
- **parsing-test.js** - 文档解析功能测试
- **evaluation-test.js** - 文档评估功能测试

#### 编辑器工具箱 (editor-toolbox/)
- **toolbar-test.js** - 工具栏功能测试
- **smart-cards-test.js** - 智能卡片系统测试
- **ai-assistance-test.js** - AI辅助功能测试

#### 导航功能 (navigation/)
- **outline-navigation-test.js** - 大纲导航测试
- **text-selection-test.js** - 文本选择测试

#### 内容管理 (content-management/)
- **section-management-test.js** - 章节管理测试
- **content-rendering-test.js** - 内容渲染测试
- **save-sync-test.js** - 保存同步测试

### 2. 集成测试 (integration/)
**目标**: 跨功能模块的集成测试

#### 跨功能集成 (cross-feature/)
- **table-ai-integration.js** - 表格与AI功能集成
- **editor-navigation.js** - 编辑器与导航集成
- **content-sync.js** - 内容同步集成

### 3. 端到端测试 (e2e/)
**目标**: 完整用户工作流程测试

#### 用户工作流 (user-workflows/)
- **complete-editing.js** - 完整编辑流程测试
- **table-management.js** - 表格管理流程测试
- **ai-assisted-editing.js** - AI辅助编辑流程测试

### 4. 性能测试 (performance/)
**目标**: 系统性能和负载测试

#### 负载测试 (load-testing/)
- **large-document.js** - 大文档处理性能
- **multiple-tables.js** - 多表格渲染性能
- **concurrent-users.js** - 并发用户性能

### 5. 手动测试 (manual/)
**目标**: 人工验证和探索性测试

#### 功能测试 (feature-testing/)
- **table-features.md** - 表格功能测试清单
- **ai-features.md** - AI功能测试清单
- **navigation-features.md** - 导航功能测试清单

## 🚀 测试执行指南

### 按功能执行测试
```bash
# 运行所有功能测试
./tests/run-tests-by-feature.sh --all

# 运行特定功能测试
./tests/run-tests-by-feature.sh --feature editor-table
./tests/run-tests-by-feature.sh --feature prd-import
./tests/run-tests-by-feature.sh --feature editor-toolbox
./tests/run-tests-by-feature.sh --feature navigation
./tests/run-tests-by-feature.sh --feature content-management

# 运行多个功能测试
./tests/run-tests-by-feature.sh --feature editor-table,ai-analysis
```

### 按测试类型执行
```bash
# 运行集成测试
./tests/run-tests-by-feature.sh --type integration

# 运行E2E测试
./tests/run-tests-by-feature.sh --type e2e

# 运行性能测试
./tests/run-tests-by-feature.sh --type performance
```

### 生成测试报告
```bash
# 生成功能测试报告
./tests/run-tests-by-feature.sh --feature editor-table --report

# 生成综合测试报告
./tests/run-tests-by-feature.sh --all --report --output reports/comprehensive-report.json
```

## 📊 测试覆盖率目标

| 功能模块 | 单元测试 | 集成测试 | E2E测试 | 总覆盖率 |
|----------|----------|----------|---------|----------|
| 编辑器表格 | 95% | 90% | 85% | 90% |
| PRD导入 | 85% | 80% | 75% | 80% |
| 编辑器工具箱 | 90% | 85% | 80% | 85% |
| 导航功能 | 85% | 80% | 75% | 80% |
| 内容管理 | 80% | 75% | 70% | 75% |

## 🔗 相关文档

- [功能特性文档](../docs/features/README.md)
- [测试组织指南](./TEST_ORGANIZATION_GUIDE.md)
- [原测试系统指南](./README_NEW.md)

---

**文档版本**: v2.0  
**最后更新**: 2025-01-20  
**维护者**: 测试团队
