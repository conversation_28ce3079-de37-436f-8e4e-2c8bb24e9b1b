/**
 * 表格编辑功能测试
 * 验证表格编辑器、数据处理和模板系统
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs').promises;

class TableEditingTester {
  constructor() {
    this.testResults = [];
    this.projectRoot = path.resolve(__dirname, '../../../..');
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
    console.log(logMessage);
    
    this.testResults.push({
      timestamp,
      type,
      message
    });
  }

  async runTest(testName, testFunction) {
    this.log(`开始测试: ${testName}`, 'test');
    try {
      await testFunction();
      this.log(`✅ 测试通过: ${testName}`, 'success');
      return true;
    } catch (error) {
      this.log(`❌ 测试失败: ${testName} - ${error.message}`, 'error');
      return false;
    }
  }

  async testTableEditorComponent() {
    this.log('检查表格编辑器组件...');
    
    try {
      const tableEditorPath = path.join(this.projectRoot, 'src/components/Editor/TableEditor.js');
      const content = await fs.readFile(tableEditorPath, 'utf8');
      
      // 检查核心组件
      if (!content.includes('const TableEditor')) {
        throw new Error('TableEditor组件未找到');
      }
      
      // 检查关键功能
      const requiredFeatures = [
        'handleCellClick',
        'handleCellDoubleClick',
        'handleKeyDown',
        'addRow',
        'deleteRow',
        'addColumn',
        'deleteColumn',
        'handleSort',
        'editHeader'
      ];
      
      for (const feature of requiredFeatures) {
        if (!content.includes(feature)) {
          throw new Error(`缺少功能: ${feature}`);
        }
      }
      
      // 检查状态管理
      const requiredStates = [
        'selectedCell',
        'editingCell',
        'editValue',
        'sortConfig'
      ];
      
      for (const state of requiredStates) {
        if (!content.includes(state)) {
          throw new Error(`缺少状态: ${state}`);
        }
      }
      
      this.log('表格编辑器组件结构正确');
    } catch (error) {
      throw new Error(`表格编辑器组件检查失败: ${error.message}`);
    }
  }

  async testTableUtils() {
    this.log('检查表格工具函数...');
    
    try {
      const tableUtilsPath = path.join(this.projectRoot, 'src/utils/tableUtils.js');
      const content = await fs.readFile(tableUtilsPath, 'utf8');
      
      // 检查核心类
      const requiredClasses = [
        'CSVParser',
        'TableValidator',
        'TableConverter',
        'FileHandler'
      ];
      
      for (const className of requiredClasses) {
        if (!content.includes(`class ${className}`)) {
          throw new Error(`缺少类: ${className}`);
        }
      }
      
      // 检查CSV解析功能
      if (!content.includes('parse(csvText') || !content.includes('stringify(data')) {
        throw new Error('CSV解析功能不完整');
      }
      
      // 检查表格验证功能
      if (!content.includes('validateData') || !content.includes('validateCell')) {
        throw new Error('表格验证功能不完整');
      }
      
      // 检查格式转换功能
      if (!content.includes('toMarkdown') || !content.includes('fromMarkdown')) {
        throw new Error('Markdown转换功能不完整');
      }
      
      // 检查文件处理功能
      if (!content.includes('readCSV') || !content.includes('downloadCSV')) {
        throw new Error('文件处理功能不完整');
      }
      
      this.log('表格工具函数结构正确');
    } catch (error) {
      throw new Error(`表格工具函数检查失败: ${error.message}`);
    }
  }

  async testTableTemplates() {
    this.log('检查表格模板系统...');
    
    try {
      const tableTemplatesPath = path.join(this.projectRoot, 'src/data/tableTemplates.js');
      const content = await fs.readFile(tableTemplatesPath, 'utf8');
      
      // 检查模板定义
      if (!content.includes('tableTemplates')) {
        throw new Error('表格模板定义未找到');
      }
      
      // 检查模板分类
      if (!content.includes('templateCategories')) {
        throw new Error('模板分类定义未找到');
      }
      
      // 检查必需的模板
      const requiredTemplates = [
        'functionalRequirements',
        'userStories',
        'apiInterfaces',
        'testCases',
        'dataDictionary'
      ];
      
      for (const template of requiredTemplates) {
        if (!content.includes(template)) {
          throw new Error(`缺少模板: ${template}`);
        }
      }
      
      // 检查工具函数
      const requiredFunctions = [
        'getTemplatesByCategory',
        'getAllTemplates',
        'getTemplateById'
      ];
      
      for (const func of requiredFunctions) {
        if (!content.includes(func)) {
          throw new Error(`缺少函数: ${func}`);
        }
      }
      
      this.log('表格模板系统结构正确');
    } catch (error) {
      throw new Error(`表格模板系统检查失败: ${error.message}`);
    }
  }

  async testTableTemplateSelector() {
    this.log('检查表格模板选择器...');
    
    try {
      const selectorPath = path.join(this.projectRoot, 'src/components/Editor/TableTemplateSelector.js');
      const content = await fs.readFile(selectorPath, 'utf8');
      
      // 检查核心组件
      if (!content.includes('const TableTemplateSelector')) {
        throw new Error('TableTemplateSelector组件未找到');
      }
      
      if (!content.includes('TableTemplatePreview')) {
        throw new Error('TableTemplatePreview组件未找到');
      }
      
      // 检查关键功能
      const requiredFeatures = [
        'getFilteredTemplates',
        'handleTemplateSelect',
        'handleCategoryChange',
        'handleSearchChange'
      ];
      
      for (const feature of requiredFeatures) {
        if (!content.includes(feature)) {
          throw new Error(`缺少功能: ${feature}`);
        }
      }
      
      // 检查模板导入
      if (!content.includes('templateCategories') || !content.includes('getAllTemplates')) {
        throw new Error('模板系统导入不完整');
      }
      
      this.log('表格模板选择器结构正确');
    } catch (error) {
      throw new Error(`表格模板选择器检查失败: ${error.message}`);
    }
  }

  async testDataValidation() {
    this.log('测试数据验证功能...');
    
    try {
      // 动态导入模块进行测试
      const tableUtilsPath = path.join(this.projectRoot, 'src/utils/tableUtils.js');
      
      // 检查文件是否可以被Node.js解析
      const content = await fs.readFile(tableUtilsPath, 'utf8');
      
      // 基本语法检查
      if (!content.includes('export class TableValidator')) {
        throw new Error('TableValidator类导出不正确');
      }
      
      // 检查验证方法
      if (!content.includes('validateData(data, schema')) {
        throw new Error('validateData方法签名不正确');
      }
      
      if (!content.includes('validateCell(value, columnDef')) {
        throw new Error('validateCell方法签名不正确');
      }
      
      this.log('数据验证功能检查通过');
    } catch (error) {
      throw new Error(`数据验证功能测试失败: ${error.message}`);
    }
  }

  async testCSVProcessing() {
    this.log('测试CSV处理功能...');
    
    try {
      const tableUtilsPath = path.join(this.projectRoot, 'src/utils/tableUtils.js');
      const content = await fs.readFile(tableUtilsPath, 'utf8');
      
      // 检查CSV解析器
      if (!content.includes('static parse(csvText')) {
        throw new Error('CSV解析方法未找到');
      }
      
      if (!content.includes('static stringify(data')) {
        throw new Error('CSV生成方法未找到');
      }
      
      if (!content.includes('parseLine(line')) {
        throw new Error('CSV行解析方法未找到');
      }
      
      if (!content.includes('stringifyRow(row')) {
        throw new Error('CSV行生成方法未找到');
      }
      
      this.log('CSV处理功能检查通过');
    } catch (error) {
      throw new Error(`CSV处理功能测试失败: ${error.message}`);
    }
  }

  async testCompilation() {
    this.log('测试应用编译...');
    
    return new Promise((resolve, reject) => {
      const buildProcess = spawn('npm', ['run', 'build'], {
        cwd: this.projectRoot,
        stdio: 'pipe'
      });

      let output = '';
      let errorOutput = '';

      buildProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      buildProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      buildProcess.on('close', (code) => {
        if (code === 0) {
          this.log('应用编译成功');
          resolve();
        } else {
          this.log(`编译失败，退出码: ${code}`);
          if (errorOutput) {
            this.log(`错误输出: ${errorOutput}`);
          }
          reject(new Error(`编译失败: ${errorOutput || '未知错误'}`));
        }
      });

      // 设置超时
      setTimeout(() => {
        buildProcess.kill();
        reject(new Error('编译超时'));
      }, 120000); // 2分钟超时
    });
  }

  async runAllTests() {
    this.log('🚀 开始表格编辑功能测试');
    
    const tests = [
      ['表格编辑器组件检查', () => this.testTableEditorComponent()],
      ['表格工具函数检查', () => this.testTableUtils()],
      ['表格模板系统检查', () => this.testTableTemplates()],
      ['表格模板选择器检查', () => this.testTableTemplateSelector()],
      ['数据验证功能测试', () => this.testDataValidation()],
      ['CSV处理功能测试', () => this.testCSVProcessing()],
      ['应用编译测试', () => this.testCompilation()]
    ];

    let passedTests = 0;
    let totalTests = tests.length;

    for (const [testName, testFunction] of tests) {
      const passed = await this.runTest(testName, testFunction);
      if (passed) {
        passedTests++;
      }
    }

    this.log(`\n📊 测试结果汇总:`);
    this.log(`总测试数: ${totalTests}`);
    this.log(`通过测试: ${passedTests}`);
    this.log(`失败测试: ${totalTests - passedTests}`);
    this.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (passedTests === totalTests) {
      this.log('🎉 所有表格编辑功能测试通过！', 'success');
      return true;
    } else {
      this.log('❌ 部分测试失败，请检查错误信息', 'error');
      return false;
    }
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      testResults: this.testResults,
      summary: {
        total: this.testResults.filter(r => r.type === 'test').length,
        passed: this.testResults.filter(r => r.type === 'success').length,
        failed: this.testResults.filter(r => r.type === 'error').length
      }
    };

    const reportPath = path.join(this.projectRoot, 'tests/table-editing-test-report.json');
    require('fs').writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    this.log(`测试报告已保存到: ${reportPath}`);
    return report;
  }
}

// 运行测试
async function main() {
  const tester = new TableEditingTester();
  
  try {
    const success = await tester.runAllTests();
    const report = tester.generateReport();
    
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('测试运行失败:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = TableEditingTester;
