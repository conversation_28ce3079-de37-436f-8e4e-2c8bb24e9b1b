#!/usr/bin/env node
/**
 * 大纲导航和文本选中功能测试
 * 验证修复后的功能是否正常工作
 */

const path = require('path');
const fs = require('fs');

// 设置项目根目录
const PROJECT_ROOT = path.join(__dirname, '..');
process.chdir(PROJECT_ROOT);

console.log('🧪 大纲导航和文本选中功能测试');
console.log('='.repeat(50));

let testsPassed = 0;
let testsTotal = 0;

function runTest(testName, testFn) {
  testsTotal++;
  try {
    const result = testFn();
    if (result) {
      console.log(`✅ ${testName}: 通过`);
      testsPassed++;
    } else {
      console.log(`❌ ${testName}: 失败`);
    }
  } catch (error) {
    console.log(`❌ ${testName}: 错误 - ${error.message}`);
  }
}

// 测试1: 大纲导航容器选择器修复验证
runTest('大纲导航容器选择器修复验证', () => {
  try {
    const useOutlineSyncPath = path.join(PROJECT_ROOT, 'src/hooks/useOutlineSync.js');
    const content = fs.readFileSync(useOutlineSyncPath, 'utf8');
    
    const checks = [
      // 检查是否使用了正确的容器选择器
      content.includes('.prd-content-container'),
      // 检查是否移除了旧的选择器
      !content.includes('.preview-content'),
      // 检查基本的滚动逻辑
      content.includes('scrollTo'),
      content.includes('behavior: \'smooth\''),
      content.includes('handleOutlineClick')
    ];
    
    const passed = checks.filter(check => check).length;
    console.log(`  容器选择器检查: ${passed}/${checks.length} 项通过`);
    
    return passed === checks.length;
  } catch (error) {
    console.log(`  容器选择器验证错误: ${error.message}`);
    return false;
  }
});

// 测试2: PRD编辑器容器类名添加验证
runTest('PRD编辑器容器类名添加验证', () => {
  try {
    const prdEditorPath = path.join(PROJECT_ROOT, 'src/components/PRDEditor/PRDEditor.js');
    const content = fs.readFileSync(prdEditorPath, 'utf8');
    
    const checks = [
      // 检查是否添加了容器类名
      content.includes('prd-content-container'),
      // 检查编辑模式容器
      content.includes('h-full overflow-y-auto p-6 prd-content-container'),
      // 检查大纲导航组件
      content.includes('OutlineNavigation'),
      content.includes('onItemClick={handleOutlineItemClick}'),
      // 检查大纲点击处理
      content.includes('handleOutlineItemClick')
    ];
    
    const passed = checks.filter(check => check).length;
    console.log(`  容器类名检查: ${passed}/${checks.length} 项通过`);
    
    return passed === checks.length;
  } catch (error) {
    console.log(`  容器类名验证错误: ${error.message}`);
    return false;
  }
});

// 测试3: 文本选中功能修复验证
runTest('文本选中功能修复验证', () => {
  try {
    const rendererPath = path.join(PROJECT_ROOT, 'src/components/Common/PRDContentRenderer.js');
    const content = fs.readFileSync(rendererPath, 'utf8');
    
    const checks = [
      // 检查文本选中处理函数
      content.includes('handleTextSelection'),
      content.includes('window.getSelection()'),
      content.includes('selectedText.length > 10'),
      
      // 检查事件绑定
      content.includes('onMouseUp={mode === \'preview\' ? handleTextSelection : undefined}'),
      
      // 检查表格容器的事件绑定
      content.includes('className="table-container"') && content.includes('onMouseUp'),
      
      // 检查整个渲染器容器的事件绑定
      content.includes('className={`prd-content-renderer ${className}`}') && content.includes('onMouseUp'),
      
      // 检查智能卡片菜单
      content.includes('SmartCardMenu'),
      content.includes('showCardMenu && selectionPosition')
    ];
    
    const passed = checks.filter(check => check).length;
    console.log(`  文本选中功能检查: ${passed}/${checks.length} 项通过`);
    
    return passed >= checks.length * 0.9; // 90%通过率
  } catch (error) {
    console.log(`  文本选中功能验证错误: ${error.message}`);
    return false;
  }
});

// 测试4: 智能卡片菜单组件验证
runTest('智能卡片菜单组件验证', () => {
  try {
    const menuPath = path.join(PROJECT_ROOT, 'src/components/Common/SmartCardMenu.js');
    const content = fs.readFileSync(menuPath, 'utf8');
    
    const checks = [
      // 检查组件基本结构
      content.includes('const SmartCardMenu'),
      content.includes('position'),
      content.includes('selectedText'),
      content.includes('onCreateCard'),
      content.includes('onClose'),
      
      // 检查卡片类型
      content.includes('深度分析'),
      content.includes('改进建议'),
      content.includes('提出问题'),
      content.includes('相关参考'),
      
      // 检查交互功能
      content.includes('handleCardCreate'),
      content.includes('handleClickOutside')
    ];
    
    const passed = checks.filter(check => check).length;
    console.log(`  智能卡片菜单检查: ${passed}/${checks.length} 项通过`);
    
    return passed === checks.length;
  } catch (error) {
    console.log(`  智能卡片菜单验证错误: ${error.message}`);
    return false;
  }
});

// 测试5: 大纲数据生成验证
runTest('大纲数据生成验证', () => {
  try {
    const syncPath = path.join(PROJECT_ROOT, 'src/utils/contentSync.js');
    const content = fs.readFileSync(syncPath, 'utf8');
    
    const checks = [
      // 检查大纲生成函数
      content.includes('generateOutlineFromSections'),
      content.includes('section.id'),
      content.includes('section.title'),
      content.includes('section.level'),
      
      // 检查数据结构
      content.includes('id: section.id'),
      content.includes('text: section.title'),
      content.includes('level: Math.max(1, section.level + 1)'),
      
      // 检查边界处理
      content.includes('if (!sections || sections.length === 0)')
    ];
    
    const passed = checks.filter(check => check).length;
    console.log(`  大纲数据生成检查: ${passed}/${checks.length} 项通过`);
    
    return passed === checks.length;
  } catch (error) {
    console.log(`  大纲数据生成验证错误: ${error.message}`);
    return false;
  }
});

// 测试6: 事件处理链完整性验证
runTest('事件处理链完整性验证', () => {
  try {
    const prdEditorPath = path.join(PROJECT_ROOT, 'src/components/PRDEditor/PRDEditor.js');
    const editorContent = fs.readFileSync(prdEditorPath, 'utf8');
    
    const rendererPath = path.join(PROJECT_ROOT, 'src/components/Common/PRDContentRenderer.js');
    const rendererContent = fs.readFileSync(rendererPath, 'utf8');
    
    const checks = [
      // 大纲点击事件链
      editorContent.includes('handleOutlineItemClick'),
      editorContent.includes('handleOutlineClick(item)'),
      editorContent.includes('onItemClick={handleOutlineItemClick}'),
      
      // 文本选中事件链
      editorContent.includes('handleTextSelection'),
      editorContent.includes('onTextSelection={handleTextSelection}'),
      rendererContent.includes('onTextSelection'),
      
      // 智能卡片创建事件链
      editorContent.includes('handleCreateCard'),
      editorContent.includes('onCreateCard={handleCreateCard}'),
      rendererContent.includes('onCreateCard')
    ];
    
    const passed = checks.filter(check => check).length;
    console.log(`  事件处理链检查: ${passed}/${checks.length} 项通过`);
    
    return passed >= checks.length * 0.9; // 90%通过率
  } catch (error) {
    console.log(`  事件处理链验证错误: ${error.message}`);
    return false;
  }
});

// 输出测试结果
console.log('\n' + '='.repeat(50));
console.log(`📊 测试结果: ${testsPassed}/${testsTotal} 通过`);

if (testsPassed === testsTotal) {
  console.log('🎉 大纲导航和文本选中功能修复验证全部通过！');
  console.log('\n✅ 修复内容:');
  console.log('  • 大纲导航容器选择器已修复');
  console.log('  • PRD编辑器容器类名已添加');
  console.log('  • 文本选中事件已添加到所有内容区域');
  console.log('  • 智能卡片菜单功能完整');
  console.log('  • 事件处理链完整连接');
  
  console.log('\n🚀 功能特性:');
  console.log('  • 点击大纲导航可定位到相应章节');
  console.log('  • 选中文本可弹出AI卡片菜单');
  console.log('  • 支持表格和文本内容的选中');
  console.log('  • 4种智能分析卡片类型');
  
  process.exit(0);
} else {
  console.log('❌ 部分测试失败，需要进一步检查');
  console.log('\n建议检查项:');
  console.log('- 容器选择器是否正确');
  console.log('- 事件绑定是否完整');
  console.log('- 组件导入是否正确');
  console.log('- 函数调用链是否连通');
  
  process.exit(1);
}
