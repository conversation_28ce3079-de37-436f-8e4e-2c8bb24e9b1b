// BSV特性需求文档 - 基于PDF 1:1 精确提取的内容
export const realPRDContent = {
  title: "BSV特性需求文档",
  sections: [
    {
      id: "cover",
      title: "BSV特性需求文档",
      level: 0,
      content: `BSV特性需求文档

编制/变更日志

| 序号 | 版本 | PRD ID | 修改章节及内容 | 修改人 | 审核人 | 批准人 | 修改日期 |
|------|------|--------|----------------|--------|--------|--------|----------|
| 1 | V1.0.0 | S-123-66 | 初次创建 | 蔡飞 | / | / | 2023-12-05 |
| 2 | V1.0.1 | S-123-66 | / | 蔡飞 | / | / | 2024-03-08 |
| 3 | V1.0.2 | S-123-66 | 1.增加关联系统故障相关文言提示相关usecase：3.4.1、3.5.1、3.5.2<br>2.增加与行车功能的相互抑制usecase：3.2.1、3.3.1、3.1.1、3.1.2<br>3.增加与BSD联动的usecase:3.3.2 | 张梓煜于芷涵 | 郑旭 | 郑福民 周希希 | 2024-10-29 |`
    },
    {
      id: "section-1",
      title: "一、文档综述",
      level: 1,
      content: `一、文档综述`
    },
    {
      id: "section-1-1",
      title: "1.1 文档背景",
      level: 2,
      content: `1.1 文档背景

本文应用于吉利集团的侧视盲区辅助功能需求文档，详细描述侧视盲区辅助的场景分析，应用流程，详细策略设计等。

项目实现过程中，请以详细设计为基础进行需求实现，如遇到歧义或变更，请及时沟通。`
    },
    {
      id: "section-1-2",
      title: "1.2 文档范围",
      level: 2,
      content: `1.2 文档范围

本文档介绍了侧视盲区辅助（Blind Spot Visualization, BSV）的产品概述和具体的场景设计。产品概述中介绍了产品的定义、使用场景、车型配置说明、功能架构、功能流程。`
    },
    {
      id: "section-1-3",
      title: "1.3 术语解释",
      level: 2,
      content: `1.3 术语解释

| 序号 | 术语 | 定义 |
|------|------|------|
| 1 | ODD | Operational Design Domain |
| 2 | HMI | Human-Machine Interface |
| 3 | AVM | Around view Monitor |
| 4 | CSD | Centerstack Display |
| 5 | HUD | Heads Up Display |
| 6 | BSD | Blind Spot Detection |
| 7 | DOW | Door Opening Warning |`
    },
    {
      id: "section-1-4",
      title: "1.4 文档关系",
      level: 2,
      content: `1.4 文档关系`
    },
    {
      id: "section-1-4-1",
      title: "1.4.1 当前功能相关文档",
      level: 3,
      content: `1.4.1 当前功能相关文档

| UI/UE文档 | FR文档 | DR文档 | 跨域关联PRD文档 |
|-----------|--------|--------|-----------------|
| v1.0.1 L946_BSV_UEV1.0.2.pdf | BSV-EADP-视频流.pdf<br>BSV-HMI-Error Message.pdf<br>BSV-ASM-Active to Failure.pdf<br>BSV-HMI-Exit.pdf<br>BSV-ASM-Active.pdf<br>BSV-HMI-Active.pdf<br>BSV-UserInput-拨杆.pdf<br>BSV-BrakeControl-车速.pdf<br>BSV-ASM-Exit.pdf<br>BSV-Propulsion-挡位.pdf | BSV-DR(ASM)-拨杆激活.pdf<br>BSV-DR(ASM)-拨杆退出.pdf<br>BSV-DR(ASM)-摄像头故障.pdf<br>BSV-DR(ASM)-跳转到故障.pdf<br>BSV-DR(ASM)-系统故障.pdf | DOW：FD-S-120-62 Door Open Warning 开门预警 V1.0.7(1).pdf<br>AVM：VPA-Basic FDR.pdf<br>BSD：FD-S-123-07 Lane Change Assist 变道辅助 V1.0.8.pdf |
| V1.0.2 | / | url:swap://10.24.24.227:1770/x0400000019C3AD93<br>url:swap://10.24.24.227:1770/x0400000019C3E534 | / | / |`
    },
    {
      id: "section-1-4-2",
      title: "1.4.2 政策法规文件",
      level: 3,
      content: `1.4.2 政策法规文件

以下法规仅用于功能开发参考。

| 区域 | 法规 | 法规相关内容 | 与本功能相关性 |
|------|------|-------------|----------------|
| 中国 | GB/T 39265（2020） | 道路车辆盲区监测系统性能要求及试验方法，对BSD系统的性能要求及测试步骤做了介绍。<br>中国暂无直接对侧视盲区辅助功能的相关规定。 | BSD联动报警时，BSV视图会有所体现。<br>5.1.1讲述了车辆盲区及参测范围 |
| 欧洲 | ECE R166 | 《关于就车辆前部和侧面附近弱势道路使用者对驾驶员的提醒功能而批准装置和机动车辆的统一规定》，是联合国制定的一项旨在提高道路交通安全性的重要法规，适用于需要安装提醒驾驶员注意车辆前部和侧面附近弱势道路使用者的装置的机动车辆及其相关设备。<br>这些设备可能包括雷达、摄像头、传感器等，它们能够实时监测车辆周围的环境，并向驾驶员提供警示信息。 | 15.2讲述了车辆盲区及参测范围 |`
    },
    {
      id: "section-1-4-3",
      title: "1.4.3 行业规范文件",
      level: 3,
      content: `1.4.3 行业规范文件

暂无相关行业规范。`
    },
    {
      id: "section-2",
      title: "二、产品概述",
      level: 1,
      content: `二、产品概述`
    },
    {
      id: "section-2-1",
      title: "2.1 产品场景及概要说明",
      level: 2,
      content: `2.1 产品场景及概要说明

**产品场景说明：** 在行驶过程中（时速高于30km/h），当驾驶员发出转向信号时，CSD和HUD即显示对应侧盲区实时视频流，为用户提供视野支持，提升驾驶安全性。

| 变道/汇入主路/汇出主路场景 | 转弯场景 |
|---------------------------|----------|
| 侧后方视野，观察侧后方来车等，预防事故 | 扩展侧后方视野，观察侧后方直行车辆等 |

**产品可用地点：** 满足一定光照条件的路段

**产品使用方式：** 设置项开启后，通过转向拨杆激活功能，通过拨杆或换档退出功能

**产品关键参数：**
- 盲区显示视频流尺寸
- 视频流清晰度
- 延迟`
    },
    {
      id: "section-2-2",
      title: "2.2 产品架构/系统架构",
      level: 2,
      content: `2.2 产品架构/系统架构

简单功能不需要架构分解。`
    },
    {
      id: "section-2-3",
      title: "2.3 产品梯度配置说明",
      level: 2,
      content: `2.3 产品梯度配置说明

| 平台 | 舱泊 | 6v | 10v | 11v |
|------|------|----|----|-----|
| 功能配置 | - | - | - | 用户D档拨动转向拨杆，显示对应侧盲区视图<br>挂车模式下挂R挡，显示双侧盲区视图<br>后门开启激活DOW报警，二排屏显示对应侧盲区视图 |`
    },
    {
      id: "section-2-4",
      title: "2.4 功能流程图",
      level: 2,
      content: `2.4 功能流程图

**正常模式下：**
[功能流程图]

**挂车模式下：**
[功能流程图]

**DOW模式下：**
[功能流程图]`
    },
    {
      id: "section-2-5",
      title: "2.5 关键状态流转",
      level: 2,
      content: `2.5 关键状态流转

**T1：OFF to Initialize**
VMM模式满足

**T2：Initialize to Standby**
系统无故障and两颗侧后摄像头之一无故障andVMM模式=Driving/Active/Convenience

**T3：Standby to Active**
正常模式下转向拨杆激活or挂车模式下R档激活orDOW模式下开门触发二级报警

**T4：Active to Standby**
同时满足以下条件：
（1）正常模式下软开关退出or拨杆回正退出or关联系统故障退出or泊车功能开启退出or行车辅助功能开启退出or切换档位退出or设置项关闭退出【没有后排屏】
（2）拖车模式下软开关退出or拨杆回正退出or关联系统故障退出or切换档位退出orR切非R车速超15退出or设置项关闭退出【没有后排屏】
（3）DOW模式下软开关退出or车门关闭退出or前后排屏都关闭or设置项关闭退出andVMM满足条件【有后排屏】

**T5：Initialize to Failure**
关联系统故障or双侧摄像头故障andVMM满足条件

**T6：Failure to Standby**
关联系统故障回复and摄像头恢复andVMM满足条件

**T7：Standby to Failure**
关联系统故障or双侧摄像头故障andVMM满足条件

**T8：Failure to Active**
一颗以上摄像头5s内恢复and系统故障5s内恢复andVMM满足条件and满足T3条件

**T9：Active to Failure**
关联系统故障or两颗摄像头故障andVMM满足条件

**T10：On to Off**
VMM不满足`
    },
    {
      id: "section-2-6",
      title: "2.6 配置",
      level: 2,
      content: `2.6 配置`
    },
    {
      id: "section-2-6-1",
      title: "2.6.1 硬件配置",
      level: 3,
      content: `2.6.1 硬件配置

配置车型必须配有侧后视摄像头
ADCU与DHU之间须有千兆以太网作为视频流传输通道`
    },
    {
      id: "section-2-6-2",
      title: "2.6.2 EBOC",
      level: 3,
      content: `2.6.2 EBOC

E100 BLIND SPOT VISSUAL 盲区可视
E101 N BSV 无盲区可视
E102 BSV 盲区可视`
    },
    {
      id: "section-2-6-3",
      title: "2.6.3 CCP",
      level: 3,
      content: `2.6.3 CCP

当参数CCP #781 == 01时，无需实现功能。
当参数CCP #781 == 02时，需要实现功能。`
    },
    {
      id: "section-2-7",
      title: "2.7 功能列表",
      level: 2,
      content: `2.7 功能列表

| 一级功能 | 二级功能 | 三级功能 |
|----------|----------|----------|
| 侧视盲区辅助 | 设置项 | 增加侧视盲区辅助功能设置项开关<br>用户查看侧视盲区辅助小i文言<br>打开侧视盲区辅助设置项<br>关闭侧视盲区辅助设置项<br>侧视盲区辅助设置项下电记忆 |
| | 功能激活 | 转向拨杆激活左侧侧视盲区辅助<br>转向拨杆激活右侧侧视盲区辅助 |
| | 功能退出 | 用户回正转向拨杆退出侧视盲区辅助<br>拨杆自动回正退出侧视盲区辅助<br>影像软开关退出侧视盲区辅助<br>R档/P档/N档退出侧视盲区辅助<br>APA开启/HPA开启/AVM开启（包括RCTA激活和PDC触发）退出侧视盲区辅助<br>故障退出左侧视盲区辅助<br>故障退出右侧视盲区辅助<br>设置项开关退出侧视盲区辅助<br>ICC/NOA/CNOA/ALCA/HWA功能开启退出侧视盲区辅助 |
| | 视图位置调整 | 视图位置调整<br>视图位置下电记忆 |
| | 与其他功能的联动逻辑 | 行车辅助功能抑制侧视盲区辅助功能<br>BSV视图上增加BSD报警视觉提示 |
| | 故障提示 | 左侧视盲区辅助激活前发生故障<br>右侧视盲区辅助激活前发生故障 |
| | 工作电源模式 | 工作电源模式 |
| | 挂车模式 | 挂车模式下转向拨杆激活双侧视盲区辅助拼接视图<br>挂车模式下R挡激活侧视盲区辅助拼接视图<br>挂车模式下软开关退出侧视盲区辅助拼接视图<br>挂车模式下切换挡位退出侧视盲区辅助拼接视图<br>挂车模式下抑制AVM<br>挂车模式下双侧视盲区辅助激活前发生故障<br>挂车模式下双侧视盲区辅助激活后发生故障<br>挂车模式下单侧视盲区辅助激活发生故障<br>挂车模式下设置项开关退出侧视盲区辅助<br>挂车模式下用户回正转向拨杆退出侧视盲区辅助<br>挂车模式下拨杆自动回正退出侧视盲区辅助 |
| | DOW模式 | 后排DOW二级报警激活后排侧视盲区辅助视图<br>后排关门退出后排侧视盲区辅助视图<br>软开关退出后排侧视盲区辅助视图<br>后排侧视盲区辅助激活前发生故障<br>后排侧视盲区辅助激活后发生故障<br>前后排视图显示逻辑<br>后排DOW模式下设置项开关退出侧视盲区辅助 |`
    },
    {
      id: "section-3",
      title: "三、功能场景设计",
      level: 1,
      content: `三、功能场景设计`
    },
    {
      id: "section-3-1",
      title: "3.1 功能激活",
      level: 2,
      content: `3.1 功能激活`
    },
    {
      id: "section-3-1-1",
      title: "3.1.1 转向拨杆激活左侧侧视盲区辅助",
      level: 3,
      content: `3.1.1 转向拨杆激活左侧侧视盲区辅助

| 内容说明 | 原型 |
|----------|------|
| **前置条件** | 包含原型图、负一屏、文言、软开关、语音及泛化等 |

1. 车辆具备侧视盲区辅助功能
2. CSD屏幕及其他显示单元正常
3. 设置项侧视盲区辅助开关开启
4. 非行车辅助功能控车状态，D档下速度超过30km/h,最高车速无限制
5. 侧视盲区辅助功能关联的域控制器、左侧后摄像头、通信均正常
6. 监测未启用拖车钩

**触发事件**
用户通过转向杆激活左转向灯；

**主流程**
1. 用户通过转向杆激活左转向灯；
2. 在转向拨杆拨下的300ms内中控特定区域显示左侧后方影像（影像尺寸、具体显示位置在第四节定义）；
3. case结束

**可选流程1**
同时满足以下条件时：
1. 车辆已激活右侧侧视盲区辅助
2. 用户将转向杆直接由最上拨到最下方（左侧），拨杆中间位停留不超过200ms
执行以下操作：
视频框不退出，由右侧后方影像，切换为左侧后方影像

**可选流程2**
同时满足以下条件时：
1. 车辆已激活左侧侧视盲区辅助
2. 用户将转向杆回正后，迅速拨到最下方（左侧），拨杆中间位停留不超过200ms
执行以下操作：
视图框不退出，持续显示左侧后方影像

**备注说明**
/`
    },
    {
      id: "section-3-1-2",
      title: "3.1.2 转向拨杆激活右侧侧视盲区辅助",
      level: 3,
      content: `3.1.2 转向拨杆激活右侧侧视盲区辅助

| 内容说明 | 原型 |
|----------|------|
| **前置条件** | 包含原型图、负一屏、文言、软开关、语音及泛化等 |

1. 车辆具备侧视盲区辅助功能
2. CSD屏幕及其他显示单元正常
3. 设置项侧视盲区辅助开关开启
4. 非行车辅助功能控车状态，D档下速度超过30km/h，最高车速无限制
5. 侧视盲区辅助功能：域控制器、右侧后摄像头、通信均正常
6. 检测未启用拖车钩

**触发事件**
用户通过转向杆激活右转向灯；

**主流程**
1. 用户通过转向杆激活右转向灯；
2. 在转向拨杆拨下的300ms内中控特定区域显示右侧后方影像（影像尺寸、具体显示位置在第四节定义）；
3. case结束

**可选流程1**
同时满足以下条件时：
1. 车辆已激活左侧侧视盲区辅助
2. 用户将转向杆直接由最下拨到最上方（右侧），拨杆中间位停留不超过200ms
执行以下操作：
视频框不退出，由左侧后方影像，切换为右侧后方影像

**可选流程2**
同时满足以下条件时：
1. 车辆已激活左侧侧视盲区辅助
2. 用户将转向杆回正后，迅速拨到最上方（左侧），拨杆中间位停留不超过200ms
执行以下操作：
视图框不退出，持续显示右侧后方影像

**备注说明**
/`
    },
    {
      id: "section-3-2",
      title: "3.2 功能退出",
      level: 2,
      content: `3.2 功能退出`
    },
    {
      id: "section-3-2-1",
      title: "3.2.1 ICC/NOA/CNOA/ALCA/HWA功能开启退出侧视盲区辅助",
      level: 3,
      content: `3.2.1 ICC/NOA/CNOA/ALCA/HWA功能开启退出侧视盲区辅助

| 内容说明 | 原型 |
|----------|------|
| **前置条件** | 包含原型图、负一屏、文言、软开关、语音及泛化等 |

1. 车辆具备侧视盲区辅助功能
2. CSD屏幕及其他显示单元正常
3. 侧视盲区辅助功能：域控制器、侧后摄像头、通信均正常
4. 侧后盲区辅助功能已激活

**触发事件**
用户激活行车辅助功能（ICC/NOA/CNOA/ALCA/HWA）

**主流程**
1. 用户激活行车辅助功能（ICC\NOA\CNOA\ALCA\HWA）
2. CSD侧后盲区辅助视图退出，进入行车辅助功能控车界面，无文言提示
3. case结束

**备注说明**
/`
    },
    {
      id: "section-3-3",
      title: "3.3 与其他功能的联动逻辑",
      level: 2,
      content: `3.3 与其他功能的联动逻辑`
    },
    {
      id: "section-3-3-1",
      title: "3.3.1 行车辅助功能抑制侧视盲区辅助功能",
      level: 3,
      content: `3.3.1 行车辅助功能抑制侧视盲区辅助功能

| 内容说明 | 原型 |
|----------|------|
| **前置条件** | 包含原型图、负一屏、文言、软开关、语音及泛化等 |

1. 车辆具备侧视盲区辅助功能
2. CSD屏幕及其他显示单元正常
3. 设置项侧视盲区辅助开关开启
4. 侧视盲区辅助功能关联的域控制器、侧后摄像头、通信均正常
5. D档下速度超过30km/h
6. 行车辅助功能（ICC\NOA\CNOA\ALCA\HWA）已激活并处于控车状态

**触发事件**
转向拨杆激活左/右转向灯

**主流程**
1. 转向拨杆激活左/右转向灯
2. 侧后盲区辅助功能不被激活，CSD保持当前界面，无文言提示
3. case结束

**备注说明**
/`
    },
    {
      id: "section-3-3-2",
      title: "3.3.2 BSV视图上增加BSD报警视觉提示",
      level: 3,
      content: `3.3.2 BSV视图上增加BSD报警视觉提示

| 内容说明 | 原型 |
|----------|------|
| **前置条件** | 包含原型图、负一屏、文言、软开关、语音及泛化等 |

1. 车辆具备侧视盲区辅助和BSD功能
2. CSD屏幕和其他显示单元正常
3. 设置项侧视盲区辅助、BSD开关开启
4. 侧视盲区辅助功能关联的域控制器、侧后摄像头、通信均正常
5. 非行车辅助功能控车状态，D档下速度超过30km/h
6. 侧后盲区辅助功能已激活

**触发事件**
侧后盲区内有车辆接近自车，激活BSD二级报警

**主流程**
1. 侧后盲区内有车辆接近自车，激活BSD二级报警
2. BSV视图的来车侧对应边缘有红色动效闪烁，闪烁频率和BSD二级报警频率保持一致，延时：<=120ms，闪烁频率：4hz，125ms on/125ms off
3. 红色动效随BSD二级报警消失而退出
4. case结束

**备注说明**
/`
    },
    {
      id: "section-3-4",
      title: "3.4 挂车模式",
      level: 2,
      content: `3.4 挂车模式`
    },
    {
      id: "section-3-4-1",
      title: "3.4.1 挂车模式下单侧视盲区辅助激活发生故障",
      level: 3,
      content: `3.4.1 挂车模式下单侧视盲区辅助激活发生故障

| 内容说明 | 原型 |
|----------|------|
| **前置条件** | 包含原型图、负一屏、文言、软开关、语音及泛化等 |

1. 车辆具备侧视盲区辅助功能
2. CSD屏幕显示正常
3. 设置项侧视盲区辅助开关开启
4. 侧视盲区辅助功能：单侧视摄像头故障或单侧通信故障或关联系统故障
5. 检测到自车同房车连接（不包括机械拖车钩直接拖车）

**触发事件**
用户通过转向杆/R挡侧视盲区辅助拼接视图；

**主流程**
1. 侧视盲区辅助功能：左侧视摄像头故障或左侧通信故障
2. 用户通过转向杆/R挡侧视盲区辅助拼接视图；
3. 中控显示侧视盲区拼接视图，左侧视图显示文言：左侧视盲区辅助功能受限，功能恢复前文言在视图上常显，文言跟影像一起退出
4. case结束

**可选流程1**
1. 侧视盲区辅助功能：右侧视摄像头故障或右侧通信故障
2. 用户通过转向杆/R挡侧视盲区辅助拼接视图；
3. 中控显示侧视盲区拼接视图，右侧视图显示文言：右侧视盲区辅助功能受限，功能恢复前文言在视图上常显，文言跟影像一起退出
4. case结束

**可选流程2**
1. 侧视盲区辅助拼接视图已激活
2. 侧视盲区辅助功能：左侧视摄像头故障或左侧通信故障
3. 侧视盲区拼接视图不退出，左侧视图显示文言：左侧视盲区辅助功能受限，功能恢复前文言在视图上常显，文言跟影像一起退出
4. case结束

**可选流程3**
1. 侧视盲区辅助拼接视图已激活
2. 侧视盲区辅助功能：右侧视摄像头故障或右侧通信故障
3. 侧视盲区拼接视图不退出，右侧视图显示文言：右侧视盲区辅助功能受限，功能恢复前文言在视图上常显，文言跟影像一起退出
4. case结束

**可选流程4**
1. 侧视盲区辅助功能：关联系统故障
2. 用户拨动转向杆/R档
3. 中控显示文言toast：侧视盲区辅助功能受限，5S后文言退出，一个点火周期文言只提示一次。文言显示过程中，用户回正拨杆/切入P档/R档切入D/N档且速度超15km/h，文言不退出。
4. case结束

**可选流程5**
1. 侧视盲区辅助拼接视图已激活
2. 侧视盲区辅助功能：关联系统故障
3. 中控显示文言toast：侧视盲区辅助功能受限，5s后文言退出。文言显示过程中，用户回正拨杆/切入P档/R档切入D/N档且速度超15km/h，文言不退出。
4. case结束

**备注说明**
/`
    },
    {
      id: "section-3-5",
      title: "3.5 后排DOW模式",
      level: 2,
      content: `3.5 后排DOW模式`
    },
    {
      id: "section-3-5-1",
      title: "3.5.1 后排侧视盲区辅助激活前发生故障",
      level: 3,
      content: `3.5.1 后排侧视盲区辅助激活前发生故障

| 内容说明 | 原型 |
|----------|------|
| **前置条件** | 包含原型图、负一屏、文言、软开关、语音及泛化等 |

1. 车辆具备侧视盲区辅助功能和DOW功能
2. 后排CSD屏幕显示正常
3. 设置项开门预警开关开启
4. 侧视盲区辅助功能关联的域控制器、对应侧后摄像头或通信之一发生故障

**触发事件**
后排车门打开触发DOW二级报警

**主流程**
1. 后排侧视盲区辅助未激活
2. 侧视盲区辅助功能单侧后摄像头或通信之一发生故障
3. 触发后排侧视盲区辅助；显示置黑影像，并提示文言：左/右侧视盲区辅助功能受限，影像窗口不退出，警示标识提示不变，故障后退出逻辑遵循故障前的退出逻辑
4. case结束

**可选流程1**
1. 后排侧视盲区辅助未激活
2. 侧视盲区辅助功能双侧后摄像头或通信之一发生故障
3. 触发后排侧视盲区辅助；显示置黑影像，并提示文言：侧视盲区辅助功能受限，影像窗口不退出，警示标识提示不变，故障后退出逻辑遵循故障前的退出逻辑
4. case结束

**可选流程2**
1. 后排侧视盲区辅助未激活
2. 侧视盲区辅助功能关联系统故障
3. 用户打开后车门触发DOW二级报警
4. 后排显示文言toast：侧视盲区辅助功能受限，5s后文言退出。一个点火周期内，文言只显示一次。文言显示过程中，用户关闭车门，文言不退出。
5. case结束

**备注说明**
/`
    },
    {
      id: "section-3-5-2",
      title: "3.5.2 后排侧视盲区辅助激活后发生故障",
      level: 3,
      content: `3.5.2 后排侧视盲区辅助激活后发生故障

| 内容说明 | 原型 |
|----------|------|
| **前置条件** | 包含原型图、负一屏、文言、软开关、语音及泛化等 |

1. 车辆具备侧视盲区辅助功能和DOW功能
2. 后排CSD屏幕显示正常
3. 设置项开门预警开关开启
4. 侧视盲区辅助功能关联的域控制器、侧后摄像头、通信均正常

**触发事件**
后排侧视盲区辅助激活后发生故障

**主流程**
1. 后排侧视盲区辅助已激活
2. 侧视盲区辅助功能对应单侧后摄像头或通信发生故障
3. 影像置黑，并提示文言：左/右侧视盲区辅助功能受限，影像窗口不退出，警示标识提示不变，故障后退出逻辑遵循故障前的退出逻辑
4. case结束

**可选流程1**
1. 后排侧视盲区辅助已激活
2. 侧视盲区辅助功能双侧后摄像头或通信发生故障
3. 影像置黑，并提示文言：侧视盲区辅助功能受限，影像窗口不退出，警示标识提示不变，故障后退出逻辑遵循故障前的退出逻辑
4. case结束

**可选流程2**
1. 后排侧视盲区辅助已激活
2. 侧视盲区辅助功能关联系统故障
3. 后排显示文言toast：侧视盲区辅助功能受限，5s后文言退出。文言显示过程中，用户关闭车门，文言不退出。
4. case结束

**备注说明**
/`
    },
    {
      id: "section-4",
      title: "四、非功能说明",
      level: 1,
      content: `四、非功能说明`
    },
    {
      id: "section-4-1",
      title: "4.1 功能指标要求",
      level: 2,
      content: `4.1 功能指标要求

| 功能指标 | 详细描述 | 关联use case id |
|----------|----------|-----------------|
| 视场角 | 水平60°/垂直37° | / |
| 车身占比 | 10%-15% | / |
| 帧率 | 大于50 | / |
| 激活延迟 | ＜300ms | / |
| 显示延迟 | ＜100ms | / |
| 显示尺寸 | ＞6寸 | / |
| 摄像头分辨率 | ＞100万 | / |
| 视频流分辨率 | 1280×720 | / |`
    },
    {
      id: "section-4-2",
      title: "4.2 数据指标需求",
      level: 2,
      content: `4.2 数据指标需求

| 序号 | 类别 | 指标项 | 指标名称 | 指标逻辑 | 备注 |
|------|------|--------|----------|----------|------|
| 1 | 运营类 | BSV活跃率 | BSV日活跃率 | 计算方式=【每日激活BSV车辆数】/【每日启动的车辆数】<br>【每日激活BSV车辆数】:在一个自然日周期内，激活BSV的车辆，即可计数<br>【每日启动的车辆数】：在一个自然日周期内，车辆上电或车辆发动机启动，即可计数 | 指定按日筛选或指定时间段筛选 |
| 2 | | | BSV月活跃率 | 计算方式=【每月主动激活BSV车辆数】/【每月启动的车辆数】<br>【每月主动激活BSV车辆数】:在一个自然月周期内，激活BSV的车辆，即可计数<br>【每月启动的车辆数】：在一个自然月周期内，车辆上电或车辆发动机启动，即可计数 | 指定按月筛选或指定时间段筛选 |
| 3 | 产品类 | | BSV设置项使用频率 | BSV设置项使用频率 | 计算方式=【该设置项开启或关闭成功次数】/【指定时间段】<br>【该设置项开启或关闭成功次数】:在指定时间段内，该设置项被开启或关闭成功，即可计数<br>【指定时间段】:根据需求选择的具体时间段 |
| 4 | | | BSV设置项状态占比 | BSV设置项状态占比 | 计算方式=【该设置项保持开启的车辆数】/【车辆总数】<br>【该设置项保持开启的车辆数】:在指定时间段，该设置项处于开启状态，即可计数<br>【车辆总数】：在指定时间段内，选定的车辆总数 | |
| 5 | 故障 | | BSV不同故障提示文言的频率 | BSV不同故障提示文言的频率 | 计算方式=【该故障触发文言的次数】/【指定时间段】<br>【该故障触发文言的次数】:在指定时间段内，该故障触发文言，即可计数<br>故障触发分类包括：触发'侧视摄像头可能被遮挡，请清理'/触发'侧视盲区辅助功能受限'<br>【指定时间段】:根据需求选择的具体时间段 | |`
    },
    {
      id: "section-4-3",
      title: "4.3 补充说明",
      level: 2,
      content: `4.3 补充说明`
    },
    {
      id: "section-4-3-1",
      title: "4.3.1 文言提示汇总",
      level: 3,
      content: `4.3.1 文言提示汇总

| 序号 | 场景 | 文言 | 备注 |
|------|------|------|------|
| 1 | 左侧摄像头、通信故障前/后激活功能 | 左侧视盲区辅助功能受限 | 5S后文言和影像退出（激活前故障每个点火周期触发一次，激活后故障无相关要求） |
| 2 | 右侧摄像头、通信故障前/后激活功能 | 右侧视盲区辅助功能受限 | 5S后文言和影像退出（激活前故障每个点火周期触发一次，激活后故障无相关要求） |
| 3 | 域控、双侧摄像头、通信故障前/后激活功能 | 侧视盲区辅助功能受限 | 5S后文言toast退出（激活前故障每个点火周期触发一次，激活后故障无相关要求） |`
    }
  ]
};

// BSV评审问题数据 - 保持现有的10个问题不变
export const mockIssues = [
  {
    id: "P001",
    section: "section-2-2",
    type: "error",
    priority: "high",
    title: "产品架构/系统架构图完全缺失",
    description: "文档中2.2章节仅说明'简单功能不需要架构分解'，但缺少具体的系统架构图和组件说明，无法理解BSV功能的技术实现架构。",
    suggestion: "补充完整的系统架构图，包括硬件组件（摄像头、ADCU、DHU等）、软件模块、数据流向和接口定义。",
    aiRecommendation: "建议参考其他ADAS功能的架构文档模板，绘制BSV功能的端到端架构图，明确各组件职责和交互关系。",
    confidence: 95,
    rule: "PRD架构完整性要求",
    ruleId: "ARCH-001",
    sectionPath: ["二、产品概述", "2.2 产品架构/系统架构"],
    targetText: "简单功能不需要架构分解",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P002",
    section: "section-1-4-1",
    type: "warning",
    priority: "medium",
    title: "相关文档版本不一致且缺少访问路径",
    description: "1.4.1章节中列出的相关文档版本信息不完整，部分文档只有文件名没有版本号，且缺少具体的访问路径或存储位置。",
    suggestion: "统一文档版本管理，为每个相关文档提供完整的版本信息和访问路径，建立文档关联矩阵。",
    aiRecommendation: "建议建立统一的文档管理平台，确保所有相关文档的版本同步和可追溯性。",
    confidence: 82,
    rule: "文档关联完整性规范",
    ruleId: "DOC-001",
    sectionPath: ["一、文档综述", "1.4 文档关系", "1.4.1 当前功能相关文档"],
    targetText: "相关文档列表",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P003",
    section: "section-2-7",
    type: "suggestion",
    priority: "low",
    title: "功能列表层级结构可优化",
    description: "2.7章节的功能列表虽然完整，但层级结构较深，部分三级功能可以合并或重新组织，提高可读性。",
    suggestion: "重新梳理功能列表的层级结构，将相关性强的功能进行归类，减少层级深度。",
    aiRecommendation: "建议采用功能域的方式重新组织，如激活域、退出域、联动域等，使功能分类更加清晰。",
    confidence: 70,
    rule: "功能列表组织规范",
    ruleId: "FUNC-001",
    sectionPath: ["二、产品概述", "2.7 功能列表"],
    targetText: "功能列表结构",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P004",
    section: "section-1-4-2",
    type: "warning",
    priority: "medium",
    title: "法规符合性分析不够深入",
    description: "1.4.2章节提到了相关法规，但对BSV功能如何符合这些法规要求的分析较为表面，缺少具体的符合性说明。",
    suggestion: "深入分析BSV功能与各项法规的符合性，明确需要满足的具体技术指标和测试要求。",
    aiRecommendation: "建议邀请法规专家参与评审，确保功能设计完全符合相关法规要求。",
    confidence: 78,
    rule: "法规符合性分析要求",
    ruleId: "REG-001",
    sectionPath: ["一、文档综述", "1.4 文档关系", "1.4.2 政策法规文件"],
    targetText: "法规相关内容",
    paragraphIndex: 2,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P005",
    section: "section-2-4",
    type: "suggestion",
    priority: "low",
    title: "功能流程图缺少异常处理路径",
    description: "2.4章节的功能流程图展示了正常模式、挂车模式和DOW模式的基本流程，但缺少异常情况的处理路径。",
    suggestion: "在流程图中补充异常处理路径，如摄像头故障、通信中断等情况的处理流程。",
    aiRecommendation: "建议采用泳道图的方式，清晰展示不同组件在异常情况下的交互和处理逻辑。",
    confidence: 75,
    rule: "流程图完整性要求",
    ruleId: "FLOW-001",
    sectionPath: ["二、产品概述", "2.4 功能流程图"],
    targetText: "功能流程图",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P006",
    section: "section-2-5",
    type: "warning",
    priority: "medium",
    title: "状态转换条件描述不够精确",
    description: "2.5章节的关键状态流转中，部分状态转换条件描述较为模糊，如'VMM模式满足'等条件缺少具体定义。",
    suggestion: "明确定义所有状态转换条件，提供具体的判断标准和参数值。",
    aiRecommendation: "建议使用状态机图配合详细的条件表格，确保状态转换逻辑的准确性和可实现性。",
    confidence: 85,
    rule: "状态机定义精确性要求",
    ruleId: "STATE-001",
    sectionPath: ["二、产品概述", "2.5 关键状态流转"],
    targetText: "状态转换条件",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P007",
    section: "section-2-5",
    type: "error",
    priority: "high",
    title: "状态转换逻辑存在潜在冲突",
    description: "在T4状态转换条件中，同时存在多个退出条件的组合判断，可能存在逻辑冲突或优先级不明确的问题。",
    suggestion: "重新梳理状态转换逻辑，明确各种退出条件的优先级和组合规则，避免逻辑冲突。",
    aiRecommendation: "建议使用决策树或优先级矩阵来明确复杂条件下的状态转换逻辑。",
    confidence: 88,
    rule: "状态机逻辑一致性要求",
    ruleId: "STATE-002",
    sectionPath: ["二、产品概述", "2.5 关键状态流转"],
    targetText: "T4状态转换条件",
    paragraphIndex: 4,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P008",
    section: "section-2-6-1",
    type: "warning",
    priority: "medium",
    title: "硬件配置要求不够详细",
    description: "2.6.1章节提到了侧后视摄像头和千兆以太网的要求，但缺少具体的技术规格和性能参数。",
    suggestion: "补充硬件配置的详细技术规格，包括摄像头分辨率、帧率、视场角等关键参数。",
    aiRecommendation: "建议参考行业标准，明确硬件配置的最低要求和推荐配置。",
    confidence: 80,
    rule: "硬件配置详细性要求",
    ruleId: "HW-001",
    sectionPath: ["二、产品概述", "2.6 配置", "2.6.1 硬件配置"],
    targetText: "硬件配置要求",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P009",
    section: "section-4-1",
    type: "suggestion",
    priority: "low",
    title: "功能指标缺少测试方法说明",
    description: "4.1章节列出了各项功能指标要求，但缺少相应的测试方法和验证标准。",
    suggestion: "为每项功能指标补充相应的测试方法、测试环境和验证标准。",
    aiRecommendation: "建议建立完整的测试规范文档，确保功能指标的可测试性和可验证性。",
    confidence: 72,
    rule: "功能指标可测试性要求",
    ruleId: "TEST-001",
    sectionPath: ["四、非功能说明", "4.1 功能指标要求"],
    targetText: "功能指标列表",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P010",
    section: "section-4-3-1",
    type: "warning",
    priority: "medium",
    title: "文言提示内容需要UX评审",
    description: "4.3.1章节的文言提示汇总中，部分提示内容较为技术化，可能不符合用户体验要求。",
    suggestion: "邀请UX团队对所有文言提示进行评审，确保用户友好性和一致性。",
    aiRecommendation: "建议建立统一的文言提示规范，包括语言风格、长度限制和多语言支持。",
    confidence: 76,
    rule: "用户体验一致性要求",
    ruleId: "UX-001",
    sectionPath: ["四、非功能说明", "4.3 补充说明", "4.3.1 文言提示汇总"],
    targetText: "文言提示内容",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  }
];
