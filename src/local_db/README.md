# Local Database - 本地数据库

## 📋 概述

`local_db` 目录是PRD评审系统的本地数据存储中心，统一管理所有PRD文档和评审数据。

## 📁 目录结构

```
local_db/
├── README.md                    # 📖 本文档
├── dataLoader.js               # 🔧 数据加载器（核心）
├── bsv_prd_content.js          # 📄 BSV特性需求文档数据
└── [future_documents].js       # 📄 未来的其他文档数据
```

## 🔧 核心组件

### dataLoader.js
统一的数据加载器，提供以下功能：
- 📊 **数据管理**: 统一管理所有PRD文档和问题数据
- 🔍 **数据查询**: 提供丰富的查询和筛选功能
- ✅ **数据验证**: 确保数据完整性和一致性
- 📈 **统计分析**: 提供问题统计和分析功能

### 主要API

```javascript
// 导入数据加载器
import { 
  getPRDContent, 
  getIssues, 
  findIssueById, 
  findSectionById,
  getIssueStatistics 
} from '../local_db/dataLoader.js';

// 获取PRD内容
const prdContent = getPRDContent('BSV');

// 获取问题数据
const issues = getIssues('BSV');

// 查找特定问题
const issue = findIssueById('P001', 'BSV');

// 获取统计信息
const stats = getIssueStatistics('BSV');
```

## 📄 数据文件

### bsv_prd_content.js
包含BSV（侧视盲区辅助）特性需求文档的完整数据：

#### 文档结构
- **标题**: BSV 特性需求文档
- **章节数**: 32个
- **问题数**: 10个
- **版本**: V1.0.2
- **最后更新**: 2024-10-29

#### 数据格式
```javascript
export const realPRDContent = {
  title: "BSV 特性需求文档",
  sections: [
    {
      id: "cover",
      title: "BSV 特性需求文档",
      level: 0,
      content: "文档内容..."
    }
    // ... 更多章节
  ]
};

export const mockIssues = [
  {
    id: "P001",
    section: "section-2-2",
    type: "error",
    priority: "high",
    title: "问题标题",
    description: "问题描述",
    suggestion: "改进建议",
    // ... 更多字段
  }
  // ... 更多问题
];
```

## 🎯 使用方式

### 1. 在应用中使用
```javascript
// src/data/realPRDContent.js
import { realPRDContent, mockIssues } from '../../local_db/dataLoader.js';
export { realPRDContent, mockIssues };
```

### 2. 直接使用数据加载器
```javascript
// 在组件中
import { getPRDContent, getIssues } from '../local_db/dataLoader.js';

function MyComponent() {
  const prdContent = getPRDContent('BSV');
  const issues = getIssues('BSV');
  // ... 使用数据
}
```

### 3. 高级查询
```javascript
import { 
  getIssuesByType, 
  getIssuesByPriority,
  getIssuesBySection 
} from '../local_db/dataLoader.js';

// 获取高优先级问题
const highPriorityIssues = getIssuesByPriority('high', 'BSV');

// 获取错误类型问题
const errorIssues = getIssuesByType('error', 'BSV');

// 获取特定章节的问题
const sectionIssues = getIssuesBySection('section-2-2', 'BSV');
```

## 📊 数据统计

### BSV文档统计
- **总章节数**: 32个
- **总问题数**: 10个
- **问题类型分布**:
  - 错误 (error): 4个
  - 警告 (warning): 3个
  - 建议 (suggestion): 3个
- **优先级分布**:
  - 高 (high): 4个
  - 中 (medium): 3个
  - 低 (low): 3个
- **平均置信度**: 82分

## 🔄 数据更新流程

### 添加新文档
1. 创建新的文档数据文件（如 `lca_prd_content.js`）
2. 在 `dataLoader.js` 中添加导入和配置
3. 更新 `DOCUMENT_DATA` 映射
4. 更新 `supportedDocuments` 列表

### 更新现有文档
1. 直接编辑对应的文档数据文件
2. 数据加载器会自动使用最新数据
3. 运行验证确保数据完整性

## ✅ 数据验证

### 自动验证
```javascript
import { validateData } from '../local_db/dataLoader.js';

const validation = validateData('BSV');
if (!validation.isValid) {
  console.error('数据验证失败:', validation.errors);
}
```

### 验证规则
- PRD内容结构完整性
- 章节数据非空验证
- 问题数据格式验证
- 问题与章节关联验证
- 元数据完整性检查

## 🚀 性能优化

### 数据缓存
- 数据在首次加载后缓存在内存中
- 避免重复的文件读取和解析
- 提供快速的数据访问

### 按需加载
- 支持按文档类型加载数据
- 避免加载不需要的数据
- 减少内存占用

## 🔧 扩展功能

### 未来计划
1. **多语言支持**: 支持中英文文档
2. **版本管理**: 支持文档版本历史
3. **数据同步**: 支持云端数据同步
4. **实时更新**: 支持数据实时更新通知

### 自定义扩展
```javascript
// 自定义查询函数
export function getCustomQuery(filter, documentType = 'BSV') {
  const issues = getIssues(documentType);
  return issues.filter(filter);
}

// 自定义统计函数
export function getCustomStatistics(documentType = 'BSV') {
  // 实现自定义统计逻辑
}
```

## 📚 相关文档

- [PRD加载工具包](../tools/prd-loader/README.md)
- [应用架构文档](../STAGE2_ARCHITECTURE.md)
- [数据结构规范](../tools/prd-loader/PRD_DYNAMIC_LOADING_GUIDE.md)

## 🤝 贡献指南

### 添加新数据
1. 遵循现有的数据结构格式
2. 确保数据完整性和一致性
3. 添加适当的元数据信息
4. 运行验证测试

### 修改数据加载器
1. 保持向后兼容性
2. 添加适当的错误处理
3. 更新相关文档
4. 添加单元测试

---

**维护者**: PRD评审系统开发团队  
**最后更新**: 2025-07-17  
**版本**: v1.0.0
