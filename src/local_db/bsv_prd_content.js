// 真实的BSV特性需求文档内容 - 完整版本
export const realPRDContent = {
  title: "BSV 特性需求文档",
  sections: [
    {
      id: "cover",
      title: "BSV 特性需求文档",
      level: 0,
      content: `BSV特性需求文档

编制/变更日志

| 序号 | 版本 | PRD ID | 修改章节及内容 | 修改人 | 审核人 | 批准人 | 修改日期 |
|------|------|--------|----------------|--------|--------|--------|----------|
| 1 | V1.0.0 | S-123-66 | 初次创建 | 钟健鑫 | / | / | 2023-12-05 |
| 2 | V1.0.1 | S-123-66 | / | 钟健鑫 | / | / | 2024-03-08 |
| 3 | V1.0.2 | S-123-66 | 1.增加关联系统故障相关文言提示相关usecase：3.4.1、3.5.1、3.5.2<br>2.增加与行车功能的相互抑制usecase：3.2.1、3.3.1、3.1.1、3.1.2<br>3.增加与BSD联动的usecase:3.3.2 | 朱凤刘镇伟 | 钟健鑫 | 钟健鑫 刘尧 | 2024-10-29 |

[图片占位符：文档封面图]`
    },
    {
      id: "section-1",
      title: "一、文档综述",
      level: 1,
      content: `本章节介绍BSV特性需求文档的背景、范围、术语解释等基础信息。`
    },
    {
      id: "section-1-1",
      title: "1.1 文档背景",
      level: 2,
      content: `本文应用于吉利集团的侧视盲区辅助功能需求文档，详细描述侧视盲区辅助的场景分析，应用流程，详细策略设计等。

项目实现过程中，请以详细设计为基础进行需求实现，如遇到歧义或变更，请及时沟通。`
    },
    {
      id: "section-1-2",
      title: "1.2 文档范围",
      level: 2,
      content: `本文档介绍了侧视盲区辅助（Blind Spot Visualization, BSV）的产品概述和具体的场景设计。产品概述中介绍了产品的定义、使用场景、车型配置说明、功能架构、功能流程。`
    },
    {
      id: "section-1-3",
      title: "1.3 术语解释",
      level: 2,
      content: `| 序号 | 术语 | 定义 |
|------|------|------|
| 1 | ODD | Operational Design Domain |
| 2 | HMI | Human-Machine Interface |
| 3 | AVM | Around view Monitor |
| 4 | CSD | Centerstack Display |
| 5 | HUD | Heads Up Display |
| 6 | BSD | Blind Spot Detection |
| 7 | DOW | Door Opening Warning |`
    },
    {
      id: "section-1-4",
      title: "1.4 文档关系",
      level: 2,
      content: `相关文档和政策法规文件的关联说明。`
    },
    {
      id: "section-1-4-1",
      title: "1.4.1 当前功能相关文档",
      level: 3,
      content: `| UI/UE文档 | FR文档 | DR文档 | 跨域关联PRD文档 |
|-----------|--------|--------|-----------------|
| v1.0.1 L946_BSV_UEV1.0.2.pdf | BSV-EADP-视频流.pdf<br>BSV-HMI-Error Message.pdf<br>BSV-ASM-Active to Failure.pdf<br>BSV-HMI-Exit.pdf<br>BSV-ASM-Active.pdf<br>BSV-HMI-Active.pdf<br>BSV-UserInput-拨杆.pdf<br>BSV-BrakeControl-车速.pdf<br>BSV-ASM-Exit.pdf<br>BSV-Propulsion-挡位.pdf | BSV-DR(ASM)-拨杆激活.pdf<br>BSV-DR(ASM)-拨杆退出.pdf<br>BSV-DR(ASM)-摄像头故障.pdf<br>BSV-DR(ASM)-跳转到故障.pdf<br>BSV-DR(ASM)-系统故障.pdf | DOW：FD-S-120-62 Door Open Warning 开门预警 V1.0.7(1).pdf<br>AVM：VPA-Basic FDR.pdf<br>BSD：FD-S-123-07 Lane Change Assist 变道辅助 V1.0.8.pdf |`
    },
    {
      id: "section-1-4-2",
      title: "1.4.2 政策法规文件",
      level: 3,
      content: `以下法规仅用于功能开发参考。

| 区域 | 法规 | 法规相关内容 | 与本功能相关性 |
|------|------|-------------|----------------|
| 中国 | GB/T 39265（2020） | 道路车辆盲区监测系统性能要求及试验方法，对BSD系统的性能要求及测试步骤做了介绍。<br>中国暂无直接对侧视盲区辅助功能的相关规定。 | BSD联动报警时，BSV视图会有所体现。<br>5.1.1讲述了车辆盲区及参测范围 |
| 欧洲 | ECE R166 | 《关于就车辆前部和侧面附近弱势道路使用者对驾驶员的提醒功能而批准装置和机动车辆的统一规定》，是联合国制定的一项旨在提高道路交通安全性的重要法规，适用于需要安装提醒驾驶员注意车辆前部和侧面附近弱势道路使用者的装置的机动车辆及其相关设备。<br>这些设备可能包括雷达、摄像头、传感器等，它们能够实时监测车辆周围的环境，并向驾驶员提供警示信息。 | 15.2讲述了车辆盲区及参测范围 |`
    },
    {
      id: "section-1-4-3",
      title: "1.4.3 行业规范文件",
      level: 3,
      content: `暂无相关行业规范。`
    },
    {
      id: "section-2",
      title: "二、产品概述",
      level: 1,
      content: `产品场景、架构、配置、流程等概述信息。`
    },
    {
      id: "section-2-1",
      title: "2.1 产品场景及概要说明",
      level: 2,
      content: `**产品场景说明：** 在行驶过程中（时速高于30km/h），当驾驶员发出转向信号时，CSD和HUD即显示对应侧盲区实时视频流，为用户提供视野支持，提升驾驶安全性。

| 变道/汇入主路/汇出主路场景 | 转弯场景 |
|---------------------------|----------|
| 侧后方视野，观察侧后方来车等，预防事故 | 扩展侧后方视野，观察侧后方直行车辆等 |

**产品可用地点：** 满足一定光照条件的路段

**产品使用方式：** 设置项开启后，通过转向拨杆激活功能，通过拨杆或换档退出功能

**产品关键参数：**
- 盲区显示视频流尺寸
- 视频流清晰度
- 延迟

[图片占位符：产品场景示意图]`
    },
    {
      id: "section-2-2",
      title: "2.2 产品架构/系统架构",
      level: 2,
      content: `简单功能不需要架构分解。

[图片占位符：产品架构图]
[图片占位符：系统架构图]`
    },
    {
      id: "section-2-3",
      title: "2.3 工作原理",
      level: 2,
      content: `2.3.1 摄像头数据采集
系统通过安装在车辆左右两侧的高清摄像头实时采集盲区画面。摄像头以30fps的频率采集数据，包括：
- 高清视频画面
- 目标物体的位置信息
- 光线和天气条件
- 画面清晰度和对比度

2.3.2 图像处理与分析
ECU接收摄像头数据后，进行以下处理：
1. 图像增强和去噪
2. 目标识别和标注
3. 画面拼接和校正
4. 与车辆状态信息融合

2.3.3 显示决策
系统根据以下条件决定显示内容：
- 转向灯状态（左转/右转）
- 驾驶员操作意图
- 车辆速度和行驶状态
- 环境光线条件
- 系统设置偏好

2.3.4 画面输出
当需要显示盲区画面时，系统会：
1. 选择合适的显示模式
2. 调整画面参数和布局
3. 发送视频信号到显示设备
4. 记录使用数据用于优化`
    },
    {
      id: "section-3",
      title: "3. 技术规格",
      level: 1,
      content: `本章节定义BSV系统的技术规格要求，包括硬件、软件和性能指标。`
    },
    {
      id: "section-3-1",
      title: "3.1 硬件要求",
      level: 2,
      content: `3.1.1 摄像头传感器规格
- 分辨率：1920×1080 (Full HD)
- 帧率：30fps
- 视角范围：
  - 水平视角：120°
  - 垂直视角：90°
- 图像质量：
  - 动态范围：≥120dB
  - 信噪比：≥40dB
  - 最低照度：0.1Lux
- 工作温度：-40°C至+85°C
- 防护等级：IP67
- 安装位置：车辆左右两侧外后视镜下方

3.1.2 ECU规格
- 处理器：ARM Cortex-A78 四核 2.0GHz
- GPU：Mali-G78 MP12
- 内存：4GB LPDDR5 RAM
- 存储：64GB UFS 3.1
- 通信接口：CAN-FD、LIN、Ethernet、USB 3.0
- 工作电压：9V-16V
- 功耗：≤25W
- 工作温度：-40°C至+85°C

3.1.3 显示设备要求
- 中控屏：分辨率≥1920×1080，支持视频播放
- 仪表盘：支持图形化界面显示
- HUD：支持图像投影显示
- 后视镜：可选集成显示屏`
    },
    {
      id: "section-3-2",
      title: "3.2 软件要求",
      level: 2,
      content: `3.2.1 实时性要求
- 图像数据处理延迟：≤33ms
- 画面显示延迟：≤100ms
- 总系统响应时间：≤150ms
- 系统启动时间：≤3s

3.2.2 可靠性要求
- 系统可用性：≥99.5%
- 平均故障间隔时间（MTBF）：≥8000小时
- 画面质量稳定性：≥95%
- 系统崩溃率：≤0.1%
- 软件缺陷密度：≤0.1个/KLOC

3.2.3 安全性要求
- 符合ISO 26262 ASIL-A安全等级
- 支持故障检测和诊断
- 具备降级模式运行能力
- 支持远程软件更新（OTA）

3.2.4 兼容性要求
- 支持AUTOSAR架构
- 兼容主流车载操作系统
- 支持多种视频格式
- 向后兼容现有车型平台`
    },
    {
      id: "section-3-3",
      title: "3.3 性能指标",
      level: 2,
      content: `3.3.1 显示性能
| 指标 | 要求 | 测试条件 |
|------|------|----------|
| 显示范围 | 0.5m - 50m | 标准测试环境 |
| 视角范围 | 120°×90° | 水平×垂直 |
| 分辨率 | 1920×1080 | Full HD |
| 帧率 | ≥30fps | 实时显示 |
| 延迟 | ≤100ms | 端到端 |

3.3.2 图像质量
| 指标 | 要求 | 说明 |
|------|------|------|
| 动态范围 | ≥120dB | HDR支持 |
| 信噪比 | ≥40dB | 图像清晰度 |
| 色彩还原 | ≥90% | 色彩准确性 |
| 对比度 | ≥1000:1 | 明暗层次 |

3.3.3 环境适应性
- 工作温度：-40°C至+85°C
- 相对湿度：5%-95%（无凝露）
- 抗振动：符合ISO 16750-3标准
- 抗电磁干扰：符合ISO 11452标准
- 防水防尘：IP67等级`
    },
    {
      id: "section-4",
      title: "4. 用户界面设计",
      level: 1,
      content: `本章节描述BSV系统的用户界面设计规范，包括各种显示设备的界面要求。`
    },
    {
      id: "section-4-1",
      title: "4.1 中控屏显示",
      level: 2,
      content: `4.1.1 主显示界面设计
在中控屏上显示盲区实时画面：

界面布局：
- 主画面区域：占屏幕70%，显示盲区实时视频
- 控制区域：占屏幕20%，显示功能按钮和状态
- 信息区域：占屏幕10%，显示系统状态和提示

显示模式：
- 单侧显示：仅显示左侧或右侧盲区画面
- 分屏显示：同时显示左右两侧盲区画面
- 全景模式：结合AVM显示完整周边环境
- 画中画：在主界面角落显示小窗口

4.1.2 视觉设计规范
画面质量要求：
- 分辨率：1920×1080 Full HD
- 帧率：30fps稳定输出
- 色彩：真实还原，对比度增强
- 亮度：自动调节，适应环境光线

界面元素设计：
- 边框：2像素白色边框，突出画面区域
- 标识：左上角显示"左侧盲区"或"右侧盲区"
- 时间戳：右下角显示当前时间
- 状态指示：绿色圆点表示系统正常工作`
    },
    {
      id: "section-4-2",
      title: "4.2 仪表盘显示",
      level: 2,
      content: `4.2.1 简化图标显示
在仪表盘上显示BSV系统状态图标：

图标设计：
- 位置：仪表盘右上角区域
- 尺寸：32×32像素
- 样式：摄像头图标 + 状态指示

显示状态：
- 正常工作：蓝色摄像头图标
- 转向灯激活：橙色摄像头图标 + 箭头
- 系统故障：红色摄像头图标 + 叉号
- 系统关闭：灰色摄像头图标

4.2.2 信息提示
状态文字显示：
- "BSV系统正常"：绿色文字
- "BSV显示中"：橙色文字
- "BSV系统故障"：红色文字
- "BSV系统关闭"：灰色文字

4.2.3 交互设计
- 支持触摸操作查看详细信息
- 可通过设置菜单调整显示参数
- 支持白天/夜间模式自动切换
- 提供系统状态和使用记录查询`
    },
    {
      id: "section-4-3",
      title: "4.3 HUD显示",
      level: 2,
      content: `4.3.1 显示内容
在HUD上显示简洁的BSV状态信息：

显示元素：
- 摄像头图标：表示BSV系统状态
- 方向指示：显示当前显示的盲区方向
- 状态文字：简短的状态描述
- 时间信息：显示时长（可选）

4.3.2 视觉规范
位置布局：
- 显示区域：HUD右下角区域
- 尺寸：60×30像素等效
- 距离：距离驾驶员视线中心20°

颜色编码：
- 蓝色：系统正常工作
- 橙色：正在显示盲区画面
- 红色：系统故障
- 亮度：根据环境光自动调节

动画效果：
- 出现：淡入效果，持续0.2秒
- 状态切换：平滑过渡动画
- 消失：淡出效果，持续0.3秒

4.3.3 显示逻辑
- 转向灯激活时自动显示
- 手动激活时持续显示
- 系统故障时警告显示
- 与其他HUD信息协调显示`
    },
    {
      id: "section-5",
      title: "5. 系统架构",
      level: 1,
      content: `本章节描述BSV系统的整体架构设计，包括硬件架构、软件架构和通信架构。`
    },
    {
      id: "section-5-1",
      title: "5.1 硬件架构",
      level: 2,
      content: `5.1.1 系统组成
BSV系统硬件架构包括以下主要组件：

摄像头子系统：
- 左侧摄像头：安装在左侧外后视镜下方
- 右侧摄像头：安装在右侧外后视镜下方
- 摄像头支架：提供稳定的安装平台
- 线束连接：连接摄像头与ECU

控制子系统：
- BSV ECU：主控制单元，负责图像处理
- GPU模块：专用图像处理加速
- 电源管理模块：提供稳定电源
- 通信接口：CAN-FD、LIN、Ethernet、USB
- 诊断接口：支持故障诊断

显示子系统：
- 中控屏接口：显示实时画面
- 仪表盘接口：显示状态图标
- HUD接口：显示状态信息
- 后视镜接口：可选集成显示

5.1.2 连接关系
系统各组件通过以下方式连接：
- 摄像头 → ECU：专用线束，支持电源和视频数据传输
- ECU → 中控屏：HDMI/DisplayPort视频接口
- ECU → 仪表盘：CAN-FD总线通信
- ECU → HUD：Ethernet通信
- ECU → 后视镜：LVDS视频接口`
    },
    {
      id: "section-5-2",
      title: "5.2 软件架构",
      level: 2,
      content: `5.2.1 软件层次结构
BSV系统软件采用分层架构设计：

应用层：
- BSV主应用：核心业务逻辑
- 用户界面：HMI交互处理
- 诊断应用：故障检测和诊断
- 配置管理：参数配置和标定

中间件层：
- 视频中间件：摄像头数据处理
- 通信中间件：CAN-FD、Ethernet通信
- 显示中间件：多屏显示管理
- 事件管理：系统事件处理
- 时间管理：实时任务调度

驱动层：
- 摄像头驱动：视频数据采集
- 显示驱动：多屏输出控制
- CAN驱动：总线通信驱动
- GPU驱动：图像处理加速

5.2.2 核心算法模块
图像处理算法：
- 图像增强：去噪、锐化、对比度调整
- 目标识别：基于深度学习的车辆检测
- 画面拼接：多摄像头画面融合

显示优化算法：
- 自适应亮度：根据环境光调整
- 画面稳定：防抖动处理
- 延迟优化：减少端到端延迟

数据融合算法：
- 多摄像头融合：全景视野构建
- 车辆状态融合：转向灯、速度等信息
- 环境信息融合：光线、天气条件`
    },
    {
      id: "section-6",
      title: "6. 测试验证",
      level: 1,
      content: `本章节描述BSV系统的测试验证方案，确保系统满足功能和性能要求。`
    },
    {
      id: "section-6-1",
      title: "6.1 功能测试",
      level: 2,
      content: `6.1.1 静态测试
在车辆静止状态下验证系统基本功能：

测试项目：
- 系统启动测试：验证系统正常启动和画面显示
- 自检功能测试：验证摄像头和ECU自检功能
- 通信测试：验证各模块间通信正常
- 界面显示测试：验证各显示设备画面质量

测试方法：
- 使用标准测试图卡验证画面质量
- 模拟各种故障场景验证诊断功能
- 检查系统状态指示和错误报告
- 验证用户界面响应和显示效果

6.1.2 动态测试
在车辆行驶过程中验证系统实时性能：

测试场景：
- 高速公路行驶：验证高速场景下的画面稳定性
- 城市道路行驶：验证复杂环境下的适应性
- 停车场低速：验证低速场景下的画面清晰度
- 恶劣天气：验证雨雪天气下的可见性

测试指标：
- 画面清晰度和色彩还原
- 响应时间和显示延迟
- 画面稳定性和连续性
- 系统稳定性和可靠性

6.1.3 边界测试
验证系统在极限条件下的表现：

测试条件：
- 极端温度：-40°C至+85°C
- 强光/弱光：直射阳光、夜间行驶
- 机械振动：模拟恶劣路况
- 电源波动：9V-16V电压范围

测试目标：
- 验证系统在边界条件下仍能正常显示
- 确认系统的故障检测和恢复能力
- 验证降级模式的有效性
- 评估系统的鲁棒性和可靠性`
    },
    {
      id: "section-6-2",
      title: "6.2 性能测试",
      level: 2,
      content: `6.2.1 响应时间测试
测量系统各环节的响应时间：

测试项目：
- 摄像头数据采集时间：≤33ms
- 图像处理和分析时间：≤50ms
- 显示决策时间：≤20ms
- 画面输出时间：≤30ms
- 总系统响应时间：≤150ms

测试方法：
- 使用高精度时间戳记录各环节时间
- 统计1000次测试的平均值和最大值
- 分析时间分布和异常情况
- 优化性能瓶颈环节

6.2.2 准确性测试
统计系统的检测准确性：

测试指标：
- 画面清晰度：图像质量评分 ≥90%
- 色彩还原度：色彩准确性 ≥85%
- 延迟时间：端到端延迟 ≤150ms
- 稳定性：画面抖动率 ≤2%

测试场景：
- 标准测试场景：使用标准测试环境
- 复杂场景：多光源、反光、阴影
- 边缘场景：极限光照、恶劣天气
- 真实道路：实际驾驶环境测试

6.2.3 稳定性测试
验证系统长时间运行的稳定性：

测试方案：
- 连续运行测试：24小时×30天
- 循环测试：启动-运行-关闭循环10000次
- 压力测试：高负载、高频率操作
- 老化测试：高温环境下长期运行

监控指标：
- 系统崩溃和重启次数
- 内存泄漏和性能下降
- 硬件故障和磨损情况
- 软件错误和异常统计`
    },
    {
      id: "section-6-3",
      title: "6.3 安全测试",
      level: 2,
      content: `6.3.1 功能安全测试
按照ISO 26262标准进行安全测试：

安全分析：
- 危险分析和风险评估（HARA）
- 故障模式影响分析（FMEA）
- 故障树分析（FTA）
- 安全完整性等级确认（ASIL-B）

测试项目：
- 单点故障测试：验证单个组件故障的影响
- 多点故障测试：验证多个故障的组合影响
- 故障检测测试：验证故障检测的及时性
- 降级模式测试：验证安全降级功能

6.3.2 网络安全测试
验证系统的网络安全防护能力：

测试内容：
- 通信加密：验证数据传输的加密保护
- 身份认证：验证设备和用户身份验证
- 访问控制：验证权限管理和访问控制
- 入侵检测：验证异常行为检测能力

攻击测试：
- 数据注入攻击：模拟恶意数据注入
- 重放攻击：模拟数据重放攻击
- 拒绝服务攻击：模拟DoS攻击
- 固件篡改：模拟固件被恶意修改

6.3.3 电磁兼容测试
验证系统的电磁兼容性：

测试标准：
- ISO 11452：道路车辆电磁兼容性
- CISPR 25：车辆电磁发射限值
- IEC 61000：电磁兼容通用标准

测试项目：
- 辐射发射测试：验证系统电磁发射水平
- 辐射抗扰度测试：验证抗电磁干扰能力
- 传导发射测试：验证传导发射限值
- 传导抗扰度测试：验证传导抗扰度`
    },
    {
      id: "section-7",
      title: "7. 质量保证",
      level: 1,
      content: `本章节描述BSV系统的质量保证措施，确保产品质量和可靠性。`
    },
    {
      id: "section-7-1",
      title: "7.1 质量管理体系",
      level: 2,
      content: `7.1.1 质量标准
BSV系统开发遵循以下质量标准：

国际标准：
- ISO 9001：质量管理体系
- ISO/TS 16949：汽车行业质量管理
- ISO 26262：道路车辆功能安全
- ASPICE：汽车软件过程改进

行业标准：
- AUTOSAR：汽车开放系统架构
- MISRA C：汽车软件编码标准
- DO-178C：软件开发保证等级

7.1.2 质量流程
建立完整的质量管理流程：

需求管理：
- 需求收集和分析
- 需求评审和确认
- 需求变更控制
- 需求追溯管理

设计管理：
- 设计评审和验证
- 设计变更控制
- 设计文档管理
- 设计质量检查

开发管理：
- 编码规范和检查
- 单元测试和集成测试
- 代码评审和质量分析
- 版本控制和配置管理`
    },
    {
      id: "section-7-2",
      title: "7.2 供应商管理",
      level: 2,
      content: `7.2.1 供应商选择
建立严格的供应商选择标准：

资质要求：
- ISO/TS 16949认证
- 汽车行业经验≥5年
- 技术能力和研发实力
- 质量管理体系完善

评估标准：
- 技术能力评估：设计和制造能力
- 质量体系评估：质量管理成熟度
- 交付能力评估：产能和交付记录
- 成本竞争力：价格和成本控制

7.2.2 供应商管理
实施全面的供应商管理：

合同管理：
- 技术规格和质量要求
- 交付时间和数量要求
- 质量保证和责任条款
- 知识产权保护条款

质量监控：
- 来料检验和测试
- 过程审核和监督
- 质量问题处理
- 持续改进要求

风险管理：
- 供应风险识别和评估
- 备选供应商准备
- 应急预案制定
- 供应链连续性保障`
    },
    {
      id: "section-8",
      title: "8. 项目管理",
      level: 1,
      content: `本章节描述BSV系统项目的管理方案，包括进度计划、资源配置和风险管理。`
    },
    {
      id: "section-8-1",
      title: "8.1 项目计划",
      level: 2,
      content: `8.1.1 项目里程碑
BSV系统开发项目主要里程碑：

| 里程碑 | 时间节点 | 主要交付物 |
|--------|----------|------------|
| 需求确认 | M1 | 需求规格书、系统架构 |
| 设计完成 | M2 | 详细设计文档、接口规范 |
| 原型验证 | M3 | 原型系统、功能验证报告 |
| 开发完成 | M4 | 软硬件系统、集成测试报告 |
| 测试完成 | M5 | 测试报告、质量评估报告 |
| 量产准备 | M6 | 生产文件、工艺规范 |

8.1.2 项目进度
项目总周期：18个月

阶段一：需求分析和系统设计（3个月）
- 需求收集和分析
- 系统架构设计
- 技术方案评估
- 供应商选择

阶段二：详细设计和原型开发（4个月）
- 硬件详细设计
- 软件详细设计
- 原型系统开发
- 功能验证测试

阶段三：系统开发和集成（6个月）
- 硬件制造和调试
- 软件开发和测试
- 系统集成和优化
- 性能调优和验证

阶段四：测试验证和量产准备（5个月）
- 全面测试验证
- 质量评估和改进
- 生产工艺开发
- 量产准备和试产`
    }
  ]
};

// 实际问题数据 - 基于BSV特性需求文档评审结果
export const mockIssues = [
  {
    id: "P001",
    section: "section-2-2",
    type: "error",
    priority: "high",
    title: "产品架构/系统架构图完全缺失",
    description: "该章节内容仅为'简单功能不需要架构分解'，完全没有提供产品架构图和系统架构图。",
    suggestion: "通过架构设计会议明确产品和系统边界。组织产品负责人、系统工程师和技术负责人召开架构设计会议。补充产品架构图和系统架构图。",
    aiRecommendation: "缺少架构图，软件和硬件的边界、模块间的交互关系、信号流和数据流都不明确。这将导致系统设计、软件开发和硬件集成工作无法有效开展。",
    confidence: 95,
    rule: "架构完整性要求",
    ruleId: "ARCH-001",
    sectionPath: ["二、产品概述", "2.2 产品架构/系统架构"],
    targetText: "简单功能不需要架构分解",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P002",
    section: "section-1-4-1",
    type: "warning",
    priority: "medium",
    title: "跨域文档关联不规范，可追溯性差",
    description: "关联文档以不规则的列表形式呈现，缺乏结构化的信息。没有清晰的分类和版本标识。",
    suggestion: "重新整理关联文档，提高可追溯性。参照标准模板的表格格式，重新整理1.4.1章节。为每个关联文档明确其类别、文档ID和准确的版本号。",
    aiRecommendation: "当关联文档发生变更时，很难评估其对BSV功能的影响范围，增加了版本不匹配和集成失败的风险。",
    confidence: 85,
    rule: "文档可追溯性规范",
    ruleId: "DOC-003",
    sectionPath: ["一、文档综述", "1.4 文档关系", "1.4.1 当前功能相关文档"],
    targetText: "UI/UE文档、FR文档、DR文档、跨域关联PRD文档",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 2
  },
  {
    id: "P003",
    section: "section-2-7",
    type: "warning",
    priority: "medium",
    title: "功能列表结构混乱，不符合MECE原则",
    description: "功能列表的层级划分不清晰，将不同模式下的功能点混合在一起。",
    suggestion: "重构功能列表，使其符合MECE原则。参照标准模板的功能列表案例，重构2.7章节。创建标准模式、挂车模式、DOW模式等二级功能分类。",
    aiRecommendation: "需求分解和任务分配会变得困难。开发人员可能错误地将某一模式下的逻辑应用到另一模式，导致功能缺陷。",
    confidence: 90,
    rule: "功能列表结构规范",
    ruleId: "FUNC-002",
    sectionPath: ["二、产品概述", "2.7 功能列表"],
    targetText: "侧视盲区辅助的一级、二级、三级功能列表",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 3
  },
  {
    id: "P004",
    section: "section-2-3",
    type: "suggestion",
    priority: "low",
    title: "产品梯度配置过于简化",
    description: "产品梯度配置说明表格仅简单罗列了平台，对于不同配置下的功能差异化未做任何说明。",
    suggestion: "详细化产品梯度配置说明。与产品规划团队对齐，明确BSV功能是否存在不同梯度配置。如果存在，则详细描述在不同配置下的功能具体表现有何不同。",
    aiRecommendation: "不利于市场和销售理解产品配置，也可能导致开发实现混淆。",
    confidence: 75,
    rule: "产品配置完整性",
    ruleId: "PROD-001",
    sectionPath: ["二、产品概述", "2.3 产品梯度配置说明"],
    targetText: "不同平台的功能配置",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 2
  },
  {
    id: "P005",
    section: "section-1-4-2",
    type: "warning",
    priority: "medium",
    title: "法规遵从性说明不足",
    description: "法规引用未明确指出具体适用的章节条款，以及这些条款与具体Use Case的关联性。",
    suggestion: "补充法规遵从性详细说明。与法规部门或合规专家合作，在表格中将法规相关内容更新为具体的章节和条款原文。将法规条款关联到具体 Use Case ID。",
    aiRecommendation: "存在产品不满足目标市场法规的风险，可能导致认证失败或产品召回。",
    confidence: 88,
    rule: "法规遵从性要求",
    ruleId: "REG-001",
    sectionPath: ["一、文档综述", "1.4 文档关系", "1.4.2 政策法规文件"],
    targetText: "中国GB/T 39265、欧洲ECE R166等相关法规",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 2
  },
  {
    id: "P006",
    section: "section-1-3",
    type: "suggestion",
    priority: "low",
    title: "术语定义不一致",
    description: "术语CSD在文档中定义为Centerstack Display，与模板中的中控屏常用表达不完全一致。",
    suggestion: "统一术语定义，提高可读性。在术语表中，为CSD的定义增加中文解释，修改为 Centerstack Display (中控显示屏)。",
    aiRecommendation: "对于非技术背景的阅读者，纯英文缩写可能造成理解障碍。",
    confidence: 70,
    rule: "术语一致性规范",
    ruleId: "TERM-001",
    sectionPath: ["一、文档综述", "1.3 术语解释"],
    targetText: "ODD、HMI、AVM、CSD、HUD、BSD、DOW等术语定义",
    paragraphIndex: 1,
    startOffset: 3,
    endOffset: 4
  },
  {
    id: "P007",
    section: "section-2-5",
    type: "warning",
    priority: "medium",
    title: "需求存在未关闭的开放点",
    description: "文档中存在多个批注，表明某些需求点尚未达成最终共识。例如：需要跟行车确认挂车模式下是否能开启ICC/ACC等功能。",
    suggestion: "关闭所有开放性需求点。产品负责人必须立即跟进所有批注中提到的待办事项。与行车功能团队开会确认，并将最终结论更新到正文中，然后删除该批注。",
    aiRecommendation: "开发人员无法实现不确定的需求，如果强行实现，极有可能在后期因为需求变更而导致大量返工。",
    confidence: 92,
    rule: "需求明确性要求",
    ruleId: "REQ-001",
    sectionPath: ["二、产品概述", "2.5 关键状态流转"],
    targetText: "OFF→Initialize→Standby→Active→Failure等状态转换",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P008",
    section: "section-3",
    type: "suggestion",
    priority: "low",
    title: "功能列表与Use Case不完全对应",
    description: "功能列表中的某些条目在Use Case章节没有完全对应的详细设计。",
    suggestion: "确保功能列表与Use Case完全对应。梳理2.7章节的功能列表，确认标准模式下是否需要独立的故障退出场景。如果需要，则在第3章中补充对应的 Use Case。",
    aiRecommendation: "可能导致部分需求没有被详细设计，开发时可能会遗漏。",
    confidence: 80,
    rule: "功能一致性要求",
    ruleId: "FUNC-003",
    sectionPath: ["三、功能场景设计"],
    targetText: "详细的功能场景用例设计",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P009",
    section: "cover",
    type: "suggestion",
    priority: "low",
    title: "变更日志描述可优化",
    description: "版本V1.0.2的修改内容描述笼统，未能体现增量修改的具体条目。",
    suggestion: "优化变更日志描述的粒度。将V1.0.2的变更描述修改得更具体，例如：1. 在Usecase 3.4.1, 3.5.1, 3.5.2中，增加关联系统故障的文言提示。2. 在Usecase 3.2.1, 3.3.1中，增加与行车功能的抑制逻辑。3. 新增Usecase 3.3.2：BSV与BSD联动报警。",
    aiRecommendation: "轻微影响版本间变更的快速追溯能力。",
    confidence: 65,
    rule: "版本控制规范",
    ruleId: "VER-001",
    sectionPath: ["BSV特性需求文档"],
    targetText: "编制/变更日志表格",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P010",
    section: "section-4-3-1",
    type: "suggestion",
    priority: "low",
    title: "文言汇总内容及位置可优化",
    description: "该章节的备注中，对文言的退出条件描述为5s后文言和影像退出，这里的影像退出可能存在歧义。",
    suggestion: "明确文言提示的逻辑和位置。将5s后文言和影像退出修改为更明确的描述，例如：Toast文言显示5s后自动消失；视图上的常显文言随影像一同退出。考虑将所有HMI相关的提示信息整合到一个独立的章节中。",
    aiRecommendation: "可能导致HMI实现与预期有细微偏差。",
    confidence: 72,
    rule: "HMI设计明确性",
    ruleId: "HMI-001",
    sectionPath: ["四、非功能说明", "4.3 补充说明", "4.3.1 文言提示汇总"],
    targetText: "各种故障场景下的文言提示内容",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  }
];
