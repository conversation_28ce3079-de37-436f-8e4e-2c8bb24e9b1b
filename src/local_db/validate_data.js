#!/usr/bin/env node
// 数据验证脚本 - 验证local_db中的数据完整性

import { 
  getPRDContent, 
  getIssues, 
  getDocumentMetadata,
  getSupportedDocuments,
  getIssueStatistics,
  validateData,
  findIssueById,
  findSectionById
} from './dataLoader.js';

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log('green', `✅ ${message}`);
}

function logError(message) {
  log('red', `❌ ${message}`);
}

function logWarning(message) {
  log('yellow', `⚠️  ${message}`);
}

function logInfo(message) {
  log('blue', `ℹ️  ${message}`);
}

// 验证数据完整性
function validateDataIntegrity() {
  logInfo('开始验证数据完整性...');
  
  const supportedDocs = getSupportedDocuments();
  let allValid = true;
  
  for (const docType of supportedDocs) {
    logInfo(`验证 ${docType} 文档数据...`);
    
    const validation = validateData(docType);
    
    if (validation.isValid) {
      logSuccess(`${docType} 数据验证通过`);
    } else {
      logError(`${docType} 数据验证失败:`);
      validation.errors.forEach(error => {
        logError(`  - ${error}`);
      });
      allValid = false;
    }
    
    if (validation.warnings.length > 0) {
      validation.warnings.forEach(warning => {
        logWarning(`  - ${warning}`);
      });
    }
  }
  
  return allValid;
}

// 验证数据结构
function validateDataStructure() {
  logInfo('验证数据结构...');
  
  try {
    const prdContent = getPRDContent('BSV');
    const issues = getIssues('BSV');
    const metadata = getDocumentMetadata('BSV');
    
    // 验证PRD内容结构
    if (!prdContent || !prdContent.title || !prdContent.sections) {
      logError('PRD内容结构不完整');
      return false;
    }
    logSuccess(`PRD内容结构正确 - 标题: ${prdContent.title}`);
    
    // 验证章节数据
    if (!Array.isArray(prdContent.sections) || prdContent.sections.length === 0) {
      logError('章节数据无效');
      return false;
    }
    logSuccess(`章节数据正确 - 共 ${prdContent.sections.length} 个章节`);
    
    // 验证问题数据
    if (!Array.isArray(issues) || issues.length === 0) {
      logError('问题数据无效');
      return false;
    }
    logSuccess(`问题数据正确 - 共 ${issues.length} 个问题`);
    
    // 验证元数据
    if (!metadata) {
      logWarning('缺少元数据');
    } else {
      logSuccess(`元数据正确 - 版本: ${metadata.version}`);
    }
    
    return true;
  } catch (error) {
    logError(`数据结构验证异常: ${error.message}`);
    return false;
  }
}

// 验证数据查询功能
function validateQueryFunctions() {
  logInfo('验证数据查询功能...');
  
  try {
    // 测试问题查询
    const issue = findIssueById('P001', 'BSV');
    if (!issue) {
      logError('问题查询功能异常 - 无法找到P001');
      return false;
    }
    logSuccess(`问题查询正常 - 找到问题: ${issue.title}`);
    
    // 测试章节查询
    const section = findSectionById('cover', 'BSV');
    if (!section) {
      logError('章节查询功能异常 - 无法找到cover章节');
      return false;
    }
    logSuccess(`章节查询正常 - 找到章节: ${section.title}`);
    
    // 测试统计功能
    const stats = getIssueStatistics('BSV');
    if (!stats || typeof stats.total !== 'number') {
      logError('统计功能异常');
      return false;
    }
    logSuccess(`统计功能正常 - 总问题数: ${stats.total}`);
    
    return true;
  } catch (error) {
    logError(`查询功能验证异常: ${error.message}`);
    return false;
  }
}

// 显示数据统计信息
function showDataStatistics() {
  logInfo('数据统计信息:');
  
  const supportedDocs = getSupportedDocuments();
  
  for (const docType of supportedDocs) {
    try {
      const prdContent = getPRDContent(docType);
      const issues = getIssues(docType);
      const metadata = getDocumentMetadata(docType);
      const stats = getIssueStatistics(docType);
      
      console.log(`\n📊 ${docType} 文档统计:`);
      console.log(`   标题: ${prdContent.title}`);
      console.log(`   版本: ${metadata?.version || '未知'}`);
      console.log(`   章节数: ${prdContent.sections.length}`);
      console.log(`   问题数: ${issues.length}`);
      console.log(`   问题类型分布:`);
      console.log(`     错误: ${stats.byType.error}`);
      console.log(`     警告: ${stats.byType.warning}`);
      console.log(`     建议: ${stats.byType.suggestion}`);
      console.log(`   优先级分布:`);
      console.log(`     高: ${stats.byPriority.high}`);
      console.log(`     中: ${stats.byPriority.medium}`);
      console.log(`     低: ${stats.byPriority.low}`);
      console.log(`   平均置信度: ${stats.averageConfidence}%`);
      
    } catch (error) {
      logError(`获取 ${docType} 统计信息失败: ${error.message}`);
    }
  }
}

// 测试数据导入导出
async function testDataImportExport() {
  logInfo('测试数据导入导出...');

  try {
    // 测试重新导出
    const { realPRDContent, mockIssues } = await import('./dataLoader.js');

    if (!realPRDContent || !mockIssues) {
      logError('数据导入导出失败');
      return false;
    }

    logSuccess('数据导入导出正常');
    return true;
  } catch (error) {
    logError(`数据导入导出异常: ${error.message}`);
    return false;
  }
}

// 主验证函数
async function main() {
  console.log('🚀 Local DB 数据验证开始\n');
  
  const tests = [
    { name: '数据完整性验证', fn: validateDataIntegrity },
    { name: '数据结构验证', fn: validateDataStructure },
    { name: '查询功能验证', fn: validateQueryFunctions },
    { name: '导入导出测试', fn: testDataImportExport }
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passedTests++;
      }
    } catch (error) {
      logError(`${test.name}执行异常: ${error.message}`);
    }
    console.log(''); // 空行分隔
  }
  
  // 显示统计信息
  showDataStatistics();
  
  // 总结
  console.log('\n📋 验证总结:');
  console.log(`   总测试数: ${tests.length}`);
  console.log(`   通过测试: ${passedTests}`);
  console.log(`   失败测试: ${tests.length - passedTests}`);
  
  if (passedTests === tests.length) {
    logSuccess('🎉 所有验证通过！Local DB 数据正常');
    process.exit(0);
  } else {
    logError('❌ 部分验证失败，请检查数据');
    process.exit(1);
  }
}

// 运行验证
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    logError(`验证过程异常: ${error.message}`);
    process.exit(1);
  });
}
