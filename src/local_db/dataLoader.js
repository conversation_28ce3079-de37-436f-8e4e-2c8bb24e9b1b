// 本地数据库加载器
// 统一管理所有PRD文档和评审数据

import { realPRDContent as bsvPRDContent, mockIssues as bsvIssues } from './bsv_prd_content_accurate.js';

// 数据库配置
const DB_CONFIG = {
  version: '1.0.0',
  lastUpdated: '2025-07-17',
  defaultDocument: 'BSV',
  supportedDocuments: ['BSV']
};

// 文档数据映射
const DOCUMENT_DATA = {
  BSV: {
    prdContent: bsvPRDContent,
    issues: bsvIssues,
    metadata: {
      title: 'BSV 特性需求文档',
      version: 'V1.0.2',
      lastModified: '2024-10-29',
      totalSections: 32,
      totalIssues: 10,
      status: 'active'
    }
  }
  // 未来可以添加其他文档类型
  // LCA: { ... },
  // AEB: { ... }
};

/**
 * 获取指定文档的PRD内容
 * @param {string} documentType - 文档类型 (BSV, LCA, AEB等)
 * @returns {Object} PRD文档内容
 */
export function getPRDContent(documentType = 'BSV') {
  const docData = DOCUMENT_DATA[documentType];
  if (!docData) {
    console.warn(`文档类型 ${documentType} 不存在，使用默认文档 ${DB_CONFIG.defaultDocument}`);
    return DOCUMENT_DATA[DB_CONFIG.defaultDocument].prdContent;
  }
  return docData.prdContent;
}

/**
 * 获取指定文档的问题数据
 * @param {string} documentType - 文档类型
 * @returns {Array} 问题数组
 */
export function getIssues(documentType = 'BSV') {
  const docData = DOCUMENT_DATA[documentType];
  if (!docData) {
    console.warn(`文档类型 ${documentType} 不存在，使用默认文档 ${DB_CONFIG.defaultDocument}`);
    return DOCUMENT_DATA[DB_CONFIG.defaultDocument].issues;
  }
  return docData.issues;
}

/**
 * 获取文档元数据
 * @param {string} documentType - 文档类型
 * @returns {Object} 元数据对象
 */
export function getDocumentMetadata(documentType = 'BSV') {
  const docData = DOCUMENT_DATA[documentType];
  if (!docData) {
    return null;
  }
  return docData.metadata;
}

/**
 * 获取所有支持的文档类型
 * @returns {Array} 文档类型数组
 */
export function getSupportedDocuments() {
  return DB_CONFIG.supportedDocuments;
}

/**
 * 获取数据库配置信息
 * @returns {Object} 配置对象
 */
export function getDBConfig() {
  return { ...DB_CONFIG };
}

/**
 * 根据问题ID查找问题
 * @param {string} issueId - 问题ID
 * @param {string} documentType - 文档类型
 * @returns {Object|null} 问题对象
 */
export function findIssueById(issueId, documentType = 'BSV') {
  const issues = getIssues(documentType);
  return issues.find(issue => issue.id === issueId) || null;
}

/**
 * 根据章节ID查找章节
 * @param {string} sectionId - 章节ID
 * @param {string} documentType - 文档类型
 * @returns {Object|null} 章节对象
 */
export function findSectionById(sectionId, documentType = 'BSV') {
  const prdContent = getPRDContent(documentType);
  return prdContent.sections.find(section => section.id === sectionId) || null;
}

/**
 * 获取指定章节的相关问题
 * @param {string} sectionId - 章节ID
 * @param {string} documentType - 文档类型
 * @returns {Array} 相关问题数组
 */
export function getIssuesBySection(sectionId, documentType = 'BSV') {
  const issues = getIssues(documentType);
  return issues.filter(issue => issue.section === sectionId);
}

/**
 * 按类型筛选问题
 * @param {string} type - 问题类型 (error, warning, suggestion)
 * @param {string} documentType - 文档类型
 * @returns {Array} 筛选后的问题数组
 */
export function getIssuesByType(type, documentType = 'BSV') {
  const issues = getIssues(documentType);
  return issues.filter(issue => issue.type === type);
}

/**
 * 按优先级筛选问题
 * @param {string} priority - 优先级 (high, medium, low)
 * @param {string} documentType - 文档类型
 * @returns {Array} 筛选后的问题数组
 */
export function getIssuesByPriority(priority, documentType = 'BSV') {
  const issues = getIssues(documentType);
  return issues.filter(issue => issue.priority === priority);
}

/**
 * 获取问题统计信息
 * @param {string} documentType - 文档类型
 * @returns {Object} 统计信息
 */
export function getIssueStatistics(documentType = 'BSV') {
  const issues = getIssues(documentType);
  
  const stats = {
    total: issues.length,
    byType: {
      error: 0,
      warning: 0,
      suggestion: 0
    },
    byPriority: {
      high: 0,
      medium: 0,
      low: 0
    },
    averageConfidence: 0
  };
  
  let totalConfidence = 0;
  
  issues.forEach(issue => {
    // 按类型统计
    if (stats.byType.hasOwnProperty(issue.type)) {
      stats.byType[issue.type]++;
    }
    
    // 按优先级统计
    if (stats.byPriority.hasOwnProperty(issue.priority)) {
      stats.byPriority[issue.priority]++;
    }
    
    // 置信度统计
    totalConfidence += issue.confidence || 0;
  });
  
  stats.averageConfidence = issues.length > 0 ? Math.round(totalConfidence / issues.length) : 0;
  
  return stats;
}

/**
 * 验证数据完整性
 * @param {string} documentType - 文档类型
 * @returns {Object} 验证结果
 */
export function validateData(documentType = 'BSV') {
  const validation = {
    isValid: true,
    errors: [],
    warnings: []
  };
  
  try {
    const prdContent = getPRDContent(documentType);
    const issues = getIssues(documentType);
    const metadata = getDocumentMetadata(documentType);
    
    // 验证PRD内容
    if (!prdContent || !prdContent.title || !prdContent.sections) {
      validation.errors.push('PRD内容结构不完整');
      validation.isValid = false;
    }
    
    // 验证章节数据
    if (prdContent.sections && prdContent.sections.length === 0) {
      validation.errors.push('章节数据为空');
      validation.isValid = false;
    }
    
    // 验证问题数据
    if (!Array.isArray(issues)) {
      validation.errors.push('问题数据格式错误');
      validation.isValid = false;
    }
    
    // 验证元数据
    if (!metadata) {
      validation.warnings.push('缺少文档元数据');
    }
    
    // 验证问题与章节的关联
    if (prdContent.sections && issues) {
      const sectionIds = new Set(prdContent.sections.map(s => s.id));
      issues.forEach(issue => {
        if (!sectionIds.has(issue.section)) {
          validation.warnings.push(`问题 ${issue.id} 关联的章节 ${issue.section} 不存在`);
        }
      });
    }
    
  } catch (error) {
    validation.errors.push(`数据验证异常: ${error.message}`);
    validation.isValid = false;
  }
  
  return validation;
}

// 默认导出当前活跃文档的数据（向后兼容）
export const realPRDContent = getPRDContent();
export const mockIssues = getIssues();

// 导出数据库信息
export const dbInfo = {
  config: getDBConfig(),
  supportedDocuments: getSupportedDocuments(),
  statistics: getIssueStatistics()
};
