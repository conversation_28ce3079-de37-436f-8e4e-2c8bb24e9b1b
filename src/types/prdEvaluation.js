/**
 * PRD评估相关的数据模型和类型定义
 */

/**
 * PRD文档数据模型
 */
export const PRDDocumentSchema = {
  id: '',
  title: '',
  uploadDate: '',
  status: 'uploaded', // "uploaded" | "analyzing" | "analyzed" | "reviewing" | "completed"
  progress: 0,
  issues: 0,
  fixed: 0,
  fileSize: '',
  fileType: '',
  content: ''
};

/**
 * PRD评审状态数据模型
 */
export const PRDReviewStateSchema = {
  currentDocument: null,
  analysisResults: null,
  reviewIssues: [],
  reviewResults: null,
  optimizationData: null
};

/**
 * 评审问题数据模型
 */
export const ReviewIssueSchema = {
  id: '',
  type: 'error', // "error" | "warning" | "suggestion"
  title: '',
  description: '',
  section: '',
  sectionTitle: '',
  suggestion: '',
  accepted: false,
  originalContent: '',
  modifiedContent: '',
  rule: '',
  ruleId: '',
  confidence: 0,
  aiRecommendation: '',
  humanFeedback: '',
  timestamp: '',
  priority: 'medium' // "high" | "medium" | "low"
};

/**
 * 分析结果数据模型
 */
export const AnalysisResultSchema = {
  documentId: '',
  analysisTimestamp: '',
  overallScore: 0,
  totalIssues: 0,
  errorCount: 0,
  warningCount: 0,
  suggestionCount: 0,
  issues: [],
  sectionScores: {},
  completenessScore: 0,
  clarityScore: 0,
  consistencyScore: 0,
  feasibilityScore: 0
};

/**
 * 评审结果数据模型
 */
export const ReviewResultsSchema = {
  documentId: '',
  reviewTimestamp: '',
  totalIssues: 0,
  reviewedIssues: 0,
  acceptedSuggestions: 0,
  rejectedSuggestions: 0,
  reviewedIssueIds: [],
  acceptedSuggestionIds: [],
  overallApproval: 0
};

/**
 * 文档章节数据模型
 */
export const DocumentSectionSchema = {
  id: '',
  title: '',
  content: '',
  issues: []
};

/**
 * 优化数据模型
 */
export const OptimizationDataSchema = {
  documentId: '',
  optimizationTimestamp: '',
  humanAdoptionRate: 0,
  aiAccuracyRate: 0,
  suggestionModificationRate: 0,
  ruleOptimizationSuggestions: [],
  intelligentSuggestions: [],
  performanceMetrics: {
    beforeOptimization: 0,
    afterOptimization: 0,
    improvementPotential: 0,
    confidenceLevel: 0
  }
};

/**
 * 规则优化建议数据模型
 */
export const RuleOptimizationSuggestionSchema = {
  id: '',
  ruleId: '',
  ruleName: '',
  currentVersion: '',
  suggestedChanges: '',
  reason: '',
  expectedImprovement: '',
  priority: 'medium', // "high" | "medium" | "low"
  status: 'pending' // "pending" | "testing" | "approved" | "rejected"
};

/**
 * 智能建议卡片数据模型
 */
export const IntelligentSuggestionCardSchema = {
  id: '',
  type: 'template', // "template" | "content" | "quality"
  title: '',
  content: '',
  priority: 'medium', // "high" | "medium" | "low"
  confidence: 0,
  estimatedImprovement: 0,
  status: 'pending', // "pending" | "applied" | "rejected"
  category: '',
  actionable: true,
  relatedSections: []
};
