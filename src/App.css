/* App.css - PRD AI编辑器主样式文件 */

.app {
  display: flex;
  height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #f8fafc;
  overflow: hidden;
}

/* 左侧边栏样式 */
.sidebar {
  width: 320px;
  background: white;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.sidebar-title {
  font-size: 18px;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 文档列表样式 */
.document-list {
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.document-item {
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 8px;
  border: 1px solid transparent;
}

.document-item:hover {
  background-color: #f1f5f9;
  border-color: #cbd5e1;
}

.document-item.active {
  background-color: #ff6b35;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  border-color: #ff6b35;
}

.document-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
}

.document-info {
  font-size: 12px;
  opacity: 0.7;
}

/* 大纲部分样式 */
.outline-section {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.outline-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 6px;
}

.outline-tree {
  font-size: 13px;
}

.outline-item {
  padding: 6px 0;
  color: #6b7280;
  cursor: pointer;
  transition: color 0.2s;
  line-height: 1.4;
}

.outline-item:hover {
  color: #ff6b35;
}

.outline-item.level-1 {
  font-weight: 600;
  color: #374151;
}

.outline-item.level-2 {
  font-weight: 500;
  color: #4b5563;
}

.outline-item.level-3 {
  color: #6b7280;
}

.outline-text {
  display: block;
  padding: 2px 0;
}

.outline-empty {
  color: #9ca3af;
  font-style: italic;
  text-align: center;
  padding: 20px 0;
}

/* 工具栏样式 */
.toolbar {
  padding: 16px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.toolbar-btn {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  color: #374151;
}

.toolbar-btn:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.toolbar-btn.active {
  background-color: #ff6b35;
  border-color: #ff6b35;
  color: white;
}

.toolbar-btn.secondary {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 分析部分样式 */
.analysis-section {
  padding: 16px;
  border-top: 1px solid #e2e8f0;
  background-color: #fefefe;
}

.analysis-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #374151;
}

.health-score {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.score-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  color: white;
}

.score-number {
  font-size: 18px;
  font-weight: 700;
}

.score-label {
  font-size: 10px;
  opacity: 0.9;
}

.health-details {
  margin-bottom: 16px;
}

.health-item {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
  font-size: 12px;
}

.health-label {
  color: #6b7280;
}

.health-value {
  font-weight: 600;
  color: #10b981;
}

.suggestions {
  margin-top: 16px;
}

.suggestions-title {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #374151;
}

.suggestion-item {
  padding: 6px 8px;
  background-color: #fef3e2;
  border-radius: 4px;
  font-size: 11px;
  margin-bottom: 4px;
  color: #92400e;
  line-height: 1.3;
}

/* 主内容区域样式 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  overflow: hidden;
}

.editor-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.document-title-header {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.editor-status {
  display: flex;
  align-items: center;
  gap: 12px;
}

.auto-save-status {
  font-size: 12px;
  color: #10b981;
  background-color: #ecfdf5;
  padding: 4px 8px;
  border-radius: 4px;
}

/* AI助手面板样式 */
.ai-panel {
  width: 380px;
  background: white;
  border-left: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: -1px 0 3px rgba(0, 0, 0, 0.1);
}

.ai-header {
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.ai-title {
  font-size: 16px;
  font-weight: 700;
  margin: 0 0 6px 0;
}

.ai-status {
  font-size: 12px;
  opacity: 0.9;
}

/* 聊天容器样式 */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* 欢迎消息样式 */
.welcome-message {
  text-align: center;
  padding: 32px 16px;
}

.welcome-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.welcome-text h4 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #374151;
}

.welcome-text p {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 12px;
  line-height: 1.5;
}

.welcome-text ul {
  text-align: left;
  font-size: 13px;
  color: #6b7280;
  margin: 12px 0;
  padding-left: 20px;
}

.welcome-text li {
  margin-bottom: 4px;
}

/* 消息样式 */
.message {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
  gap: 12px;
}

.message.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.message.user .message-avatar {
  background-color: #ff6b35;
  color: white;
}

.message.assistant .message-avatar {
  background-color: #667eea;
  color: white;
}

.message-content {
  max-width: 75%;
  padding: 12px 16px;
  border-radius: 16px;
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
}

.message.user .message-content {
  background-color: #ff6b35;
  color: white;
  border-bottom-right-radius: 4px;
}

.message.assistant .message-content {
  background-color: #f1f5f9;
  color: #374151;
  border-bottom-left-radius: 4px;
}

/* 输入指示器样式 */
.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #9ca3af;
  animation: typing 1.4s infinite;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

/* 聊天输入区域样式 */
.chat-input-container {
  padding: 16px;
  border-top: 1px solid #e2e8f0;
  background: white;
}

.chat-input-wrapper {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.chat-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 24px;
  font-size: 14px;
  outline: none;
  resize: none;
  transition: border-color 0.2s;
}

.chat-input:focus {
  border-color: #667eea;
}

.chat-input:disabled {
  background-color: #f9fafb;
  opacity: 0.6;
}

.send-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-button:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.input-tips {
  font-size: 11px;
  color: #9ca3af;
  text-align: center;
}

/* 响应式样式 */
@media (max-width: 1200px) {
  .ai-panel {
    width: 320px;
  }
}

@media (max-width: 1024px) {
  .sidebar {
    width: 280px;
  }
  
  .ai-panel {
    width: 300px;
  }
}

@media (max-width: 768px) {
  .app {
    flex-direction: column;
  }
  
  .sidebar,
  .ai-panel {
    width: 100%;
    height: auto;
    max-height: 200px;
  }
  
  .main-content {
    flex: 1;
    min-height: 400px;
  }
}

/* 滚动条样式 */
.outline-section::-webkit-scrollbar,
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.outline-section::-webkit-scrollbar-track,
.chat-messages::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.outline-section::-webkit-scrollbar-thumb,
.chat-messages::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.outline-section::-webkit-scrollbar-thumb:hover,
.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 文本截断样式 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}