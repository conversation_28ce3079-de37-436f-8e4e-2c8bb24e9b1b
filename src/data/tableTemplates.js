/**
 * 表格模板数据
 * 提供常用的PRD表格模板
 */

export const tableTemplates = {
  // 功能需求表
  functionalRequirements: {
    name: '功能需求表',
    description: '用于描述产品的功能需求',
    category: 'requirements',
    headers: ['需求ID', '功能名称', '优先级', '描述', '验收标准', '负责人', '状态'],
    data: [
      ['REQ-001', '用户注册', '高', '用户可以通过邮箱注册账号', '用户输入有效邮箱和密码后可成功注册', '张三', '待开发'],
      ['REQ-002', '用户登录', '高', '用户可以通过邮箱和密码登录', '用户输入正确凭据后可成功登录', '李四', '开发中'],
      ['REQ-003', '密码重置', '中', '用户可以重置忘记的密码', '用户通过邮箱验证后可重置密码', '王五', '待开发']
    ],
    validation: {
      columns: [
        { type: 'string', required: true, pattern: '^REQ-\\d+$' }, // 需求ID
        { type: 'string', required: true, minLength: 2, maxLength: 50 }, // 功能名称
        { type: 'string', required: true, enum: ['高', '中', '低'] }, // 优先级
        { type: 'string', required: true, minLength: 10 }, // 描述
        { type: 'string', required: true, minLength: 10 }, // 验收标准
        { type: 'string', required: true }, // 负责人
        { type: 'string', required: true, enum: ['待开发', '开发中', '测试中', '已完成', '已取消'] } // 状态
      ]
    }
  },

  // 用户故事表
  userStories: {
    name: '用户故事表',
    description: '用于描述用户故事和场景',
    category: 'requirements',
    headers: ['故事ID', '作为', '我想要', '以便于', '验收标准', '故事点数', '状态'],
    data: [
      ['US-001', '普通用户', '注册账号', '使用产品功能', '能够成功创建账号并收到确认邮件', '3', '待开发'],
      ['US-002', '注册用户', '登录系统', '访问个人功能', '能够使用正确凭据登录并进入主界面', '2', '开发中'],
      ['US-003', '忘记密码的用户', '重置密码', '重新获得账号访问权限', '能够通过邮箱重置密码并重新登录', '5', '待开发']
    ],
    validation: {
      columns: [
        { type: 'string', required: true, pattern: '^US-\\d+$' },
        { type: 'string', required: true },
        { type: 'string', required: true },
        { type: 'string', required: true },
        { type: 'string', required: true },
        { type: 'number', required: true },
        { type: 'string', required: true, enum: ['待开发', '开发中', '测试中', '已完成'] }
      ]
    }
  },

  // API接口表
  apiInterfaces: {
    name: 'API接口表',
    description: '用于描述系统API接口',
    category: 'technical',
    headers: ['接口名称', '请求方法', '请求路径', '请求参数', '响应格式', '状态码', '负责人'],
    data: [
      ['用户注册', 'POST', '/api/users/register', '{"email": "string", "password": "string"}', '{"success": true, "userId": "string"}', '200/400', '后端团队'],
      ['用户登录', 'POST', '/api/users/login', '{"email": "string", "password": "string"}', '{"success": true, "token": "string"}', '200/401', '后端团队'],
      ['获取用户信息', 'GET', '/api/users/profile', 'Authorization: Bearer token', '{"user": {...}}', '200/401', '后端团队']
    ],
    validation: {
      columns: [
        { type: 'string', required: true },
        { type: 'string', required: true, enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'] },
        { type: 'string', required: true, pattern: '^/api/' },
        { type: 'string' },
        { type: 'string' },
        { type: 'string', required: true },
        { type: 'string', required: true }
      ]
    }
  },

  // 测试用例表
  testCases: {
    name: '测试用例表',
    description: '用于描述测试用例',
    category: 'testing',
    headers: ['用例ID', '测试场景', '前置条件', '测试步骤', '预期结果', '优先级', '执行状态'],
    data: [
      ['TC-001', '用户注册成功', '用户未注册', '1.打开注册页面\n2.输入有效邮箱\n3.输入密码\n4.点击注册', '注册成功，跳转到登录页', '高', '未执行'],
      ['TC-002', '用户注册失败-邮箱已存在', '邮箱已被注册', '1.打开注册页面\n2.输入已存在邮箱\n3.输入密码\n4.点击注册', '显示邮箱已存在错误', '高', '未执行'],
      ['TC-003', '用户登录成功', '用户已注册', '1.打开登录页面\n2.输入正确邮箱密码\n3.点击登录', '登录成功，进入主页', '高', '未执行']
    ],
    validation: {
      columns: [
        { type: 'string', required: true, pattern: '^TC-\\d+$' },
        { type: 'string', required: true },
        { type: 'string' },
        { type: 'string', required: true },
        { type: 'string', required: true },
        { type: 'string', required: true, enum: ['高', '中', '低'] },
        { type: 'string', required: true, enum: ['未执行', '通过', '失败', '阻塞'] }
      ]
    }
  },

  // 数据字典表
  dataDictionary: {
    name: '数据字典表',
    description: '用于描述数据库字段定义',
    category: 'technical',
    headers: ['表名', '字段名', '字段类型', '长度', '是否必填', '默认值', '说明'],
    data: [
      ['users', 'id', 'VARCHAR', '36', '是', 'UUID', '用户唯一标识'],
      ['users', 'email', 'VARCHAR', '255', '是', '', '用户邮箱'],
      ['users', 'password_hash', 'VARCHAR', '255', '是', '', '密码哈希值'],
      ['users', 'created_at', 'TIMESTAMP', '', '是', 'CURRENT_TIMESTAMP', '创建时间'],
      ['users', 'updated_at', 'TIMESTAMP', '', '是', 'CURRENT_TIMESTAMP', '更新时间']
    ],
    validation: {
      columns: [
        { type: 'string', required: true },
        { type: 'string', required: true },
        { type: 'string', required: true, enum: ['VARCHAR', 'INT', 'BIGINT', 'DECIMAL', 'TIMESTAMP', 'TEXT', 'BOOLEAN'] },
        { type: 'string' },
        { type: 'string', required: true, enum: ['是', '否'] },
        { type: 'string' },
        { type: 'string', required: true }
      ]
    }
  },

  // 风险评估表
  riskAssessment: {
    name: '风险评估表',
    description: '用于评估项目风险',
    category: 'management',
    headers: ['风险ID', '风险描述', '风险类型', '发生概率', '影响程度', '风险等级', '应对措施', '负责人'],
    data: [
      ['RISK-001', '第三方API服务不稳定', '技术风险', '中', '高', '高', '准备备用API服务', '技术负责人'],
      ['RISK-002', '需求变更频繁', '业务风险', '高', '中', '高', '建立需求变更流程', '产品经理'],
      ['RISK-003', '关键人员离职', '人员风险', '低', '高', '中', '知识文档化和备份人员培养', 'HR']
    ],
    validation: {
      columns: [
        { type: 'string', required: true, pattern: '^RISK-\\d+$' },
        { type: 'string', required: true },
        { type: 'string', required: true, enum: ['技术风险', '业务风险', '人员风险', '外部风险'] },
        { type: 'string', required: true, enum: ['低', '中', '高'] },
        { type: 'string', required: true, enum: ['低', '中', '高'] },
        { type: 'string', required: true, enum: ['低', '中', '高'] },
        { type: 'string', required: true },
        { type: 'string', required: true }
      ]
    }
  },

  // 项目里程碑表
  projectMilestones: {
    name: '项目里程碑表',
    description: '用于跟踪项目重要节点',
    category: 'management',
    headers: ['里程碑', '计划开始时间', '计划结束时间', '实际开始时间', '实际结束时间', '完成度', '状态', '备注'],
    data: [
      ['需求分析完成', '2024-01-01', '2024-01-15', '2024-01-01', '', '80%', '进行中', ''],
      ['UI设计完成', '2024-01-10', '2024-01-25', '', '', '0%', '未开始', '依赖需求分析'],
      ['后端开发完成', '2024-01-20', '2024-02-20', '', '', '0%', '未开始', ''],
      ['前端开发完成', '2024-01-25', '2024-02-25', '', '', '0%', '未开始', '依赖UI设计']
    ],
    validation: {
      columns: [
        { type: 'string', required: true },
        { type: 'string', required: true, pattern: '^\\d{4}-\\d{2}-\\d{2}$' },
        { type: 'string', required: true, pattern: '^\\d{4}-\\d{2}-\\d{2}$' },
        { type: 'string', pattern: '^\\d{4}-\\d{2}-\\d{2}$' },
        { type: 'string', pattern: '^\\d{4}-\\d{2}-\\d{2}$' },
        { type: 'string', required: true, pattern: '^\\d+%$' },
        { type: 'string', required: true, enum: ['未开始', '进行中', '已完成', '已延期'] },
        { type: 'string' }
      ]
    }
  }
};

// 模板分类
export const templateCategories = {
  requirements: {
    name: '需求管理',
    description: '用于需求分析和管理的表格模板',
    icon: '📋'
  },
  technical: {
    name: '技术文档',
    description: '用于技术设计和开发的表格模板',
    icon: '⚙️'
  },
  testing: {
    name: '测试管理',
    description: '用于测试计划和执行的表格模板',
    icon: '🧪'
  },
  management: {
    name: '项目管理',
    description: '用于项目管理和跟踪的表格模板',
    icon: '📊'
  }
};

// 获取指定分类的模板
export const getTemplatesByCategory = (category) => {
  return Object.entries(tableTemplates)
    .filter(([_, template]) => template.category === category)
    .reduce((acc, [key, template]) => {
      acc[key] = template;
      return acc;
    }, {});
};

// 获取所有模板列表
export const getAllTemplates = () => {
  return Object.entries(tableTemplates).map(([key, template]) => ({
    id: key,
    ...template
  }));
};

// 根据ID获取模板
export const getTemplateById = (id) => {
  return tableTemplates[id] || null;
};

export default {
  tableTemplates,
  templateCategories,
  getTemplatesByCategory,
  getAllTemplates,
  getTemplateById
};
