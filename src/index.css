@tailwind base;
@tailwind components;
@tailwind utilities;

/* 防止横向滚动条 */
html, body {
  overflow-x: hidden;
  max-width: 100vw;
  box-sizing: border-box;
}

*, *::before, *::after {
  box-sizing: border-box;
}

/* 科技蓝主题自定义样式 */
:root { 
    --primary: #2B6CB0; /* 科技蓝 */
    --success: #38A169; /* 绿色 */
    --warning: #D69E2E; /* 黄色 */
    --highlight: #FFF9C4; 
}

/* 平滑过渡动画 */
.transition-all {
    transition: all 0.3s ease;
}

/* 高亮脉冲动画 */
@keyframes pulse { 
    0% { background-color: transparent; } 
    50% { background-color: var(--highlight); } 
    100% { background-color: transparent; } 
}

.highlight-pulse { 
    animation: pulse 1.2s ease-in-out 2; 
}

/* 滚动条样式 */
.no-scrollbar::-webkit-scrollbar {
    display: none;
}

.no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

/* 呼吸感间距 */
.breathing-space {
    padding: 1.5rem;
}

/* 编辑器响应式样式 */
.enhanced-milkdown-editor {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
}

.editor-container {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    word-wrap: break-word;
    word-break: break-word;
}

/* 确保编辑器内容不会超出容器 */
.milkdown {
    width: 100% !important;
    max-width: 100% !important;
}

.milkdown .editor {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
}

/* 表格响应式处理 */
.milkdown table {
    width: 100% !important;
    max-width: 100% !important;
    table-layout: auto;
    overflow-x: auto;
    border-collapse: collapse;
}

.milkdown table tbody,
.milkdown table thead,
.milkdown table tr {
    display: table-row-group;
    width: 100%;
}

.milkdown table tr {
    display: table-row;
}

/* 表格单元格自动换行 */
.milkdown table th,
.milkdown table td {
    word-wrap: break-word !important;
    word-break: break-word !important;
    overflow-wrap: anywhere !important;
    white-space: normal !important;
    vertical-align: top !important;
    min-width: 100px; /* 设置最小宽度确保可读性 */
    max-width: 300px; /* 设置最大宽度防止单元格过宽 */
    line-height: 1.5 !important;
}

/* 编辑器内表格特殊处理 */
.enhanced-milkdown-editor table th,
.enhanced-milkdown-editor table td,
.editor-container table th,
.editor-container table td {
    word-wrap: break-word !important;
    word-break: break-word !important;
    overflow-wrap: anywhere !important;
    white-space: normal !important;
    vertical-align: top !important;
    hyphens: auto !important;
}

/* 确保表格内的长文本能够正确换行 */
.milkdown table p,
.enhanced-milkdown-editor table p,
.editor-container table p {
    margin: 0 !important;
    word-wrap: break-word !important;
    word-break: break-word !important;
    overflow-wrap: anywhere !important;
    white-space: normal !important;
}

/* 文档样式增强 */
.prose h1 { 
    scroll-margin-top: 5rem;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary);
    transition: background-color 0.3s ease;
}

.prose h2 { 
    scroll-margin-top: 5rem;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary);
    transition: background-color 0.3s ease;
}

.prose h3 { 
    scroll-margin-top: 5rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary);
    transition: background-color 0.3s ease;
}

/* 大纲导航样式 */
.outline-item {
    position: relative;
    transition: all 0.2s ease;
    cursor: pointer;
}

.outline-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: transparent;
    transition: background 0.2s ease;
    border-radius: 0 2px 2px 0;
}

.outline-item:hover::before {
    background: var(--primary);
}

.outline-item.active::before {
    background: var(--primary);
}

/* 大纲层级样式 - 调整左边距确保竖线不覆盖文字 */
.outline-level-1 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--primary);
    padding-left: 1rem; /* 增加左边距，为竖线留空间 */
    margin-bottom: 0.25rem;
}

.outline-level-2 {
    font-size: 0.875rem;
    font-weight: 500;
    color: #4B5563;
    padding-left: 2.25rem; /* 增加左边距 */
    margin-bottom: 0.125rem;
}

.outline-level-3 {
    font-size: 0.75rem;
    color: #6B7280;
    padding-left: 3.5rem; /* 增加左边距 */
    margin-bottom: 0.125rem;
}

/* 大纲项悬浮效果 */
.outline-item:hover {
    background-color: rgba(43, 108, 176, 0.05);
}

.outline-item.active {
    background-color: rgba(43, 108, 176, 0.1);
    color: var(--primary);
    font-weight: 600;
}

/* 层级缩进指示器 - 调整位置避免与竖线冲突 */
.outline-level-2::after {
    content: '├─';
    position: absolute;
    left: 1.25rem; /* 调整位置，避免与竖线重叠 */
    color: #CBD5E1;
    font-size: 0.75rem;
}

.outline-level-3::after {
    content: '└─';
    position: absolute;
    left: 2.5rem; /* 调整位置 */
    color: #CBD5E1;
    font-size: 0.75rem;
}

/* 折叠功能样式 */
.outline-toggle {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1rem;
    height: 1rem;
    border: none;
    background: transparent;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.outline-toggle:hover {
    background-color: rgba(43, 108, 176, 0.1);
}

.outline-toggle.collapsed {
    transform: translateY(-50%) rotate(-90deg);
}

.outline-children {
    overflow: hidden;
    transition: all 0.3s ease;
}

.outline-children.collapsed {
    max-height: 0;
    opacity: 0;
}

/* 大纲点击高亮状态 */
.outline-item.clicked-active {
    background: rgba(249, 115, 22, 0.2) !important;
    border-left: 4px solid #f97316 !important;
    color: #ea580c !important;
    font-weight: 700 !important;
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(249, 115, 22, 0.2);
}

/* 内容区域被点击高亮 */
.outline-clicked-highlight {
    background: rgba(249, 115, 22, 0.15) !important;
    border-left: 4px solid #f97316;
    padding-left: 12px !important;
    border-radius: 4px;
}

/* 多行文本截断 */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 章节区域高亮（大纲点击后） */
.outline-section-highlight {
    background: rgba(249, 115, 22, 0.12) !important;
    border-left: 6px solid #f97316;
    padding-left: 16px !important;
    border-radius: 6px;
    box-shadow: 0 2px 12px rgba(249, 115, 22, 0.2);
    transition: all 0.3s ease;
} 