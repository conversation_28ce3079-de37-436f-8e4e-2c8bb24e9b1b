// Agent 相关的类型定义和常量

// Agent 任务状态
export const TASK_STATUS = {
  PENDING: 'pending',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
};

// Agent 类型
export const AGENT_TYPE = {
  DIALOGUE: 'dialogue-agent',
  INSPIRATION: 'inspiration-agent',
  ANALYSIS: 'analysis-agent',
  KNOWLEDGE: 'knowledge-agent',
  COLLABORATION: 'collaboration-agent'
};

// Agent 状态
export const AGENT_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  BUSY: 'busy',
  ERROR: 'error'
};

// 任务优先级
export const TASK_PRIORITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  URGENT: 'urgent'
};
