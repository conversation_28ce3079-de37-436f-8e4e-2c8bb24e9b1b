import { httpClient } from '../api/httpClient';
import { API_CONFIG, AGENT_DISCOVERY, getCurrentConfig } from '../api/config';
import EventEmitter from 'events';
import { MockAgentService } from './MockAgentService';
import { TASK_STATUS, AGENT_TYPE } from './types';

// 重新导出类型以保持向后兼容
export { TASK_STATUS, AGENT_TYPE };

// Agent 客户端类
class AgentClient extends EventEmitter {
  constructor() {
    super();
    this.agents = new Map(); // 可用的Agent实例
    this.tasks = new Map();  // 任务队列
    this.loadBalancer = new LoadBalancer();
    this.config = getCurrentConfig();

    // 在开发环境中使用Mock服务
    if (this.config.enableMockAgent) {
      this.mockService = new MockAgentService();
      this.initializeMockAgents();
    } else {
      // 启动服务发现
      this.startDiscovery();
    }

    // 设置任务监控
    this.setupTaskMonitoring();
  }
  
  // ============= Mock Agent 初始化 =============

  initializeMockAgents() {
    try {
      const mockAgents = this.mockService.getAgents();
      mockAgents.forEach(agent => this.registerAgent(agent));

      console.log(`Mock Agent服务已初始化，注册了 ${mockAgents.length} 个Agent`);
      this.emit('discovery:completed', { count: mockAgents.length });
    } catch (error) {
      console.error('Mock Agent初始化失败:', error);
      this.emit('discovery:failed', error);
    }
  }

  // ============= Agent 发现与管理 =============

  async startDiscovery() {
    try {
      // 获取可用的Agent列表
      const response = await httpClient.get(API_CONFIG.ENDPOINTS.AGENT_DISCOVERY);
      const agents = response.data.agents || [];
      
      // 注册Agent
      agents.forEach(agent => this.registerAgent(agent));
      
      // 启动健康检查
      this.startHealthCheck();
      
      this.emit('discovery:completed', { count: agents.length });
    } catch (error) {
      console.error('Agent发现失败:', error);
      this.emit('discovery:failed', error);
    }
  }
  
  registerAgent(agentInfo) {
    const { id, type, endpoint, capabilities, metadata } = agentInfo;
    
    const agent = {
      id,
      type,
      endpoint,
      capabilities: capabilities || [],
      metadata: metadata || {},
      status: 'active',
      lastHeartbeat: Date.now(),
      taskCount: 0,
      successRate: 1.0
    };
    
    this.agents.set(id, agent);
    this.emit('agent:registered', agent);
  }
  
  startHealthCheck() {
    setInterval(async () => {
      for (const [agentId, agent] of this.agents) {
        try {
          const response = await httpClient.get(
            `${agent.endpoint}/health`,
            { timeout: 5000 }
          );
          
          if (response.status === 200) {
            agent.lastHeartbeat = Date.now();
            agent.status = 'active';
          }
        } catch (error) {
          // 标记为不可用
          agent.status = 'inactive';
          this.emit('agent:unhealthy', { agentId, error });
        }
      }
    }, AGENT_DISCOVERY.HEALTH_CHECK_INTERVAL);
  }
  
  // ============= 任务执行 =============
  
  async executeTask(taskConfig) {
    const task = {
      id: this.generateTaskId(),
      type: taskConfig.type,
      input: taskConfig.input,
      context: taskConfig.context || {},
      priority: taskConfig.priority || 1,
      timeout: taskConfig.timeout || 30000,
      status: TASK_STATUS.PENDING,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
    
    this.tasks.set(task.id, task);
    this.emit('task:created', task);
    
    try {
      // 选择合适的Agent
      const agent = await this.selectAgent(task);
      if (!agent) {
        throw new Error('没有可用的Agent');
      }
      
      // 执行任务
      task.status = TASK_STATUS.RUNNING;
      task.agentId = agent.id;
      task.updatedAt = Date.now();
      this.emit('task:started', task);
      
      const result = await this.executeOnAgent(agent, task);
      
      // 任务完成
      task.status = TASK_STATUS.COMPLETED;
      task.result = result;
      task.completedAt = Date.now();
      task.updatedAt = Date.now();
      
      // 更新Agent统计
      agent.taskCount++;
      agent.successRate = this.calculateSuccessRate(agent);
      
      this.emit('task:completed', task);
      return result;
      
    } catch (error) {
      // 任务失败
      task.status = TASK_STATUS.FAILED;
      task.error = error.message;
      task.updatedAt = Date.now();
      
      if (task.agentId) {
        const agent = this.agents.get(task.agentId);
        if (agent) {
          agent.taskCount++;
          agent.successRate = this.calculateSuccessRate(agent);
        }
      }
      
      this.emit('task:failed', task);
      throw error;
    }
  }
  
  async selectAgent(task) {
    // 筛选可用的Agent
    const availableAgents = Array.from(this.agents.values()).filter(agent => {
      return (
        agent.status === 'active' &&
        this.agentSupportsTask(agent, task) &&
        Date.now() - agent.lastHeartbeat < 60000 // 1分钟内有心跳
      );
    });
    
    if (availableAgents.length === 0) {
      return null;
    }
    
    // 使用负载均衡选择Agent
    return this.loadBalancer.select(availableAgents, task);
  }
  
  agentSupportsTask(agent, task) {
    // 检查Agent是否支持任务类型
    if (agent.type !== task.type && agent.type !== AGENT_TYPE.ORCHESTRATOR) {
      return false;
    }
    
    // 检查能力匹配
    const requiredCapabilities = task.context.requiredCapabilities || [];
    return requiredCapabilities.every(cap => agent.capabilities.includes(cap));
  }
  
  async executeOnAgent(agent, task) {
    const endpoint = `${agent.endpoint}/execute`;
    
    const response = await httpClient.post(endpoint, {
      taskId: task.id,
      type: task.type,
      input: task.input,
      context: task.context
    }, {
      timeout: task.timeout
    });
    
    return response.data;
  }
  
  // ============= 多Agent协同 =============
  
  async executeMultiAgentTask(taskConfig) {
    const orchestratorTask = {
      ...taskConfig,
      type: AGENT_TYPE.ORCHESTRATOR,
      context: {
        ...taskConfig.context,
        multiAgent: true,
        subTasks: taskConfig.subTasks || []
      }
    };
    
    return await this.executeTask(orchestratorTask);
  }
  
  async executeParallelTasks(tasks) {
    const promises = tasks.map(task => this.executeTask(task));
    return await Promise.allSettled(promises);
  }
  
  async executeSequentialTasks(tasks) {
    const results = [];
    let context = {};
    
    for (const task of tasks) {
      // 将前一个任务的结果作为上下文传递
      const taskWithContext = {
        ...task,
        context: { ...task.context, previousResults: results, sharedContext: context }
      };
      
      const result = await this.executeTask(taskWithContext);
      results.push(result);
      
      // 更新共享上下文
      if (result.sharedContext) {
        context = { ...context, ...result.sharedContext };
      }
    }
    
    return results;
  }
  
  // ============= 工具方法 =============
  
  generateTaskId() {
    return `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
  
  calculateSuccessRate(agent) {
    // 简化的成功率计算，实际应该基于历史数据
    return Math.max(0.5, 1 - (agent.taskCount > 10 ? 0.1 : 0));
  }
  
  setupTaskMonitoring() {
    // 任务超时监控
    setInterval(() => {
      const now = Date.now();
      for (const [, task] of this.tasks) {
        if (
          task.status === TASK_STATUS.RUNNING &&
          now - task.updatedAt > task.timeout
        ) {
          task.status = TASK_STATUS.CANCELLED;
          task.error = '任务执行超时';
          task.updatedAt = now;
          this.emit('task:timeout', task);
        }
      }
    }, 5000);
    
    // 清理已完成的任务
    setInterval(() => {
      const cutoff = Date.now() - 24 * 60 * 60 * 1000; // 24小时前
      for (const [taskId, task] of this.tasks) {
        if (task.completedAt && task.completedAt < cutoff) {
          this.tasks.delete(taskId);
        }
      }
    }, 60 * 60 * 1000); // 每小时清理一次
  }
  
  // ============= 公共接口 =============
  
  getAvailableAgents() {
    return Array.from(this.agents.values()).filter(agent => agent.status === 'active');
  }
  
  getTaskStatus(taskId) {
    return this.tasks.get(taskId);
  }
  
  cancelTask(taskId) {
    const task = this.tasks.get(taskId);
    if (task && task.status === TASK_STATUS.RUNNING) {
      task.status = TASK_STATUS.CANCELLED;
      task.updatedAt = Date.now();
      this.emit('task:cancelled', task);
    }
  }
}

// 负载均衡器
class LoadBalancer {
  constructor() {
    this.strategy = AGENT_DISCOVERY.LOAD_BALANCE_STRATEGY;
    this.roundRobinIndex = 0;
  }
  
  select(agents, task) {
    switch (this.strategy) {
      case 'round-robin':
        return this.roundRobin(agents);
      case 'least-connections':
        return this.leastConnections(agents);
      case 'weighted':
        return this.weighted(agents);
      default:
        return this.roundRobin(agents);
    }
  }
  
  roundRobin(agents) {
    const agent = agents[this.roundRobinIndex % agents.length];
    this.roundRobinIndex++;
    return agent;
  }
  
  leastConnections(agents) {
    return agents.reduce((prev, current) => 
      prev.taskCount < current.taskCount ? prev : current
    );
  }
  
  weighted(agents) {
    // 基于成功率的加权选择
    const totalWeight = agents.reduce((sum, agent) => sum + agent.successRate, 0);
    const random = Math.random() * totalWeight;
    
    let currentWeight = 0;
    for (const agent of agents) {
      currentWeight += agent.successRate;
      if (random <= currentWeight) {
        return agent;
      }
    }
    
    return agents[0]; // 备选
  }
}

// 导出单例实例
export const agentClient = new AgentClient();
export { AgentClient }; 