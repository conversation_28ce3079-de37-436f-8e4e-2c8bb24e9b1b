import { AGENT_TYPE } from './types';

// Mock Agent 服务 - 用于开发环境
class MockAgentService {
  constructor() {
    this.agents = this.createMockAgents();
    this.taskHistory = [];
  }
  
  createMockAgents() {
    return [
      {
        id: 'analysis-agent-001',
        type: AGENT_TYPE.ANALYSIS,
        endpoint: 'http://localhost:8001',
        capabilities: ['evaluate', 'inspect', 'validate'],
        metadata: {
          name: '分析助手',
          version: '1.0.0',
          description: '专门负责需求分析和评估'
        },
        status: 'active',
        lastHeartbeat: Date.now(),
        taskCount: 0,
        successRate: 0.95
      },
      {
        id: 'knowledge-agent-001',
        type: AGENT_TYPE.KNOWLEDGE,
        endpoint: 'http://localhost:8002',
        capabilities: ['supplement', 'reference', 'standards'],
        metadata: {
          name: '知识助手',
          version: '1.0.0',
          description: '提供行业知识和标准规范'
        },
        status: 'active',
        lastHeartbeat: Date.now(),
        taskCount: 0,
        successRate: 0.92
      },
      {
        id: 'cases-agent-001',
        type: AGENT_TYPE.CASES,
        endpoint: 'http://localhost:8003',
        capabilities: ['examples', 'patterns', 'best-practices'],
        metadata: {
          name: '案例助手',
          version: '1.0.0',
          description: '提供相关案例和最佳实践'
        },
        status: 'active',
        lastHeartbeat: Date.now(),
        taskCount: 0,
        successRate: 0.88
      },
      {
        id: 'inspiration-agent-001',
        type: AGENT_TYPE.INSPIRATION,
        endpoint: 'http://localhost:8004',
        capabilities: ['creative', 'brainstorm', 'innovative'],
        metadata: {
          name: '灵感助手',
          version: '1.0.0',
          description: '激发创意思维和创新方案'
        },
        status: 'active',
        lastHeartbeat: Date.now(),
        taskCount: 0,
        successRate: 0.90
      }
    ];
  }
  
  // ============= Mock API 响应 =============
  
  async getDiscovery() {
    return {
      success: true,
      data: {
        agents: this.agents,
        timestamp: Date.now()
      }
    };
  }

  // 获取所有Agent
  getAgents() {
    return this.agents;
  }

  async executeTask(agentType, taskConfig) {
    // 模拟网络延迟
    await this.delay(1000 + Math.random() * 2000);
    
    const agent = this.agents.find(a => a.type === agentType);
    if (!agent) {
      throw new Error(`未找到类型为 ${agentType} 的Agent`);
    }
    
    // 根据不同的Agent类型生成不同的响应
    const result = await this.generateMockResponse(agentType, taskConfig);
    
    // 记录任务历史
    this.taskHistory.push({
      taskId: taskConfig.taskId,
      agentType,
      input: taskConfig.input,
      result,
      timestamp: Date.now()
    });
    
    // 更新Agent统计
    agent.taskCount++;
    agent.lastHeartbeat = Date.now();
    
    return {
      success: true,
      data: result
    };
  }
  
  async generateMockResponse(agentType, taskConfig) {
    const { input, context } = taskConfig;
    const selectedText = input.selectedText || '';
    const analysisType = context.analysisType || 'general';
    
    switch (agentType) {
      case AGENT_TYPE.ANALYSIS:
        return this.generateAnalysisResponse(selectedText, analysisType);
        
      case AGENT_TYPE.KNOWLEDGE:
        return this.generateKnowledgeResponse(selectedText, analysisType);
        
      case AGENT_TYPE.CASES:
        return this.generateCasesResponse(selectedText, analysisType);
        
      case AGENT_TYPE.INSPIRATION:
        return this.generateInspirationResponse(selectedText, analysisType);
        
      default:
        return {
          type: 'general',
          title: 'AI分析结果',
          content: `针对文本"${selectedText}"的分析结果`,
          confidence: 0.8,
          timestamp: Date.now()
        };
    }
  }
  
  generateAnalysisResponse(selectedText, analysisType) {
    const responses = {
      evaluate: {
        type: 'analysis',
        title: '需求评估分析',
        content: `
## 需求完整性评估

**分析文本**: ${selectedText}

### 🎯 核心要点分析
- **明确性**: 需求表述相对清晰，但可以进一步细化技术实现细节
- **可行性**: 技术实现方案可行，建议考虑性能优化
- **完整性**: 基本功能完备，建议补充异常处理场景

### 📊 评分详情
- **清晰度**: 85/100
- **完整性**: 78/100  
- **可实现性**: 92/100
- **测试性**: 75/100

### 💡 改进建议
1. 补充边界条件的处理方案
2. 明确性能指标要求
3. 增加用户体验优化考虑
        `,
        confidence: 0.89,
        tags: ['需求分析', '评估', '改进建议'],
        metadata: {
          agentType: 'analysis',
          analysisType: 'evaluate'
        }
      },
      general: {
        type: 'analysis', 
        title: '智能分析结果',
        content: `
## 内容分析

**分析目标**: ${selectedText}

### 🔍 关键信息提取
- 主要功能点已识别
- 技术架构清晰
- 业务流程合理

### 📈 质量评估
- **结构化程度**: 良好
- **逻辑连贯性**: 优秀
- **细节完整性**: 待补充

### 🚀 优化方向
1. 增强细节描述
2. 补充技术选型依据
3. 完善测试策略
        `,
        confidence: 0.82,
        tags: ['分析', '评估'],
        metadata: {
          agentType: 'analysis',
          analysisType: 'general'
        }
      }
    };
    
    return responses[analysisType] || responses.general;
  }
  
  generateKnowledgeResponse(selectedText, analysisType) {
    return {
      type: 'knowledge',
      title: '行业知识补充',
      content: `
## 相关标准与规范

**关联内容**: ${selectedText}

### 📚 行业标准
- **ISO 9241-11**: 用户体验设计原则
- **W3C WCAG 2.1**: Web无障碍指南
- **IEEE 830**: 软件需求规格说明标准

### 🏢 最佳实践
1. **用户体验设计**
   - 遵循直观性原则
   - 保持一致性设计
   - 提供清晰反馈

2. **系统架构**
   - 模块化设计
   - 松耦合原则
   - 可扩展性考虑

### 🔗 参考资料
- [Material Design Guidelines](https://material.io/design)
- [REST API设计最佳实践](https://restfulapi.net/)
- [微服务架构模式](https://microservices.io/patterns/)
      `,
      confidence: 0.91,
      tags: ['知识库', '标准规范', '最佳实践'],
      metadata: {
        agentType: 'knowledge',
        sources: ['ISO', 'W3C', 'IEEE'],
        lastUpdated: Date.now()
      }
    };
  }
  
  generateCasesResponse(selectedText, analysisType) {
    return {
      type: 'cases',
      title: '相关案例参考',
      content: `
## 典型案例分析

**需求领域**: ${selectedText}

### 🏆 成功案例

#### 案例1: Slack团队协作平台
- **相似点**: 实时交互、用户体验优化
- **技术栈**: React + Node.js + WebSocket
- **关键特性**: 
  - 实时消息同步
  - 多媒体文件共享
  - 第三方集成生态

#### 案例2: Notion笔记系统
- **相似点**: 内容编辑、数据组织
- **技术方案**: 
  - 块式编辑器架构
  - 实时协作编辑
  - 灵活的数据结构

### 📊 案例对比

| 维度 | Slack | Notion | 建议方案 |
|------|-------|--------|----------|
| 实时性 | 极佳 | 良好 | 重点优化 |
| 扩展性 | 优秀 | 优秀 | 模块化设计 |
| 用户体验 | 简洁 | 强大 | 平衡易用性 |

### 💡 经验借鉴
1. 渐进式功能发布
2. 用户反馈驱动迭代
3. 性能优化持续进行
      `,
      confidence: 0.87,
      tags: ['案例分析', 'Slack', 'Notion', '最佳实践'],
      metadata: {
        agentType: 'cases',
        caseCount: 2,
        industryDomain: 'productivity-tools'
      }
    };
  }
  
  generateInspirationResponse(selectedText, analysisType) {
    return {
      type: 'inspiration',
      title: '创新灵感与建议',
      content: `
## 创新思路激发

**当前需求**: ${selectedText}

### 💫 创新方向

#### 1. AI驱动优化
- **智能内容推荐**: 基于用户行为预测需求
- **自动代码生成**: 根据需求描述生成原型
- **智能质量检测**: 实时发现潜在问题

#### 2. 用户体验创新
- **语音交互**: 支持语音输入和反馈
- **手势控制**: 移动端手势导航
- **个性化界面**: 自适应用户偏好

#### 3. 协作模式创新
- **异步协作**: 跨时区团队协作工具
- **版本分支**: Git-like的需求版本管理
- **实时评审**: 在线需求评审系统

### 🎨 设计灵感

#### 游戏化元素
- 需求完成度进度条
- 质量评分排行榜
- 团队协作成就系统

#### 视觉化增强
- 需求关系图谱
- 交互式流程图
- 3D数据可视化

### 🚀 未来展望
1. **AR/VR集成**: 沉浸式需求设计体验
2. **区块链溯源**: 需求变更不可篡改记录
3. **量子计算**: 超大规模需求优化算法
      `,
      confidence: 0.75,
      tags: ['创新', 'AI驱动', '用户体验', '未来技术'],
      metadata: {
        agentType: 'inspiration',
        creativityScore: 0.88,
        noveltyFactor: 0.72
      }
    };
  }
  
  // ============= 工具方法 =============
  
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  getAgentHealth(agentId) {
    const agent = this.agents.find(a => a.id === agentId);
    return {
      success: !!agent,
      data: {
        status: agent?.status || 'unknown',
        lastHeartbeat: agent?.lastHeartbeat || 0,
        taskCount: agent?.taskCount || 0,
        successRate: agent?.successRate || 0
      }
    };
  }
  
  getTaskHistory(limit = 10) {
    return {
      success: true,
      data: {
        tasks: this.taskHistory.slice(-limit),
        total: this.taskHistory.length
      }
    };
  }
}

// 导出单例实例
export const mockAgentService = new MockAgentService();
export { MockAgentService }; 