import { httpClient } from './httpClient';
import { logger } from '../utils/logger';

// Agent发现和管理
export const agentDiscovery = {
  // 获取可用Agent列表
  async getAvailableAgents() {
    try {
      const response = await httpClient.get('/api/agents/discovery');
      
      logger.info('Agent discovery completed', {
        agentCount: response.data.agents.length,
        activeCount: response.data.activeAgents
      });
      
      return {
        success: true,
        agents: response.data.agents,
        metadata: {
          totalAgents: response.data.totalAgents,
          activeAgents: response.data.activeAgents,
          timestamp: response.data.timestamp
        }
      };
    } catch (error) {
      logger.error('Agent discovery failed', { error: error.message });
      throw error;
    }
  },

  // 检查Agent健康状态
  async checkAgentHealth(agentId) {
    try {
      const response = await httpClient.get(`/api/agents/${agentId}/health`);
      
      return {
        success: true,
        health: response.data,
        agentId
      };
    } catch (error) {
      logger.error('Agent health check failed', { 
        agentId, 
        error: error.message 
      });
      throw error;
    }
  },

  // 获取Agent统计信息
  async getAgentStats() {
    try {
      const response = await httpClient.get('/api/agents/stats');
      
      return {
        success: true,
        stats: response.data
      };
    } catch (error) {
      logger.error('Failed to get agent stats', { error: error.message });
      throw error;
    }
  }
};

// 多Agent协同执行
export const multiAgentCollaboration = {
  // 并行执行多个Agent任务
  async executeParallel(tasks, context = {}) {
    try {
      const requestPayload = {
        tasks: tasks.map(task => ({
          type: task.agentType,
          input: task.input,
          context: task.context || {}
        })),
        mode: 'parallel',
        context
      };

      logger.info('Starting parallel agent collaboration', {
        taskCount: tasks.length,
        agentTypes: tasks.map(t => t.agentType)
      });

      const response = await httpClient.post('/api/agents/collaborate', requestPayload);
      
      logger.info('Parallel collaboration completed', {
        collaborationId: response.data.collaborationId,
        duration: response.data.duration,
        taskCount: tasks.length
      });

      return {
        success: true,
        collaboration: response.data,
        results: response.data.results
      };
    } catch (error) {
      logger.error('Parallel collaboration failed', { 
        tasks: tasks.length,
        error: error.message 
      });
      throw error;
    }
  },

  // 顺序执行多个Agent任务
  async executeSequential(tasks, context = {}) {
    try {
      const requestPayload = {
        tasks: tasks.map(task => ({
          type: task.agentType,
          input: task.input,
          context: task.context || {}
        })),
        mode: 'sequential',
        context
      };

      logger.info('Starting sequential agent collaboration', {
        taskCount: tasks.length,
        agentTypes: tasks.map(t => t.agentType)
      });

      const response = await httpClient.post('/api/agents/collaborate', requestPayload);
      
      logger.info('Sequential collaboration completed', {
        collaborationId: response.data.collaborationId,
        duration: response.data.duration,
        taskCount: tasks.length
      });

      return {
        success: true,
        collaboration: response.data,
        results: response.data.results
      };
    } catch (error) {
      logger.error('Sequential collaboration failed', { 
        tasks: tasks.length,
        error: error.message 
      });
      throw error;
    }
  },

  // 智能协同执行（根据任务类型自动选择模式）
  async executeIntelligent(selectedText, analysisTypes, context = {}) {
    try {
      // 根据分析类型判断是否适合并行执行
      const canParallel = analysisTypes.every(type => 
        ['evaluate', 'supplement', 'examples', 'inspire'].includes(type)
      );

      const tasks = analysisTypes.map(analysisType => ({
        agentType: this.getAgentTypeByAnalysis(analysisType),
        input: { selectedText },
        context: { analysisType, ...context }
      }));

      if (canParallel && tasks.length > 1) {
        return await this.executeParallel(tasks, context);
      } else {
        return await this.executeSequential(tasks, context);
      }
    } catch (error) {
      logger.error('Intelligent collaboration failed', { 
        analysisTypes,
        error: error.message 
      });
      throw error;
    }
  },

  // 获取协同执行历史
  async getCollaborationHistory(options = {}) {
    try {
      const params = new URLSearchParams({
        limit: options.limit || 20,
        offset: options.offset || 0,
        mode: options.mode || '',
        dateFrom: options.dateFrom || '',
        dateTo: options.dateTo || ''
      });

      const response = await httpClient.get(`/api/collaborations/history?${params}`);
      
      return {
        success: true,
        history: response.data.collaborations,
        pagination: response.data.pagination,
        statistics: response.data.statistics
      };
    } catch (error) {
      logger.error('Failed to get collaboration history', { error: error.message });
      throw error;
    }
  },

  // 工具方法：根据分析类型获取Agent类型
  getAgentTypeByAnalysis(analysisType) {
    const mapping = {
      'evaluate': 'analysis-agent',
      'inspect': 'analysis-agent',
      'supplement': 'knowledge-agent',
      'reference': 'knowledge-agent',
      'examples': 'cases-agent',
      'cases': 'cases-agent',
      'inspire': 'inspiration-agent',
      'creative': 'inspiration-agent'
    };
    return mapping[analysisType] || 'analysis-agent';
  }
};

// 任务管理增强
export const taskManagement = {
  // 创建任务
  async createTask(agentType, input, options = {}) {
    try {
      const requestPayload = {
        type: agentType,
        input,
        context: options.context || {},
        priority: options.priority || 1,
        timeout: options.timeout || 30000
      };

      const response = await httpClient.post('/api/tasks', requestPayload);
      
      logger.info('Task created', {
        taskId: response.data.taskId,
        agentType,
        priority: options.priority
      });

      return {
        success: true,
        task: response.data
      };
    } catch (error) {
      logger.error('Task creation failed', { 
        agentType,
        error: error.message 
      });
      throw error;
    }
  },

  // 获取任务状态
  async getTaskStatus(taskId) {
    try {
      const response = await httpClient.get(`/api/tasks/${taskId}`);
      
      return {
        success: true,
        task: response.data
      };
    } catch (error) {
      logger.error('Failed to get task status', { 
        taskId,
        error: error.message 
      });
      throw error;
    }
  },

  // 取消任务
  async cancelTask(taskId) {
    try {
      const response = await httpClient.delete(`/api/tasks/${taskId}`);
      
      logger.info('Task cancelled', { taskId });

      return {
        success: true,
        result: response.data
      };
    } catch (error) {
      logger.error('Task cancellation failed', { 
        taskId,
        error: error.message 
      });
      throw error;
    }
  },

  // 重试任务
  async retryTask(taskId) {
    try {
      const response = await httpClient.post(`/api/tasks/${taskId}/retry`);
      
      logger.info('Task retried', { 
        originalTaskId: taskId,
        newTaskId: response.data.newTaskId
      });

      return {
        success: true,
        result: response.data
      };
    } catch (error) {
      logger.error('Task retry failed', { 
        taskId,
        error: error.message 
      });
      throw error;
    }
  },

  // 获取任务列表
  async getTasks(filters = {}) {
    try {
      const params = new URLSearchParams({
        status: filters.status || '',
        type: filters.type || '',
        limit: filters.limit || 50,
        offset: filters.offset || 0,
        sort: filters.sort || 'createdAt',
        order: filters.order || 'desc'
      });

      const response = await httpClient.get(`/api/tasks?${params}`);
      
      return {
        success: true,
        tasks: response.data.tasks,
        pagination: response.data.pagination
      };
    } catch (error) {
      logger.error('Failed to get tasks', { error: error.message });
      throw error;
    }
  },

  // 批量操作任务
  async batchOperation(action, taskIds) {
    try {
      const requestPayload = {
        action,
        taskIds
      };

      const response = await httpClient.post('/api/tasks/batch', requestPayload);
      
      logger.info('Batch task operation completed', {
        action,
        taskCount: taskIds.length,
        successCount: response.data.summary.successful
      });

      return {
        success: true,
        results: response.data.results,
        summary: response.data.summary
      };
    } catch (error) {
      logger.error('Batch task operation failed', { 
        action,
        taskCount: taskIds.length,
        error: error.message 
      });
      throw error;
    }
  }
};

// 分析服务增强
export const analysisService = {
  // 执行分析
  async performAnalysis(selectedText, analysisType, options = {}) {
    try {
      const requestPayload = {
        selectedText,
        analysisType,
        context: options.context || {},
        multiAgent: options.multiAgent || false,
        priority: options.priority || 1
      };

      logger.info('Starting analysis', {
        analysisType,
        textLength: selectedText.length,
        multiAgent: options.multiAgent
      });

      const response = await httpClient.post('/api/analysis', requestPayload);
      
      logger.info('Analysis completed', {
        analysisId: response.data.analysisId,
        analysisType,
        duration: response.data.duration,
        confidence: response.data.confidence
      });

      return {
        success: true,
        analysis: response.data
      };
    } catch (error) {
      logger.error('Analysis failed', { 
        analysisType,
        error: error.message 
      });
      throw error;
    }
  },

  // 获取分析历史
  async getAnalysisHistory(options = {}) {
    try {
      const params = new URLSearchParams({
        limit: options.limit || 20,
        offset: options.offset || 0,
        type: options.type || '',
        dateFrom: options.dateFrom || '',
        dateTo: options.dateTo || '',
        sort: options.sort || 'createdAt',
        order: options.order || 'desc'
      });

      const response = await httpClient.get(`/api/analysis/history?${params}`);
      
      return {
        success: true,
        history: response.data.analyses,
        pagination: response.data.pagination,
        statistics: response.data.statistics
      };
    } catch (error) {
      logger.error('Failed to get analysis history', { error: error.message });
      throw error;
    }
  },

  // 获取分析详情
  async getAnalysisDetails(analysisId) {
    try {
      const response = await httpClient.get(`/api/analysis/${analysisId}`);
      
      return {
        success: true,
        analysis: response.data
      };
    } catch (error) {
      logger.error('Failed to get analysis details', { 
        analysisId,
        error: error.message 
      });
      throw error;
    }
  },

  // 重新运行分析
  async rerunAnalysis(analysisId, context = {}) {
    try {
      const response = await httpClient.post(`/api/analysis/${analysisId}/rerun`, { context });
      
      logger.info('Analysis rerun completed', {
        originalAnalysisId: analysisId,
        newAnalysisId: response.data.newAnalysisId
      });

      return {
        success: true,
        result: response.data
      };
    } catch (error) {
      logger.error('Analysis rerun failed', { 
        analysisId,
        error: error.message 
      });
      throw error;
    }
  },

  // 提交分析反馈
  async submitFeedback(analysisId, feedback) {
    try {
      const response = await httpClient.post(`/api/analysis/${analysisId}/feedback`, feedback);
      
      logger.info('Analysis feedback submitted', {
        analysisId,
        rating: feedback.rating,
        feedbackId: response.data.feedbackId
      });

      return {
        success: true,
        result: response.data
      };
    } catch (error) {
      logger.error('Failed to submit analysis feedback', { 
        analysisId,
        error: error.message 
      });
      throw error;
    }
  },

  // 获取分析统计报告
  async getAnalysisReport(options = {}) {
    try {
      const params = new URLSearchParams({
        period: options.period || '7d',
        agentType: options.agentType || ''
      });

      const response = await httpClient.get(`/api/analysis/stats/report?${params}`);
      
      return {
        success: true,
        report: response.data
      };
    } catch (error) {
      logger.error('Failed to get analysis report', { error: error.message });
      throw error;
    }
  }
}; 