// API 配置管理
export const API_CONFIG = {
  // 基础配置
  BASE_URL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:3550',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  
  // Agent 服务端点
  ENDPOINTS: {
    // 核心API - 修复路径匹配后端
    AGENTS: '/api/agents',
    TASKS: '/api/tasks',
    ANALYSIS: '/api/analysis',
    
    // 实时通信
    WEBSOCKET: '/ws',
    EVENTS: '/api/events',
    
    // Agent 专用端点
    AGENT_DISCOVERY: '/api/agents/discovery',
    AGENT_HEALTH: '/api/agents/health',
    MULTI_AGENT: '/api/agents/collaborate'
  },
  
  // Agent 类型映射 - 支持5种类型
  AGENT_TYPES: {
    DIALOGUE: 'dialogue-agent',
    INSPIRATION: 'inspiration-agent',
    ANALYSIS: 'analysis-agent',
    KNOWLEDGE: 'knowledge-agent', 
    CASES: 'cases-agent',
    ORCHESTRATOR: 'orchestrator-agent'
  },
  
  // 请求配置
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Client-Version': '1.0.0'
  },
  
  // 环境适配
  ENV: {
    DEVELOPMENT: {
      enableMockAgent: true,
      logLevel: 'debug',
      enableDevTools: true
    },
    PRODUCTION: {
      enableMockAgent: false,
      logLevel: 'error',
      enableDevTools: false
    }
  }
};

// 获取当前环境配置
export const getCurrentConfig = () => {
  const env = process.env.NODE_ENV || 'development';
  return {
    ...API_CONFIG,
    ...API_CONFIG.ENV[env.toUpperCase()]
  };
};

// Agent 服务发现配置
export const AGENT_DISCOVERY = {
  // 服务注册间隔
  HEARTBEAT_INTERVAL: 30000,
  
  // 健康检查
  HEALTH_CHECK_INTERVAL: 10000,
  
  // 负载均衡策略
  LOAD_BALANCE_STRATEGY: 'round-robin', // 'round-robin' | 'least-connections' | 'weighted'
  
  // Agent 能力注册
  CAPABILITIES: {
    'dialogue-agent': ['conversation', 'discussion', 'q&a', 'clarification'],
    'inspiration-agent': ['creative', 'brainstorm', 'innovative', 'inspire'],
    'analysis-agent': ['evaluate', 'inspect', 'validate'],
    'knowledge-agent': ['supplement', 'reference', 'standards'],
    'cases-agent': ['examples', 'patterns', 'best-practices']
  }
}; 