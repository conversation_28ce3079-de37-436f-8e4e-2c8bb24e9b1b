import { getCurrentConfig } from './config';

// HTTP 客户端类
class HttpClient {
  constructor() {
    this.config = getCurrentConfig();
    this.baseURL = this.config.BASE_URL;
    this.timeout = this.config.TIMEOUT;
    this.retryAttempts = this.config.RETRY_ATTEMPTS;
    
    // 请求拦截器队列
    this.requestInterceptors = [];
    this.responseInterceptors = [];
    
    // 初始化
    this.setupDefaults();
  }
  
  setupDefaults() {
    // 添加默认请求拦截器
    this.addRequestInterceptor((config) => {
      // 添加认证头
      const token = localStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      
      // 添加请求ID用于追踪
      config.headers['X-Request-ID'] = this.generateRequestId();
      
      // 添加时间戳
      config.headers['X-Timestamp'] = Date.now().toString();
      
      return config;
    });
    
    // 添加默认响应拦截器
    this.addResponseInterceptor(
      (response) => response,
      (error) => this.handleError(error)
    );
  }
  
  generateRequestId() {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
  
  addRequestInterceptor(fulfilled, rejected) {
    this.requestInterceptors.push({ fulfilled, rejected });
  }
  
  addResponseInterceptor(fulfilled, rejected) {
    this.responseInterceptors.push({ fulfilled, rejected });
  }
  
  async executeInterceptors(interceptors, value, isRequest = false) {
    let result = value;
    
    for (const interceptor of interceptors) {
      try {
        if (interceptor.fulfilled) {
          result = await interceptor.fulfilled(result);
        }
      } catch (error) {
        if (interceptor.rejected) {
          result = await interceptor.rejected(error);
        } else {
          throw error;
        }
      }
    }
    
    return result;
  }
  
  async request(config) {
    // 合并配置
    const finalConfig = {
      method: 'GET',
      timeout: this.timeout,
      ...config,
      url: `${this.baseURL}${config.url}`,
      headers: {
        ...this.config.DEFAULT_HEADERS,
        ...config.headers
      }
    };
    
    // 执行请求拦截器
    const processedConfig = await this.executeInterceptors(
      this.requestInterceptors, 
      finalConfig, 
      true
    );
    
    // 执行请求
    let response;
    let lastError;
    
    for (let attempt = 0; attempt <= this.retryAttempts; attempt++) {
      try {
        response = await this.executeRequest(processedConfig);
        break;
      } catch (error) {
        lastError = error;
        
        // 如果是最后一次尝试或不应重试的错误，直接抛出
        if (attempt === this.retryAttempts || !this.shouldRetry(error)) {
          throw error;
        }
        
        // 等待后重试
        await this.delay(Math.pow(2, attempt) * 1000);
      }
    }
    
    if (!response) {
      throw lastError;
    }
    
    // 执行响应拦截器
    return await this.executeInterceptors(this.responseInterceptors, response);
  }
  
  async executeRequest(config) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), config.timeout);
    
    try {
      const response = await fetch(config.url, {
        method: config.method,
        headers: config.headers,
        body: config.body ? JSON.stringify(config.body) : undefined,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new HttpError(
          `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          response
        );
      }
      
      const data = await response.json();
      return {
        data,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        config
      };
      
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }
  
  shouldRetry(error) {
    // 网络错误或5xx服务器错误才重试
    return (
      error.name === 'TypeError' || // 网络错误
      error.name === 'AbortError' || // 超时
      (error.status >= 500 && error.status < 600) // 服务器错误
    );
  }
  
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  handleError(error) {
    if (this.config.logLevel === 'debug') {
      console.error('HTTP请求错误:', error);
    }
    
    // 统一错误格式
    const normalizedError = {
      message: error.message || '请求失败',
      status: error.status || 0,
      code: error.code || 'UNKNOWN_ERROR',
      timestamp: Date.now()
    };
    
    // 根据错误类型处理
    if (error.status === 401) {
      // 认证失败，清除token
      localStorage.removeItem('auth_token');
      normalizedError.code = 'UNAUTHORIZED';
    } else if (error.status === 403) {
      normalizedError.code = 'FORBIDDEN';
    } else if (error.status >= 500) {
      normalizedError.code = 'SERVER_ERROR';
    } else if (error.name === 'AbortError') {
      normalizedError.code = 'TIMEOUT';
    }
    
    throw normalizedError;
  }
  
  // 便捷方法
  get(url, config = {}) {
    return this.request({ ...config, method: 'GET', url });
  }
  
  post(url, data, config = {}) {
    return this.request({ ...config, method: 'POST', url, body: data });
  }
  
  put(url, data, config = {}) {
    return this.request({ ...config, method: 'PUT', url, body: data });
  }
  
  delete(url, config = {}) {
    return this.request({ ...config, method: 'DELETE', url });
  }
  
  patch(url, data, config = {}) {
    return this.request({ ...config, method: 'PATCH', url, body: data });
  }
}

// HTTP 错误类
class HttpError extends Error {
  constructor(message, status, response) {
    super(message);
    this.name = 'HttpError';
    this.status = status;
    this.response = response;
  }
}

// 导出单例实例
export const httpClient = new HttpClient();
export { HttpClient, HttpError }; 