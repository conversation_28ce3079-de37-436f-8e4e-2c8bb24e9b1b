import { useState, useCallback } from 'react';
import { PRDDocumentSchema } from '../types/prdEvaluation';

/**
 * PRD文档上传Hook
 */
export const usePRDUpload = () => {
  const [isDragging, setIsDragging] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  // 文件类型验证
  const validateFileType = useCallback((file) => {
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/markdown',
      'text/plain'
    ];
    return allowedTypes.includes(file.type);
  }, []);

  // 文件大小验证
  const validateFileSize = useCallback((file, maxSizeMB = 50) => {
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    return file.size <= maxSizeBytes;
  }, []);

  // 文件大小格式化
  const formatFileSize = useCallback((bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  // 读取文件内容
  const readFileContent = useCallback(async (file) => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result || '');
      reader.readAsText(file);
    });
  }, []);

  // 文件上传处理
  const handleFileUpload = useCallback(async (files, onDocumentAdded) => {
    setIsUploading(true);
    setUploadProgress(0);

    const uploadedDocs = [];

    for (const file of Array.from(files)) {
      // 验证文件格式
      if (!validateFileType(file)) {
        console.error(`不支持的文件格式: ${file.name}`);
        continue;
      }

      // 验证文件大小
      if (!validateFileSize(file)) {
        console.error(`文件过大: ${file.name}`);
        continue;
      }

      // 模拟上传进度
      for (let i = 0; i <= 100; i += 10) {
        setUploadProgress(i);
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 创建文档记录
      const newDocument = {
        ...PRDDocumentSchema,
        id: Date.now().toString() + Math.random().toString(36).substr(2),
        title: file.name.replace(/\.[^/.]+$/, ""),
        uploadDate: new Date().toLocaleDateString(),
        status: "uploaded",
        progress: 0,
        issues: 0,
        fixed: 0,
        fileSize: formatFileSize(file.size),
        fileType: file.type,
        content: await readFileContent(file)
      };

      uploadedDocs.push(newDocument);
    }

    // 通知父组件添加文档
    if (onDocumentAdded && uploadedDocs.length > 0) {
      onDocumentAdded(uploadedDocs);
    }

    setIsUploading(false);
    setUploadProgress(0);
    
    return uploadedDocs;
  }, [validateFileType, validateFileSize, formatFileSize, readFileContent]);

  // 状态样式映射
  const getStatusVariant = useCallback((status) => {
    switch (status) {
      case "completed": return "default";
      case "reviewing": return "secondary";
      case "analyzing": return "secondary";
      case "analyzed": return "outline";
      default: return "outline";
    }
  }, []);

  const getStatusText = useCallback((status) => {
    switch (status) {
      case "uploaded": return "已上传";
      case "analyzing": return "分析中";
      case "analyzed": return "已分析";
      case "reviewing": return "评审中";
      case "completed": return "已完成";
      default: return "未知";
    }
  }, []);

  return {
    isDragging,
    setIsDragging,
    uploadProgress,
    isUploading,
    handleFileUpload,
    validateFileType,
    validateFileSize,
    formatFileSize,
    getStatusVariant,
    getStatusText
  };
};
