import { useState, useEffect, useCallback } from 'react';

export const useOutlineSync = () => {
  const [activeSection, setActiveSection] = useState(null);

  const handleOutlineClick = useCallback((outlineItem) => {
    // 首先更新活动部分状态，确保大纲高亮正确
    setActiveSection(outlineItem.id);

    // 查找目标元素
    const targetElement = document.getElementById(outlineItem.id);
    if (!targetElement) {
      return;
    }

    // 查找预览容器 - 使用明确的容器类名
    const previewContainer = document.querySelector('.prd-content-container');
    
    if (previewContainer) {
      // 计算目标元素在容器中的准确位置（移除未使用的变量）
      
      // 计算元素相对于容器的绝对位置
      const elementOffsetTop = targetElement.offsetTop;
      
      // 计算目标滚动位置（元素顶部距离容器顶部20px）
      const targetPosition = Math.max(0, elementOffsetTop - 20);
      
      // 执行滚动到章节起始处
      try {
        previewContainer.scrollTo({
          top: targetPosition,
          behavior: 'smooth'
        });
      } catch (error) {
        // 回退方案：直接设置scrollTop
        previewContainer.scrollTop = targetPosition;
      }
    } else {
      // 回退到窗口滚动
      const rect = targetElement.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const elementTop = rect.top + scrollTop;
      const targetPosition = elementTop - 20;
      
      try {
        window.scrollTo({
          top: Math.max(0, targetPosition),
          behavior: 'smooth'
        });
      } catch (error) {
        window.scrollTop = Math.max(0, targetPosition);
      }
    }

    // 移除之前的高亮
    const prevHighlighted = document.querySelectorAll('.outline-clicked-highlight, .outline-section-highlight');
    prevHighlighted.forEach(el => {
      el.classList.remove('outline-clicked-highlight', 'outline-section-highlight');
    });

    // 添加章节高亮效果（高亮整个章节区域）
    targetElement.classList.add('outline-section-highlight', 'highlight-pulse');
    
    // 同时高亮大纲项
    const outlineItems = document.querySelectorAll('.outline-item');
    outlineItems.forEach(item => {
      item.classList.remove('clicked-active');
      // 如果是当前激活的项目，添加active类
      if (item.getAttribute('data-outline-id') === outlineItem.id) {
        item.classList.add('clicked-active');
      }
    });

    // 移除高亮效果
    setTimeout(() => {
      targetElement.classList.remove('highlight-pulse');
    }, 2000);

    // 3秒后移除点击高亮，但保持激活状态
    setTimeout(() => {
      targetElement.classList.remove('outline-section-highlight');
      const clickedOutlineItem = document.querySelector(`[data-outline-id="${outlineItem.id}"]`);
      if (clickedOutlineItem) {
        clickedOutlineItem.classList.remove('clicked-active');
      }
    }, 3000);
  }, []);

  // 计算当前可见的章节
  const calculateActiveSection = useCallback((scrollContainer) => {
    const headings = scrollContainer.querySelectorAll('h1, h2, h3, h4, h5, h6');
    if (headings.length === 0) return null;

    const containerRect = scrollContainer.getBoundingClientRect();
    const scrollTop = scrollContainer.scrollTop;
    
    // 找到当前最接近顶部的标题
    let activeHeading = null;
    let minDistance = Infinity;
    
    headings.forEach(heading => {
      const headingRect = heading.getBoundingClientRect();
      const headingTop = headingRect.top - containerRect.top + scrollTop;
      const distance = Math.abs(headingTop - scrollTop - 80); // 80px 偏移
      
      if (headingTop <= scrollTop + 80 && distance < minDistance) {
        minDistance = distance;
        activeHeading = heading;
      }
    });
    
    return activeHeading?.id || null;
  }, []);

  // 滚动监听器
  useEffect(() => {
    const handleScroll = () => {
      // 查找预览容器
      const previewContainer = document.querySelector('.preview-content');
      if (!previewContainer) return;

      const newActiveSection = calculateActiveSection(previewContainer);
      if (newActiveSection && newActiveSection !== activeSection) {
        setActiveSection(newActiveSection);
      }
    };

    // 防抖处理
    let ticking = false;
    const scrollListener = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    // 监听预览容器的滚动
    const previewContainer = document.querySelector('.preview-content');
    if (previewContainer) {
      previewContainer.addEventListener('scroll', scrollListener);
      
      // 初始计算
      handleScroll();
      
      return () => {
        previewContainer.removeEventListener('scroll', scrollListener);
      };
    }

    // 如果没有找到预览容器，回退到window监听
    window.addEventListener('scroll', scrollListener);
    return () => window.removeEventListener('scroll', scrollListener);
  }, [activeSection, calculateActiveSection]);

  return {
    activeSection,
    handleOutlineClick
  };
}; 