import { useState, useEffect, useCallback } from 'react';
import { loadContent, saveContent, getMetadata } from '../utils/storageUtils';
import { generateOutline } from '../utils/outlineParser';
import { realPRDContent } from '../data/realPRDContent';
import {
  generateMarkdownFromSections,
  generateOutlineFromSections,
  addSection as addSectionToArray,
  deleteSection as deleteSectionFromArray,
  moveSectionUp,
  moveSectionDown,
  updateSectionLevel as updateSectionLevelInArray,
  duplicateSection as duplicateSectionInArray,
  reorderSections as reorderSectionsInArray
} from '../utils/contentSync';

// 全局状态，防止React.StrictMode导致的重复初始化
let globalInitialized = false;
let globalContent = null;
let globalSections = null;
let globalMetadata = null;

const useContentState = (initialContent, useRealPRD = false) => {
  const [content, setContent] = useState('');
  const [outline, setOutline] = useState([]);
  const [isModified, setIsModified] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const [metadata, setMetadata] = useState(null);

  // 新增：章节管理相关状态
  const [sections, setSections] = useState([]);
  const [editHistory, setEditHistory] = useState([]);
  const [currentVersion, setCurrentVersion] = useState(0);
  const [isInitialized, setIsInitialized] = useState(false);

  // 初始化内容 - 防止重复初始化
  useEffect(() => {
    if (isInitialized) {
      console.log('🔄 跳过重复初始化，已经初始化过');
      return;
    }
    let initContent;
    let initOutline;
    let initMetadata;

    if (useRealPRD) {
      // 检查全局状态，防止重复初始化
      if (globalInitialized && globalContent && globalSections && globalMetadata) {
        console.log('🔄 使用全局缓存的BSV文档数据');
        initContent = globalContent;
        initOutline = generateOutlineFromSections(globalSections);
        initMetadata = globalMetadata;
        setSections(globalSections);
      } else {
        // 首次初始化
        console.log('🚀 首次初始化BSV PRD文档数据');
        initContent = generateMarkdownFromSections(realPRDContent.sections);
        initOutline = generateOutlineFromSections(realPRDContent.sections);
        initMetadata = {
          title: realPRDContent.title,
          lastSaved: new Date().toISOString(),
          sections: realPRDContent.sections.length
        };

        // 缓存到全局状态
        globalContent = initContent;
        globalSections = realPRDContent.sections;
        globalMetadata = initMetadata;
        globalInitialized = true;

        // 设置章节数据
        setSections(realPRDContent.sections);

        console.log('✅ 加载真实BSV PRD文档:', {
          title: realPRDContent.title,
          sections: realPRDContent.sections.length
        });
      }
    } else {
      // 使用默认内容或保存的内容
      const savedContent = loadContent();
      const savedMetadata = getMetadata();

      initContent = savedContent || initialContent;
      initOutline = generateOutline(initContent);
      initMetadata = savedMetadata;

      // 如果没有章节数据，设置为空数组
      setSections([]);
    }

    setContent(initContent);
    setOutline(initOutline);
    setMetadata(initMetadata);

    if (initMetadata && initMetadata.lastSaved) {
      setLastSaved(new Date(initMetadata.lastSaved));
    }

    // 标记为已初始化
    setIsInitialized(true);
    console.log('✅ 内容初始化完成');
  }, [initialContent, useRealPRD, isInitialized]);

  // 记录编辑历史
  const recordHistory = useCallback((action, data) => {
    const historyEntry = {
      action,
      data,
      timestamp: Date.now(),
      sections: [...sections]
    };

    const newHistory = editHistory.slice(0, currentVersion + 1);
    newHistory.push(historyEntry);
    setEditHistory(newHistory);
    setCurrentVersion(newHistory.length - 1);
  }, [sections, editHistory, currentVersion]);

  // 同步更新内容和大纲
  const syncContentAndOutline = useCallback((updatedSections) => {
    const newMarkdown = generateMarkdownFromSections(updatedSections);
    const newOutline = generateOutlineFromSections(updatedSections);

    setContent(newMarkdown);
    setOutline(newOutline);
    setIsModified(true);
  }, []);

  // 更新内容
  const updateContent = useCallback((newContent) => {
    console.log('🔄 updateContent 被调用，内容长度:', newContent?.length || 0);

    setContent(newContent);
    setOutline(generateOutline(newContent));

    // 只有在非真实PRD模式下才同步更新章节数据
    // 真实PRD模式下，章节数据由初始化时设置，不需要重新解析
    if (!useRealPRD) {
      try {
        const { parseMarkdownToSections } = require('../utils/contentSync');
        const updatedSections = parseMarkdownToSections(newContent, sections);
        setSections(updatedSections);
        console.log('📝 章节数据已同步更新，章节数:', updatedSections.length);
      } catch (e) {
        console.warn('Failed to sync sections from markdown:', e);
      }
    } else {
      console.log('📝 真实PRD模式，跳过章节数据同步');
    }

    setIsModified(true);
  }, [sections, useRealPRD]);

  // 手动保存内容
  const saveContentManually = useCallback(async () => {
    try {
      const newMetadata = await saveContent(content);
      setIsModified(false);
      setLastSaved(new Date());
      setMetadata(newMetadata);

      // 保存后确保章节数据和大纲同步
      try {
        const { parseMarkdownToSections, generateOutlineFromSections } = require('../utils/contentSync');
        const updatedSections = parseMarkdownToSections(content, sections);
        setSections(updatedSections);

        // 更新大纲
        const newOutline = generateOutlineFromSections(updatedSections);
        setOutline(newOutline);
      } catch (e) {
        console.warn('Failed to sync sections after save:', e);
      }

      return { success: true, metadata: newMetadata };
    } catch (error) {
      console.error('保存失败:', error);
      return { success: false, error: error.message };
    }
  }, [content, sections]);

  // 重置内容
  const resetContent = useCallback((newContent) => {
    setContent(newContent);
    setOutline(generateOutline(newContent));
    setIsModified(false);
    setLastSaved(null);
    setMetadata(null);
    setSections([]);
    setEditHistory([]);
    setCurrentVersion(0);
  }, []);

  // ==================== 章节操作方法 ====================

  // 新增章节
  const addSection = useCallback((newSection, position = 'end') => {
    const updatedSections = addSectionToArray(sections, newSection, position);
    setSections(updatedSections);
    syncContentAndOutline(updatedSections);
    recordHistory('add_section', { section: newSection, position });
  }, [sections, syncContentAndOutline, recordHistory]);

  // 删除章节
  const deleteSection = useCallback((sectionId) => {
    const updatedSections = deleteSectionFromArray(sections, sectionId);
    setSections(updatedSections);
    syncContentAndOutline(updatedSections);
    recordHistory('delete_section', { sectionId });
  }, [sections, syncContentAndOutline, recordHistory]);

  // 移动章节
  const moveSection = useCallback((sectionId, direction) => {
    let updatedSections;
    if (direction === 'up') {
      updatedSections = moveSectionUp(sections, sectionId);
    } else if (direction === 'down') {
      updatedSections = moveSectionDown(sections, sectionId);
    } else {
      return; // 无效的方向
    }

    if (updatedSections !== sections) {
      setSections(updatedSections);
      syncContentAndOutline(updatedSections);
      recordHistory('move_section', { sectionId, direction });
    }
  }, [sections, syncContentAndOutline, recordHistory]);

  // 调整章节层级
  const updateSectionLevel = useCallback((sectionId, newLevel) => {
    const updatedSections = updateSectionLevelInArray(sections, sectionId, newLevel);
    setSections(updatedSections);
    syncContentAndOutline(updatedSections);
    recordHistory('update_level', { sectionId, newLevel });
  }, [sections, syncContentAndOutline, recordHistory]);

  // 复制章节
  const duplicateSection = useCallback((sectionId) => {
    const updatedSections = duplicateSectionInArray(sections, sectionId);
    setSections(updatedSections);
    syncContentAndOutline(updatedSections);
    recordHistory('duplicate_section', { sectionId });
  }, [sections, syncContentAndOutline, recordHistory]);

  // 重新排序章节
  const reorderSections = useCallback((fromIndex, toIndex) => {
    const updatedSections = reorderSectionsInArray(sections, fromIndex, toIndex);
    setSections(updatedSections);
    syncContentAndOutline(updatedSections);
    recordHistory('reorder_sections', { fromIndex, toIndex });
  }, [sections, syncContentAndOutline, recordHistory]);

  // 撤销操作
  const undoLastAction = useCallback(() => {
    if (editHistory.length > 0 && currentVersion > 0) {
      const previousVersion = currentVersion - 1;
      const previousState = editHistory[previousVersion];

      setSections(previousState.sections);
      syncContentAndOutline(previousState.sections);
      setCurrentVersion(previousVersion);
    }
  }, [editHistory, currentVersion, syncContentAndOutline]);

  // 重做操作
  const redoLastAction = useCallback(() => {
    if (currentVersion < editHistory.length - 1) {
      const nextVersion = currentVersion + 1;
      const nextState = editHistory[nextVersion];

      setSections(nextState.sections);
      syncContentAndOutline(nextState.sections);
      setCurrentVersion(nextVersion);
    }
  }, [editHistory, currentVersion, syncContentAndOutline]);

  return {
    content,
    outline,
    isModified,
    lastSaved,
    metadata,
    // 新增：章节相关状态和方法
    sections,
    editHistory,
    canUndo: currentVersion > 0,
    canRedo: currentVersion < editHistory.length - 1,
    // 原有方法
    updateContent,
    saveContentManually,
    resetContent,
    // 新增：章节操作方法
    addSection,
    deleteSection,
    moveSection,
    updateSectionLevel,
    duplicateSection,
    reorderSections,
    undoLastAction,
    redoLastAction
  };
};

export default useContentState; 