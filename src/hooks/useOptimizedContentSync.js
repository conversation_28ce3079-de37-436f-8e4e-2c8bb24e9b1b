import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { debounce, throttle, globalBatchUpdater, globalPerformanceTimer } from '../utils/performanceUtils';
import { contentCacheManager } from '../utils/cacheManager';
import { parseMarkdownToSections, generateMarkdownFromSections, generateOutlineFromSections } from '../utils/contentSync';

/**
 * 优化的内容同步Hook
 * 提供高性能的内容同步、缓存和批量更新功能
 */
export const useOptimizedContentSync = (initialContent = '', options = {}) => {
  const {
    debounceMs = 300,
    enableCache = true,
    enableBatchUpdates = true,
    maxCacheSize = 100,
    enablePerformanceTracking = false
  } = options;

  // 状态管理
  const [content, setContent] = useState(initialContent);
  const [sections, setSections] = useState([]);
  const [outline, setOutline] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastProcessedContent, setLastProcessedContent] = useState('');

  // 引用管理
  const contentRef = useRef(content);
  const sectionsRef = useRef(sections);
  const outlineRef = useRef(outline);
  const processingRef = useRef(false);

  // 缓存键生成
  const generateCacheKey = useCallback((content) => {
    // 使用内容的哈希作为缓存键
    return btoa(content).substring(0, 32);
  }, []);

  // 从缓存获取处理结果
  const getFromCache = useCallback((content) => {
    if (!enableCache) return null;
    
    const cacheKey = generateCacheKey(content);
    const cachedSections = contentCacheManager.get('sections', cacheKey);
    const cachedOutline = contentCacheManager.get('outline', cacheKey);
    
    if (cachedSections && cachedOutline) {
      return { sections: cachedSections, outline: cachedOutline };
    }
    
    return null;
  }, [enableCache, generateCacheKey]);

  // 保存到缓存
  const saveToCache = useCallback((content, sections, outline) => {
    if (!enableCache) return;
    
    const cacheKey = generateCacheKey(content);
    contentCacheManager.set('sections', cacheKey, sections);
    contentCacheManager.set('outline', cacheKey, outline);
  }, [enableCache, generateCacheKey]);

  // 处理内容变化的核心函数
  const processContentChange = useCallback(async (newContent) => {
    if (processingRef.current || newContent === lastProcessedContent) {
      return;
    }

    processingRef.current = true;
    setIsProcessing(true);

    try {
      // 性能追踪开始
      if (enablePerformanceTracking) {
        globalPerformanceTimer.start('contentSync');
      }

      // 尝试从缓存获取
      const cached = getFromCache(newContent);
      if (cached) {
        const updateFunction = () => {
          setSections(cached.sections);
          setOutline(cached.outline);
          sectionsRef.current = cached.sections;
          outlineRef.current = cached.outline;
          setLastProcessedContent(newContent);
        };

        if (enableBatchUpdates) {
          globalBatchUpdater.add(updateFunction);
        } else {
          updateFunction();
        }

        if (enablePerformanceTracking) {
          const duration = globalPerformanceTimer.end('contentSync');
          console.log(`Content sync (cached): ${duration.toFixed(2)}ms`);
        }

        return;
      }

      // 解析内容
      const newSections = parseMarkdownToSections(newContent);
      const newOutline = generateOutlineFromSections(newSections);

      // 保存到缓存
      saveToCache(newContent, newSections, newOutline);

      // 批量更新状态
      const updateFunction = () => {
        setSections(newSections);
        setOutline(newOutline);
        sectionsRef.current = newSections;
        outlineRef.current = newOutline;
        setLastProcessedContent(newContent);
      };

      if (enableBatchUpdates) {
        globalBatchUpdater.add(updateFunction);
      } else {
        updateFunction();
      }

      if (enablePerformanceTracking) {
        const duration = globalPerformanceTimer.end('contentSync');
        console.log(`Content sync (processed): ${duration.toFixed(2)}ms`);
      }

    } catch (error) {
      console.error('Content processing error:', error);
    } finally {
      processingRef.current = false;
      setIsProcessing(false);
    }
  }, [
    lastProcessedContent,
    getFromCache,
    saveToCache,
    enableBatchUpdates,
    enablePerformanceTracking
  ]);

  // 防抖的内容处理函数
  const debouncedProcessContent = useMemo(
    () => debounce(processContentChange, debounceMs),
    [processContentChange, debounceMs]
  );

  // 节流的内容处理函数（用于实时预览）
  const throttledProcessContent = useMemo(
    () => throttle(processContentChange, 100),
    [processContentChange]
  );

  // 更新内容
  const updateContent = useCallback((newContent, immediate = false) => {
    setContent(newContent);
    contentRef.current = newContent;

    if (immediate) {
      processContentChange(newContent);
    } else {
      debouncedProcessContent(newContent);
    }
  }, [processContentChange, debouncedProcessContent]);

  // 实时预览更新（节流）
  const updateContentPreview = useCallback((newContent) => {
    setContent(newContent);
    contentRef.current = newContent;
    throttledProcessContent(newContent);
  }, [throttledProcessContent]);

  // 从章节生成Markdown
  const generateMarkdownFromCurrentSections = useCallback(() => {
    try {
      const markdown = generateMarkdownFromSections(sectionsRef.current);
      updateContent(markdown, true);
      return markdown;
    } catch (error) {
      console.error('Error generating markdown from sections:', error);
      return content;
    }
  }, [content, updateContent]);

  // 更新特定章节
  const updateSection = useCallback((sectionId, updates) => {
    const newSections = sectionsRef.current.map(section => 
      section.id === sectionId ? { ...section, ...updates } : section
    );
    
    setSections(newSections);
    sectionsRef.current = newSections;
    
    // 重新生成Markdown
    const newMarkdown = generateMarkdownFromSections(newSections);
    updateContent(newMarkdown, true);
  }, [updateContent]);

  // 添加章节
  const addSection = useCallback((newSection, afterSectionId = null) => {
    let newSections;
    
    if (afterSectionId) {
      const index = sectionsRef.current.findIndex(s => s.id === afterSectionId);
      newSections = [
        ...sectionsRef.current.slice(0, index + 1),
        newSection,
        ...sectionsRef.current.slice(index + 1)
      ];
    } else {
      newSections = [...sectionsRef.current, newSection];
    }
    
    setSections(newSections);
    sectionsRef.current = newSections;
    
    // 重新生成Markdown
    const newMarkdown = generateMarkdownFromSections(newSections);
    updateContent(newMarkdown, true);
  }, [updateContent]);

  // 删除章节
  const deleteSection = useCallback((sectionId) => {
    const newSections = sectionsRef.current.filter(section => section.id !== sectionId);
    setSections(newSections);
    sectionsRef.current = newSections;
    
    // 重新生成Markdown
    const newMarkdown = generateMarkdownFromSections(newSections);
    updateContent(newMarkdown, true);
  }, [updateContent]);

  // 获取缓存统计
  const getCacheStats = useCallback(() => {
    return contentCacheManager.getStats();
  }, []);

  // 清理缓存
  const clearCache = useCallback(() => {
    contentCacheManager.clear();
  }, []);

  // 初始化处理
  useEffect(() => {
    if (initialContent && initialContent !== lastProcessedContent) {
      processContentChange(initialContent);
    }
  }, [initialContent, processContentChange, lastProcessedContent]);

  // 清理函数
  useEffect(() => {
    return () => {
      // 取消防抖函数
      debouncedProcessContent.cancel?.();
    };
  }, [debouncedProcessContent]);

  return {
    // 状态
    content,
    sections,
    outline,
    isProcessing,
    
    // 更新函数
    updateContent,
    updateContentPreview,
    updateSection,
    addSection,
    deleteSection,
    generateMarkdownFromCurrentSections,
    
    // 缓存管理
    getCacheStats,
    clearCache,
    
    // 引用（用于性能优化）
    contentRef,
    sectionsRef,
    outlineRef
  };
};

export default useOptimizedContentSync;
