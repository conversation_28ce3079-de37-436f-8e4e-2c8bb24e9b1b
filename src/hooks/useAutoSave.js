import { useEffect, useState, useRef } from 'react';
import { saveContent } from '../utils/storageUtils';

const useAutoSave = (content, isModified, enabled = true, delay = 3000) => {
  const [isSaving, setIsSaving] = useState(false);
  const [lastAutoSaved, setLastAutoSaved] = useState(null);
  const [saveError, setSaveError] = useState(null);
  const timerRef = useRef(null);

  useEffect(() => {
    // 清除错误状态
    setSaveError(null);

    if (!enabled || !isModified || !content) {
      return;
    }

    // 清除之前的计时器
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    // 设置新的自动保存计时器
    timerRef.current = setTimeout(async () => {
      setIsSaving(true);
      try {
        const metadata = await saveContent(content);
        setLastAutoSaved(new Date());
        setSaveError(null);
        console.log('内容已自动保存', metadata);
      } catch (error) {
        console.error('自动保存失败:', error);
        setSaveError(error.message);
      } finally {
        setIsSaving(false);
      }
    }, delay);

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [content, isModified, enabled, delay]);

  // 组件卸载时清理计时器
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  return { 
    isSaving, 
    lastAutoSaved, 
    saveError,
    clearError: () => setSaveError(null)
  };
};

export default useAutoSave; 