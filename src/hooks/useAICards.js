import { useState, useEffect, useCallback, useRef } from 'react';
import { agentClient, AGENT_TYPE } from '../services/agents/AgentClient';
import { mockAgentService } from '../services/agents/MockAgentService';
import { getCurrentConfig } from '../services/api/config';

// AI卡片状态管理
export const useAICards = () => {
  const [cards, setCards] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [availableAgents, setAvailableAgents] = useState([]);
  const [taskQueue, setTaskQueue] = useState([]);
  const cardIdCounter = useRef(1);
  const config = getCurrentConfig();
  
  // ============= 初始化和事件监听 =============
  
  useEffect(() => {
    const initializeAgents = async () => {
      if (config.enableMockAgent) {
        // 使用Mock Agent数据
        try {
          const mockAgents = mockAgentService.agents || [];
          setAvailableAgents(mockAgents);
          console.log('Mock Agents initialized:', mockAgents);
        } catch (error) {
          console.error('Failed to initialize mock agents:', error);
        }
      } else {
        // 使用真实Agent客户端
        const handleAgentRegistered = (agent) => {
          setAvailableAgents(prev => [...prev.filter(a => a.id !== agent.id), agent]);
        };

        const handleTaskCompleted = (task) => {
          updateCardWithResult(task.id, task.result, 'completed');
          setTaskQueue(prev => prev.filter(t => t.id !== task.id));
        };

        const handleTaskFailed = (task) => {
          updateCardWithResult(task.id, null, 'failed', task.error);
          setTaskQueue(prev => prev.filter(t => t.id !== task.id));
        };

        // 注册事件监听器
        agentClient.on('agent:registered', handleAgentRegistered);
        agentClient.on('task:completed', handleTaskCompleted);
        agentClient.on('task:failed', handleTaskFailed);

        // 初始化可用Agent列表
        setAvailableAgents(agentClient.getAvailableAgents());

        // 清理函数
        return () => {
          agentClient.off('agent:registered', handleAgentRegistered);
          agentClient.off('task:completed', handleTaskCompleted);
          agentClient.off('task:failed', handleTaskFailed);
        };
      }
    };

    initializeAgents();
  }, [config.enableMockAgent]);

  // ============= 工具方法（提前定义） =============
  
  const getAgentTypeByAnalysis = (analysisType) => {
    const mapping = {
      'evaluate': AGENT_TYPE.ANALYSIS,
      'inspect': AGENT_TYPE.ANALYSIS,
      'supplement': AGENT_TYPE.KNOWLEDGE,
      'reference': AGENT_TYPE.KNOWLEDGE,
      'examples': AGENT_TYPE.CASES,
      'cases': AGENT_TYPE.CASES,
      'inspire': AGENT_TYPE.INSPIRATION,
      'creative': AGENT_TYPE.INSPIRATION
    };
    return mapping[analysisType] || AGENT_TYPE.ANALYSIS;
  };
  
  const getCardTypeByAnalysis = (analysisType) => {
    const mapping = {
      'dialogue': 'dialogue',
      'inspire': 'inspire', 
      'evaluate': 'evaluate',
      'knowledge': 'knowledge',
      'cases': 'cases',
      // 保持向后兼容的旧映射
      'supplement': 'knowledge',
      'reference': 'knowledge',
      'examples': 'cases',
      'creative': 'inspire'
    };
    return mapping[analysisType] || 'evaluate';
  };
  

  
  const getRequiredCapabilities = (analysisType) => {
    const mapping = {
      'evaluate': ['evaluate', 'validate'],
      'inspect': ['inspect', 'validate'], 
      'supplement': ['supplement', 'reference'],
      'reference': ['reference', 'standards'],
      'examples': ['examples', 'patterns'],
      'cases': ['examples', 'best-practices'],
      'inspire': ['creative', 'brainstorm'],
      'creative': ['creative', 'innovative']
    };
    return mapping[analysisType] || [];
  };

  const getAnalysisDisplayName = (analysisType) => {
    const mapping = {
      'dialogue': '对话分析',
      'inspire': '灵感激发',
      'evaluate': '评估分析',
      'knowledge': '知识补充',
      'cases': '案例分析'
    };
    return mapping[analysisType] || 'AI分析';
  };

  const executeMockTask = async (agentType, taskConfig) => {
    const response = await mockAgentService.executeTask(agentType, taskConfig);
    return response.data;
  };
  
  // ============= 工具方法（卡片创建） =============
  
  const createPendingCard = useCallback((selectedText, analysisType, context) => {
    const cardId = `card-${Date.now()}-${cardIdCounter.current++}`;
    
    return {
      id: cardId,
      type: getCardTypeByAnalysis(analysisType),
      title: `${getAnalysisDisplayName(analysisType)}中...`,
      timestamp: new Date().toLocaleString(),
      selectedText,
      analysisType,
      context,
      status: 'loading',
      hasUnread: true,
      aiResponse: '正在分析中，请稍候...',
      tags: [analysisType],
      metadata: {
        agentType: getAgentTypeByAnalysis(analysisType),
        requestId: cardId,
        startTime: Date.now()
      }
    };
  }, []);
  
  const createErrorCard = useCallback((selectedText, analysisType, errorMessage) => {
    return {
      id: `error-${Date.now()}-${cardIdCounter.current++}`,
      type: 'error',
      title: `${getAnalysisDisplayName(analysisType)}失败`,
      timestamp: new Date().toLocaleString(),
      selectedText,
      analysisType,
      status: 'failed',
      hasUnread: true,
      aiResponse: `分析失败：${errorMessage}`,
      tags: ['错误', analysisType],
      metadata: {
        errorType: 'analysis_failed',
        errorMessage
      }
    };
  }, []);
  
  const createCollaborationCard = useCallback((selectedText, agentCount) => {
    return {
      id: `collab-${Date.now()}-${cardIdCounter.current++}`,
      type: 'collaboration',
      title: `多Agent协同分析中...`,
      timestamp: new Date().toLocaleString(),
      selectedText,
      analysisType: 'multi-agent',
      status: 'loading',
      hasUnread: true,
      aiResponse: `正在协调${agentCount}个专业Agent进行深度分析...`,
      tags: ['多Agent', '协同分析'],
      metadata: {
        collaborationType: 'parallel',
        agentCount,
        startTime: Date.now()
      }
    };
  }, []);
  
  const updateCardContent = (cardId, result, status) => {
    setCards(prev => prev.map(card => 
      card.id === cardId 
        ? {
            ...card,
            title: result.title || card.title,
            aiResponse: result.content || result.message || card.aiResponse,
            status,
            hasUnread: true,
            tags: result.tags || card.tags,
            metadata: {
              ...card.metadata,
              ...result.metadata,
              confidence: result.confidence,
              completedAt: Date.now(),
              processingTime: Date.now() - card.metadata.startTime
            }
          }
        : card
    ));
  };
  
  const updateCardWithResult = (taskId, result, status, error = null) => {
    setCards(prev => prev.map(card => {
      if (card.metadata?.requestId === taskId || card.id === taskId) {
        return {
          ...card,
          title: result?.title || (error ? `分析失败` : card.title),
          aiResponse: result?.content || error || card.aiResponse,
          status,
          hasUnread: true,
          metadata: {
            ...card.metadata,
            confidence: result?.confidence,
            completedAt: Date.now(),
            ...(error && { errorMessage: error })
          }
        };
      }
      return card;
    }));
  };
  
  // ============= 核心AI交互功能 =============
  
  const handleAIInteraction = useCallback(async (selectedText, analysisType, context = {}) => {
    console.log('🎯 useAICards.handleAIInteraction 被调用');
    console.log('📝 选中文本:', selectedText);
    console.log('🔍 分析类型:', analysisType);
    console.log('📊 当前卡片数量:', cards.length);

    if (!selectedText?.trim()) {
      console.warn('未选择有效文本');
      return;
    }

    setIsLoading(true);

    try {
      // 创建新卡片（预创建，显示加载状态）
      const newCard = createPendingCard(selectedText, analysisType, context);
      console.log('🆕 创建新卡片:', newCard);
      setCards(prev => {
        const newCards = [newCard, ...prev];
        console.log('📊 更新后卡片数量:', newCards.length);
        return newCards;
      });
      
      // 根据分析类型确定Agent类型
      const agentType = getAgentTypeByAnalysis(analysisType);
      
      // 构建任务配置
      const taskConfig = {
        type: agentType,
        input: {
          selectedText,
          context: context.documentContext || ''
        },
        context: {
          analysisType,
          documentId: context.documentId,
          timestamp: Date.now(),
          requiredCapabilities: getRequiredCapabilities(analysisType)
        },
        priority: context.priority || 1,
        timeout: 45000 // 45秒超时
      };
      
      // 根据环境决定使用真实Agent还是Mock服务
      let result;
      if (config.enableMockAgent) {
        result = await executeMockTask(agentType, taskConfig);
      } else {
        result = await agentClient.executeTask(taskConfig);
      }
      
      // 更新卡片内容
      updateCardContent(newCard.id, result, 'completed');
      
    } catch (error) {
      console.error('AI交互失败:', error);
      // 创建错误卡片
      const errorCard = createErrorCard(selectedText, analysisType, error.message);
      setCards(prev => prev.map(card => 
        card.status === 'loading' ? errorCard : card
      ));
    } finally {
      setIsLoading(false);
    }
  }, [config.enableMockAgent, createErrorCard, createPendingCard]);
  
  // ============= 多Agent协同功能 =============
  
  const executeMultiAgentAnalysis = useCallback(async (selectedText, context = {}) => {
    setIsLoading(true);
    
    try {
      // 创建多Agent协同任务
      const tasks = [
        {
          type: AGENT_TYPE.ANALYSIS,
          input: { selectedText },
          context: { analysisType: 'evaluate', ...context }
        },
        {
          type: AGENT_TYPE.KNOWLEDGE,
          input: { selectedText },
          context: { analysisType: 'supplement', ...context }
        },
        {
          type: AGENT_TYPE.CASES,
          input: { selectedText },
          context: { analysisType: 'examples', ...context }
        }
      ];
      
      // 创建协同卡片
      const collaborationCard = createCollaborationCard(selectedText, tasks.length);
      setCards(prev => [collaborationCard, ...prev]);
      
      // 并行执行任务
      const results = await Promise.allSettled(
        tasks.map(task => 
          config.enableMockAgent 
            ? executeMockTask(task.type, task)
            : agentClient.executeTask(task)
        )
      );
      
      // 合并结果
      const combinedResult = combineMultiAgentResults(results);
      updateCardContent(collaborationCard.id, combinedResult, 'completed');
      
    } catch (error) {
      console.error('多Agent协同分析失败:', error);
      const errorCard = createErrorCard(selectedText, 'multi-agent', error.message);
      setCards(prev => prev.map(card => 
        card.status === 'loading' ? errorCard : card
      ));
    } finally {
      setIsLoading(false);
    }
  }, [config.enableMockAgent, createCollaborationCard, createErrorCard]);
  
  // ============= 卡片管理功能 =============
  
  const adoptCard = useCallback((cardId) => {
    setCards(prev => prev.map(card => 
      card.id === cardId 
        ? { ...card, status: 'adopted', adoptedAt: Date.now() }
        : card
    ));
  }, []);
  
  const ignoreCard = useCallback((cardId) => {
    setCards(prev => prev.map(card => 
      card.id === cardId 
        ? { ...card, status: 'ignored', ignoredAt: Date.now() }
        : card
    ));
  }, []);
  
  const deleteCard = useCallback((cardId) => {
    setCards(prev => prev.filter(card => card.id !== cardId));
  }, []);
  
  const markAsRead = useCallback((cardId) => {
    setCards(prev => prev.map(card =>
      card.id === cardId
        ? { ...card, hasUnread: false, readAt: Date.now() }
        : card
    ));
  }, []);

  const restoreCard = useCallback((cardId) => {
    setCards(prev => prev.map(card =>
      card.id === cardId
        ? {
            ...card,
            status: 'completed',
            hasUnread: true,
            restoredAt: Date.now(),
            // 清除采纳/忽略时间戳
            adoptedAt: undefined,
            ignoredAt: undefined
          }
        : card
    ));
  }, []);
  
  const combineMultiAgentResults = (results) => {
    const successful = results.filter(r => r.status === 'fulfilled').map(r => r.value);
    const failed = results.filter(r => r.status === 'rejected');
    
    if (successful.length === 0) {
      throw new Error('所有Agent都执行失败');
    }
    
    return {
      title: '多Agent协同分析报告',
      content: `
## 协同分析结果

本次分析由${successful.length}个专业Agent协同完成：

${successful.map((result, index) => `
### ${index + 1}. ${result.title}

${result.content}

---
`).join('')}

## 综合建议

基于多Agent的分析结果，建议综合考虑以上各个维度的建议，制定全面的优化方案。

${failed.length > 0 ? `\n⚠️ 注意：有${failed.length}个Agent执行失败，可能影响分析完整性。` : ''}
      `,
      confidence: successful.reduce((sum, r) => sum + (r.confidence || 0.8), 0) / successful.length,
      tags: ['多Agent', '协同分析', '综合报告'],
      metadata: {
        agentResults: successful,
        failedCount: failed.length,
        collaborationType: 'parallel'
      }
    };
  };
  

  
  // ============= 对话功能 =============
  
  const updateCard = useCallback((cardId, updates) => {
    setCards(prev => prev.map(card => 
      card.id === cardId ? { ...card, ...updates } : card
    ));
  }, []);

  const executeAIAnalysis = useCallback(async ({ text, type, context, cardId }) => {
    try {
      // 使用现有的Agent系统
      const agentType = getAgentTypeByAnalysis(type);
      const taskConfig = {
        text,
        type,
        context,
        requirements: getRequiredCapabilities(type)
      };

      // 根据是否有可用的真实Agent决定使用哪种方式
      if (availableAgents.length > 0) {
        // 使用真实Agent
        const result = await agentClient.executeTask(agentType, taskConfig);
        return result.content || result.message || '分析完成';
      } else {
        // 使用Mock服务
        const result = await executeMockTask(agentType, taskConfig);
        return result.content || result.message || '分析完成';
      }
    } catch (error) {
      console.error('AI分析失败:', error);
      throw new Error('AI分析服务暂时不可用，请稍后重试');
         }
   }, [availableAgents]);

  // 重新分析卡片
  const reAnalyzeCard = useCallback(async (cardId, newType) => {
    try {
      const card = cards.find(c => c.id === cardId);
      if (!card) {
        throw new Error('卡片不存在');
      }

      // 获取原始文本
      const originalText = card.selectedText || card.sourceText;
      if (!originalText) {
        throw new Error('没有原始文本可供重新分析');
      }

      // 调用AI重新分析
      const result = await executeAIAnalysis({
        text: originalText,
        type: newType,
        context: {
          previousAnalysis: card.aiResponse,
          originalType: card.analysisType,
          reAnalyze: true
        },
        cardId: cardId
      });

      // 更新卡片内容
      updateCard(cardId, {
        type: newType,
        analysisType: newType,
        aiResponse: result,
        title: getAnalysisDisplayName(newType) + '结果',
        isProcessing: false,
        hasUnread: true,
        tags: [newType, '重新分析'],
        metadata: {
          ...card.metadata,
          reAnalyzedAt: Date.now(),
          previousType: card.analysisType
        }
      });

      return result;
    } catch (error) {
      console.error('重新分析失败:', error);
      throw error;
    }
  }, [cards, executeAIAnalysis, updateCard]);

  // ============= 返回接口 =============
  
  return {
    // 状态
    cards,
    isLoading,
    availableAgents,
    taskQueue,
    
    // 核心功能
    handleAIInteraction,
    executeMultiAgentAnalysis,
    executeAIAnalysis,
    updateCard,
    reAnalyzeCard,
    
    // 卡片管理
    adoptCard,
    ignoreCard,
    deleteCard,
    markAsRead,
    restoreCard,
    
    // 统计信息
    getStats: () => ({
      totalCards: cards.length,
      unreadCards: cards.filter(c => c.hasUnread).length,
      adoptedCards: cards.filter(c => c.status === 'adopted').length,
      ignoredCards: cards.filter(c => c.status === 'ignored').length,
      runningTasks: taskQueue.length,
      availableAgentCount: availableAgents.length
    })
  };
}; 