import { useState, useCallback } from 'react';

/**
 * PRD智能评估状态管理Hook
 */
export const usePRDEvaluation = () => {
  // 页面状态管理
  const [activeTab, setActiveTab] = useState("upload");
  const [hasStartedAnalysis, setHasStartedAnalysis] = useState(false);
  const [hasCompletedAnalysis, setHasCompletedAnalysis] = useState(false);
  const [hasStartedReview, setHasStartedReview] = useState(false);
  const [hasCompletedReview, setHasCompletedReview] = useState(false);
  const [hasStartedOptimization, setHasStartedOptimization] = useState(false);
  const [hasCompletedOptimization, setHasCompletedOptimization] = useState(false);

  // 数据状态管理
  const [uploadedDocuments, setUploadedDocuments] = useState([
    {
      id: 'doc-001',
      title: 'LCA特性需求文档',
      uploadDate: '2024-01-15',
      status: 'completed',
      progress: 100,
      issues: 12,
      fixed: 10,
      fileSize: '2.3 MB',
      fileType: 'application/pdf',
      content: 'LCA特性需求文档内容...'
    },
    {
      id: 'doc-002',
      title: 'Auto Hold自动驻车特性需求文档',
      uploadDate: '2024-01-16',
      status: 'reviewing',
      progress: 85,
      issues: 8,
      fixed: 5,
      fileSize: '1.8 MB',
      fileType: 'application/pdf',
      content: 'Auto Hold自动驻车特性需求文档内容...'
    },
    {
      id: 'doc-003',
      title: 'BSV特性需求-打样版本',
      uploadDate: '2024-01-17',
      status: 'analyzing',
      progress: 45,
      issues: 0,
      fixed: 0,
      fileSize: '3.1 MB',
      fileType: 'application/pdf',
      content: 'BSV特性需求文档内容...'
    },
    {
      id: 'doc-004',
      title: 'HSA坡起辅助特性需求文档',
      uploadDate: '2024-01-18',
      status: 'analyzed',
      progress: 100,
      issues: 15,
      fixed: 0,
      fileSize: '2.7 MB',
      fileType: 'application/pdf',
      content: 'HSA坡起辅助特性需求文档内容...'
    },
    {
      id: 'doc-005',
      title: 'PRD模版-正式版',
      uploadDate: '2024-01-19',
      status: 'uploaded',
      progress: 0,
      issues: 0,
      fixed: 0,
      fileSize: '1.2 MB',
      fileType: 'application/pdf',
      content: 'PRD模版文档内容...'
    },
    {
      id: 'doc-006',
      title: '会议纪要-产品需求评审会',
      uploadDate: '2024-01-20',
      status: 'analyzing',
      progress: 25,
      issues: 0,
      fixed: 0,
      fileSize: '0.8 MB',
      fileType: 'application/msword',
      content: '会议纪要内容...'
    },
    {
      id: 'doc-007',
      title: '需求变更申请-V2.1版本',
      uploadDate: '2024-01-21',
      status: 'completed',
      progress: 100,
      issues: 6,
      fixed: 6,
      fileSize: '1.5 MB',
      fileType: 'text/markdown',
      content: '需求变更申请内容...'
    }
  ]);
  const [currentDocument, setCurrentDocument] = useState(null);
  const [analysisResults, setAnalysisResults] = useState(null);
  const [reviewResults, setReviewResults] = useState(null);
  const [optimizationData, setOptimizationData] = useState(null);

  // 流程控制函数
  const handleStartAnalysis = useCallback((document) => {
    console.log('handleStartAnalysis 被调用，文档:', document);
    setCurrentDocument(document);
    setHasStartedAnalysis(true);
    setActiveTab("analysis");
    console.log('设置 activeTab 为 analysis');

    // 更新文档状态
    setUploadedDocuments(prev =>
      prev.map(doc =>
        doc.id === document.id
          ? { ...doc, status: "analyzing", progress: 0 }
          : doc
      )
    );
  }, []);

  const handleAnalysisComplete = useCallback((results) => {
    setAnalysisResults(results);
    setHasCompletedAnalysis(true);

    // 更新文档状态
    if (currentDocument) {
      setUploadedDocuments(prev =>
        prev.map(doc =>
          doc.id === currentDocument.id
            ? {
                ...doc,
                status: "analyzed",
                progress: 100,
                issues: results.totalIssues
              }
            : doc
        )
      );
    }
  }, [currentDocument]);

  const handleEnterReview = useCallback(() => {
    console.log('handleEnterReview 被调用');
    setHasStartedReview(true);
    setActiveTab("review");
    console.log('设置 activeTab 为 review');

    // 更新文档状态
    if (currentDocument) {
      setUploadedDocuments(prev =>
        prev.map(doc =>
          doc.id === currentDocument.id
            ? { ...doc, status: "reviewing" }
            : doc
        )
      );
      console.log('更新文档状态为 reviewing');
    }
  }, [currentDocument]);

  const handleReviewComplete = useCallback((results) => {
    console.log('handleReviewComplete 被调用，结果:', results);
    setReviewResults(results);
    setHasCompletedReview(true);
    setActiveTab("results");
    console.log('设置 activeTab 为 results');

    // 更新文档状态
    if (currentDocument) {
      setUploadedDocuments(prev =>
        prev.map(doc =>
          doc.id === currentDocument.id
            ? {
                ...doc,
                status: "completed",
                progress: 100,
                fixed: results.acceptedSuggestions
              }
            : doc
        )
      );
      console.log('更新文档状态为 completed');
    }
  }, [currentDocument]);

  const handleStartOptimization = useCallback(() => {
    console.log('handleStartOptimization 被调用');
    setHasStartedOptimization(true);
    setActiveTab("optimization");
    console.log('设置 activeTab 为 optimization');
  }, []);

  const handleCompleteOptimization = useCallback(() => {
    console.log('handleCompleteOptimization 被调用');
    setHasCompletedOptimization(true);
    console.log('自检优化流程完成');
  }, []);

  // 重置状态
  const resetEvaluationState = useCallback(() => {
    setActiveTab("upload");
    setHasStartedAnalysis(false);
    setHasCompletedAnalysis(false);
    setHasStartedReview(false);
    setHasCompletedReview(false);
    setHasStartedOptimization(false);
    setHasCompletedOptimization(false);
    setCurrentDocument(null);
    setAnalysisResults(null);
    setReviewResults(null);
    setOptimizationData(null);
  }, []);

  // 快速测试模式 - 直接跳到评审结果页面
  const enableTestMode = useCallback(() => {
    const testDocument = {
      id: 'doc-003',
      title: 'BSV特性需求文档',
      uploadDate: '2024-01-17',
      status: 'completed',
      progress: 100,
      issues: 10,
      fixed: 8,
      fileSize: '3.1 MB',
      fileType: 'application/pdf',
      content: 'BSV特性需求文档内容...'
    };

    const testAnalysisResults = {
      totalIssues: 10,
      criticalIssues: 1,
      warningIssues: 4,
      suggestionIssues: 5,
      overallScore: 69
    };

    const testReviewResults = {
      reviewedIssues: 10,
      acceptedSuggestions: 8,
      rejectedSuggestions: 2
    };

    setCurrentDocument(testDocument);
    setAnalysisResults(testAnalysisResults);
    setReviewResults(testReviewResults);
    setHasStartedAnalysis(true);
    setHasCompletedAnalysis(true);
    setHasStartedReview(true);
    setHasCompletedReview(true);
    setActiveTab("results");

    console.log('测试模式已启用，直接跳转到评审结果页面');
  }, []);

  return {
    // 状态
    activeTab,
    hasStartedAnalysis,
    hasCompletedAnalysis,
    hasStartedReview,
    hasCompletedReview,
    hasStartedOptimization,
    hasCompletedOptimization,
    uploadedDocuments,
    currentDocument,
    analysisResults,
    reviewResults,
    optimizationData,

    // 操作函数
    setActiveTab,
    setUploadedDocuments,
    handleStartAnalysis,
    handleAnalysisComplete,
    handleEnterReview,
    handleReviewComplete,
    handleStartOptimization,
    handleCompleteOptimization,
    resetEvaluationState,
    enableTestMode
  };
};
