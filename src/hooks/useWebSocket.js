import { useState, useEffect, useRef, useCallback } from 'react';
import { logger } from '../utils/logger';

export const useWebSocket = (url, options = {}) => {
  const {
    autoConnect = true,
    reconnectAttempts = 5,
    reconnectInterval = 3000,
    heartbeatInterval = 30000,
    onConnect,
    onDisconnect,
    onMessage,
    onError,
    onReconnect
  } = options;

  const [connectionState, setConnectionState] = useState('disconnected');
  const [lastMessage, setLastMessage] = useState(null);
  const [error, setError] = useState(null);
  
  const ws = useRef(null);
  const reconnectCount = useRef(0);
  const heartbeatTimer = useRef(null);
  const reconnectTimer = useRef(null);
  const messageQueue = useRef([]);

  // 创建WebSocket连接
  const connect = useCallback(() => {
    if (ws.current?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      setConnectionState('connecting');
      setError(null);
      
      ws.current = new WebSocket(url);

      ws.current.onopen = (event) => {
        logger.info('WebSocket connected', { url });
        setConnectionState('connected');
        reconnectCount.current = 0;
        
        // 发送队列中的消息
        if (messageQueue.current.length > 0) {
          messageQueue.current.forEach(message => {
            ws.current.send(JSON.stringify(message));
          });
          messageQueue.current = [];
        }
        
        // 启动心跳
        startHeartbeat();
        
        onConnect?.(event);
      };

      ws.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          setLastMessage(data);
          
          // 处理心跳响应
          if (data.type === 'pong') {
            logger.debug('Heartbeat pong received');
            return;
          }
          
          onMessage?.(data);
        } catch (error) {
          logger.warn('Failed to parse WebSocket message', { 
            message: event.data,
            error: error.message 
          });
        }
      };

      ws.current.onclose = (event) => {
        logger.info('WebSocket disconnected', { 
          code: event.code, 
          reason: event.reason,
          wasClean: event.wasClean 
        });
        
        setConnectionState('disconnected');
        stopHeartbeat();
        
        onDisconnect?.(event);
        
        // 自动重连
        if (!event.wasClean && reconnectCount.current < reconnectAttempts) {
          scheduleReconnect();
        }
      };

      ws.current.onerror = (event) => {
        const errorMsg = 'WebSocket连接错误';
        logger.error(errorMsg, { url, event });
        setError(errorMsg);
        onError?.(event);
      };

    } catch (error) {
      const errorMsg = `WebSocket连接失败: ${error.message}`;
      logger.error(errorMsg, { url, error });
      setError(errorMsg);
      setConnectionState('disconnected');
    }
  }, [url, reconnectAttempts, onConnect, onMessage, onDisconnect, onError]);

  // 断开连接
  const disconnect = useCallback(() => {
    if (ws.current) {
      ws.current.close(1000, 'Manual disconnect');
      ws.current = null;
    }
    stopHeartbeat();
    clearReconnectTimer();
    setConnectionState('disconnected');
  }, []);

  // 发送消息
  const sendMessage = useCallback((message) => {
    if (ws.current?.readyState === WebSocket.OPEN) {
      try {
        const payload = typeof message === 'string' ? message : JSON.stringify(message);
        ws.current.send(payload);
        logger.debug('WebSocket message sent', { message });
        return true;
      } catch (error) {
        logger.error('Failed to send WebSocket message', { message, error });
        return false;
      }
    } else {
      // 连接未建立时，将消息加入队列
      if (typeof message === 'object') {
        messageQueue.current.push(message);
        logger.debug('Message queued for sending', { message });
      }
      return false;
    }
  }, []);

  // 启动心跳
  const startHeartbeat = useCallback(() => {
    if (heartbeatInterval > 0) {
      heartbeatTimer.current = setInterval(() => {
        if (ws.current?.readyState === WebSocket.OPEN) {
          sendMessage({ type: 'ping', timestamp: Date.now() });
        }
      }, heartbeatInterval);
    }
  }, [heartbeatInterval, sendMessage]);

  // 停止心跳
  const stopHeartbeat = useCallback(() => {
    if (heartbeatTimer.current) {
      clearInterval(heartbeatTimer.current);
      heartbeatTimer.current = null;
    }
  }, []);

  // 计划重连
  const scheduleReconnect = useCallback(() => {
    if (reconnectCount.current < reconnectAttempts) {
      reconnectCount.current += 1;
      
      logger.info('Scheduling WebSocket reconnection', {
        attempt: reconnectCount.current,
        maxAttempts: reconnectAttempts,
        delay: reconnectInterval
      });
      
      setConnectionState('reconnecting');
      
      reconnectTimer.current = setTimeout(() => {
        connect();
        onReconnect?.(reconnectCount.current);
      }, reconnectInterval);
    } else {
      logger.warn('Max reconnection attempts reached', {
        maxAttempts: reconnectAttempts
      });
      setError('连接失败，已达到最大重试次数');
      setConnectionState('failed');
    }
  }, [reconnectAttempts, reconnectInterval, connect, onReconnect]);

  // 清理重连定时器
  const clearReconnectTimer = useCallback(() => {
    if (reconnectTimer.current) {
      clearTimeout(reconnectTimer.current);
      reconnectTimer.current = null;
    }
  }, []);

  // 手动重连
  const reconnect = useCallback(() => {
    disconnect();
    reconnectCount.current = 0;
    setTimeout(connect, 100);
  }, [disconnect, connect]);

  // 获取连接状态
  const getReadyState = useCallback(() => {
    return ws.current?.readyState || WebSocket.CLOSED;
  }, []);

  // 订阅特定事件
  const subscribe = useCallback((eventType, callback) => {
    const message = {
      type: 'subscribe',
      eventType,
      timestamp: Date.now()
    };
    
    sendMessage(message);
    
    // 返回取消订阅函数
    return () => {
      const unsubMessage = {
        type: 'unsubscribe',
        eventType,
        timestamp: Date.now()
      };
      sendMessage(unsubMessage);
    };
  }, [sendMessage]);

  // 组件挂载时自动连接
  useEffect(() => {
    if (autoConnect) {
      connect();
    }
    
    return () => {
      disconnect();
    };
  }, [autoConnect, connect, disconnect]);

  // 清理定时器
  useEffect(() => {
    return () => {
      stopHeartbeat();
      clearReconnectTimer();
    };
  }, [stopHeartbeat, clearReconnectTimer]);

  return {
    // 状态
    connectionState,
    lastMessage,
    error,
    isConnected: connectionState === 'connected',
    isConnecting: connectionState === 'connecting',
    isReconnecting: connectionState === 'reconnecting',
    
    // 方法
    connect,
    disconnect,
    reconnect,
    sendMessage,
    subscribe,
    getReadyState,
    
    // 统计信息
    reconnectAttempts: reconnectCount.current,
    maxReconnectAttempts: reconnectAttempts,
    queuedMessages: messageQueue.current.length
  };
}; 