import React from 'react';
import { globalErrorHandler } from '../utils/errorHandler';

/**
 * React Hook: 错误处理
 * 提供错误状态管理和错误处理功能
 */
export const useErrorHandler = () => {
  const [errors, setErrors] = React.useState([]);

  const addError = React.useCallback((error) => {
    const normalizedError = globalErrorHandler.normalizeError(error);
    setErrors(prev => [...prev, normalizedError]);
  }, []);

  const removeError = React.useCallback((errorId) => {
    setErrors(prev => prev.filter(error => error.id !== errorId));
  }, []);

  const clearErrors = React.useCallback(() => {
    setErrors([]);
  }, []);

  const handleError = React.useCallback((error) => {
    addError(error);
    globalErrorHandler.handleError(error);
  }, [addError]);

  return {
    errors,
    addError,
    removeError,
    clearErrors,
    handleError
  };
};

/**
 * React Hook: 异步操作错误处理
 * 专门用于处理异步操作中的错误
 */
export const useAsyncErrorHandler = () => {
  const { handleError } = useErrorHandler();
  
  const executeAsync = React.useCallback(async (asyncFunction, options = {}) => {
    const { 
      onSuccess, 
      onError, 
      showError = true,
      retries = 0 
    } = options;
    
    let lastError;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const result = await asyncFunction();
        if (onSuccess) {
          onSuccess(result);
        }
        return result;
      } catch (error) {
        lastError = error;
        
        if (attempt < retries) {
          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, attempt)));
          continue;
        }
        
        // 最后一次尝试失败
        if (showError) {
          handleError(error);
        }
        
        if (onError) {
          onError(error);
        }
        
        throw error;
      }
    }
  }, [handleError]);

  return { executeAsync };
};

/**
 * React Hook: 网络请求错误处理
 * 专门用于处理网络请求错误
 */
export const useNetworkErrorHandler = () => {
  const { handleError } = useErrorHandler();
  
  const handleNetworkRequest = React.useCallback(async (requestFunction, options = {}) => {
    const {
      retries = 3,
      timeout = 10000,
      onRetry,
      onSuccess,
      onError
    } = options;
    
    let lastError;
    
    for (let attempt = 0; attempt < retries; attempt++) {
      try {
        // 设置超时
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout')), timeout);
        });
        
        const result = await Promise.race([
          requestFunction(),
          timeoutPromise
        ]);
        
        if (onSuccess) {
          onSuccess(result);
        }
        
        return result;
      } catch (error) {
        lastError = error;
        
        if (attempt < retries - 1) {
          if (onRetry) {
            onRetry(attempt + 1, error);
          }
          
          // 指数退避
          await new Promise(resolve => 
            setTimeout(resolve, 1000 * Math.pow(2, attempt))
          );
          continue;
        }
        
        // 最后一次尝试失败
        handleError(error);
        
        if (onError) {
          onError(error);
        }
        
        throw error;
      }
    }
  }, [handleError]);

  return { handleNetworkRequest };
};

export default useErrorHandler;
