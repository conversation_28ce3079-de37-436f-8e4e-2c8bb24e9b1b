<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速访问 - 表格渲染测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            line-height: 1.6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            color: #333;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .header p {
            font-size: 1.2em;
            color: #666;
        }
        
        .quick-links {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .link-card {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 24px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            display: block;
        }
        
        .link-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
        }
        
        .link-card h3 {
            margin: 0 0 10px 0;
            font-size: 1.3em;
            color: #333;
        }
        
        .link-card p {
            margin: 0;
            color: #666;
            font-size: 0.95em;
        }
        
        .link-card .icon {
            font-size: 2em;
            margin-bottom: 10px;
            display: block;
        }
        
        .status-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 30px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px 0;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .status-success { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
        .status-unknown { background: #6c757d; }
        
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 30px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #0066cc;
        }
        
        .instructions ol {
            margin: 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .footer {
            text-align: center;
            color: #666;
            font-size: 0.9em;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }
        
        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-left: 10px;
        }
        
        .refresh-btn:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 表格渲染测试工具</h1>
            <p>快速访问和诊断Milkdown编辑器表格渲染问题</p>
        </div>

        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li><strong>确保React应用正在运行</strong>：点击下面的链接访问应用</li>
                <li><strong>导航到测试页面</strong>：在左侧菜单找到"调试工具" → "表格渲染测试"</li>
                <li><strong>运行诊断</strong>：使用测试工具检查编辑器状态</li>
                <li><strong>测试表格粘贴</strong>：在编辑器中粘贴表格Markdown语法</li>
                <li><strong>查看修复效果</strong>：观察表格是否正确渲染</li>
            </ol>
        </div>

        <div class="status-section">
            <h3>🔍 服务状态检查</h3>
            <div class="status-item">
                <span class="status-indicator" id="react-status"></span>
                <span>React开发服务器</span>
                <button class="refresh-btn" onclick="checkReactServer()">检查</button>
            </div>
            <div class="status-item">
                <span class="status-indicator" id="editor-status"></span>
                <span>编辑器调试函数</span>
                <button class="refresh-btn" onclick="checkEditorFunctions()">检查</button>
            </div>
        </div>

        <div class="quick-links">
            <a href="http://localhost:3812" class="link-card" target="_blank">
                <span class="icon">🚀</span>
                <h3>打开React应用</h3>
                <p>访问主应用，然后导航到"调试工具" → "表格渲染测试"</p>
            </a>

            <a href="http://localhost:3812/table-test" class="link-card" target="_blank">
                <span class="icon">📊</span>
                <h3>直接访问测试页面</h3>
                <p>直接跳转到表格渲染测试工具（支持上下滚动）</p>
            </a>

            <a href="javascript:void(0)" class="link-card" onclick="openConsoleInstructions()">
                <span class="icon">🔍</span>
                <h3>控制台调试指南</h3>
                <p>查看如何在浏览器控制台中手动运行调试命令</p>
            </a>

            <a href="javascript:void(0)" class="link-card" onclick="showTestTable()">
                <span class="icon">📋</span>
                <h3>获取测试表格</h3>
                <p>复制测试用的Markdown表格语法到剪贴板</p>
            </a>
        </div>

        <div class="footer">
            <p>💡 提示：如果遇到问题，请确保React开发服务器正在运行，并且浏览器控制台已打开以查看详细日志。</p>
        </div>
    </div>

    <script>
        // 检查React服务器状态
        async function checkReactServer() {
            const indicator = document.getElementById('react-status');
            
            try {
                const response = await fetch('http://localhost:3812', { 
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                indicator.className = 'status-indicator status-success';
                console.log('React服务器运行正常');
            } catch (e) {
                indicator.className = 'status-indicator status-error';
                console.log('React服务器无法访问:', e);
            }
        }

        // 检查编辑器调试函数
        function checkEditorFunctions() {
            const indicator = document.getElementById('editor-status');
            
            // 尝试在新窗口中检查
            const testWindow = window.open('http://localhost:3812', '_blank');
            
            setTimeout(() => {
                try {
                    if (testWindow && typeof testWindow.debugEditor === 'function') {
                        indicator.className = 'status-indicator status-success';
                        console.log('编辑器调试函数可用');
                    } else {
                        indicator.className = 'status-indicator status-warning';
                        console.log('编辑器调试函数暂不可用，可能需要等待页面完全加载');
                    }
                } catch (e) {
                    indicator.className = 'status-indicator status-warning';
                    console.log('无法检查编辑器函数，可能是跨域限制');
                }
            }, 2000);
        }

        // 显示控制台调试指南
        function openConsoleInstructions() {
            const instructions = `
🔍 浏览器控制台调试指南

1. 打开React应用：http://localhost:3812
2. 按F12打开开发者工具，切换到Console标签
3. 等待页面完全加载后，运行以下命令：

基础检查：
window.debugEditor()                    // 检查编辑器状态
window.checkMilkdownTableSupport()      // 检查表格支持

测试渲染：
window.testMarkdownRendering()          // 测试表格渲染

强制修复：
window.forceMarkdownReparse()           // 强制重新解析
window.forceTableRender()               // 强制表格渲染
window.recreateEditor()                 // 重建编辑器

4. 在编辑器中粘贴测试表格：
| 姓名 | 年龄 | 城市 |
|------|------|------|
| 张三 | 25 | 北京 |
| 李四 | 30 | 上海 |

5. 观察表格是否正确渲染为HTML表格
            `;
            
            alert(instructions);
            console.log(instructions);
        }

        // 显示测试表格
        function showTestTable() {
            const testTable = `| 姓名 | 年龄 | 城市 |
|------|------|------|
| 张三 | 25 | 北京 |
| 李四 | 30 | 上海 |`;

            // 尝试复制到剪贴板
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(testTable).then(() => {
                    alert('✅ 测试表格已复制到剪贴板！\n\n请在编辑器中粘贴测试。');
                }).catch(e => {
                    showTableInAlert(testTable);
                });
            } else {
                showTableInAlert(testTable);
            }
        }

        function showTableInAlert(table) {
            alert(`📋 请手动复制以下测试表格：\n\n${table}\n\n然后在编辑器中粘贴测试。`);
        }

        // 页面加载时自动检查状态
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkReactServer();
            }, 1000);
            
            // 设置初始状态
            document.getElementById('react-status').className = 'status-indicator status-unknown';
            document.getElementById('editor-status').className = 'status-indicator status-unknown';
        });
    </script>
</body>
</html>
