<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格渲染测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .test-description {
            color: #666;
            margin-bottom: 15px;
        }
        
        .test-content {
            background: white;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #ddd;
            font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        
        .button-group {
            margin: 15px 0;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .test-button.secondary {
            background: #6c757d;
        }
        
        .test-button.secondary:hover {
            background: #545b62;
        }
        
        .console-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success {
            background: #28a745;
        }
        
        .status-warning {
            background: #ffc107;
        }
        
        .status-error {
            background: #dc3545;
        }
        
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #0066cc;
        }
    </style>
</head>
<body>
    <h1>🔧 Milkdown表格渲染问题诊断工具</h1>
    
    <div class="instructions">
        <h3>📋 使用说明</h3>
        <p>这个页面用于诊断和测试Milkdown编辑器的表格渲染问题。请按照以下步骤操作：</p>
        <ol>
            <li><strong>打开编辑器页面</strong>：确保您的编辑器页面已经加载</li>
            <li><strong>运行诊断</strong>：点击下面的按钮运行各种诊断测试</li>
            <li><strong>查看结果</strong>：观察控制台输出和状态指示器</li>
            <li><strong>尝试修复</strong>：如果发现问题，使用修复按钮尝试解决</li>
        </ol>
        <p><strong>注意</strong>：请确保浏览器控制台已打开，以便查看详细的调试信息。</p>
    </div>

    <div class="test-section">
        <div class="test-title">
            <span class="status-indicator" id="editor-status"></span>
            1. 编辑器状态检查
        </div>
        <div class="test-description">
            检查编辑器是否正确初始化，以及基本状态信息。
        </div>
        <div class="button-group">
            <button class="test-button" onclick="checkEditorStatus()">检查编辑器状态</button>
            <button class="test-button secondary" onclick="getEditorInfo()">获取详细信息</button>
        </div>
        <div id="editor-info" class="console-output" style="display: none;"></div>
    </div>

    <div class="test-section">
        <div class="test-title">
            <span class="status-indicator" id="table-support-status"></span>
            2. 表格支持检查
        </div>
        <div class="test-description">
            检查Milkdown是否正确加载了GFM插件和表格支持。
        </div>
        <div class="button-group">
            <button class="test-button" onclick="checkTableSupport()">检查表格支持</button>
            <button class="test-button secondary" onclick="listSchemaNodes()">查看Schema节点</button>
        </div>
        <div id="table-support-info" class="console-output" style="display: none;"></div>
    </div>

    <div class="test-section">
        <div class="test-title">
            <span class="status-indicator" id="rendering-status"></span>
            3. 表格渲染测试
        </div>
        <div class="test-description">
            测试表格Markdown语法的实时渲染功能。
        </div>
        <div class="test-content" id="test-markdown">| 姓名 | 年龄 | 城市 |
|------|------|------|
| 张三 | 25 | 北京 |
| 李四 | 30 | 上海 |</div>
        <div class="button-group">
            <button class="test-button" onclick="testTableRendering()">测试表格渲染</button>
            <button class="test-button secondary" onclick="insertTestTable()">插入测试表格</button>
        </div>
        <div id="rendering-info" class="console-output" style="display: none;"></div>
    </div>

    <div class="test-section">
        <div class="test-title">
            <span class="status-indicator" id="paste-status"></span>
            4. 粘贴事件测试
        </div>
        <div class="test-description">
            测试粘贴表格Markdown语法时的处理逻辑。
        </div>
        <div class="button-group">
            <button class="test-button" onclick="simulatePaste()">模拟粘贴事件</button>
            <button class="test-button secondary" onclick="checkPasteHandlers()">检查粘贴处理器</button>
        </div>
        <div id="paste-info" class="console-output" style="display: none;"></div>
    </div>

    <div class="test-section">
        <div class="test-title">
            <span class="status-indicator" id="fix-status"></span>
            5. 修复工具
        </div>
        <div class="test-description">
            如果发现问题，可以尝试使用这些修复工具。
        </div>
        <div class="button-group">
            <button class="test-button" onclick="forceReparse()">强制重新解析</button>
            <button class="test-button" onclick="forceTableRender()">强制表格渲染</button>
            <button class="test-button" onclick="recreateEditor()">重建编辑器</button>
        </div>
        <div id="fix-info" class="console-output" style="display: none;"></div>
    </div>

    <script>
        // 工具函数
        function updateStatus(elementId, status) {
            const element = document.getElementById(elementId);
            element.className = `status-indicator status-${status}`;
        }

        function showOutput(elementId, content) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.textContent = content;
        }

        function log(message) {
            console.log(`[测试工具] ${message}`);
        }

        // 1. 编辑器状态检查
        function checkEditorStatus() {
            log('开始检查编辑器状态...');
            
            try {
                if (typeof window.debugEditor === 'function') {
                    const result = window.debugEditor();
                    updateStatus('editor-status', 'success');
                    showOutput('editor-info', JSON.stringify(result, null, 2));
                    log('编辑器状态检查完成');
                } else {
                    updateStatus('editor-status', 'error');
                    showOutput('editor-info', '错误：找不到debugEditor函数，请确保编辑器页面已加载');
                    log('错误：debugEditor函数不可用');
                }
            } catch (e) {
                updateStatus('editor-status', 'error');
                showOutput('editor-info', `错误：${e.message}`);
                log(`编辑器状态检查失败：${e.message}`);
            }
        }

        function getEditorInfo() {
            log('获取编辑器详细信息...');
            
            const info = {
                windowFunctions: {
                    debugEditor: typeof window.debugEditor,
                    forceTableRender: typeof window.forceTableRender,
                    forceMarkdownReparse: typeof window.forceMarkdownReparse,
                    recreateEditor: typeof window.recreateEditor,
                    testMarkdownRendering: typeof window.testMarkdownRendering,
                    checkMilkdownTableSupport: typeof window.checkMilkdownTableSupport
                },
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString()
            };
            
            showOutput('editor-info', JSON.stringify(info, null, 2));
        }

        // 2. 表格支持检查
        function checkTableSupport() {
            log('检查表格支持...');
            
            try {
                if (typeof window.checkMilkdownTableSupport === 'function') {
                    const result = window.checkMilkdownTableSupport();
                    updateStatus('table-support-status', 'success');
                    showOutput('table-support-info', '表格支持检查完成，请查看控制台输出');
                    log('表格支持检查完成');
                } else {
                    updateStatus('table-support-status', 'error');
                    showOutput('table-support-info', '错误：找不到checkMilkdownTableSupport函数');
                    log('错误：checkMilkdownTableSupport函数不可用');
                }
            } catch (e) {
                updateStatus('table-support-status', 'error');
                showOutput('table-support-info', `错误：${e.message}`);
                log(`表格支持检查失败：${e.message}`);
            }
        }

        function listSchemaNodes() {
            log('列出Schema节点...');
            // 这个功能包含在checkTableSupport中
            checkTableSupport();
        }

        // 3. 表格渲染测试
        function testTableRendering() {
            log('测试表格渲染...');
            
            try {
                if (typeof window.testMarkdownRendering === 'function') {
                    window.testMarkdownRendering();
                    updateStatus('rendering-status', 'success');
                    showOutput('rendering-info', '表格渲染测试已启动，请查看编辑器和控制台输出');
                    log('表格渲染测试完成');
                } else {
                    updateStatus('rendering-status', 'error');
                    showOutput('rendering-info', '错误：找不到testMarkdownRendering函数');
                    log('错误：testMarkdownRendering函数不可用');
                }
            } catch (e) {
                updateStatus('rendering-status', 'error');
                showOutput('rendering-info', `错误：${e.message}`);
                log(`表格渲染测试失败：${e.message}`);
            }
        }

        function insertTestTable() {
            log('插入测试表格...');
            
            const testTable = document.getElementById('test-markdown').textContent;
            
            try {
                // 尝试通过剪贴板API插入
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    navigator.clipboard.writeText(testTable).then(() => {
                        showOutput('rendering-info', '测试表格已复制到剪贴板，请在编辑器中粘贴');
                        updateStatus('rendering-status', 'warning');
                    });
                } else {
                    showOutput('rendering-info', '请手动复制以下内容到编辑器：\n' + testTable);
                    updateStatus('rendering-status', 'warning');
                }
            } catch (e) {
                showOutput('rendering-info', `复制失败：${e.message}\n请手动复制：\n${testTable}`);
                updateStatus('rendering-status', 'warning');
            }
        }

        // 4. 粘贴事件测试
        function simulatePaste() {
            log('模拟粘贴事件...');
            
            const testTable = document.getElementById('test-markdown').textContent;
            
            try {
                // 创建模拟粘贴事件
                const pasteEvent = new ClipboardEvent('paste', {
                    clipboardData: new DataTransfer()
                });
                
                // 设置剪贴板数据
                pasteEvent.clipboardData.setData('text/plain', testTable);
                
                // 尝试在编辑器上触发事件
                if (document.querySelector('.editor-container')) {
                    document.querySelector('.editor-container').dispatchEvent(pasteEvent);
                    updateStatus('paste-status', 'success');
                    showOutput('paste-info', '粘贴事件已模拟，请查看编辑器和控制台输出');
                } else {
                    updateStatus('paste-status', 'warning');
                    showOutput('paste-info', '找不到编辑器容器，请确保编辑器已加载');
                }
            } catch (e) {
                updateStatus('paste-status', 'error');
                showOutput('paste-info', `模拟粘贴失败：${e.message}`);
                log(`模拟粘贴失败：${e.message}`);
            }
        }

        function checkPasteHandlers() {
            log('检查粘贴处理器...');
            
            const editorContainer = document.querySelector('.editor-container');
            if (editorContainer) {
                const listeners = getEventListeners ? getEventListeners(editorContainer) : {};
                showOutput('paste-info', `编辑器容器事件监听器：\n${JSON.stringify(listeners, null, 2)}`);
                updateStatus('paste-status', 'success');
            } else {
                showOutput('paste-info', '找不到编辑器容器');
                updateStatus('paste-status', 'error');
            }
        }

        // 5. 修复工具
        function forceReparse() {
            log('强制重新解析...');
            
            try {
                if (typeof window.forceMarkdownReparse === 'function') {
                    window.forceMarkdownReparse();
                    updateStatus('fix-status', 'success');
                    showOutput('fix-info', '强制重新解析已执行，请查看编辑器效果');
                } else {
                    updateStatus('fix-status', 'error');
                    showOutput('fix-info', '错误：找不到forceMarkdownReparse函数');
                }
            } catch (e) {
                updateStatus('fix-status', 'error');
                showOutput('fix-info', `强制重新解析失败：${e.message}`);
            }
        }

        function forceTableRender() {
            log('强制表格渲染...');
            
            try {
                if (typeof window.forceTableRender === 'function') {
                    window.forceTableRender();
                    updateStatus('fix-status', 'success');
                    showOutput('fix-info', '强制表格渲染已执行，请查看编辑器效果');
                } else {
                    updateStatus('fix-status', 'error');
                    showOutput('fix-info', '错误：找不到forceTableRender函数');
                }
            } catch (e) {
                updateStatus('fix-status', 'error');
                showOutput('fix-info', `强制表格渲染失败：${e.message}`);
            }
        }

        function recreateEditor() {
            log('重建编辑器...');
            
            try {
                if (typeof window.recreateEditor === 'function') {
                    window.recreateEditor();
                    updateStatus('fix-status', 'success');
                    showOutput('fix-info', '编辑器重建已启动，请等待完成');
                } else {
                    updateStatus('fix-status', 'error');
                    showOutput('fix-info', '错误：找不到recreateEditor函数');
                }
            } catch (e) {
                updateStatus('fix-status', 'error');
                showOutput('fix-info', `编辑器重建失败：${e.message}`);
            }
        }

        // 页面加载时的初始化
        window.addEventListener('load', () => {
            log('测试工具页面已加载');
            
            // 设置初始状态
            ['editor-status', 'table-support-status', 'rendering-status', 'paste-status', 'fix-status'].forEach(id => {
                updateStatus(id, 'warning');
            });
            
            // 自动检查编辑器状态
            setTimeout(checkEditorStatus, 1000);
        });
    </script>
</body>
</html>
