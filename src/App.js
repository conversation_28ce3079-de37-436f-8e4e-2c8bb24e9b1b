import React from 'react';
import MainLayout from './components/Layout/MainLayout';
import ErrorBoundary from './components/Common/ErrorBoundary';
import { globalErrorHandler } from './utils/errorHandler';
import './App.css';

function App() {
  React.useEffect(() => {
    // 设置全局错误处理
    const handleError = (error) => {
      console.error('Global Error Handler:', error);
    };

    globalErrorHandler.addErrorListener(handleError);

    return () => {
      globalErrorHandler.removeErrorListener(handleError);
    };
  }, []);

  return (
    <ErrorBoundary level="app">
      <MainLayout />
    </ErrorBoundary>
  );
}

export default App;