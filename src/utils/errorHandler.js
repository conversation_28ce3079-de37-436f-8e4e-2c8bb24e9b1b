/**
 * 错误处理工具集
 * 提供统一的错误处理、重试机制和用户友好的错误提示
 */

/**
 * 错误类型枚举
 */
export const ErrorTypes = {
  NETWORK: 'NETWORK',
  VALIDATION: 'VALIDATION',
  PERMISSION: 'PERMISSION',
  NOT_FOUND: 'NOT_FOUND',
  SERVER: 'SERVER',
  CLIENT: 'CLIENT',
  TIMEOUT: 'TIMEOUT',
  UNKNOWN: 'UNKNOWN'
};

/**
 * 自定义错误类
 */
export class AppError extends Error {
  constructor(message, type = ErrorTypes.UNKNOWN, code = null, details = null) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.code = code;
    this.details = details;
    this.timestamp = new Date().toISOString();
  }
}

/**
 * 网络错误处理器
 */
export class NetworkErrorHandler {
  constructor(options = {}) {
    this.maxRetries = options.maxRetries || 3;
    this.retryDelay = options.retryDelay || 1000;
    this.timeout = options.timeout || 10000;
  }

  /**
   * 处理fetch请求错误
   */
  async handleFetch(url, options = {}, retryCount = 0) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new AppError(
          `HTTP ${response.status}: ${response.statusText}`,
          this.getErrorTypeFromStatus(response.status),
          response.status
        );
      }

      return response;
    } catch (error) {
      if (error.name === 'AbortError') {
        throw new AppError(
          '请求超时，请检查网络连接',
          ErrorTypes.TIMEOUT
        );
      }

      if (retryCount < this.maxRetries && this.shouldRetry(error)) {
        console.warn(`请求失败，${this.retryDelay}ms后重试 (${retryCount + 1}/${this.maxRetries}):`, error.message);
        await this.delay(this.retryDelay * Math.pow(2, retryCount)); // 指数退避
        return this.handleFetch(url, options, retryCount + 1);
      }

      throw this.normalizeError(error);
    }
  }

  /**
   * 根据HTTP状态码确定错误类型
   */
  getErrorTypeFromStatus(status) {
    if (status >= 400 && status < 500) {
      switch (status) {
        case 401:
        case 403:
          return ErrorTypes.PERMISSION;
        case 404:
          return ErrorTypes.NOT_FOUND;
        case 422:
          return ErrorTypes.VALIDATION;
        default:
          return ErrorTypes.CLIENT;
      }
    } else if (status >= 500) {
      return ErrorTypes.SERVER;
    }
    return ErrorTypes.UNKNOWN;
  }

  /**
   * 判断是否应该重试
   */
  shouldRetry(error) {
    // 网络错误、超时、服务器错误可以重试
    return (
      error.type === ErrorTypes.NETWORK ||
      error.type === ErrorTypes.TIMEOUT ||
      error.type === ErrorTypes.SERVER ||
      error.name === 'TypeError' // fetch网络错误
    );
  }

  /**
   * 标准化错误对象
   */
  normalizeError(error) {
    if (error instanceof AppError) {
      return error;
    }

    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return new AppError(
        '网络连接失败，请检查网络设置',
        ErrorTypes.NETWORK
      );
    }

    return new AppError(
      error.message || '未知错误',
      ErrorTypes.UNKNOWN,
      null,
      error
    );
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 全局错误处理器
 */
export class GlobalErrorHandler {
  constructor() {
    this.errorListeners = [];
    this.setupGlobalHandlers();
  }

  /**
   * 设置全局错误监听
   */
  setupGlobalHandlers() {
    // 捕获未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled Promise Rejection:', event.reason);
      this.handleError(new AppError(
        event.reason?.message || '未处理的Promise错误',
        ErrorTypes.UNKNOWN,
        null,
        event.reason
      ));
      event.preventDefault();
    });

    // 捕获全局JavaScript错误
    window.addEventListener('error', (event) => {
      console.error('Global Error:', event.error);
      this.handleError(new AppError(
        event.error?.message || event.message || '全局JavaScript错误',
        ErrorTypes.CLIENT,
        null,
        event.error
      ));
    });
  }

  /**
   * 添加错误监听器
   */
  addErrorListener(listener) {
    this.errorListeners.push(listener);
  }

  /**
   * 移除错误监听器
   */
  removeErrorListener(listener) {
    const index = this.errorListeners.indexOf(listener);
    if (index > -1) {
      this.errorListeners.splice(index, 1);
    }
  }

  /**
   * 处理错误
   */
  handleError(error) {
    const normalizedError = this.normalizeError(error);
    
    // 通知所有监听器
    this.errorListeners.forEach(listener => {
      try {
        listener(normalizedError);
      } catch (e) {
        console.error('Error in error listener:', e);
      }
    });

    // 记录错误
    this.logError(normalizedError);
  }

  /**
   * 标准化错误
   */
  normalizeError(error) {
    if (error instanceof AppError) {
      return error;
    }
    return new AppError(
      error.message || '未知错误',
      ErrorTypes.UNKNOWN,
      null,
      error
    );
  }

  /**
   * 记录错误
   */
  logError(error) {
    const errorLog = {
      id: Date.now().toString(36) + Math.random().toString(36).substr(2),
      message: error.message,
      type: error.type,
      code: error.code,
      stack: error.stack,
      timestamp: error.timestamp,
      url: window.location.href,
      userAgent: navigator.userAgent
    };

    console.error('Error Log:', errorLog);

    // 在生产环境中，可以发送到错误监控服务
    if (process.env.NODE_ENV === 'production' && window.errorReportingService) {
      window.errorReportingService.report(errorLog);
    }
  }
}

/**
 * 错误消息映射
 */
export const ErrorMessages = {
  [ErrorTypes.NETWORK]: '网络连接失败，请检查网络设置',
  [ErrorTypes.TIMEOUT]: '请求超时，请稍后重试',
  [ErrorTypes.PERMISSION]: '权限不足，请联系管理员',
  [ErrorTypes.NOT_FOUND]: '请求的资源不存在',
  [ErrorTypes.SERVER]: '服务器错误，请稍后重试',
  [ErrorTypes.CLIENT]: '请求错误，请检查输入',
  [ErrorTypes.VALIDATION]: '数据验证失败，请检查输入',
  [ErrorTypes.UNKNOWN]: '未知错误，请稍后重试'
};

/**
 * 获取用户友好的错误消息
 */
export const getUserFriendlyMessage = (error) => {
  if (error instanceof AppError) {
    return ErrorMessages[error.type] || error.message;
  }
  return error.message || ErrorMessages[ErrorTypes.UNKNOWN];
};

// 创建全局实例
export const globalErrorHandler = new GlobalErrorHandler();
export const networkErrorHandler = new NetworkErrorHandler();

// React Hooks 已移动到 src/hooks/useErrorHandler.js
