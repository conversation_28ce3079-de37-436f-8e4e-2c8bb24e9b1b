/**
 * 测试辅助工具集
 * 提供测试数据生成、模拟对象和测试断言等功能
 */

/**
 * 测试数据生成器
 */
export class TestDataGenerator {
  static generateMarkdownContent(sections = 5, wordsPerSection = 100) {
    const sections_data = [];
    
    for (let i = 0; i < sections; i++) {
      const level = Math.floor(Math.random() * 3); // 0-2级标题
      const title = `测试章节 ${i + 1}`;
      const content = this.generateRandomText(wordsPerSection);
      
      sections_data.push({
        level,
        title,
        content,
        markdown: `${'#'.repeat(level + 1)} ${title}\n\n${content}\n\n`
      });
    }
    
    return sections_data.map(s => s.markdown).join('');
  }

  static generateRandomText(wordCount = 100) {
    const words = [
      '产品', '需求', '功能', '用户', '系统', '界面', '交互', '体验',
      '设计', '开发', '测试', '部署', '维护', '优化', '性能', '安全',
      '可用性', '可扩展性', '兼容性', '稳定性', '效率', '质量'
    ];
    
    const sentences = [];
    let currentSentence = [];
    
    for (let i = 0; i < wordCount; i++) {
      currentSentence.push(words[Math.floor(Math.random() * words.length)]);
      
      if (currentSentence.length >= 5 + Math.floor(Math.random() * 10)) {
        sentences.push(currentSentence.join('') + '。');
        currentSentence = [];
      }
    }
    
    if (currentSentence.length > 0) {
      sentences.push(currentSentence.join('') + '。');
    }
    
    return sentences.join(' ');
  }

  static generateSectionData(count = 10) {
    return Array.from({ length: count }, (_, index) => ({
      id: `section-${index + 1}`,
      title: `测试章节 ${index + 1}`,
      level: Math.floor(Math.random() * 4),
      content: this.generateRandomText(50),
      order: index
    }));
  }

  static generateLargeDocument(sectionCount = 100) {
    return this.generateMarkdownContent(sectionCount, 200);
  }
}

/**
 * 模拟对象工厂
 */
export class MockFactory {
  static createMockEditor() {
    return {
      getValue: () => '',
      setValue: () => {},
      onDidChangeModelContent: () => {},
      dispose: () => {},
      focus: () => {},
      getModel: () => ({
        getLineCount: () => 10,
        getValueInRange: () => 'mock content'
      })
    };
  }

  static createMockContentState() {
    return {
      content: TestDataGenerator.generateMarkdownContent(5),
      sections: TestDataGenerator.generateSectionData(5),
      outline: [
        { id: '1', title: '概述', level: 0 },
        { id: '2', title: '功能需求', level: 0 },
        { id: '3', title: '用户界面', level: 1 }
      ],
      isModified: false,
      lastSaved: new Date().toISOString()
    };
  }

  static createMockAIAnalysis() {
    return {
      completeness: 85,
      clarity: 90,
      feasibility: 80,
      innovation: 75,
      overallScore: 82.5,
      suggestions: [
        '建议添加更多技术细节',
        '用户故事需要更具体',
        '缺少性能要求说明'
      ],
      problems: [
        { type: 'missing', description: '缺少API文档' },
        { type: 'unclear', description: '用户权限描述不清晰' }
      ]
    };
  }

  static createMockLocalStorage() {
    const store = {};
    
    return {
      getItem: (key) => store[key] || null,
      setItem: (key, value) => {
        store[key] = value.toString();
      },
      removeItem: (key) => {
        delete store[key];
      },
      clear: () => {
        Object.keys(store).forEach(key => delete store[key]);
      }
    };
  }
}

/**
 * 性能测试辅助函数
 */
export class PerformanceTestHelper {
  static measureRenderTime(renderFunction) {
    const startTime = performance.now();
    const result = renderFunction();
    const endTime = performance.now();
    
    return {
      result,
      renderTime: endTime - startTime
    };
  }

  static async measureAsyncOperation(asyncFunction) {
    const startTime = performance.now();
    const result = await asyncFunction();
    const endTime = performance.now();
    
    return {
      result,
      duration: endTime - startTime
    };
  }

  static createPerformanceObserver(entryTypes = ['measure', 'navigation']) {
    const entries = [];
    
    if (typeof PerformanceObserver !== 'undefined') {
      const observer = new PerformanceObserver((list) => {
        entries.push(...list.getEntries());
      });
      
      observer.observe({ entryTypes });
      
      return {
        observer,
        getEntries: () => entries,
        disconnect: () => observer.disconnect()
      };
    }
    
    return {
      observer: null,
      getEntries: () => entries,
      disconnect: () => {}
    };
  }

  static simulateSlowNetwork(delay = 1000) {
    const originalFetch = global.fetch || window.fetch;
    
    const slowFetch = async (...args) => {
      await new Promise(resolve => setTimeout(resolve, delay));
      return originalFetch(...args);
    };
    
    if (global.fetch) {
      global.fetch = slowFetch;
    }
    if (window.fetch) {
      window.fetch = slowFetch;
    }
    
    return () => {
      if (global.fetch) {
        global.fetch = originalFetch;
      }
      if (window.fetch) {
        window.fetch = originalFetch;
      }
    };
  }
}

/**
 * 测试断言辅助函数
 */
export class TestAssertions {
  static expectElementToHaveText(element, text) {
    if (!element) {
      throw new Error('Element is null or undefined');
    }
    if (!element.textContent.includes(text)) {
      throw new Error(`Expected element to contain text "${text}", but got "${element.textContent}"`);
    }
  }

  static expectElementToBeVisible(element) {
    if (!element) {
      throw new Error('Element is null or undefined');
    }
    const style = window.getComputedStyle(element);
    if (style.display === 'none' || style.visibility === 'hidden') {
      throw new Error('Expected element to be visible');
    }
  }

  static expectArrayToHaveLength(array, length) {
    if (!Array.isArray(array)) {
      throw new Error('Expected an array');
    }
    if (array.length !== length) {
      throw new Error(`Expected array to have length ${length}, but got ${array.length}`);
    }
  }

  static expectObjectToHaveProperty(object, property, value = undefined) {
    if (!object || typeof object !== 'object') {
      throw new Error('Expected an object');
    }
    if (!(property in object)) {
      throw new Error(`Expected object to have property "${property}"`);
    }
    if (value !== undefined && object[property] !== value) {
      throw new Error(`Expected property "${property}" to be ${value}, but got ${object[property]}`);
    }
  }

  static expectPerformanceToBeWithinRange(duration, min, max) {
    if (duration < min || duration > max) {
      throw new Error(`Expected duration ${duration}ms to be between ${min}ms and ${max}ms`);
    }
  }
}

/**
 * 测试环境设置
 */
export class TestEnvironment {
  static setupBrowserMocks() {
    // 模拟ResizeObserver
    if (typeof ResizeObserver === 'undefined') {
      global.ResizeObserver = class ResizeObserver {
        constructor(callback) {
          this.callback = callback;
        }
        observe() {}
        unobserve() {}
        disconnect() {}
      };
    }

    // 模拟IntersectionObserver
    if (typeof IntersectionObserver === 'undefined') {
      global.IntersectionObserver = class IntersectionObserver {
        constructor(callback) {
          this.callback = callback;
        }
        observe() {}
        unobserve() {}
        disconnect() {}
      };
    }

    // 模拟performance API
    if (!global.performance) {
      global.performance = {
        now: () => Date.now(),
        mark: () => {},
        measure: () => {},
        memory: {
          usedJSHeapSize: 1000000,
          totalJSHeapSize: 2000000,
          jsHeapSizeLimit: 4000000
        }
      };
    }

    // 模拟localStorage
    if (!global.localStorage) {
      global.localStorage = MockFactory.createMockLocalStorage();
    }
  }

  static cleanupAfterTest() {
    // 清理DOM
    if (typeof document !== 'undefined') {
      document.body.innerHTML = '';
    }
    
    // 清理定时器
    if (typeof jest !== 'undefined') {
      jest.clearAllTimers();
    }
  }
}

// 导出所有工具类
export default {
  TestDataGenerator,
  MockFactory,
  PerformanceTestHelper,
  TestAssertions,
  TestEnvironment
};
