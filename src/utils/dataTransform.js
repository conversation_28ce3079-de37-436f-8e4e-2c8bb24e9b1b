// 数据转换工具函数

/**
 * 将现有文档格式转换为新的评审文档格式
 * @param {Object} existingDoc - 现有文档对象
 * @returns {Object} 转换后的评审文档对象
 */
export const transformToReviewDocument = (existingDoc) => {
  return {
    // 保持现有字段
    id: existingDoc.id,
    title: existingDoc.title,
    uploadDate: existingDoc.uploadDate,
    fileSize: existingDoc.fileSize,
    status: existingDoc.status,
    progress: existingDoc.progress,
    issues: existingDoc.issues,
    fixed: existingDoc.fixed,

    // 新增字段 (使用默认值或从现有数据推导)
    name: existingDoc.title,
    version: existingDoc.version || 'v1.0.0',
    fileName: existingDoc.fileName || `${existingDoc.title}.pdf`,
    size: existingDoc.fileSize,
    uploadTime: existingDoc.uploadDate,
    productManager: existingDoc.productManager || '产品经理',
    architect: existingDoc.architect || '架构师',
    testManager: existingDoc.testManager || '测试经理',
    source: existingDoc.source || 'manual',
    submitter: existingDoc.submitter || '提交人',
    hasNewVersion: existingDoc.hasNewVersion || false,
    isCurrentVersion: existingDoc.isCurrentVersion !== false,
    versions: existingDoc.versions || []
  };
};

/**
 * 将新的评审文档格式转换回现有格式 (如果需要)
 * @param {Object} reviewDoc - 评审文档对象
 * @returns {Object} 转换后的现有格式文档对象
 */
export const transformFromReviewDocument = (reviewDoc) => {
  return {
    id: reviewDoc.id,
    title: reviewDoc.title,
    uploadDate: reviewDoc.uploadDate,
    fileSize: reviewDoc.fileSize,
    status: reviewDoc.status,
    progress: reviewDoc.progress,
    issues: reviewDoc.issues,
    fixed: reviewDoc.fixed
  };
};

/**
 * 生成模拟评审数据
 * @returns {Array} 模拟的评审文档列表
 */
export const generateMockReviewData = () => {
  return [
    {
      id: "review-doc1",
      name: "智能驾驶系统PRD",
      title: "智能驾驶系统PRD",
      version: "v1.3.0",
      fileName: "智能驾驶系统PRD_v1.3.0.pdf",
      size: "2.5 MB",
      fileSize: "2.5 MB",
      uploadTime: "2025-01-10 16:45",
      uploadDate: "2025-01-10 16:45",
      status: "reviewing",
      progress: 65,
      issues: 12,
      fixed: 8,
      productManager: "张三",
      architect: "李四",
      testManager: "王五",
      source: "ai",
      submitter: "张三",
      hasNewVersion: true,
      isCurrentVersion: true,
      versions: [
        {
          id: "review-doc1-v1",
          name: "智能驾驶系统PRD",
          version: "v1.0.0",
          fileName: "智能驾驶系统PRD_v1.0.0.pdf",
          size: "2.1 MB",
          uploadTime: "2024-12-15 14:30",
          status: "completed",
          issues: 18,
          fixed: 15,
          productManager: "张三",
          architect: "李四",
          testManager: "王五",
          source: "ai",
          submitter: "张三"
        }
      ]
    },
    {
      id: "review-doc2",
      name: "用户体验优化方案",
      title: "用户体验优化方案",
      version: "v2.1.0",
      fileName: "用户体验优化方案_v2.1.0.pdf",
      size: "1.8 MB",
      fileSize: "1.8 MB",
      uploadTime: "2025-01-08 14:20",
      uploadDate: "2025-01-08 14:20",
      status: "completed",
      progress: 100,
      issues: 8,
      fixed: 8,
      productManager: "李明",
      architect: "王强",
      testManager: "赵敏",
      source: "manual",
      submitter: "李明",
      hasNewVersion: false,
      isCurrentVersion: true,
      versions: []
    },
    {
      id: "review-doc3",
      name: "API接口设计文档",
      title: "API接口设计文档",
      version: "v1.0.0",
      fileName: "API接口设计文档_v1.0.0.pdf",
      size: "3.2 MB",
      fileSize: "3.2 MB",
      uploadTime: "2025-01-05 09:30",
      uploadDate: "2025-01-05 09:30",
      status: "needs-revision",
      progress: 45,
      issues: 15,
      fixed: 3,
      productManager: "陈华",
      architect: "刘洋",
      testManager: "孙丽",
      source: "self-review",
      submitter: "陈华",
      hasNewVersion: false,
      isCurrentVersion: true,
      versions: []
    }
  ];
};

/**
 * 生成模拟统计数据
 * @returns {Object} 模拟的统计数据
 */
export const generateMockStats = () => {
  return {
    pendingReviews: 5,
    passRate: 85,
    issueAdoptionRate: 72,
    ruleCount: 24
  };
};
