/**
 * 性能优化工具集
 * 提供防抖、节流、内存管理等性能优化功能
 */

/**
 * 防抖函数
 * 在事件被触发n秒后再执行回调，如果在这n秒内又被触发，则重新计时
 */
export function debounce(func, wait, immediate = false) {
  let timeout;
  
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func.apply(this, args);
    };
    
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func.apply(this, args);
  };
}

/**
 * 节流函数
 * 规定在一个单位时间内，只能触发一次函数
 */
export function throttle(func, limit) {
  let inThrottle;
  
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 请求动画帧节流
 * 使用requestAnimationFrame来节流函数调用
 */
export function rafThrottle(func) {
  let rafId = null;
  
  return function executedFunction(...args) {
    if (rafId === null) {
      rafId = requestAnimationFrame(() => {
        func.apply(this, args);
        rafId = null;
      });
    }
  };
}

/**
 * 批量更新函数
 * 将多个更新操作批量执行，减少重复渲染
 */
export class BatchUpdater {
  constructor(delay = 16) { // 约60fps
    this.delay = delay;
    this.updates = [];
    this.timeoutId = null;
  }

  add(updateFunction) {
    this.updates.push(updateFunction);
    
    if (!this.timeoutId) {
      this.timeoutId = setTimeout(() => {
        this.flush();
      }, this.delay);
    }
  }

  flush() {
    const updates = this.updates.slice();
    this.updates = [];
    this.timeoutId = null;
    
    // 执行所有更新
    updates.forEach(update => {
      try {
        update();
      } catch (error) {
        console.error('Batch update error:', error);
      }
    });
  }

  clear() {
    this.updates = [];
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }
}

/**
 * 内存使用监控器
 */
export class MemoryMonitor {
  constructor() {
    this.measurements = [];
    this.maxMeasurements = 100;
  }

  measure() {
    if (performance.memory) {
      const measurement = {
        timestamp: Date.now(),
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        totalJSHeapSize: performance.memory.totalJSHeapSize,
        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
      };
      
      this.measurements.push(measurement);
      
      // 保持最近的测量数据
      if (this.measurements.length > this.maxMeasurements) {
        this.measurements.shift();
      }
      
      return measurement;
    }
    return null;
  }

  getStats() {
    if (this.measurements.length === 0) {
      return null;
    }

    const latest = this.measurements[this.measurements.length - 1];
    const oldest = this.measurements[0];
    
    return {
      current: latest,
      growth: latest.usedJSHeapSize - oldest.usedJSHeapSize,
      measurements: this.measurements.length,
      averageUsage: this.measurements.reduce((sum, m) => sum + m.usedJSHeapSize, 0) / this.measurements.length
    };
  }

  isMemoryLeaking(threshold = 50 * 1024 * 1024) { // 50MB threshold
    const stats = this.getStats();
    return stats && stats.growth > threshold;
  }
}

/**
 * 性能计时器
 */
export class PerformanceTimer {
  constructor() {
    this.timers = new Map();
  }

  start(name) {
    this.timers.set(name, performance.now());
  }

  end(name) {
    const startTime = this.timers.get(name);
    if (startTime !== undefined) {
      const duration = performance.now() - startTime;
      this.timers.delete(name);
      return duration;
    }
    return null;
  }

  measure(name, func) {
    this.start(name);
    const result = func();
    const duration = this.end(name);
    
    console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`);
    return { result, duration };
  }

  async measureAsync(name, asyncFunc) {
    this.start(name);
    const result = await asyncFunc();
    const duration = this.end(name);
    
    console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`);
    return { result, duration };
  }
}

/**
 * 长任务分割器
 * 将长时间运行的任务分割成小块，避免阻塞UI
 */
export class TaskSplitter {
  constructor(timeSlice = 5) { // 5ms时间片
    this.timeSlice = timeSlice;
  }

  async process(items, processor, onProgress = null) {
    const results = [];
    let processed = 0;
    
    for (let i = 0; i < items.length; i++) {
      const startTime = performance.now();
      
      // 处理当前项
      const result = await processor(items[i], i);
      results.push(result);
      processed++;
      
      // 检查是否需要让出控制权
      if (performance.now() - startTime > this.timeSlice) {
        // 让出控制权给浏览器
        await new Promise(resolve => setTimeout(resolve, 0));
        
        // 报告进度
        if (onProgress) {
          onProgress(processed, items.length);
        }
      }
    }
    
    return results;
  }
}

/**
 * 懒加载管理器
 */
export class LazyLoader {
  constructor() {
    this.observers = new Map();
    this.loadedItems = new Set();
  }

  observe(element, loadCallback, options = {}) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !this.loadedItems.has(element)) {
          this.loadedItems.add(element);
          loadCallback(element);
          observer.unobserve(element);
        }
      });
    }, {
      rootMargin: '50px',
      threshold: 0.1,
      ...options
    });

    observer.observe(element);
    this.observers.set(element, observer);
  }

  unobserve(element) {
    const observer = this.observers.get(element);
    if (observer) {
      observer.unobserve(element);
      this.observers.delete(element);
    }
    this.loadedItems.delete(element);
  }

  cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    this.loadedItems.clear();
  }
}

/**
 * 创建优化的事件处理器
 */
export function createOptimizedHandler(handler, options = {}) {
  const { 
    debounceMs = 0, 
    throttleMs = 0, 
    useRaf = false,
    immediate = false 
  } = options;

  if (useRaf) {
    return rafThrottle(handler);
  } else if (debounceMs > 0) {
    return debounce(handler, debounceMs, immediate);
  } else if (throttleMs > 0) {
    return throttle(handler, throttleMs);
  }
  
  return handler;
}

// 创建全局实例
export const globalBatchUpdater = new BatchUpdater();
export const globalMemoryMonitor = new MemoryMonitor();
export const globalPerformanceTimer = new PerformanceTimer();
export const globalTaskSplitter = new TaskSplitter();
export const globalLazyLoader = new LazyLoader();

// 定期监控内存使用
setInterval(() => {
  globalMemoryMonitor.measure();
  
  // 检查内存泄漏
  if (globalMemoryMonitor.isMemoryLeaking()) {
    console.warn('Potential memory leak detected!', globalMemoryMonitor.getStats());
  }
}, 30000); // 每30秒检查一次

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
  globalLazyLoader.cleanup();
  globalBatchUpdater.clear();
});
