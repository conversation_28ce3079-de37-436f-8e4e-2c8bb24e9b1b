/**
 * 表格数据处理工具
 * 提供CSV/Excel导入导出、表格验证等功能
 */

/**
 * CSV解析器
 */
export class CSVParser {
  static parse(csvText, options = {}) {
    const {
      delimiter = ',',
      quote = '"',
      escape = '"',
      skipEmptyLines = true,
      hasHeader = true
    } = options;

    const lines = csvText.split(/\r?\n/);
    const result = [];
    let headers = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      if (skipEmptyLines && !line) {
        continue;
      }

      const row = this.parseLine(line, delimiter, quote, escape);
      
      if (i === 0 && hasHeader) {
        headers = row;
      } else {
        result.push(row);
      }
    }

    return {
      headers: hasHeader ? headers : [],
      data: result
    };
  }

  static parseLine(line, delimiter, quote, escape) {
    const result = [];
    let current = '';
    let inQuotes = false;
    let i = 0;

    while (i < line.length) {
      const char = line[i];
      const nextChar = line[i + 1];

      if (char === quote) {
        if (inQuotes && nextChar === quote) {
          // 转义的引号
          current += quote;
          i += 2;
        } else {
          // 开始或结束引号
          inQuotes = !inQuotes;
          i++;
        }
      } else if (char === delimiter && !inQuotes) {
        // 字段分隔符
        result.push(current);
        current = '';
        i++;
      } else {
        current += char;
        i++;
      }
    }

    result.push(current);
    return result;
  }

  static stringify(data, headers = [], options = {}) {
    const {
      delimiter = ',',
      quote = '"',
      includeHeader = true,
      forceQuotes = false
    } = options;

    const lines = [];

    // 添加表头
    if (includeHeader && headers.length > 0) {
      lines.push(this.stringifyRow(headers, delimiter, quote, forceQuotes));
    }

    // 添加数据行
    data.forEach(row => {
      lines.push(this.stringifyRow(row, delimiter, quote, forceQuotes));
    });

    return lines.join('\n');
  }

  static stringifyRow(row, delimiter, quote, forceQuotes) {
    return row.map(cell => {
      const cellStr = String(cell || '');
      
      if (forceQuotes || cellStr.includes(delimiter) || cellStr.includes(quote) || cellStr.includes('\n')) {
        return quote + cellStr.replace(new RegExp(quote, 'g'), quote + quote) + quote;
      }
      
      return cellStr;
    }).join(delimiter);
  }
}

/**
 * 表格数据验证器
 */
export class TableValidator {
  static validateData(data, schema = {}) {
    const errors = [];
    const {
      columns = [],
      maxRows = 1000,
      maxCols = 50,
      allowEmpty = true
    } = schema;

    // 检查行数限制
    if (data.length > maxRows) {
      errors.push({
        type: 'row_limit',
        message: `表格行数超过限制 (${data.length} > ${maxRows})`
      });
    }

    // 检查列数限制
    const maxColCount = Math.max(...data.map(row => row.length));
    if (maxColCount > maxCols) {
      errors.push({
        type: 'column_limit',
        message: `表格列数超过限制 (${maxColCount} > ${maxCols})`
      });
    }

    // 验证每一行
    data.forEach((row, rowIndex) => {
      // 检查列定义
      columns.forEach((colDef, colIndex) => {
        const cellValue = row[colIndex];
        const cellErrors = this.validateCell(cellValue, colDef, rowIndex, colIndex);
        errors.push(...cellErrors);
      });

      // 检查空行
      if (!allowEmpty && row.every(cell => !cell || String(cell).trim() === '')) {
        errors.push({
          type: 'empty_row',
          message: `第 ${rowIndex + 1} 行为空行`,
          row: rowIndex
        });
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  static validateCell(value, columnDef, rowIndex, colIndex) {
    const errors = [];
    const {
      type = 'string',
      required = false,
      minLength,
      maxLength,
      pattern,
      enum: enumValues
    } = columnDef;

    const cellValue = String(value || '').trim();

    // 必填检查
    if (required && !cellValue) {
      errors.push({
        type: 'required',
        message: `第 ${rowIndex + 1} 行第 ${colIndex + 1} 列为必填项`,
        row: rowIndex,
        col: colIndex
      });
      return errors;
    }

    if (!cellValue) {
      return errors; // 空值且非必填，跳过其他验证
    }

    // 类型验证
    switch (type) {
      case 'number':
        if (isNaN(Number(cellValue))) {
          errors.push({
            type: 'type_error',
            message: `第 ${rowIndex + 1} 行第 ${colIndex + 1} 列应为数字`,
            row: rowIndex,
            col: colIndex
          });
        }
        break;
      case 'email':
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailPattern.test(cellValue)) {
          errors.push({
            type: 'format_error',
            message: `第 ${rowIndex + 1} 行第 ${colIndex + 1} 列邮箱格式不正确`,
            row: rowIndex,
            col: colIndex
          });
        }
        break;
      case 'url':
        try {
          new URL(cellValue);
        } catch {
          errors.push({
            type: 'format_error',
            message: `第 ${rowIndex + 1} 行第 ${colIndex + 1} 列URL格式不正确`,
            row: rowIndex,
            col: colIndex
          });
        }
        break;
    }

    // 长度验证
    if (minLength && cellValue.length < minLength) {
      errors.push({
        type: 'min_length',
        message: `第 ${rowIndex + 1} 行第 ${colIndex + 1} 列长度不能少于 ${minLength} 个字符`,
        row: rowIndex,
        col: colIndex
      });
    }

    if (maxLength && cellValue.length > maxLength) {
      errors.push({
        type: 'max_length',
        message: `第 ${rowIndex + 1} 行第 ${colIndex + 1} 列长度不能超过 ${maxLength} 个字符`,
        row: rowIndex,
        col: colIndex
      });
    }

    // 正则表达式验证
    if (pattern && !new RegExp(pattern).test(cellValue)) {
      errors.push({
        type: 'pattern_error',
        message: `第 ${rowIndex + 1} 行第 ${colIndex + 1} 列格式不符合要求`,
        row: rowIndex,
        col: colIndex
      });
    }

    // 枚举值验证
    if (enumValues && !enumValues.includes(cellValue)) {
      errors.push({
        type: 'enum_error',
        message: `第 ${rowIndex + 1} 行第 ${colIndex + 1} 列值必须为: ${enumValues.join(', ')}`,
        row: rowIndex,
        col: colIndex
      });
    }

    return errors;
  }
}

/**
 * 表格转换器
 */
export class TableConverter {
  static toMarkdown(data, headers = []) {
    if (!data || data.length === 0) {
      return '';
    }

    const rows = [];
    
    // 添加表头
    if (headers.length > 0) {
      rows.push('| ' + headers.join(' | ') + ' |');
      rows.push('| ' + headers.map(() => '---').join(' | ') + ' |');
    }

    // 添加数据行
    data.forEach(row => {
      const cells = row.map(cell => String(cell || '').replace(/\|/g, '\\|'));
      rows.push('| ' + cells.join(' | ') + ' |');
    });

    return rows.join('\n');
  }

  static fromMarkdown(markdownTable) {
    const lines = markdownTable.trim().split('\n');
    const data = [];
    let headers = [];

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      
      if (!trimmedLine.startsWith('|') || !trimmedLine.endsWith('|')) {
        return;
      }

      // 移除首尾的 |
      const content = trimmedLine.slice(1, -1);
      const cells = content.split('|').map(cell => cell.trim());

      if (index === 0) {
        headers = cells;
      } else if (index === 1) {
        // 跳过分隔行
        return;
      } else {
        data.push(cells);
      }
    });

    return { headers, data };
  }

  static toHTML(data, headers = [], options = {}) {
    const {
      className = 'table-auto border-collapse border border-gray-300',
      includeHeader = true,
      cellPadding = '8px',
      enableWordWrap = false
    } = options;

    let html = `<table class="${className}" style="border-collapse: collapse; width: 100%;">`;

    // 基础单元格样式
    const baseCellStyle = `border: 1px solid #ddd; padding: ${cellPadding}; vertical-align: top;`;
    const wordWrapStyle = enableWordWrap ? 'word-wrap: break-word; word-break: break-word; overflow-wrap: anywhere; white-space: normal; min-width: 80px; max-width: 250px;' : '';

    // 添加表头
    if (includeHeader && headers.length > 0) {
      html += '<thead><tr>';
      headers.forEach(header => {
        html += `<th style="${baseCellStyle} ${wordWrapStyle} background-color: #f5f5f5; font-weight: bold; text-align: left;">${this.escapeHtml(header)}</th>`;
      });
      html += '</tr></thead>';
    }

    // 添加数据行
    html += '<tbody>';
    data.forEach(row => {
      html += '<tr>';
      row.forEach(cell => {
        html += `<td style="${baseCellStyle} ${wordWrapStyle}">${this.escapeHtml(cell || '')}</td>`;
      });
      html += '</tr>';
    });
    html += '</tbody>';

    html += '</table>';
    return html;
  }

  static escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}

/**
 * 文件处理工具
 */
export class FileHandler {
  static async readCSV(file) {
    const text = await this.readFileAsText(file);
    return CSVParser.parse(text);
  }

  static downloadCSV(data, headers = [], filename = 'table.csv') {
    const csvContent = CSVParser.stringify(data, headers);
    this.downloadFile(csvContent, filename, 'text/csv');
  }

  static downloadMarkdown(data, headers = [], filename = 'table.md') {
    const markdownContent = TableConverter.toMarkdown(data, headers);
    this.downloadFile(markdownContent, filename, 'text/markdown');
  }

  static readFileAsText(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = (e) => reject(e);
      reader.readAsText(file);
    });
  }

  static downloadFile(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }
}

export default {
  CSVParser,
  TableValidator,
  TableConverter,
  FileHandler
};
