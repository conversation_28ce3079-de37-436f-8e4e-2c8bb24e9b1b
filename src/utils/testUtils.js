/**
 * 测试工具类
 * 用于在测试环境中动态注入测试功能，避免测试代码污染业务代码
 */

class TestUtils {
  constructor() {
    this.isTestMode = false;
    this.testBarElement = null;
    this.originalConsoleLog = console.log;
  }

  /**
   * 启用测试模式
   */
  enableTestMode() {
    this.isTestMode = true;
    console.log('🧪 测试模式已启用');
    
    // 在控制台显示测试提示
    console.log('%c🎯 PRD智能评估测试模式', 'color: #3b82f6; font-size: 16px; font-weight: bold;');
    console.log('%c使用 testUtils.injectTestBar() 来注入测试导航bar', 'color: #6b7280;');
    console.log('%c使用 testUtils.removeTestBar() 来移除测试导航bar', 'color: #6b7280;');
  }

  /**
   * 禁用测试模式
   */
  disableTestMode() {
    this.isTestMode = false;
    this.removeTestBar();
    console.log('🧪 测试模式已禁用');
  }

  /**
   * 动态注入测试导航bar
   */
  injectTestBar() {
    if (!this.isTestMode) {
      console.warn('请先启用测试模式: testUtils.enableTestMode()');
      return;
    }

    // 如果已经存在，先移除
    this.removeTestBar();

    // 查找PRD评估页面的容器
    const prdContainer = document.querySelector('[class*="PRDEvaluation"], .h-full.bg-gray-50');
    if (!prdContainer) {
      console.warn('未找到PRD评估页面容器');
      return;
    }

    // 创建测试bar元素
    const testBar = document.createElement('div');
    testBar.id = 'test-navigation-bar';
    testBar.className = 'bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 mx-4';
    testBar.innerHTML = `
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
          <div>
            <h3 class="text-sm font-medium text-blue-800">
              🧪 快速测试导航功能
            </h3>
            <p class="text-sm text-blue-700 mt-1">
              点击此按钮可直接跳转到评审结果页面，测试章节导航功能
            </p>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <button id="enable-test-mode-btn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm transition-colors">
            启用测试模式
          </button>
          <button id="close-test-bar-btn" class="bg-gray-500 hover:bg-gray-600 text-white px-2 py-2 rounded-md text-sm transition-colors" title="关闭测试bar">
            ✕
          </button>
        </div>
      </div>
    `;

    // 插入到页面顶部
    const firstChild = prdContainer.firstElementChild;
    if (firstChild) {
      prdContainer.insertBefore(testBar, firstChild);
    } else {
      prdContainer.appendChild(testBar);
    }

    this.testBarElement = testBar;

    // 绑定事件
    this.bindTestBarEvents();

    console.log('✅ 测试导航bar已注入');
  }

  /**
   * 绑定测试bar的事件
   */
  bindTestBarEvents() {
    if (!this.testBarElement) return;

    // 启用测试模式按钮
    const enableBtn = this.testBarElement.querySelector('#enable-test-mode-btn');
    if (enableBtn) {
      enableBtn.addEventListener('click', () => {
        this.triggerTestMode();
      });
    }

    // 关闭按钮
    const closeBtn = this.testBarElement.querySelector('#close-test-bar-btn');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.removeTestBar();
      });
    }
  }

  /**
   * 触发测试模式（模拟原来的enableTestMode功能）
   */
  triggerTestMode() {
    console.log('🚀 触发测试模式...');
    
    // 模拟点击评审结果tab
    const resultsTab = document.querySelector('[data-tab="results"], button[class*="results"]');
    if (resultsTab) {
      resultsTab.click();
      console.log('✅ 已切换到评审结果tab');
    } else {
      console.warn('未找到评审结果tab按钮');
    }

    // 移除测试bar
    this.removeTestBar();
  }

  /**
   * 移除测试导航bar
   */
  removeTestBar() {
    if (this.testBarElement) {
      this.testBarElement.remove();
      this.testBarElement = null;
      console.log('🗑️ 测试导航bar已移除');
    }
  }

  /**
   * 检查是否在测试环境中
   */
  isInTestEnvironment() {
    return process.env.NODE_ENV === 'test' || 
           window.location.hostname === 'localhost' ||
           window.location.search.includes('test=true');
  }

  /**
   * 自动检测并提供测试功能
   */
  autoDetectAndSetup() {
    if (this.isInTestEnvironment()) {
      console.log('🔍 检测到测试环境');
      console.log('💡 使用以下命令启用测试功能:');
      console.log('   testUtils.enableTestMode()');
      console.log('   testUtils.injectTestBar()');
      
      // 将testUtils暴露到全局，方便在控制台使用
      window.testUtils = this;
    }
  }

  /**
   * 生成测试报告
   */
  generateTestReport() {
    const report = {
      testMode: this.isTestMode,
      testBarInjected: !!this.testBarElement,
      environment: this.isInTestEnvironment() ? 'test' : 'production',
      timestamp: new Date().toISOString(),
      availableCommands: [
        'testUtils.enableTestMode()',
        'testUtils.injectTestBar()',
        'testUtils.removeTestBar()',
        'testUtils.disableTestMode()'
      ]
    };

    console.table(report);
    return report;
  }
}

// 创建全局实例
const testUtils = new TestUtils();

// 自动检测环境
testUtils.autoDetectAndSetup();

export default testUtils;
