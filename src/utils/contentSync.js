/**
 * 内容同步工具
 * 处理章节数据和Markdown之间的双向转换
 */

// 将章节数据转换为Markdown格式
export const generateMarkdownFromSections = (sections) => {
  if (!sections || sections.length === 0) {
    return '';
  }

  return sections.map(section => {
    // 根据层级生成标题前缀
    const titlePrefix = '#'.repeat(Math.max(1, section.level + 1));
    
    // 处理标题
    const title = section.title || '';
    
    // 处理内容
    const content = section.content || '';
    
    // 组合标题和内容
    if (section.level === 0) {
      // 封面页特殊处理
      return `${titlePrefix} ${title}\n\n${content}\n\n`;
    } else {
      return `${titlePrefix} ${title}\n\n${content}\n\n`;
    }
  }).join('');
};

// 从章节数据生成大纲
export const generateOutlineFromSections = (sections) => {
  if (!sections || sections.length === 0) {
    return [];
  }

  return sections.map((section, index) => ({
    id: section.id,
    text: section.title || `章节 ${index + 1}`,
    level: Math.max(1, section.level + 1), // 调整级别，确保从1开始
    line: index + 1 // 简化处理，使用索引作为行号
  }));
};

// 将Markdown解析为章节数据（简化版本）
export const parseMarkdownToSections = (markdown, existingSections = []) => {
  if (!markdown) {
    return existingSections;
  }

  const lines = markdown.split('\n');
  const sections = [];
  let currentSection = null;
  let currentContent = [];
  let sectionId = 1;

  lines.forEach((line, index) => {
    const trimmedLine = line.trim();
    
    // 检查是否是标题行
    const titleMatch = trimmedLine.match(/^(#{1,6})\s+(.+)$/);
    
    if (titleMatch) {
      // 保存当前章节
      if (currentSection) {
        currentSection.content = currentContent.join('\n').trim();
        sections.push(currentSection);
      }
      
      // 创建新章节
      const level = Math.max(0, titleMatch[1].length - 1); // 调整级别
      currentSection = {
        id: `section-${sectionId}`,
        title: titleMatch[2],
        level: level,
        content: ''
      };
      currentContent = [];
      sectionId++;
    } else if (trimmedLine) {
      // 添加到当前章节内容
      currentContent.push(line);
    } else if (currentContent.length > 0) {
      // 保留空行
      currentContent.push('');
    }
  });

  // 保存最后一个章节
  if (currentSection) {
    currentSection.content = currentContent.join('\n').trim();
    sections.push(currentSection);
  }

  return sections.length > 0 ? sections : existingSections;
};

// 更新章节内容
export const updateSectionContent = (sections, sectionId, field, value) => {
  return sections.map(section => {
    if (section.id === sectionId) {
      if (field === 'cell') {
        // 处理表格单元格编辑
        const lines = section.content.split('\n');
        const tableLines = lines.filter(line => line.includes('|'));
        
        if (tableLines[value.rowIndex]) {
          const cells = tableLines[value.rowIndex].split('|');
          if (cells[value.cellIndex + 1]) { // +1 因为第一个元素通常是空的
            cells[value.cellIndex + 1] = ` ${value.value} `;
            tableLines[value.rowIndex] = cells.join('|');
            
            // 重新组装内容
            let tableLineIndex = 0;
            const newLines = lines.map(line => {
              if (line.includes('|')) {
                return tableLines[tableLineIndex++];
              }
              return line;
            });
            
            return { ...section, content: newLines.join('\n') };
          }
        }
        return section;
      } else {
        // 处理普通字段编辑
        return { ...section, [field]: value };
      }
    }
    return section;
  });
};

// 验证章节数据结构
export const validateSections = (sections) => {
  if (!Array.isArray(sections)) {
    return false;
  }

  return sections.every(section => {
    return (
      section &&
      typeof section === 'object' &&
      typeof section.id === 'string' &&
      typeof section.title === 'string' &&
      typeof section.level === 'number' &&
      typeof section.content === 'string'
    );
  });
};

// 获取章节统计信息
export const getSectionStats = (sections) => {
  if (!sections || sections.length === 0) {
    return {
      totalSections: 0,
      totalCharacters: 0,
      levelDistribution: {},
      hasTableContent: false
    };
  }

  const stats = {
    totalSections: sections.length,
    totalCharacters: 0,
    levelDistribution: {},
    hasTableContent: false
  };

  sections.forEach(section => {
    // 统计字符数
    stats.totalCharacters += (section.title?.length || 0) + (section.content?.length || 0);

    // 统计层级分布
    const level = section.level;
    stats.levelDistribution[level] = (stats.levelDistribution[level] || 0) + 1;

    // 检查是否有表格内容
    if (section.content && section.content.includes('|')) {
      stats.hasTableContent = true;
    }
  });

  return stats;
};

// ==================== 基础章节管理操作函数 ====================

// 新增章节
export const addSection = (sections, newSection, position = 'end') => {
  if (!sections || !newSection) {
    console.error('addSection: 无效的参数');
    return sections || [];
  }

  const updatedSections = [...sections];

  if (position === 'end') {
    updatedSections.push(newSection);
  } else if (typeof position === 'number' && position >= 0 && position <= sections.length) {
    updatedSections.splice(position, 0, newSection);
  } else {
    // 默认添加到末尾
    updatedSections.push(newSection);
  }

  return updatedSections;
};

// 删除章节
export const deleteSection = (sections, sectionId) => {
  if (!sections || !sectionId) {
    console.error('deleteSection: 无效的参数');
    return sections || [];
  }

  return sections.filter(section => section.id !== sectionId);
};

// 上移章节
export const moveSectionUp = (sections, sectionId) => {
  if (!sections || !sectionId) {
    console.error('moveSectionUp: 无效的参数');
    return sections || [];
  }

  const index = sections.findIndex(s => s.id === sectionId);
  if (index > 0) {
    const newSections = [...sections];
    [newSections[index - 1], newSections[index]] =
    [newSections[index], newSections[index - 1]];
    return newSections;
  }
  return sections;
};

// 下移章节
export const moveSectionDown = (sections, sectionId) => {
  if (!sections || !sectionId) {
    console.error('moveSectionDown: 无效的参数');
    return sections || [];
  }

  const index = sections.findIndex(s => s.id === sectionId);
  if (index >= 0 && index < sections.length - 1) {
    const newSections = [...sections];
    [newSections[index], newSections[index + 1]] =
    [newSections[index + 1], newSections[index]];
    return newSections;
  }
  return sections;
};

// 调整章节层级
export const updateSectionLevel = (sections, sectionId, newLevel) => {
  if (!sections || !sectionId || typeof newLevel !== 'number') {
    console.error('updateSectionLevel: 无效的参数');
    return sections || [];
  }

  return sections.map(section =>
    section.id === sectionId
      ? { ...section, level: Math.max(0, Math.min(3, newLevel)) }
      : section
  );
};

// 复制章节
export const duplicateSection = (sections, sectionId) => {
  if (!sections || !sectionId) {
    console.error('duplicateSection: 无效的参数');
    return sections || [];
  }

  const index = sections.findIndex(s => s.id === sectionId);
  if (index !== -1) {
    const originalSection = sections[index];
    const newSection = {
      ...originalSection,
      id: `section-${Date.now()}`,
      title: `${originalSection.title} (副本)`
    };
    const newSections = [...sections];
    newSections.splice(index + 1, 0, newSection);
    return newSections;
  }
  return sections;
};

// 重新排序章节
export const reorderSections = (sections, fromIndex, toIndex) => {
  if (!sections || typeof fromIndex !== 'number' || typeof toIndex !== 'number') {
    console.error('reorderSections: 无效的参数');
    return sections || [];
  }

  if (fromIndex < 0 || fromIndex >= sections.length ||
      toIndex < 0 || toIndex >= sections.length ||
      fromIndex === toIndex) {
    return sections;
  }

  const newSections = [...sections];
  const [movedSection] = newSections.splice(fromIndex, 1);
  newSections.splice(toIndex, 0, movedSection);
  return newSections;
};
