/**
 * 内容缓存管理器
 * 提供高效的内容缓存、LRU清理和持久化存储
 */

/**
 * LRU缓存实现
 */
class LRUCache {
  constructor(maxSize = 100) {
    this.maxSize = maxSize;
    this.cache = new Map();
  }

  get(key) {
    if (this.cache.has(key)) {
      // 移动到最前面（最近使用）
      const value = this.cache.get(key);
      this.cache.delete(key);
      this.cache.set(key, value);
      return value;
    }
    return null;
  }

  set(key, value) {
    if (this.cache.has(key)) {
      // 更新现有项
      this.cache.delete(key);
    } else if (this.cache.size >= this.maxSize) {
      // 删除最久未使用的项
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, value);
  }

  has(key) {
    return this.cache.has(key);
  }

  delete(key) {
    return this.cache.delete(key);
  }

  clear() {
    this.cache.clear();
  }

  size() {
    return this.cache.size;
  }

  keys() {
    return Array.from(this.cache.keys());
  }
}

/**
 * 内容缓存管理器
 */
class ContentCacheManager {
  constructor(options = {}) {
    this.maxCacheSize = options.maxCacheSize || 100;
    this.ttl = options.ttl || 5 * 60 * 1000; // 5分钟默认TTL
    this.enablePersistence = options.enablePersistence || false;
    
    // 不同类型的缓存
    this.caches = {
      content: new LRUCache(this.maxCacheSize),
      sections: new LRUCache(this.maxCacheSize),
      outline: new LRUCache(this.maxCacheSize),
      analysis: new LRUCache(this.maxCacheSize),
      rendered: new LRUCache(this.maxCacheSize)
    };
    
    // TTL管理
    this.ttlTimers = new Map();
    
    // 统计信息
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      evictions: 0
    };

    // 如果启用持久化，从localStorage加载
    if (this.enablePersistence) {
      this.loadFromStorage();
    }
  }

  /**
   * 生成缓存键
   */
  generateKey(type, identifier, version = null) {
    const baseKey = `${type}:${identifier}`;
    return version ? `${baseKey}:${version}` : baseKey;
  }

  /**
   * 获取缓存项
   */
  get(type, identifier, version = null) {
    const key = this.generateKey(type, identifier, version);
    const cache = this.caches[type];
    
    if (!cache) {
      return null;
    }

    const item = cache.get(key);
    
    if (item) {
      // 检查TTL
      if (this.isExpired(item)) {
        this.delete(type, identifier, version);
        this.stats.misses++;
        return null;
      }
      
      this.stats.hits++;
      return item.data;
    }
    
    this.stats.misses++;
    return null;
  }

  /**
   * 设置缓存项
   */
  set(type, identifier, data, version = null, customTTL = null) {
    const key = this.generateKey(type, identifier, version);
    const cache = this.caches[type];
    
    if (!cache) {
      return false;
    }

    const ttl = customTTL || this.ttl;
    const item = {
      data,
      timestamp: Date.now(),
      ttl,
      key
    };

    // 清除旧的TTL定时器
    if (this.ttlTimers.has(key)) {
      clearTimeout(this.ttlTimers.get(key));
    }

    // 设置新的TTL定时器
    if (ttl > 0) {
      const timer = setTimeout(() => {
        this.delete(type, identifier, version);
      }, ttl);
      this.ttlTimers.set(key, timer);
    }

    cache.set(key, item);
    this.stats.sets++;

    // 持久化到localStorage
    if (this.enablePersistence) {
      this.saveToStorage();
    }

    return true;
  }

  /**
   * 删除缓存项
   */
  delete(type, identifier, version = null) {
    const key = this.generateKey(type, identifier, version);
    const cache = this.caches[type];
    
    if (!cache) {
      return false;
    }

    // 清除TTL定时器
    if (this.ttlTimers.has(key)) {
      clearTimeout(this.ttlTimers.get(key));
      this.ttlTimers.delete(key);
    }

    const deleted = cache.delete(key);
    if (deleted) {
      this.stats.evictions++;
    }

    return deleted;
  }

  /**
   * 检查项目是否过期
   */
  isExpired(item) {
    if (item.ttl <= 0) {
      return false; // 永不过期
    }
    return Date.now() - item.timestamp > item.ttl;
  }

  /**
   * 清理过期项目
   */
  cleanup() {
    let cleanedCount = 0;
    
    Object.entries(this.caches).forEach(([type, cache]) => {
      const keys = cache.keys();
      keys.forEach(key => {
        const item = cache.get(key);
        if (item && this.isExpired(item)) {
          cache.delete(key);
          if (this.ttlTimers.has(key)) {
            clearTimeout(this.ttlTimers.get(key));
            this.ttlTimers.delete(key);
          }
          cleanedCount++;
        }
      });
    });

    return cleanedCount;
  }

  /**
   * 清空所有缓存
   */
  clear(type = null) {
    if (type) {
      const cache = this.caches[type];
      if (cache) {
        cache.clear();
      }
    } else {
      Object.values(this.caches).forEach(cache => cache.clear());
      
      // 清除所有TTL定时器
      this.ttlTimers.forEach(timer => clearTimeout(timer));
      this.ttlTimers.clear();
    }

    // 重置统计
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      evictions: 0
    };
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests * 100).toFixed(2) : 0;
    
    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      totalRequests,
      cacheSize: Object.entries(this.caches).reduce((total, [type, cache]) => {
        total[type] = cache.size();
        return total;
      }, {})
    };
  }

  /**
   * 保存到localStorage
   */
  saveToStorage() {
    if (!this.enablePersistence) return;
    
    try {
      const data = {};
      Object.entries(this.caches).forEach(([type, cache]) => {
        data[type] = Array.from(cache.cache.entries());
      });
      
      localStorage.setItem('contentCache', JSON.stringify({
        data,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.warn('Failed to save cache to localStorage:', error);
    }
  }

  /**
   * 从localStorage加载
   */
  loadFromStorage() {
    if (!this.enablePersistence) return;
    
    try {
      const stored = localStorage.getItem('contentCache');
      if (!stored) return;
      
      const { data, timestamp } = JSON.parse(stored);
      
      // 检查存储的数据是否过期（24小时）
      if (Date.now() - timestamp > 24 * 60 * 60 * 1000) {
        localStorage.removeItem('contentCache');
        return;
      }
      
      Object.entries(data).forEach(([type, entries]) => {
        const cache = this.caches[type];
        if (cache) {
          entries.forEach(([key, item]) => {
            if (!this.isExpired(item)) {
              cache.cache.set(key, item);
            }
          });
        }
      });
    } catch (error) {
      console.warn('Failed to load cache from localStorage:', error);
      localStorage.removeItem('contentCache');
    }
  }
}

// 创建全局缓存管理器实例
export const contentCacheManager = new ContentCacheManager({
  maxCacheSize: 200,
  ttl: 10 * 60 * 1000, // 10分钟
  enablePersistence: true
});

// 定期清理过期缓存
setInterval(() => {
  contentCacheManager.cleanup();
}, 60 * 1000); // 每分钟清理一次

export { LRUCache, ContentCacheManager };
export default contentCacheManager;
