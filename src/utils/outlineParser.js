export const generateOutline = (content) => {
  console.log('=== 开始生成大纲 ===');
  console.log('输入内容长度:', content ? content.length : 0);
  
  if (!content) {
    console.log('内容为空，返回空大纲');
    return [];
  }

  const lines = content.split('\n');
  console.log('内容行数:', lines.length);
  
  const outline = [];
  let headingIndex = 0;
  
  lines.forEach((line, lineIndex) => {
    const match = line.match(/^(#{1,3})\s+(.+)$/);
    if (match) {
      const level = match[1].length;
      const text = match[2].trim();
      const id = `heading-${headingIndex}-${text.toLowerCase().replace(/[^a-z0-9]/g, '-')}`;
      
      console.log(`大纲项 ${headingIndex + 1}:`, { 
        lineIndex,
        line: line.trim(),
        level, 
        text, 
        headingIndex, 
        id 
      });
      
      outline.push({
        id,
        level,
        text,
        line: headingIndex,
        element: null // 将在DOM中找到对应元素时填充
      });
      
      headingIndex++;
    }
  });
  
  console.log('生成的大纲总数:', outline.length);
  console.log('完整大纲结构:', outline);
  
  // 在生成大纲后立即检查 DOM 中的标题元素
  setTimeout(() => {
    console.log('=== 检查DOM中的标题元素（来自outlineParser） ===');
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    console.log('DOM中标题数量:', headings.length);
    
    Array.from(headings).forEach((h, index) => {
      console.log(`DOM标题 ${index + 1}:`, {
        id: h.id,
        text: h.textContent.trim(),
        tagName: h.tagName,
        hasId: !!h.id,
        outerHTML: h.outerHTML.substring(0, 100)
      });
    });
    
    // 检查大纲项与DOM元素的匹配情况
    console.log('=== 检查大纲与DOM的匹配情况 ===');
    outline.forEach((item, index) => {
      const domElement = document.getElementById(item.id);
      console.log(`匹配检查 ${index + 1}:`, {
        outlineId: item.id,
        outlineText: item.text,
        domElementFound: !!domElement,
        domElementText: domElement ? domElement.textContent.trim() : null
      });
    });
    
    console.log('=== DOM检查完成（来自outlineParser） ===');
  }, 200); // 稍微延长延迟时间
  
  console.log('=== 大纲生成完成 ===');
  return outline;
};

export const findHeadingInDOM = (outline) => {
  console.log('=== 开始在DOM中查找标题元素 ===');
  
  const result = outline.map((item, index) => {
    const element = document.getElementById(item.id);
    console.log(`查找标题 ${index + 1}:`, {
      id: item.id,
      text: item.text,
      elementFound: !!element
    });
    return { ...item, element };
  });
  
  console.log('=== DOM查找完成 ===');
  return result;
};

export const scrollToHeading = (headingId) => {
  console.log('=== scrollToHeading 被调用 ===');
  console.log('目标ID:', headingId);
  
  const element = document.getElementById(headingId);
  console.log('找到的元素:', element);
  
  if (element) {
    console.log('开始滚动到元素...');
    element.scrollIntoView({ 
      behavior: 'smooth', 
      block: 'start' 
    });
    
    // 高亮效果
    console.log('添加高亮效果...');
    element.classList.add('highlight-pulse');
    setTimeout(() => {
      element.classList.remove('highlight-pulse');
      console.log('移除高亮效果');
    }, 2000);
  } else {
    console.log('未找到目标元素，无法滚动');
  }
  
  console.log('=== scrollToHeading 完成 ===');
}; 