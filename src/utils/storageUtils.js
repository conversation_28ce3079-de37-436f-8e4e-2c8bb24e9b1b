const STORAGE_KEY = 'prd-ai-editor-content';
const METADATA_KEY = 'prd-ai-editor-metadata';

export const saveContent = async (content) => {
  try {
    const metadata = {
      lastSaved: new Date().toISOString(),
      wordCount: content.split(/\s+/).filter(word => word.length > 0).length,
      characterCount: content.length,
      lineCount: content.split('\n').length
    };
    
    localStorage.setItem(STORAGE_KEY, content);
    localStorage.setItem(METADATA_KEY, JSON.stringify(metadata));
    
    return metadata;
  } catch (error) {
    throw new Error('保存内容失败: ' + error.message);
  }
};

export const loadContent = () => {
  try {
    return localStorage.getItem(STORAGE_KEY);
  } catch (error) {
    console.error('加载内容失败:', error);
    return null;
  }
};

export const getMetadata = () => {
  try {
    const metadata = localStorage.getItem(METADATA_KEY);
    return metadata ? JSON.parse(metadata) : null;
  } catch (error) {
    console.error('加载元数据失败:', error);
    return null;
  }
};

export const clearStorage = () => {
  try {
    localStorage.removeItem(STORAGE_KEY);
    localStorage.removeItem(METADATA_KEY);
    return true;
  } catch (error) {
    console.error('清除存储失败:', error);
    return false;
  }
};

export const exportContent = (content, filename = 'prd-document.md') => {
  try {
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
    return true;
  } catch (error) {
    console.error('导出失败:', error);
    return false;
  }
}; 