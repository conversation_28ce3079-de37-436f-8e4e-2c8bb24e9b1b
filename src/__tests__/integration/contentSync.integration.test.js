/**
 * 内容同步集成测试
 * 测试编辑器、章节管理和内容同步的集成功能
 */

import { TestDataGenerator, TestEnvironment } from '../../utils/testHelpers';
import { parseMarkdownToSections, generateMarkdownFromSections } from '../../utils/contentSync';
import { contentCacheManager } from '../../utils/cacheManager';
import { globalBatchUpdater } from '../../utils/performanceUtils';

// 设置测试环境
beforeAll(() => {
  TestEnvironment.setupBrowserMocks();
});

beforeEach(() => {
  // 清理缓存
  contentCacheManager.clear();
  globalBatchUpdater.clear();
});

afterEach(() => {
  TestEnvironment.cleanupAfterTest();
});

describe('内容同步集成测试', () => {
  describe('Markdown解析和生成的往返测试', () => {
    test('应该能够完整地往返转换简单内容', () => {
      const originalMarkdown = `# 产品需求文档

## 1. 概述
这是产品的概述部分。

### 1.1 目标
产品的主要目标。

## 2. 功能需求
详细的功能需求描述。

### 2.1 用户管理
用户管理相关功能。

#### 2.1.1 用户注册
用户注册流程。`;

      // 解析为章节
      const sections = parseMarkdownToSections(originalMarkdown);
      
      // 验证解析结果
      expect(sections).toHaveLength(6);
      expect(sections[0].title).toBe('产品需求文档');
      expect(sections[0].level).toBe(0);
      expect(sections[5].title).toBe('2.1.1 用户注册');
      expect(sections[5].level).toBe(3);
      
      // 重新生成Markdown
      const regeneratedMarkdown = generateMarkdownFromSections(sections);
      
      // 再次解析
      const sectionsAgain = parseMarkdownToSections(regeneratedMarkdown);
      
      // 验证往返转换的一致性
      expect(sectionsAgain).toHaveLength(sections.length);
      sectionsAgain.forEach((section, index) => {
        expect(section.title).toBe(sections[index].title);
        expect(section.level).toBe(sections[index].level);
        expect(section.content.trim()).toBe(sections[index].content.trim());
      });
    });

    test('应该能够处理复杂的Markdown内容', () => {
      const complexMarkdown = `# 复杂文档测试

## 包含代码块的章节
\`\`\`javascript
function test() {
  return "hello world";
}
\`\`\`

## 包含表格的章节
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |

## 包含列表的章节
- 项目1
- 项目2
  - 子项目1
  - 子项目2

### 包含引用的子章节
> 这是一个引用块
> 包含多行内容

## 包含链接和图片的章节
[链接文本](https://example.com)
![图片描述](image.png)`;

      const sections = parseMarkdownToSections(complexMarkdown);
      const regeneratedMarkdown = generateMarkdownFromSections(sections);
      const sectionsAgain = parseMarkdownToSections(regeneratedMarkdown);
      
      expect(sectionsAgain).toHaveLength(sections.length);
      
      // 验证代码块内容保持完整
      const codeSection = sections.find(s => s.title === '包含代码块的章节');
      expect(codeSection.content).toContain('function test()');
      expect(codeSection.content).toContain('return "hello world"');
      
      // 验证表格内容保持完整
      const tableSection = sections.find(s => s.title === '包含表格的章节');
      expect(tableSection.content).toContain('| 列1 | 列2 | 列3 |');
      expect(tableSection.content).toContain('| 数据1 | 数据2 | 数据3 |');
    });
  });

  describe('缓存集成测试', () => {
    test('应该能够缓存解析结果', () => {
      const markdown = TestDataGenerator.generateMarkdownContent(10);
      
      // 第一次解析
      const startTime1 = performance.now();
      const sections1 = parseMarkdownToSections(markdown);
      const endTime1 = performance.now();
      const duration1 = endTime1 - startTime1;
      
      // 手动缓存结果
      const cacheKey = btoa(markdown).substring(0, 32);
      contentCacheManager.set('sections', cacheKey, sections1);
      
      // 从缓存获取
      const startTime2 = performance.now();
      const cachedSections = contentCacheManager.get('sections', cacheKey);
      const endTime2 = performance.now();
      const duration2 = endTime2 - startTime2;
      
      expect(cachedSections).toEqual(sections1);
      expect(duration2).toBeLessThan(duration1); // 缓存应该更快
    });

    test('应该能够处理缓存过期', async () => {
      const markdown = '# 测试标题\n测试内容';
      const cacheKey = 'test-key';
      
      // 设置短TTL的缓存
      contentCacheManager.set('sections', cacheKey, { test: 'data' }, null, 50);
      
      // 立即获取应该成功
      expect(contentCacheManager.get('sections', cacheKey)).toEqual({ test: 'data' });
      
      // 等待过期
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 过期后应该返回null
      expect(contentCacheManager.get('sections', cacheKey)).toBeNull();
    });
  });

  describe('性能集成测试', () => {
    test('应该能够高效处理大文档', () => {
      const largeMarkdown = TestDataGenerator.generateLargeDocument(200);
      
      const startTime = performance.now();
      
      // 解析大文档
      const sections = parseMarkdownToSections(largeMarkdown);
      
      // 生成大纲
      const outline = sections.map(section => ({
        id: section.id,
        title: section.title,
        level: section.level
      }));
      
      // 重新生成Markdown
      const regeneratedMarkdown = generateMarkdownFromSections(sections);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(sections.length).toBeGreaterThan(100);
      expect(outline.length).toBe(sections.length);
      expect(regeneratedMarkdown.length).toBeGreaterThan(10000);
      expect(duration).toBeLessThan(2000); // 应该在2秒内完成
    });

    test('应该能够处理批量更新', async () => {
      const batchUpdater = globalBatchUpdater;
      const updates = [];
      
      // 添加多个更新操作
      for (let i = 0; i < 10; i++) {
        batchUpdater.add(() => {
          updates.push(i);
        });
      }
      
      // 等待批量执行
      await new Promise(resolve => setTimeout(resolve, 50));
      
      expect(updates).toHaveLength(10);
      expect(updates).toEqual([0, 1, 2, 3, 4, 5, 6, 7, 8, 9]);
    });
  });

  describe('错误处理集成测试', () => {
    test('应该能够处理格式错误的Markdown', () => {
      const malformedMarkdown = `# 正常标题

## 缺少空格的标题
正常内容

###不规范的标题
内容

# 另一个正常标题
更多内容`;

      // 应该能够解析，即使格式不完美
      const sections = parseMarkdownToSections(malformedMarkdown);
      
      expect(sections.length).toBeGreaterThan(0);
      
      // 验证能够重新生成
      const regenerated = generateMarkdownFromSections(sections);
      expect(regenerated.length).toBeGreaterThan(0);
    });

    test('应该能够处理空内容和边界情况', () => {
      const testCases = [
        '', // 空字符串
        '   ', // 只有空格
        '\n\n\n', // 只有换行符
        '# ', // 只有标题标记
        '## \n\n', // 空标题
        'no headers here', // 没有标题的内容
      ];
      
      testCases.forEach(testCase => {
        expect(() => {
          const sections = parseMarkdownToSections(testCase);
          generateMarkdownFromSections(sections);
        }).not.toThrow();
      });
    });
  });

  describe('内存管理集成测试', () => {
    test('应该能够处理大量操作而不泄漏内存', () => {
      const initialMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
      
      // 执行大量操作
      for (let i = 0; i < 100; i++) {
        const markdown = TestDataGenerator.generateMarkdownContent(20);
        const sections = parseMarkdownToSections(markdown);
        generateMarkdownFromSections(sections);
        
        // 定期清理缓存
        if (i % 20 === 0) {
          contentCacheManager.cleanup();
        }
      }
      
      // 强制垃圾回收（如果可用）
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
      const memoryGrowth = finalMemory - initialMemory;
      
      // 内存增长应该在合理范围内（小于50MB）
      expect(memoryGrowth).toBeLessThan(50 * 1024 * 1024);
    });
  });

  describe('并发处理集成测试', () => {
    test('应该能够处理并发的内容同步操作', async () => {
      const promises = [];
      
      // 创建多个并发操作
      for (let i = 0; i < 10; i++) {
        const promise = new Promise(resolve => {
          setTimeout(() => {
            const markdown = TestDataGenerator.generateMarkdownContent(10);
            const sections = parseMarkdownToSections(markdown);
            const regenerated = generateMarkdownFromSections(sections);
            resolve({ sections, regenerated });
          }, Math.random() * 100);
        });
        promises.push(promise);
      }
      
      // 等待所有操作完成
      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(result.sections.length).toBeGreaterThan(0);
        expect(result.regenerated.length).toBeGreaterThan(0);
      });
    });
  });
});
