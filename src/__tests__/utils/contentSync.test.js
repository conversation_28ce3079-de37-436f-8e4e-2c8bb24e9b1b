/**
 * 内容同步工具单元测试
 */

import {
  parseMarkdownToSections,
  generateMarkdownFromSections,
  generateOutlineFromSections,
  updateSectionContent
} from '../../utils/contentSync';
import { TestDataGenerator } from '../../utils/testHelpers';

describe('contentSync utils', () => {
  describe('parseMarkdownToSections', () => {
    test('应该正确解析简单的Markdown内容', () => {
      const markdown = `# 第一章
这是第一章的内容。

## 1.1 子章节
这是子章节的内容。

# 第二章
这是第二章的内容。`;

      const sections = parseMarkdownToSections(markdown);
      
      expect(sections).toHaveLength(3);
      expect(sections[0].title).toBe('第一章');
      expect(sections[0].level).toBe(0);
      expect(sections[1].title).toBe('1.1 子章节');
      expect(sections[1].level).toBe(1);
      expect(sections[2].title).toBe('第二章');
      expect(sections[2].level).toBe(0);
    });

    test('应该处理空内容', () => {
      const sections = parseMarkdownToSections('');
      expect(sections).toHaveLength(0);
    });

    test('应该处理没有标题的内容', () => {
      const markdown = '这是一段没有标题的内容。';
      const sections = parseMarkdownToSections(markdown);
      expect(sections).toHaveLength(0);
    });

    test('应该正确处理多级标题', () => {
      const markdown = `# H1标题
## H2标题
### H3标题
#### H4标题
##### H5标题
###### H6标题`;

      const sections = parseMarkdownToSections(markdown);
      
      expect(sections).toHaveLength(6);
      expect(sections[0].level).toBe(0); // H1
      expect(sections[1].level).toBe(1); // H2
      expect(sections[2].level).toBe(2); // H3
      expect(sections[3].level).toBe(3); // H4
      expect(sections[4].level).toBe(3); // H5 -> H4 (最大4级)
      expect(sections[5].level).toBe(3); // H6 -> H4 (最大4级)
    });
  });

  describe('generateMarkdownFromSections', () => {
    test('应该正确生成Markdown内容', () => {
      const sections = [
        {
          id: 'section-1',
          title: '第一章',
          level: 0,
          content: '这是第一章的内容。'
        },
        {
          id: 'section-2',
          title: '1.1 子章节',
          level: 1,
          content: '这是子章节的内容。'
        }
      ];

      const markdown = generateMarkdownFromSections(sections);
      
      expect(markdown).toContain('# 第一章');
      expect(markdown).toContain('## 1.1 子章节');
      expect(markdown).toContain('这是第一章的内容。');
      expect(markdown).toContain('这是子章节的内容。');
    });

    test('应该处理空章节数组', () => {
      const markdown = generateMarkdownFromSections([]);
      expect(markdown).toBe('');
    });

    test('应该正确处理不同级别的标题', () => {
      const sections = [
        { id: '1', title: 'H1', level: 0, content: 'content1' },
        { id: '2', title: 'H2', level: 1, content: 'content2' },
        { id: '3', title: 'H3', level: 2, content: 'content3' },
        { id: '4', title: 'H4', level: 3, content: 'content4' }
      ];

      const markdown = generateMarkdownFromSections(sections);
      
      expect(markdown).toContain('# H1');
      expect(markdown).toContain('## H2');
      expect(markdown).toContain('### H3');
      expect(markdown).toContain('#### H4');
    });
  });

  describe('generateOutlineFromSections', () => {
    test('应该正确生成大纲', () => {
      const sections = [
        { id: '1', title: '概述', level: 0, content: '...' },
        { id: '2', title: '功能需求', level: 0, content: '...' },
        { id: '3', title: '用户界面', level: 1, content: '...' },
        { id: '4', title: 'API接口', level: 1, content: '...' }
      ];

      const outline = generateOutlineFromSections(sections);
      
      expect(outline).toHaveLength(4);
      expect(outline[0].title).toBe('概述');
      expect(outline[0].level).toBe(0);
      expect(outline[2].title).toBe('用户界面');
      expect(outline[2].level).toBe(1);
    });

    test('应该处理空章节数组', () => {
      const outline = generateOutlineFromSections([]);
      expect(outline).toHaveLength(0);
    });
  });

  describe('updateSectionContent', () => {
    test('应该正确更新章节内容', () => {
      const sections = [
        { id: '1', title: '章节1', level: 0, content: '原始内容' },
        { id: '2', title: '章节2', level: 0, content: '其他内容' }
      ];

      const updatedSections = updateSectionContent(sections, '1', '更新后的内容');
      
      expect(updatedSections[0].content).toBe('更新后的内容');
      expect(updatedSections[1].content).toBe('其他内容'); // 其他章节不变
    });

    test('应该处理不存在的章节ID', () => {
      const sections = [
        { id: '1', title: '章节1', level: 0, content: '原始内容' }
      ];

      const updatedSections = updateSectionContent(sections, 'nonexistent', '新内容');
      
      expect(updatedSections).toEqual(sections); // 应该返回原数组
    });
  });

  describe('性能测试', () => {
    test('应该能够处理大文档', () => {
      const largeMarkdown = TestDataGenerator.generateLargeDocument(100);
      
      const startTime = performance.now();
      const sections = parseMarkdownToSections(largeMarkdown);
      const endTime = performance.now();
      
      const processingTime = endTime - startTime;
      
      expect(sections.length).toBeGreaterThan(0);
      expect(processingTime).toBeLessThan(1000); // 应该在1秒内完成
    });

    test('应该能够快速生成大文档的Markdown', () => {
      const largeSections = TestDataGenerator.generateSectionData(100);
      
      const startTime = performance.now();
      const markdown = generateMarkdownFromSections(largeSections);
      const endTime = performance.now();
      
      const processingTime = endTime - startTime;
      
      expect(markdown.length).toBeGreaterThan(0);
      expect(processingTime).toBeLessThan(500); // 应该在0.5秒内完成
    });
  });

  describe('边界情况测试', () => {
    test('应该处理包含特殊字符的标题', () => {
      const markdown = `# 标题 with "quotes" & symbols
## 子标题 <with> [brackets]
### 另一个 *标题* _with_ emphasis`;

      const sections = parseMarkdownToSections(markdown);
      
      expect(sections).toHaveLength(3);
      expect(sections[0].title).toBe('标题 with "quotes" & symbols');
      expect(sections[1].title).toBe('子标题 <with> [brackets]');
      expect(sections[2].title).toBe('另一个 *标题* _with_ emphasis');
    });

    test('应该处理非常长的内容', () => {
      const longContent = 'a'.repeat(10000);
      const markdown = `# 长内容测试\n${longContent}`;
      
      const sections = parseMarkdownToSections(markdown);
      
      expect(sections).toHaveLength(1);
      expect(sections[0].content.length).toBe(longContent.length);
    });

    test('应该处理混合的换行符', () => {
      const markdown = "# 标题1\r\n内容1\r\n\r\n## 标题2\n内容2\n\n### 标题3\r内容3";
      
      const sections = parseMarkdownToSections(markdown);
      
      expect(sections).toHaveLength(3);
      expect(sections[0].title).toBe('标题1');
      expect(sections[1].title).toBe('标题2');
      expect(sections[2].title).toBe('标题3');
    });
  });
});
