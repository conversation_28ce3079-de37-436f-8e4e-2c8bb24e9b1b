/**
 * 性能优化工具单元测试
 */

import {
  debounce,
  throttle,
  rafThrottle,
  BatchUpdater,
  MemoryMonitor,
  PerformanceTimer,
  TaskSplitter,
  createOptimizedHandler
} from '../../utils/performanceUtils';
import { TestEnvironment } from '../../utils/testHelpers';

// 设置测试环境
beforeAll(() => {
  TestEnvironment.setupBrowserMocks();
});

afterEach(() => {
  TestEnvironment.cleanupAfterTest();
});

describe('debounce', () => {
  test('应该延迟函数执行', async () => {
    const mockFn = jest.fn();
    const debouncedFn = debounce(mockFn, 100);
    
    debouncedFn();
    debouncedFn();
    debouncedFn();
    
    // 立即检查，函数不应该被调用
    expect(mockFn).not.toHaveBeenCalled();
    
    // 等待延迟时间
    await new Promise(resolve => setTimeout(resolve, 150));
    
    // 现在函数应该被调用一次
    expect(mockFn).toHaveBeenCalledTimes(1);
  });

  test('应该在immediate模式下立即执行', () => {
    const mockFn = jest.fn();
    const debouncedFn = debounce(mockFn, 100, true);
    
    debouncedFn();
    
    // 立即模式下应该立即执行
    expect(mockFn).toHaveBeenCalledTimes(1);
    
    debouncedFn();
    debouncedFn();
    
    // 后续调用不应该立即执行
    expect(mockFn).toHaveBeenCalledTimes(1);
  });

  test('应该传递正确的参数', async () => {
    const mockFn = jest.fn();
    const debouncedFn = debounce(mockFn, 50);
    
    debouncedFn('arg1', 'arg2');
    
    await new Promise(resolve => setTimeout(resolve, 100));
    
    expect(mockFn).toHaveBeenCalledWith('arg1', 'arg2');
  });
});

describe('throttle', () => {
  test('应该限制函数调用频率', async () => {
    const mockFn = jest.fn();
    const throttledFn = throttle(mockFn, 100);
    
    throttledFn();
    throttledFn();
    throttledFn();
    
    // 第一次调用应该立即执行
    expect(mockFn).toHaveBeenCalledTimes(1);
    
    // 等待节流时间
    await new Promise(resolve => setTimeout(resolve, 150));
    
    throttledFn();
    
    // 现在应该可以再次执行
    expect(mockFn).toHaveBeenCalledTimes(2);
  });

  test('应该传递正确的参数', () => {
    const mockFn = jest.fn();
    const throttledFn = throttle(mockFn, 100);
    
    throttledFn('arg1', 'arg2');
    
    expect(mockFn).toHaveBeenCalledWith('arg1', 'arg2');
  });
});

describe('BatchUpdater', () => {
  test('应该批量执行更新', async () => {
    const batchUpdater = new BatchUpdater(50);
    const mockFn1 = jest.fn();
    const mockFn2 = jest.fn();
    
    batchUpdater.add(mockFn1);
    batchUpdater.add(mockFn2);
    
    // 立即检查，函数不应该被调用
    expect(mockFn1).not.toHaveBeenCalled();
    expect(mockFn2).not.toHaveBeenCalled();
    
    // 等待批量延迟时间
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 现在函数应该被调用
    expect(mockFn1).toHaveBeenCalledTimes(1);
    expect(mockFn2).toHaveBeenCalledTimes(1);
  });

  test('应该能够手动刷新', () => {
    const batchUpdater = new BatchUpdater(1000);
    const mockFn = jest.fn();
    
    batchUpdater.add(mockFn);
    batchUpdater.flush();
    
    expect(mockFn).toHaveBeenCalledTimes(1);
  });

  test('应该能够清空队列', () => {
    const batchUpdater = new BatchUpdater(1000);
    const mockFn = jest.fn();
    
    batchUpdater.add(mockFn);
    batchUpdater.clear();
    batchUpdater.flush();
    
    expect(mockFn).not.toHaveBeenCalled();
  });
});

describe('MemoryMonitor', () => {
  test('应该能够测量内存使用', () => {
    const monitor = new MemoryMonitor();
    
    const measurement = monitor.measure();
    
    if (measurement) {
      expect(measurement).toHaveProperty('timestamp');
      expect(measurement).toHaveProperty('usedJSHeapSize');
      expect(measurement).toHaveProperty('totalJSHeapSize');
      expect(measurement).toHaveProperty('jsHeapSizeLimit');
    }
  });

  test('应该能够获取统计信息', () => {
    const monitor = new MemoryMonitor();
    
    // 添加一些测量数据
    monitor.measure();
    monitor.measure();
    
    const stats = monitor.getStats();
    
    if (stats) {
      expect(stats).toHaveProperty('current');
      expect(stats).toHaveProperty('growth');
      expect(stats).toHaveProperty('measurements');
      expect(stats).toHaveProperty('averageUsage');
    }
  });
});

describe('PerformanceTimer', () => {
  test('应该能够测量执行时间', () => {
    const timer = new PerformanceTimer();
    
    timer.start('test');
    
    // 模拟一些工作
    for (let i = 0; i < 1000; i++) {
      Math.random();
    }
    
    const duration = timer.end('test');
    
    expect(duration).toBeGreaterThan(0);
    expect(typeof duration).toBe('number');
  });

  test('应该能够测量同步函数', () => {
    const timer = new PerformanceTimer();
    
    const testFunction = () => {
      for (let i = 0; i < 1000; i++) {
        Math.random();
      }
      return 'result';
    };
    
    const { result, duration } = timer.measure('sync-test', testFunction);
    
    expect(result).toBe('result');
    expect(duration).toBeGreaterThan(0);
  });

  test('应该能够测量异步函数', async () => {
    const timer = new PerformanceTimer();
    
    const asyncFunction = async () => {
      await new Promise(resolve => setTimeout(resolve, 50));
      return 'async-result';
    };
    
    const { result, duration } = await timer.measureAsync('async-test', asyncFunction);
    
    expect(result).toBe('async-result');
    expect(duration).toBeGreaterThanOrEqual(50);
  });
});

describe('TaskSplitter', () => {
  test('应该能够分割长任务', async () => {
    const splitter = new TaskSplitter(5); // 5ms时间片
    const items = Array.from({ length: 100 }, (_, i) => i);
    const results = [];
    
    const processor = async (item) => {
      // 模拟一些处理时间
      await new Promise(resolve => setTimeout(resolve, 1));
      return item * 2;
    };
    
    const processedResults = await splitter.process(items, processor);
    
    expect(processedResults).toHaveLength(100);
    expect(processedResults[0]).toBe(0);
    expect(processedResults[99]).toBe(198);
  });

  test('应该报告处理进度', async () => {
    const splitter = new TaskSplitter(1);
    const items = Array.from({ length: 10 }, (_, i) => i);
    const progressReports = [];
    
    const processor = async (item) => item;
    const onProgress = (processed, total) => {
      progressReports.push({ processed, total });
    };
    
    await splitter.process(items, processor, onProgress);
    
    expect(progressReports.length).toBeGreaterThan(0);
    expect(progressReports[progressReports.length - 1].processed).toBe(10);
  });
});

describe('createOptimizedHandler', () => {
  test('应该创建防抖处理器', async () => {
    const mockFn = jest.fn();
    const handler = createOptimizedHandler(mockFn, { debounceMs: 100 });
    
    handler();
    handler();
    handler();
    
    expect(mockFn).not.toHaveBeenCalled();
    
    await new Promise(resolve => setTimeout(resolve, 150));
    
    expect(mockFn).toHaveBeenCalledTimes(1);
  });

  test('应该创建节流处理器', () => {
    const mockFn = jest.fn();
    const handler = createOptimizedHandler(mockFn, { throttleMs: 100 });
    
    handler();
    handler();
    handler();
    
    expect(mockFn).toHaveBeenCalledTimes(1);
  });

  test('应该返回原始处理器当没有优化选项时', () => {
    const mockFn = jest.fn();
    const handler = createOptimizedHandler(mockFn);
    
    handler();
    
    expect(mockFn).toHaveBeenCalledTimes(1);
  });
});

describe('性能基准测试', () => {
  test('防抖函数应该有良好的性能', () => {
    const mockFn = jest.fn();
    const debouncedFn = debounce(mockFn, 100);
    
    const startTime = performance.now();
    
    // 大量调用
    for (let i = 0; i < 10000; i++) {
      debouncedFn();
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    expect(duration).toBeLessThan(100); // 应该在100ms内完成
  });

  test('节流函数应该有良好的性能', () => {
    const mockFn = jest.fn();
    const throttledFn = throttle(mockFn, 100);
    
    const startTime = performance.now();
    
    // 大量调用
    for (let i = 0; i < 10000; i++) {
      throttledFn();
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    expect(duration).toBeLessThan(100); // 应该在100ms内完成
  });
});
