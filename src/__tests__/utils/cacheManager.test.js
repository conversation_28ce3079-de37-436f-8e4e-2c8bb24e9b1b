/**
 * 缓存管理器单元测试
 */

import { LRUCache, ContentCacheManager } from '../../utils/cacheManager';
import { TestEnvironment } from '../../utils/testHelpers';

// 设置测试环境
beforeAll(() => {
  TestEnvironment.setupBrowserMocks();
});

afterEach(() => {
  TestEnvironment.cleanupAfterTest();
});

describe('LRUCache', () => {
  let cache;

  beforeEach(() => {
    cache = new LRUCache(3); // 最大容量为3
  });

  test('应该能够设置和获取值', () => {
    cache.set('key1', 'value1');
    expect(cache.get('key1')).toBe('value1');
  });

  test('应该在超过容量时移除最久未使用的项', () => {
    cache.set('key1', 'value1');
    cache.set('key2', 'value2');
    cache.set('key3', 'value3');
    cache.set('key4', 'value4'); // 应该移除key1
    
    expect(cache.get('key1')).toBeNull();
    expect(cache.get('key2')).toBe('value2');
    expect(cache.get('key3')).toBe('value3');
    expect(cache.get('key4')).toBe('value4');
  });

  test('应该在访问时更新项的位置', () => {
    cache.set('key1', 'value1');
    cache.set('key2', 'value2');
    cache.set('key3', 'value3');
    
    // 访问key1，使其成为最近使用的
    cache.get('key1');
    
    // 添加新项，应该移除key2（最久未使用）
    cache.set('key4', 'value4');
    
    expect(cache.get('key1')).toBe('value1'); // 仍然存在
    expect(cache.get('key2')).toBeNull(); // 被移除
    expect(cache.get('key3')).toBe('value3');
    expect(cache.get('key4')).toBe('value4');
  });

  test('应该正确报告缓存大小', () => {
    expect(cache.size()).toBe(0);
    
    cache.set('key1', 'value1');
    expect(cache.size()).toBe(1);
    
    cache.set('key2', 'value2');
    expect(cache.size()).toBe(2);
    
    cache.clear();
    expect(cache.size()).toBe(0);
  });

  test('应该能够删除特定项', () => {
    cache.set('key1', 'value1');
    cache.set('key2', 'value2');
    
    expect(cache.delete('key1')).toBe(true);
    expect(cache.get('key1')).toBeNull();
    expect(cache.get('key2')).toBe('value2');
    expect(cache.size()).toBe(1);
  });

  test('应该能够检查项是否存在', () => {
    cache.set('key1', 'value1');
    
    expect(cache.has('key1')).toBe(true);
    expect(cache.has('key2')).toBe(false);
  });
});

describe('ContentCacheManager', () => {
  let cacheManager;

  beforeEach(() => {
    cacheManager = new ContentCacheManager({
      maxCacheSize: 5,
      ttl: 1000, // 1秒TTL
      enablePersistence: false // 禁用持久化以避免测试干扰
    });
  });

  afterEach(() => {
    cacheManager.clear();
  });

  test('应该能够设置和获取不同类型的缓存', () => {
    const testData = { test: 'data' };
    
    cacheManager.set('content', 'test-id', testData);
    const retrieved = cacheManager.get('content', 'test-id');
    
    expect(retrieved).toEqual(testData);
  });

  test('应该正确处理版本化的缓存', () => {
    const data1 = { version: 1 };
    const data2 = { version: 2 };
    
    cacheManager.set('content', 'test-id', data1, 'v1');
    cacheManager.set('content', 'test-id', data2, 'v2');
    
    expect(cacheManager.get('content', 'test-id', 'v1')).toEqual(data1);
    expect(cacheManager.get('content', 'test-id', 'v2')).toEqual(data2);
  });

  test('应该在TTL过期后返回null', async () => {
    const testData = { test: 'data' };
    
    cacheManager.set('content', 'test-id', testData, null, 100); // 100ms TTL
    
    // 立即获取应该成功
    expect(cacheManager.get('content', 'test-id')).toEqual(testData);
    
    // 等待TTL过期
    await new Promise(resolve => setTimeout(resolve, 150));
    
    // 过期后应该返回null
    expect(cacheManager.get('content', 'test-id')).toBeNull();
  });

  test('应该能够删除特定缓存项', () => {
    const testData = { test: 'data' };
    
    cacheManager.set('content', 'test-id', testData);
    expect(cacheManager.get('content', 'test-id')).toEqual(testData);
    
    cacheManager.delete('content', 'test-id');
    expect(cacheManager.get('content', 'test-id')).toBeNull();
  });

  test('应该能够清理过期项目', async () => {
    cacheManager.set('content', 'test-1', { data: 1 }, null, 50); // 50ms TTL
    cacheManager.set('content', 'test-2', { data: 2 }, null, 200); // 200ms TTL
    
    // 等待第一个项目过期
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const cleanedCount = cacheManager.cleanup();
    
    expect(cleanedCount).toBe(1);
    expect(cacheManager.get('content', 'test-1')).toBeNull();
    expect(cacheManager.get('content', 'test-2')).toEqual({ data: 2 });
  });

  test('应该正确生成缓存统计信息', () => {
    // 添加一些缓存项
    cacheManager.set('content', 'test-1', { data: 1 });
    cacheManager.set('sections', 'test-2', { data: 2 });
    
    // 产生一些命中和未命中
    cacheManager.get('content', 'test-1'); // 命中
    cacheManager.get('content', 'nonexistent'); // 未命中
    
    const stats = cacheManager.getStats();
    
    expect(stats.hits).toBe(1);
    expect(stats.misses).toBe(1);
    expect(stats.sets).toBe(2);
    expect(stats.hitRate).toBe('50.00%');
    expect(stats.cacheSize.content).toBe(1);
    expect(stats.cacheSize.sections).toBe(1);
  });

  test('应该能够清空所有缓存', () => {
    cacheManager.set('content', 'test-1', { data: 1 });
    cacheManager.set('sections', 'test-2', { data: 2 });
    
    cacheManager.clear();
    
    expect(cacheManager.get('content', 'test-1')).toBeNull();
    expect(cacheManager.get('sections', 'test-2')).toBeNull();
    
    const stats = cacheManager.getStats();
    expect(stats.hits).toBe(0);
    expect(stats.misses).toBe(0);
    expect(stats.sets).toBe(0);
  });

  test('应该能够清空特定类型的缓存', () => {
    cacheManager.set('content', 'test-1', { data: 1 });
    cacheManager.set('sections', 'test-2', { data: 2 });
    
    cacheManager.clear('content');
    
    expect(cacheManager.get('content', 'test-1')).toBeNull();
    expect(cacheManager.get('sections', 'test-2')).toEqual({ data: 2 });
  });

  test('应该正确处理无效的缓存类型', () => {
    const result = cacheManager.set('invalid-type', 'test-id', { data: 1 });
    expect(result).toBe(false);
    
    const retrieved = cacheManager.get('invalid-type', 'test-id');
    expect(retrieved).toBeNull();
  });
});

describe('缓存性能测试', () => {
  test('LRU缓存应该有良好的性能', () => {
    const cache = new LRUCache(1000);
    const startTime = performance.now();
    
    // 添加大量数据
    for (let i = 0; i < 1000; i++) {
      cache.set(`key-${i}`, `value-${i}`);
    }
    
    // 随机访问
    for (let i = 0; i < 1000; i++) {
      const randomKey = `key-${Math.floor(Math.random() * 1000)}`;
      cache.get(randomKey);
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    expect(duration).toBeLessThan(100); // 应该在100ms内完成
  });

  test('内容缓存管理器应该有良好的性能', () => {
    const cacheManager = new ContentCacheManager({
      maxCacheSize: 1000,
      enablePersistence: false
    });
    
    const startTime = performance.now();
    
    // 添加大量数据
    for (let i = 0; i < 500; i++) {
      cacheManager.set('content', `test-${i}`, { data: i });
      cacheManager.set('sections', `test-${i}`, { sections: i });
    }
    
    // 随机访问
    for (let i = 0; i < 500; i++) {
      const randomId = `test-${Math.floor(Math.random() * 500)}`;
      cacheManager.get('content', randomId);
      cacheManager.get('sections', randomId);
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    expect(duration).toBeLessThan(200); // 应该在200ms内完成
    
    cacheManager.clear();
  });
});
