export const DEFAULT_PRD_CONTENT = `# 智能共享停车App产品需求文档

## 1. 产品概述

### 1.1 项目背景
随着城市化进程的加快，停车难问题日益突出。本产品旨在通过共享经济模式，有效整合闲置停车资源，为用户提供便捷的停车解决方案。

### 1.2 用户画像
- **车主用户**：需要寻找停车位的城市驾驶者
- **车位提供者**：拥有闲置停车位的个人或企业
- **年龄分布**：25-45岁的城市中产阶级
- **技术素养**：熟悉移动应用操作

## 2. 核心功能

### 2.1 瞬时停车功能
用户可以根据目的地搜索附近的共享车位，并进行预订。

**主要特性：**
- 实时地图展示可用车位
- 按距离、价格、评分筛选
- 一键导航到目标车位
- 在线支付和电子发票

### 2.2 长租车位
为有固定停车需求的用户提供包月、包年服务。

### 2.3 功能优先级
1. **P0**：地图找车位、预订支付
2. **P1**：用户评价、客服系统  
3. **P2**：长租服务、积分体系

## 3. 非功能性需求

### 3.1 性能
- 地图加载时间 < 3秒
- 支付处理时间 < 5秒
- App启动时间 < 2秒

### 3.2 安全性
支付流程必须符合PCI DSS标准，用户信息需加密存储。

## 4. 风险与对策

### 市场风险
- **风险**：用户增长不及预期，无法形成网络效应
- **对策**：初期通过地推、线上活动吸引种子用户

### 技术风险  
- **风险**：地图定位不准，支付流程存在漏洞
- **对策**：
  - 采用成熟的地图服务商
  - 进行充分的压力测试和安全审计

### 运营风险
- **风险**：出现车位纠纷（如超时停车、车位被占）
- **对策**：建立用户信用体系和纠纷处理机制

## 5. 迭代计划

### 5.1 版本规划

| 版本号 | 核心目标 | 主要功能 | 预计发布日期 |
|-------|---------|---------|-------------|
| V1.0 (MVP) | 核心功能验证 | 用户注册、地图找车位、在线预订与支付 | 2025-08-01 |
| V1.1 | 体验优化 | 优化UI/UX、增加筛选功能、长租车位 | 2025-09-15 |
| V2.0 | 功能完善 | 社区功能、积分体系、高级筛选 | 2025-11-01 |

### 5.2 V1.0 任务分解
- **Week 1-2**：需求分析、UI设计
- **Week 3-6**：核心功能开发
- **Week 7-8**：测试与优化
- **Week 9-10**：上线准备

## 6. 竞品分析

### 主要竞品
1. **停简单**：覆盖面广，但用户体验待提升
2. **ETCP**：商业模式成熟，技术相对滞后
3. **小强停车**：创新性强，但市场份额较小

### 差异化策略
- 更智能的推荐算法
- 更友好的社区化体验
- 更灵活的定价机制
`; 