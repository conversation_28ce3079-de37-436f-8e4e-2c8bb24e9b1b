import React from 'react';
import { tableCommands } from '../utils/commands';

const TableMenu = ({ editor, onLog }) => {
  if (!editor) return null;

  const isInTable = editor.isActive('table');

  const handleCommand = (commandName, commandFn) => {
    try {
      const result = commandFn();
      onLog && onLog(`表格操作: ${commandName} - ${result ? '成功' : '失败'}`, result ? 'success' : 'error');
      return result;
    } catch (error) {
      onLog && onLog(`表格操作失败: ${commandName} - ${error.message}`, 'error');
      return false;
    }
  };

  const menuStyle = {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px',
    padding: '12px',
    background: '#fff',
    border: '1px solid #e1e5e9',
    borderRadius: '6px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    minWidth: '200px',
  };

  const buttonStyle = {
    padding: '6px 12px',
    border: '1px solid #ddd',
    background: '#fff',
    borderRadius: '4px',
    cursor: 'pointer',
    fontSize: '14px',
    transition: 'all 0.2s',
    textAlign: 'left',
  };

  const disabledButtonStyle = {
    ...buttonStyle,
    opacity: 0.5,
    cursor: 'not-allowed',
    background: '#f5f5f5',
  };

  const sectionStyle = {
    borderBottom: '1px solid #eee',
    paddingBottom: '8px',
    marginBottom: '8px',
  };

  const sectionTitleStyle = {
    fontSize: '12px',
    fontWeight: 'bold',
    color: '#666',
    marginBottom: '4px',
    textTransform: 'uppercase',
  };

  return (
    <div style={menuStyle}>
      {/* 插入表格部分 */}
      <div style={sectionStyle}>
        <div style={sectionTitleStyle}>插入表格</div>
        <button
          style={buttonStyle}
          onClick={() => handleCommand('插入简单表格', () => tableCommands.insertTable(editor, 3, 3, true))}
          onMouseEnter={(e) => e.target.style.background = '#f8f9fa'}
          onMouseLeave={(e) => e.target.style.background = '#fff'}
        >
          📊 插入表格 (3×3)
        </button>
        <button
          style={buttonStyle}
          onClick={() => handleCommand('插入大表格', () => tableCommands.insertTable(editor, 5, 4, true))}
          onMouseEnter={(e) => e.target.style.background = '#f8f9fa'}
          onMouseLeave={(e) => e.target.style.background = '#fff'}
        >
          📋 插入大表格 (5×4)
        </button>
      </div>

      {/* 列操作部分 */}
      {isInTable && (
        <div style={sectionStyle}>
          <div style={sectionTitleStyle}>列操作</div>
          <button
            style={tableCommands.canAddColumnBefore(editor) ? buttonStyle : disabledButtonStyle}
            disabled={!tableCommands.canAddColumnBefore(editor)}
            onClick={() => handleCommand('在前面添加列', () => tableCommands.addColumnBefore(editor))}
            onMouseEnter={(e) => e.target.style.background = tableCommands.canAddColumnBefore(editor) ? '#f8f9fa' : '#f5f5f5'}
            onMouseLeave={(e) => e.target.style.background = tableCommands.canAddColumnBefore(editor) ? '#fff' : '#f5f5f5'}
          >
            ⬅️ 在前面添加列
          </button>
          <button
            style={tableCommands.canAddColumnAfter(editor) ? buttonStyle : disabledButtonStyle}
            disabled={!tableCommands.canAddColumnAfter(editor)}
            onClick={() => handleCommand('在后面添加列', () => tableCommands.addColumnAfter(editor))}
            onMouseEnter={(e) => e.target.style.background = tableCommands.canAddColumnAfter(editor) ? '#f8f9fa' : '#f5f5f5'}
            onMouseLeave={(e) => e.target.style.background = tableCommands.canAddColumnAfter(editor) ? '#fff' : '#f5f5f5'}
          >
            ➡️ 在后面添加列
          </button>
          <button
            style={tableCommands.canDeleteColumn(editor) ? buttonStyle : disabledButtonStyle}
            disabled={!tableCommands.canDeleteColumn(editor)}
            onClick={() => handleCommand('删除列', () => tableCommands.deleteColumn(editor))}
            onMouseEnter={(e) => e.target.style.background = tableCommands.canDeleteColumn(editor) ? '#ffebee' : '#f5f5f5'}
            onMouseLeave={(e) => e.target.style.background = tableCommands.canDeleteColumn(editor) ? '#fff' : '#f5f5f5'}
          >
            🗑️ 删除列
          </button>
        </div>
      )}

      {/* 行操作部分 */}
      {isInTable && (
        <div style={sectionStyle}>
          <div style={sectionTitleStyle}>行操作</div>
          <button
            style={tableCommands.canAddRowBefore(editor) ? buttonStyle : disabledButtonStyle}
            disabled={!tableCommands.canAddRowBefore(editor)}
            onClick={() => handleCommand('在上面添加行', () => tableCommands.addRowBefore(editor))}
            onMouseEnter={(e) => e.target.style.background = tableCommands.canAddRowBefore(editor) ? '#f8f9fa' : '#f5f5f5'}
            onMouseLeave={(e) => e.target.style.background = tableCommands.canAddRowBefore(editor) ? '#fff' : '#f5f5f5'}
          >
            ⬆️ 在上面添加行
          </button>
          <button
            style={tableCommands.canAddRowAfter(editor) ? buttonStyle : disabledButtonStyle}
            disabled={!tableCommands.canAddRowAfter(editor)}
            onClick={() => handleCommand('在下面添加行', () => tableCommands.addRowAfter(editor))}
            onMouseEnter={(e) => e.target.style.background = tableCommands.canAddRowAfter(editor) ? '#f8f9fa' : '#f5f5f5'}
            onMouseLeave={(e) => e.target.style.background = tableCommands.canAddRowAfter(editor) ? '#fff' : '#f5f5f5'}
          >
            ⬇️ 在下面添加行
          </button>
          <button
            style={tableCommands.canDeleteRow(editor) ? buttonStyle : disabledButtonStyle}
            disabled={!tableCommands.canDeleteRow(editor)}
            onClick={() => handleCommand('删除行', () => tableCommands.deleteRow(editor))}
            onMouseEnter={(e) => e.target.style.background = tableCommands.canDeleteRow(editor) ? '#ffebee' : '#f5f5f5'}
            onMouseLeave={(e) => e.target.style.background = tableCommands.canDeleteRow(editor) ? '#fff' : '#f5f5f5'}
          >
            🗑️ 删除行
          </button>
        </div>
      )}

      {/* 单元格操作部分 */}
      {isInTable && (
        <div style={sectionStyle}>
          <div style={sectionTitleStyle}>单元格操作</div>
          <button
            style={tableCommands.canMergeCells(editor) ? buttonStyle : disabledButtonStyle}
            disabled={!tableCommands.canMergeCells(editor)}
            onClick={() => handleCommand('合并单元格', () => tableCommands.mergeCells(editor))}
            onMouseEnter={(e) => e.target.style.background = tableCommands.canMergeCells(editor) ? '#f8f9fa' : '#f5f5f5'}
            onMouseLeave={(e) => e.target.style.background = tableCommands.canMergeCells(editor) ? '#fff' : '#f5f5f5'}
          >
            🔗 合并单元格
          </button>
          <button
            style={tableCommands.canSplitCell(editor) ? buttonStyle : disabledButtonStyle}
            disabled={!tableCommands.canSplitCell(editor)}
            onClick={() => handleCommand('拆分单元格', () => tableCommands.splitCell(editor))}
            onMouseEnter={(e) => e.target.style.background = tableCommands.canSplitCell(editor) ? '#f8f9fa' : '#f5f5f5'}
            onMouseLeave={(e) => e.target.style.background = tableCommands.canSplitCell(editor) ? '#fff' : '#f5f5f5'}
          >
            ✂️ 拆分单元格
          </button>
        </div>
      )}

      {/* 表头操作部分 */}
      {isInTable && (
        <div style={sectionStyle}>
          <div style={sectionTitleStyle}>表头操作</div>
          <button
            style={buttonStyle}
            onClick={() => handleCommand('切换表头行', () => tableCommands.toggleHeaderRow(editor))}
            onMouseEnter={(e) => e.target.style.background = '#f8f9fa'}
            onMouseLeave={(e) => e.target.style.background = '#fff'}
          >
            📑 切换表头行
          </button>
          <button
            style={buttonStyle}
            onClick={() => handleCommand('切换表头列', () => tableCommands.toggleHeaderColumn(editor))}
            onMouseEnter={(e) => e.target.style.background = '#f8f9fa'}
            onMouseLeave={(e) => e.target.style.background = '#fff'}
          >
            📄 切换表头列
          </button>
        </div>
      )}

      {/* 表格操作部分 */}
      {isInTable && (
        <div>
          <div style={sectionTitleStyle}>表格操作</div>
          <button
            style={{...buttonStyle, background: '#ffebee', borderColor: '#f44336'}}
            onClick={() => handleCommand('删除整个表格', () => tableCommands.deleteTable(editor))}
            onMouseEnter={(e) => e.target.style.background = '#ffcdd2'}
            onMouseLeave={(e) => e.target.style.background = '#ffebee'}
          >
            🗑️ 删除整个表格
          </button>
        </div>
      )}

      {/* 状态信息 */}
      <div style={{
        marginTop: '8px',
        padding: '8px',
        background: '#f8f9fa',
        borderRadius: '4px',
        fontSize: '12px',
        color: '#666',
      }}>
        {isInTable ? (
          <div>
            <div>✅ 光标在表格内</div>
            <div>可以进行表格编辑操作</div>
          </div>
        ) : (
          <div>
            <div>📍 光标不在表格内</div>
            <div>可以插入新表格</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TableMenu;
