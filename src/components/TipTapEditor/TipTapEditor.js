import React, { useEffect, useState } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Table } from '@tiptap/extension-table';
import { TableRow } from '@tiptap/extension-table-row';
import { TableHeader } from '@tiptap/extension-table-header';
import { TableCell } from '@tiptap/extension-table-cell';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import Placeholder from '@tiptap/extension-placeholder';
import Focus from '@tiptap/extension-focus';
import { MarkdownPasteHandler } from './extensions/MarkdownPasteHandler';


import './TipTapEditor.css';

const TipTapEditor = ({
  content = '',
  onChange,
  onSelectionUpdate,
  onUpdate,
  onCreate,
  onFocus,
  onBlur,
  placeholder = '开始输入内容...',
  editable = true,
  className = '',
  style = {},
}) => {
  const [isReady, setIsReady] = useState(false);
  const [currentSelection, setCurrentSelection] = useState(null);

  // 配置编辑器扩展
  const extensions = [
    StarterKit.configure({
      // 禁用默认的表格，使用自定义表格扩展
      table: false,
    }),
    Table.configure({
      resizable: true,
      handleWidth: 5,
      cellMinWidth: 100,
      allowTableNodeSelection: true,
    }),
    TableRow,
    TableHeader,
    TableCell,
    Image.configure({
      inline: false,
      allowBase64: true,
    }),
    Link.configure({
      openOnClick: false,
      HTMLAttributes: {
        class: 'tiptap-link',
      },
    }),
    Placeholder.configure({
      placeholder,
    }),
    Focus.configure({
      className: 'has-focus',
      mode: 'all',
    }),
    MarkdownPasteHandler,

  ];

  // 创建编辑器实例
  const editor = useEditor({
    extensions,
    content,
    editable,
    editorProps: {
      handleKeyDown: (view, event) => {
        // 在表格中按Ctrl+Enter键在当前行下方添加新行
        if (event.key === 'Enter' && event.ctrlKey) {
          const { state } = view;
          const { $from } = state.selection;

          // 检查是否在表格单元格中
          let depth = $from.depth;
          let inTable = false;

          while (depth > 0) {
            const node = $from.node(depth);
            if (node.type.name === 'table') {
              inTable = true;
              break;
            }
            depth--;
          }

          if (inTable) {
            event.preventDefault();
            // 直接使用ProseMirror命令添加行
            const tr = state.tr;
            const tablePos = $from.before(depth);
            const table = $from.node(depth);

            // 找到当前行
            let currentRowIndex = 0;
            let pos = tablePos + 1;

            for (let i = 0; i < table.childCount; i++) {
              const row = table.child(i);
              if (pos <= $from.pos && $from.pos < pos + row.nodeSize) {
                currentRowIndex = i;
                break;
              }
              pos += row.nodeSize;
            }

            // 创建新行
            const currentRow = table.child(currentRowIndex);
            const newCells = [];
            for (let i = 0; i < currentRow.childCount; i++) {
              newCells.push(state.schema.nodes.tableCell.createAndFill());
            }
            const newRow = state.schema.nodes.tableRow.create(null, newCells);

            // 插入新行
            const insertPos = pos + currentRow.nodeSize;
            view.dispatch(tr.insert(insertPos, newRow));

            return true;
          }
        }
        return false;
      },
    },
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      const json = editor.getJSON();
      const text = editor.getText();

      onChange && onChange({
        html,
        json,
        text,
        editor,
      });

      onUpdate && onUpdate({ editor });
    },
    onSelectionUpdate: ({ editor }) => {
      const { from, to } = editor.state.selection;
      const selectedText = editor.state.doc.textBetween(from, to);
      const selection = {
        from,
        to,
        text: selectedText,
        isEmpty: from === to,
      };

      setCurrentSelection(selection);
      onSelectionUpdate && onSelectionUpdate({ editor, selection });
    },
    onCreate: ({ editor }) => {
      setIsReady(true);
      onCreate && onCreate({ editor });
    },
    onFocus: ({ editor }) => {
      onFocus && onFocus({ editor });
    },
    onBlur: ({ editor }) => {
      onBlur && onBlur({ editor });
    },
  });

  // 更新内容
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content, false);
    }
  }, [content, editor]);

  // 工具栏命令
  const commands = {
    // 文本格式化
    toggleBold: () => {
      if (editor) {
        editor.chain().focus().toggleBold().run();
      }
    },
    toggleItalic: () => {
      if (editor) {
        editor.chain().focus().toggleItalic().run();
      }
    },
    toggleStrike: () => {
      if (editor) {
        editor.chain().focus().toggleStrike().run();
      }
    },
    toggleCode: () => {
      if (editor) {
        editor.chain().focus().toggleCode().run();
      }
    },

    // 标题
    setHeading: (level) => {
      if (editor) {
        editor.chain().focus().toggleHeading({ level }).run();
      }
    },
    setParagraph: () => {
      if (editor) {
        editor.chain().focus().setParagraph().run();
      }
    },

    // 列表
    toggleBulletList: () => {
      if (editor) {
        editor.chain().focus().toggleBulletList().run();
      }
    },
    toggleOrderedList: () => {
      if (editor) {
        editor.chain().focus().toggleOrderedList().run();
      }
    },
    
    // 表格
    insertTable: (rows = 3, cols = 3) => {
      if (editor) {
        editor.chain().focus().insertTable({ rows, cols, withHeaderRow: true }).run();
      }
    },
    addColumnBefore: () => {
      if (editor) {
        editor.chain().focus().addColumnBefore().run();
      }
    },
    addColumnAfter: () => {
      if (editor) {
        editor.chain().focus().addColumnAfter().run();
      }
    },
    deleteColumn: () => {
      if (editor) {
        editor.chain().focus().deleteColumn().run();
      }
    },
    addRowBefore: () => {
      if (editor) {
        editor.chain().focus().addRowBefore().run();
      }
    },
    addRowAfter: () => {
      if (editor) {
        editor.chain().focus().addRowAfter().run();
      }
    },
    deleteRow: () => {
      if (editor) {
        editor.chain().focus().deleteRow().run();
      }
    },
    deleteTable: () => {
      if (editor) {
        editor.chain().focus().deleteTable().run();
      }
    },
    mergeCells: () => {
      if (editor) {
        editor.chain().focus().mergeCells().run();
      }
    },
    splitCell: () => {
      if (editor) {
        editor.chain().focus().splitCell().run();
      }
    },
    toggleHeaderColumn: () => {
      if (editor) {
        editor.chain().focus().toggleHeaderColumn().run();
      }
    },
    toggleHeaderRow: () => {
      if (editor) {
        editor.chain().focus().toggleHeaderRow().run();
      }
    },
    toggleHeaderCell: () => {
      if (editor) {
        editor.chain().focus().toggleHeaderCell().run();
      }
    },

    // 其他
    insertImage: (src, alt = '') => {
      if (editor) {
        editor.chain().focus().setImage({ src, alt }).run();
      }
    },
    setLink: (href) => {
      if (editor) {
        editor.chain().focus().setLink({ href }).run();
      }
    },
    unsetLink: () => {
      if (editor) {
        editor.chain().focus().unsetLink().run();
      }
    },
    insertHorizontalRule: () => {
      if (editor) {
        editor.chain().focus().setHorizontalRule().run();
      }
    },

    // 引用和代码块
    toggleBlockquote: () => {
      if (editor) {
        editor.chain().focus().toggleBlockquote().run();
      }
    },
    toggleCodeBlock: () => {
      if (editor) {
        editor.chain().focus().toggleCodeBlock().run();
      }
    },
  };

  // 检查命令状态
  const isActive = {
    bold: () => editor?.isActive('bold') || false,
    italic: () => editor?.isActive('italic') || false,
    strike: () => editor?.isActive('strike') || false,
    code: () => editor?.isActive('code') || false,
    heading: (level) => editor?.isActive('heading', { level }) || false,
    bulletList: () => editor?.isActive('bulletList') || false,
    orderedList: () => editor?.isActive('orderedList') || false,
    blockquote: () => editor?.isActive('blockquote') || false,
    codeBlock: () => editor?.isActive('codeBlock') || false,
    link: () => editor?.isActive('link') || false,
  };

  // 工具栏组件
  const Toolbar = () => (
    <div className="tiptap-editor-toolbar">
      {/* 文本格式化 */}
      <button
        className={`toolbar-button ${isActive.bold() ? 'is-active' : ''}`}
        onClick={commands.toggleBold}
        title="粗体"
      >
        <strong>B</strong>
      </button>
      <button
        className={`toolbar-button ${isActive.italic() ? 'is-active' : ''}`}
        onClick={commands.toggleItalic}
        title="斜体"
      >
        <em>I</em>
      </button>
      <button
        className={`toolbar-button ${isActive.strike() ? 'is-active' : ''}`}
        onClick={commands.toggleStrike}
        title="删除线"
      >
        <s>S</s>
      </button>
      <button
        className={`toolbar-button ${isActive.code() ? 'is-active' : ''}`}
        onClick={commands.toggleCode}
        title="内联代码"
      >
        &lt;/&gt;
      </button>
      
      {/* 分隔符 */}
      <div style={{ width: '1px', height: '24px', background: '#ddd', margin: '0 4px' }} />
      
      {/* 标题 */}
      <button
        className={`toolbar-button ${isActive.heading(1) ? 'is-active' : ''}`}
        onClick={() => commands.setHeading(1)}
        title="标题1"
      >
        H1
      </button>
      <button
        className={`toolbar-button ${isActive.heading(2) ? 'is-active' : ''}`}
        onClick={() => commands.setHeading(2)}
        title="标题2"
      >
        H2
      </button>
      <button
        className={`toolbar-button ${isActive.heading(3) ? 'is-active' : ''}`}
        onClick={() => commands.setHeading(3)}
        title="标题3"
      >
        H3
      </button>
      
      {/* 分隔符 */}
      <div style={{ width: '1px', height: '24px', background: '#ddd', margin: '0 4px' }} />
      
      {/* 列表 */}
      <button
        className={`toolbar-button ${isActive.bulletList() ? 'is-active' : ''}`}
        onClick={commands.toggleBulletList}
        title="无序列表"
      >
        • 列表
      </button>
      <button
        className={`toolbar-button ${isActive.orderedList() ? 'is-active' : ''}`}
        onClick={commands.toggleOrderedList}
        title="有序列表"
      >
        1. 列表
      </button>
      
      {/* 分隔符 */}
      <div style={{ width: '1px', height: '24px', background: '#ddd', margin: '0 4px' }} />
      
      {/* 表格 */}
      <button
        className="toolbar-button"
        onClick={() => commands.insertTable()}
        title="插入表格"
      >
        📊 表格
      </button>
      
      {/* 其他 */}
      <button
        className={`toolbar-button ${isActive.blockquote() ? 'is-active' : ''}`}
        onClick={commands.toggleBlockquote}
        title="引用"
      >
        💬 引用
      </button>
      <button
        className={`toolbar-button ${isActive.codeBlock() ? 'is-active' : ''}`}
        onClick={commands.toggleCodeBlock}
        title="代码块"
      >
        💻 代码
      </button>
    </div>
  );

  if (!editor) {
    return <div>编辑器加载中...</div>;
  }

  return (
    <div className={`tiptap-editor-container ${className}`} style={style}>
      <Toolbar />
      
      <div className="tiptap-editor-content">
        <EditorContent editor={editor} />
      </div>
    </div>
  );
};

export default TipTapEditor;
export { TipTapEditor };
