import React, { useEffect, useState } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Table } from '@tiptap/extension-table';
import { TableRow } from '@tiptap/extension-table-row';
import { TableHeader } from '@tiptap/extension-table-header';
import { TableCell } from '@tiptap/extension-table-cell';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import Placeholder from '@tiptap/extension-placeholder';
import Focus from '@tiptap/extension-focus';


import './TipTapEditor.css';

const TipTapEditor = ({
  content = '',
  onChange,
  onSelectionUpdate,
  onUpdate,
  onCreate,
  onFocus,
  onBlur,
  placeholder = '开始输入内容...',
  editable = true,
  className = '',
  style = {},
}) => {
  const [isReady, setIsReady] = useState(false);
  const [currentSelection, setCurrentSelection] = useState(null);

  // 配置编辑器扩展
  const extensions = [
    StarterKit.configure({
      // 禁用默认的表格，使用自定义表格扩展
      table: false,
    }),
    Table.configure({
      resizable: true,
      handleWidth: 5,
      cellMinWidth: 100,
      allowTableNodeSelection: true,
    }),
    TableRow,
    TableHeader,
    TableCell,
    Image.configure({
      inline: false,
      allowBase64: true,
    }),
    Link.configure({
      openOnClick: false,
      HTMLAttributes: {
        class: 'tiptap-link',
      },
    }),
    Placeholder.configure({
      placeholder,
    }),
    Focus.configure({
      className: 'has-focus',
      mode: 'all',
    }),

  ];

  // 创建编辑器实例
  const editor = useEditor({
    extensions,
    content,
    editable,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      const json = editor.getJSON();
      const text = editor.getText();
      
      onChange && onChange({
        html,
        json,
        text,
        editor,
      });
      
      onUpdate && onUpdate({ editor });
    },
    onSelectionUpdate: ({ editor }) => {
      const { from, to } = editor.state.selection;
      const selectedText = editor.state.doc.textBetween(from, to);
      const selection = {
        from,
        to,
        text: selectedText,
        isEmpty: from === to,
      };
      
      setCurrentSelection(selection);
      onSelectionUpdate && onSelectionUpdate({ editor, selection });
    },
    onCreate: ({ editor }) => {
      setIsReady(true);
      onCreate && onCreate({ editor });
    },
    onFocus: ({ editor }) => {
      onFocus && onFocus({ editor });
    },
    onBlur: ({ editor }) => {
      onBlur && onBlur({ editor });
    },
  });

  // 更新内容
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content, false);
    }
  }, [content, editor]);

  // 工具栏命令
  const commands = {
    // 文本格式化
    toggleBold: () => editor.chain().focus().toggleBold().run(),
    toggleItalic: () => editor.chain().focus().toggleItalic().run(),
    toggleStrike: () => editor.chain().focus().toggleStrike().run(),
    toggleCode: () => editor.chain().focus().toggleCode().run(),
    
    // 标题
    setHeading: (level) => editor.chain().focus().toggleHeading({ level }).run(),
    setParagraph: () => editor.chain().focus().setParagraph().run(),
    
    // 列表
    toggleBulletList: () => editor.chain().focus().toggleBulletList().run(),
    toggleOrderedList: () => editor.chain().focus().toggleOrderedList().run(),
    
    // 表格
    insertTable: (rows = 3, cols = 3) => {
      editor.chain().focus().insertTable({ rows, cols, withHeaderRow: true }).run();
    },
    addColumnBefore: () => editor.chain().focus().addColumnBefore().run(),
    addColumnAfter: () => editor.chain().focus().addColumnAfter().run(),
    deleteColumn: () => editor.chain().focus().deleteColumn().run(),
    addRowBefore: () => editor.chain().focus().addRowBefore().run(),
    addRowAfter: () => editor.chain().focus().addRowAfter().run(),
    deleteRow: () => editor.chain().focus().deleteRow().run(),
    deleteTable: () => editor.chain().focus().deleteTable().run(),
    mergeCells: () => editor.chain().focus().mergeCells().run(),
    splitCell: () => editor.chain().focus().splitCell().run(),
    toggleHeaderColumn: () => editor.chain().focus().toggleHeaderColumn().run(),
    toggleHeaderRow: () => editor.chain().focus().toggleHeaderRow().run(),
    toggleHeaderCell: () => editor.chain().focus().toggleHeaderCell().run(),
    
    // 其他
    insertImage: (src, alt = '') => {
      editor.chain().focus().setImage({ src, alt }).run();
    },
    setLink: (href) => {
      editor.chain().focus().setLink({ href }).run();
    },
    unsetLink: () => editor.chain().focus().unsetLink().run(),
    insertHorizontalRule: () => editor.chain().focus().setHorizontalRule().run(),
    
    // 引用和代码块
    toggleBlockquote: () => editor.chain().focus().toggleBlockquote().run(),
    toggleCodeBlock: () => editor.chain().focus().toggleCodeBlock().run(),
  };

  // 检查命令状态
  const isActive = {
    bold: () => editor?.isActive('bold') || false,
    italic: () => editor?.isActive('italic') || false,
    strike: () => editor?.isActive('strike') || false,
    code: () => editor?.isActive('code') || false,
    heading: (level) => editor?.isActive('heading', { level }) || false,
    bulletList: () => editor?.isActive('bulletList') || false,
    orderedList: () => editor?.isActive('orderedList') || false,
    blockquote: () => editor?.isActive('blockquote') || false,
    codeBlock: () => editor?.isActive('codeBlock') || false,
    link: () => editor?.isActive('link') || false,
  };

  // 工具栏组件
  const Toolbar = () => (
    <div className="tiptap-editor-toolbar">
      {/* 文本格式化 */}
      <button
        className={`toolbar-button ${isActive.bold() ? 'is-active' : ''}`}
        onClick={commands.toggleBold}
        title="粗体"
      >
        <strong>B</strong>
      </button>
      <button
        className={`toolbar-button ${isActive.italic() ? 'is-active' : ''}`}
        onClick={commands.toggleItalic}
        title="斜体"
      >
        <em>I</em>
      </button>
      <button
        className={`toolbar-button ${isActive.strike() ? 'is-active' : ''}`}
        onClick={commands.toggleStrike}
        title="删除线"
      >
        <s>S</s>
      </button>
      
      {/* 分隔符 */}
      <div style={{ width: '1px', height: '24px', background: '#ddd', margin: '0 4px' }} />
      
      {/* 标题 */}
      <button
        className={`toolbar-button ${isActive.heading(1) ? 'is-active' : ''}`}
        onClick={() => commands.setHeading(1)}
        title="标题1"
      >
        H1
      </button>
      <button
        className={`toolbar-button ${isActive.heading(2) ? 'is-active' : ''}`}
        onClick={() => commands.setHeading(2)}
        title="标题2"
      >
        H2
      </button>
      <button
        className={`toolbar-button ${isActive.heading(3) ? 'is-active' : ''}`}
        onClick={() => commands.setHeading(3)}
        title="标题3"
      >
        H3
      </button>
      
      {/* 分隔符 */}
      <div style={{ width: '1px', height: '24px', background: '#ddd', margin: '0 4px' }} />
      
      {/* 列表 */}
      <button
        className={`toolbar-button ${isActive.bulletList() ? 'is-active' : ''}`}
        onClick={commands.toggleBulletList}
        title="无序列表"
      >
        • 列表
      </button>
      <button
        className={`toolbar-button ${isActive.orderedList() ? 'is-active' : ''}`}
        onClick={commands.toggleOrderedList}
        title="有序列表"
      >
        1. 列表
      </button>
      
      {/* 分隔符 */}
      <div style={{ width: '1px', height: '24px', background: '#ddd', margin: '0 4px' }} />
      
      {/* 表格 */}
      <button
        className="toolbar-button"
        onClick={() => commands.insertTable()}
        title="插入表格"
      >
        📊 表格
      </button>
      
      {/* 其他 */}
      <button
        className={`toolbar-button ${isActive.blockquote() ? 'is-active' : ''}`}
        onClick={commands.toggleBlockquote}
        title="引用"
      >
        💬 引用
      </button>
      <button
        className={`toolbar-button ${isActive.codeBlock() ? 'is-active' : ''}`}
        onClick={commands.toggleCodeBlock}
        title="代码块"
      >
        💻 代码
      </button>
    </div>
  );

  if (!editor) {
    return <div>编辑器加载中...</div>;
  }

  return (
    <div className={`tiptap-editor-container ${className}`} style={style}>
      <Toolbar />
      
      <div className="tiptap-editor-content">
        <EditorContent editor={editor} />
      </div>
    </div>
  );
};

export default TipTapEditor;
export { TipTapEditor };
