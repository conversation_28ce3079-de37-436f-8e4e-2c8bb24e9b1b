import { Node, mergeAttributes } from '@tiptap/core';
import { Table } from '@tiptap/extension-table';

export const TableWithEditButton = Table.extend({
  name: 'table',

  addNodeView() {
    return ({ node, HTMLAttributes, getPos, editor }) => {
      const dom = document.createElement('div');
      dom.className = 'table-wrapper';
      
      const table = document.createElement('table');
      Object.entries(HTMLAttributes).forEach(([key, value]) => {
        table.setAttribute(key, value);
      });
      
      const editButton = document.createElement('button');
      editButton.className = 'table-edit-button';
      editButton.innerHTML = '✏️';
      editButton.title = '编辑表格';
      
      editButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        
        // 触发自定义事件，让父组件处理
        const event = new CustomEvent('table-edit-click', {
          detail: {
            node,
            getPos,
            editor,
            buttonElement: editButton,
          }
        });
        dom.dispatchEvent(event);
      });
      
      dom.appendChild(table);
      dom.appendChild(editButton);
      
      return {
        dom,
        contentDOM: table,
        update: (updatedNode) => {
          if (updatedNode.type !== node.type) {
            return false;
          }
          return true;
        },
        destroy: () => {
          editButton.removeEventListener('click', () => {});
        }
      };
    };
  },
});

export default TableWithEditButton;
