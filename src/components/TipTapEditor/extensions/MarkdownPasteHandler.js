import { Extension } from '@tiptap/core';
import { Plugin, PluginKey } from 'prosemirror-state';

// Markdown表格解析器
const parseMarkdownTable = (text) => {
  const lines = text.trim().split('\n');
  if (lines.length < 2) return null;

  // 检查是否是表格格式
  const headerLine = lines[0];
  const separatorLine = lines[1];

  // 更严格的表格格式检测
  if (!headerLine.includes('|') || !separatorLine.includes('|')) {
    return null;
  }

  // 检查分隔符行是否包含足够的破折号
  const separatorPattern = /^\s*\|?[\s\-\|:]+\|?\s*$/;
  if (!separatorPattern.test(separatorLine)) {
    return null;
  }

  // 解析表头 - 保留空单元格
  const headerCells = headerLine.split('|');
  // 移除首尾可能的空元素（由于开头或结尾的|导致的）
  if (headerCells[0].trim() === '') headerCells.shift();
  if (headerCells[headerCells.length - 1].trim() === '') headerCells.pop();

  const headers = headerCells.map(cell => cell.trim());

  // 解析数据行 - 保留空单元格
  const rows = [];
  for (let i = 2; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line.includes('|')) {
      const rowCells = line.split('|');
      // 移除首尾可能的空元素
      if (rowCells[0].trim() === '') rowCells.shift();
      if (rowCells[rowCells.length - 1].trim() === '') rowCells.pop();

      const cells = rowCells.map(cell => cell.trim());

      // 确保行的列数与表头一致
      while (cells.length < headers.length) {
        cells.push('');
      }
      cells.length = headers.length; // 截断多余的列

      rows.push(cells);
    }
  }

  if (headers.length === 0) return null;

  console.log('Parsed table:', { headers, rows, colCount: headers.length, rowCount: rows.length + 1 });

  return {
    headers,
    rows,
    colCount: headers.length,
    rowCount: rows.length + 1 // +1 for header
  };
};

// 检查是否是Markdown内容
const isMarkdownContent = (text) => {
  const markdownPatterns = [
    /^\s*\|.*\|.*$/m, // 表格行
    /^\s*\|?[\s\-\|:]+\|?\s*$/m, // 表格分隔符
    /^#{1,6}\s+/m, // 标题
    /^\*\s+/m, // 无序列表
    /^\d+\.\s+/m, // 有序列表
    /^>\s+/m, // 引用
    /^```/m, // 代码块
    /\*\*.*\*\*/m, // 粗体
    /\*.*\*/m, // 斜体
    /`.*`/m, // 内联代码
  ];

  return markdownPatterns.some(pattern => pattern.test(text));
};

// 创建确认对话框
const createConfirmDialog = (content, onConfirm, onCancel) => {
  const overlay = document.createElement('div');
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
  `;
  
  const dialog = document.createElement('div');
  dialog.style.cssText = `
    background: white;
    border-radius: 8px;
    padding: 24px;
    max-width: 500px;
    max-height: 400px;
    overflow: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  `;
  
  const title = document.createElement('h3');
  title.textContent = '检测到Markdown内容';
  title.style.cssText = 'margin: 0 0 16px 0; color: #333;';
  
  const message = document.createElement('p');
  message.textContent = '是否将粘贴的Markdown内容转换为格式化文档？';
  message.style.cssText = 'margin: 0 0 16px 0; color: #666;';
  
  const preview = document.createElement('div');
  preview.style.cssText = `
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 12px;
    margin: 16px 0;
    max-height: 200px;
    overflow: auto;
    font-family: monospace;
    font-size: 12px;
    white-space: pre-wrap;
  `;
  preview.textContent = content.length > 500 ? content.substring(0, 500) + '...' : content;
  
  const buttonContainer = document.createElement('div');
  buttonContainer.style.cssText = 'display: flex; gap: 12px; justify-content: flex-end;';
  
  const cancelButton = document.createElement('button');
  cancelButton.textContent = '保持原文';
  cancelButton.style.cssText = `
    padding: 8px 16px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
  `;
  
  const confirmButton = document.createElement('button');
  confirmButton.textContent = '转换格式';
  confirmButton.style.cssText = `
    padding: 8px 16px;
    border: none;
    background: #1976d2;
    color: white;
    border-radius: 4px;
    cursor: pointer;
  `;
  
  cancelButton.onclick = () => {
    document.body.removeChild(overlay);
    onCancel();
  };
  
  confirmButton.onclick = () => {
    document.body.removeChild(overlay);
    onConfirm();
  };
  
  buttonContainer.appendChild(cancelButton);
  buttonContainer.appendChild(confirmButton);
  
  dialog.appendChild(title);
  dialog.appendChild(message);
  dialog.appendChild(preview);
  dialog.appendChild(buttonContainer);
  
  overlay.appendChild(dialog);
  document.body.appendChild(overlay);
  
  // 默认焦点在确认按钮上
  confirmButton.focus();
};

// 填充表格数据
const fillTableWithData = (editor, tableData) => {
  const { headers, rows } = tableData;

  // 填充表头
  headers.forEach((header, colIndex) => {
    if (colIndex === 0) {
      editor.commands.setTextSelection(editor.state.selection.from);
    } else {
      editor.commands.goToNextCell();
    }
    editor.commands.insertContent(header);
  });

  // 移动到下一行
  editor.commands.goToNextCell();

  // 填充数据行
  rows.forEach((row, rowIndex) => {
    row.forEach((cell, colIndex) => {
      if (colIndex > 0) {
        editor.commands.goToNextCell();
      }
      editor.commands.insertContent(cell);
    });

    // 如果不是最后一行，移动到下一行
    if (rowIndex < rows.length - 1) {
      editor.commands.goToNextCell();
    }
  });
};

// 插入格式化的Markdown
const insertFormattedMarkdown = (editor, text) => {
  // 简单的Markdown转换
  let html = text;

  // 标题
  html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
  html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
  html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');

  // 粗体和斜体
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

  // 内联代码
  html = html.replace(/`(.*?)`/g, '<code>$1</code>');

  // 引用
  html = html.replace(/^> (.*$)/gm, '<blockquote><p>$1</p></blockquote>');

  // 列表
  html = html.replace(/^\* (.*$)/gm, '<ul><li>$1</li></ul>');
  html = html.replace(/^\d+\. (.*$)/gm, '<ol><li>$1</li></ol>');

  // 段落
  html = html.replace(/\n\n/g, '</p><p>');
  html = '<p>' + html + '</p>';

  // 清理HTML
  html = html.replace(/<\/ul>\s*<ul>/g, '');
  html = html.replace(/<\/ol>\s*<ol>/g, '');
  html = html.replace(/<p><\/p>/g, '');

  editor.commands.insertContent(html);
};

// 基于ProseMirror view的格式化Markdown插入
const insertFormattedMarkdownWithView = (view, text) => {
  const { state } = view;
  const { from } = state.selection;
  const schema = state.schema;

  // 简单的Markdown解析和转换
  const lines = text.split('\n');
  const nodes = [];

  let i = 0;
  while (i < lines.length) {
    const line = lines[i].trim();

    if (!line) {
      i++;
      continue;
    }

    // 标题
    if (line.startsWith('# ')) {
      const content = line.substring(2).trim();
      if (content) {
        if (schema.nodes.heading) {
          nodes.push(schema.nodes.heading.create({ level: 1 }, schema.text(content)));
        } else {
          nodes.push(schema.nodes.paragraph.create(null, schema.text(content)));
        }
      }
    } else if (line.startsWith('## ')) {
      const content = line.substring(3).trim();
      if (content) {
        if (schema.nodes.heading) {
          nodes.push(schema.nodes.heading.create({ level: 2 }, schema.text(content)));
        } else {
          nodes.push(schema.nodes.paragraph.create(null, schema.text(content)));
        }
      }
    } else if (line.startsWith('### ')) {
      const content = line.substring(4).trim();
      if (content) {
        if (schema.nodes.heading) {
          nodes.push(schema.nodes.heading.create({ level: 3 }, schema.text(content)));
        } else {
          nodes.push(schema.nodes.paragraph.create(null, schema.text(content)));
        }
      }
    }
    // 引用
    else if (line.startsWith('> ')) {
      const content = line.substring(2).trim();
      if (content) {
        const paragraph = schema.nodes.paragraph.create(null, schema.text(content));
        if (schema.nodes.blockquote) {
          nodes.push(schema.nodes.blockquote.create(null, paragraph));
        } else {
          nodes.push(paragraph);
        }
      }
    }
    // 无序列表
    else if (line.startsWith('* ') || line.startsWith('- ')) {
      const content = line.substring(2).trim();
      if (content) {
        if (schema.nodes.bulletList && schema.nodes.listItem) {
          const listItem = schema.nodes.listItem.create(null,
            schema.nodes.paragraph.create(null, schema.text(content))
          );
          nodes.push(schema.nodes.bulletList.create(null, listItem));
        } else {
          nodes.push(schema.nodes.paragraph.create(null, schema.text(content)));
        }
      }
    }
    // 有序列表
    else if (/^\d+\.\s/.test(line)) {
      const content = line.replace(/^\d+\.\s/, '').trim();
      if (content) {
        if (schema.nodes.orderedList && schema.nodes.listItem) {
          const listItem = schema.nodes.listItem.create(null,
            schema.nodes.paragraph.create(null, schema.text(content))
          );
          nodes.push(schema.nodes.orderedList.create(null, listItem));
        } else {
          nodes.push(schema.nodes.paragraph.create(null, schema.text(content)));
        }
      }
    }
    // 代码块
    else if (line.startsWith('```')) {
      const codeLines = [];
      i++; // 跳过开始的```
      while (i < lines.length && !lines[i].trim().startsWith('```')) {
        codeLines.push(lines[i]);
        i++;
      }
      const codeContent = codeLines.join('\n');
      if (codeContent.trim()) {
        if (schema.nodes.codeBlock) {
          nodes.push(schema.nodes.codeBlock.create(null, schema.text(codeContent)));
        } else {
          nodes.push(schema.nodes.paragraph.create(null, schema.text(codeContent)));
        }
      }
    }
    // 普通段落（处理内联格式）
    else {
      const trimmedLine = line.trim();
      if (trimmedLine) {
        const inlineNodes = parseInlineMarkdown(schema, trimmedLine);
        if (inlineNodes.length > 0) {
          nodes.push(schema.nodes.paragraph.create(null, inlineNodes));
        }
      }
    }

    i++;
  }

  // 插入所有节点
  if (nodes.length > 0) {
    const tr = state.tr;
    let pos = from;

    nodes.forEach(node => {
      tr.insert(pos, node);
      pos += node.nodeSize;
    });

    view.dispatch(tr);
  }
};

// 解析内联Markdown格式
const parseInlineMarkdown = (schema, text) => {
  const nodes = [];

  // 检查schema中可用的marks
  const availableMarks = {
    strong: schema.marks.strong || schema.marks.bold,
    em: schema.marks.em || schema.marks.italic,
    code: schema.marks.code
  };

  // 简单的内联格式解析
  const patterns = [
    { regex: /\*\*(.*?)\*\*/g, mark: 'strong' },
    { regex: /\*(.*?)\*/g, mark: 'em' },
    { regex: /`(.*?)`/g, mark: 'code' },
  ];

  const matches = [];
  patterns.forEach(pattern => {
    // 只处理schema中存在的mark类型
    if (availableMarks[pattern.mark]) {
      let match;
      const regex = new RegExp(pattern.regex.source, pattern.regex.flags);
      while ((match = regex.exec(text)) !== null) {
        matches.push({
          start: match.index,
          end: match.index + match[0].length,
          content: match[1],
          mark: pattern.mark,
          full: match[0]
        });
      }
    }
  });

  // 按位置排序
  matches.sort((a, b) => a.start - b.start);

  // 构建节点
  let pos = 0;
  matches.forEach(match => {
    // 添加前面的普通文本
    if (pos < match.start) {
      const plainText = text.substring(pos, match.start);
      if (plainText && plainText.trim()) {
        nodes.push(schema.text(plainText));
      }
    }

    // 添加格式化文本
    const markType = availableMarks[match.mark];
    if (markType && match.content && match.content.trim()) {
      const mark = markType.create();
      nodes.push(schema.text(match.content, [mark]));
    } else if (match.content && match.content.trim()) {
      // 如果mark不存在，添加为普通文本
      nodes.push(schema.text(match.content));
    }

    pos = match.end;
  });

  // 添加剩余的普通文本
  if (pos < text.length) {
    const plainText = text.substring(pos);
    if (plainText && plainText.trim()) {
      nodes.push(schema.text(plainText));
    }
  }

  // 如果没有节点或者原文本为空，返回一个空格文本节点
  if (nodes.length === 0) {
    const trimmedText = text.trim();
    if (trimmedText) {
      return [schema.text(trimmedText)];
    } else {
      return [schema.text(' ')]; // 返回一个空格而不是空字符串
    }
  }

  return nodes;
};

// 转换并插入Markdown
const convertAndInsertMarkdown = (editor, text) => {
  // 检查是否是表格
  const tableData = parseMarkdownTable(text);
  if (tableData) {
    // 插入表格
    const { colCount, rowCount } = tableData;
    editor.chain().focus().insertTable({
      rows: rowCount,
      cols: colCount,
      withHeaderRow: true
    }).run();

    // 填充表格内容
    setTimeout(() => {
      fillTableWithData(editor, tableData);
    }, 100);

    return true;
  }

  // 处理其他Markdown格式
  insertFormattedMarkdown(editor, text);
  return true;
};

// 基于ProseMirror view的Markdown转换
const convertAndInsertMarkdownWithView = (view, text) => {
  const { state } = view;
  const { from } = state.selection;

  console.log('Converting markdown text:', text);

  // 检查是否是表格
  const tableData = parseMarkdownTable(text);
  console.log('Table data result:', tableData);

  if (tableData) {
    // 创建表格节点
    const { headers, rows } = tableData;
    const schema = state.schema;

    console.log('Schema nodes available:', {
      table: !!schema.nodes.table,
      tableRow: !!schema.nodes.tableRow,
      tableHeader: !!schema.nodes.tableHeader,
      tableCell: !!schema.nodes.tableCell
    });

    // 检查schema中是否有表格节点
    if (schema.nodes.table && schema.nodes.tableRow &&
        schema.nodes.tableHeader && schema.nodes.tableCell) {

      console.log('Creating table with headers:', headers, 'and rows:', rows);

      // 创建表格行
      const tableRows = [];

      // 创建表头行
      const headerCells = headers.map(header => {
        const content = header.trim() || ' '; // 确保不为空
        const paragraph = schema.nodes.paragraph.create(null, schema.text(content));
        return schema.nodes.tableHeader.create(null, paragraph);
      });
      const headerRow = schema.nodes.tableRow.create(null, headerCells);
      tableRows.push(headerRow);

      // 创建数据行
      rows.forEach(row => {
        const cells = row.map(cell => {
          const content = cell.trim() || ' '; // 确保不为空
          const paragraph = schema.nodes.paragraph.create(null, schema.text(content));
          return schema.nodes.tableCell.create(null, paragraph);
        });
        const dataRow = schema.nodes.tableRow.create(null, cells);
        tableRows.push(dataRow);
      });

      // 创建表格节点
      const table = schema.nodes.table.create(null, tableRows);

      // 插入表格
      const tr = state.tr.insert(from, table);
      view.dispatch(tr);

      console.log('Table inserted successfully');
      return;
    } else {
      // 如果没有表格节点，回退到普通文本处理
      console.warn('Table nodes not available in schema, falling back to text');
    }
  }

  // 处理其他Markdown格式
  insertFormattedMarkdownWithView(view, text);
};

export const MarkdownPasteHandler = Extension.create({
  name: 'markdownPasteHandler',
  
  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('markdownPasteHandler'),
        props: {
          handlePaste: (view, event, slice) => {
            const clipboardData = event.clipboardData;
            if (!clipboardData) return false;
            
            const text = clipboardData.getData('text/plain');
            if (!text || !isMarkdownContent(text)) return false;
            
            // 阻止默认粘贴行为
            event.preventDefault();
            
            // 显示确认对话框
            createConfirmDialog(
              text,
              () => {
                // 用户选择转换格式
                convertAndInsertMarkdownWithView(view, text);
              },
              () => {
                // 用户选择保持原文
                const { state } = view;
                const { from } = state.selection;
                const tr = state.tr.insertText(text, from);
                view.dispatch(tr);
              }
            );
            
            return true;
          },
        },
      }),
    ];
  },
  

});
