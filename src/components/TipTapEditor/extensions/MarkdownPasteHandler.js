import { Extension } from '@tiptap/core';
import { Plugin, Plugin<PERSON>ey } from 'prosemirror-state';

// Markdown表格解析器
const parseMarkdownTable = (text) => {
  const lines = text.trim().split('\n');
  if (lines.length < 2) return null;
  
  // 检查是否是表格格式
  const headerLine = lines[0];
  const separatorLine = lines[1];
  
  if (!headerLine.includes('|') || !separatorLine.includes('|') || !separatorLine.includes('-')) {
    return null;
  }
  
  // 解析表头
  const headers = headerLine.split('|').map(cell => cell.trim()).filter(cell => cell);
  
  // 解析数据行
  const rows = [];
  for (let i = 2; i < lines.length; i++) {
    const line = lines[i];
    if (line.includes('|')) {
      const cells = line.split('|').map(cell => cell.trim()).filter(cell => cell);
      if (cells.length > 0) {
        rows.push(cells);
      }
    }
  }
  
  if (headers.length === 0) return null;
  
  return {
    headers,
    rows,
    colCount: headers.length,
    rowCount: rows.length + 1 // +1 for header
  };
};

// 检查是否是Markdown内容
const isMarkdownContent = (text) => {
  const markdownPatterns = [
    /^\|.*\|.*$/m, // 表格
    /^#{1,6}\s+/m, // 标题
    /^\*\s+/m, // 无序列表
    /^\d+\.\s+/m, // 有序列表
    /^>\s+/m, // 引用
    /^```/m, // 代码块
    /\*\*.*\*\*/m, // 粗体
    /\*.*\*/m, // 斜体
    /`.*`/m, // 内联代码
  ];
  
  return markdownPatterns.some(pattern => pattern.test(text));
};

// 创建确认对话框
const createConfirmDialog = (content, onConfirm, onCancel) => {
  const overlay = document.createElement('div');
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
  `;
  
  const dialog = document.createElement('div');
  dialog.style.cssText = `
    background: white;
    border-radius: 8px;
    padding: 24px;
    max-width: 500px;
    max-height: 400px;
    overflow: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  `;
  
  const title = document.createElement('h3');
  title.textContent = '检测到Markdown内容';
  title.style.cssText = 'margin: 0 0 16px 0; color: #333;';
  
  const message = document.createElement('p');
  message.textContent = '是否将粘贴的Markdown内容转换为格式化文档？';
  message.style.cssText = 'margin: 0 0 16px 0; color: #666;';
  
  const preview = document.createElement('div');
  preview.style.cssText = `
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 12px;
    margin: 16px 0;
    max-height: 200px;
    overflow: auto;
    font-family: monospace;
    font-size: 12px;
    white-space: pre-wrap;
  `;
  preview.textContent = content.length > 500 ? content.substring(0, 500) + '...' : content;
  
  const buttonContainer = document.createElement('div');
  buttonContainer.style.cssText = 'display: flex; gap: 12px; justify-content: flex-end;';
  
  const cancelButton = document.createElement('button');
  cancelButton.textContent = '保持原文';
  cancelButton.style.cssText = `
    padding: 8px 16px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
  `;
  
  const confirmButton = document.createElement('button');
  confirmButton.textContent = '转换格式';
  confirmButton.style.cssText = `
    padding: 8px 16px;
    border: none;
    background: #1976d2;
    color: white;
    border-radius: 4px;
    cursor: pointer;
  `;
  
  cancelButton.onclick = () => {
    document.body.removeChild(overlay);
    onCancel();
  };
  
  confirmButton.onclick = () => {
    document.body.removeChild(overlay);
    onConfirm();
  };
  
  buttonContainer.appendChild(cancelButton);
  buttonContainer.appendChild(confirmButton);
  
  dialog.appendChild(title);
  dialog.appendChild(message);
  dialog.appendChild(preview);
  dialog.appendChild(buttonContainer);
  
  overlay.appendChild(dialog);
  document.body.appendChild(overlay);
  
  // 默认焦点在确认按钮上
  confirmButton.focus();
};

// 填充表格数据
const fillTableWithData = (editor, tableData) => {
  const { headers, rows } = tableData;

  // 填充表头
  headers.forEach((header, colIndex) => {
    if (colIndex === 0) {
      editor.commands.setTextSelection(editor.state.selection.from);
    } else {
      editor.commands.goToNextCell();
    }
    editor.commands.insertContent(header);
  });

  // 移动到下一行
  editor.commands.goToNextCell();

  // 填充数据行
  rows.forEach((row, rowIndex) => {
    row.forEach((cell, colIndex) => {
      if (colIndex > 0) {
        editor.commands.goToNextCell();
      }
      editor.commands.insertContent(cell);
    });

    // 如果不是最后一行，移动到下一行
    if (rowIndex < rows.length - 1) {
      editor.commands.goToNextCell();
    }
  });
};

// 插入格式化的Markdown
const insertFormattedMarkdown = (editor, text) => {
  // 简单的Markdown转换
  let html = text;

  // 标题
  html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
  html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
  html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');

  // 粗体和斜体
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

  // 内联代码
  html = html.replace(/`(.*?)`/g, '<code>$1</code>');

  // 引用
  html = html.replace(/^> (.*$)/gm, '<blockquote><p>$1</p></blockquote>');

  // 列表
  html = html.replace(/^\* (.*$)/gm, '<ul><li>$1</li></ul>');
  html = html.replace(/^\d+\. (.*$)/gm, '<ol><li>$1</li></ol>');

  // 段落
  html = html.replace(/\n\n/g, '</p><p>');
  html = '<p>' + html + '</p>';

  // 清理HTML
  html = html.replace(/<\/ul>\s*<ul>/g, '');
  html = html.replace(/<\/ol>\s*<ol>/g, '');
  html = html.replace(/<p><\/p>/g, '');

  editor.commands.insertContent(html);
};

// 转换并插入Markdown
const convertAndInsertMarkdown = (editor, text) => {
  // 检查是否是表格
  const tableData = parseMarkdownTable(text);
  if (tableData) {
    // 插入表格
    const { colCount, rowCount } = tableData;
    editor.chain().focus().insertTable({
      rows: rowCount,
      cols: colCount,
      withHeaderRow: true
    }).run();

    // 填充表格内容
    setTimeout(() => {
      fillTableWithData(editor, tableData);
    }, 100);

    return true;
  }

  // 处理其他Markdown格式
  insertFormattedMarkdown(editor, text);
  return true;
};

export const MarkdownPasteHandler = Extension.create({
  name: 'markdownPasteHandler',
  
  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('markdownPasteHandler'),
        props: {
          handlePaste: (view, event, slice) => {
            const clipboardData = event.clipboardData;
            if (!clipboardData) return false;
            
            const text = clipboardData.getData('text/plain');
            if (!text || !isMarkdownContent(text)) return false;
            
            // 阻止默认粘贴行为
            event.preventDefault();
            
            // 显示确认对话框
            createConfirmDialog(
              text,
              () => {
                // 用户选择转换格式
                const editor = view.state.config.editor || view.state.editor;
                if (editor) {
                  convertAndInsertMarkdown(editor, text);
                }
              },
              () => {
                // 用户选择保持原文
                const { state } = view;
                const { from } = state.selection;
                const tr = state.tr.insertText(text, from);
                view.dispatch(tr);
              }
            );
            
            return true;
          },
        },
      }),
    ];
  },
  

});
