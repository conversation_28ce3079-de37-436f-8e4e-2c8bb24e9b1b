import { Extension } from '@tiptap/core';
import { Plugin, Plugin<PERSON>ey } from 'prosemirror-state';

// Markdown表格解析器
const parseMarkdownTable = (text) => {
  const lines = text.trim().split('\n');
  if (lines.length < 2) return null;
  
  // 检查是否是表格格式
  const headerLine = lines[0];
  const separatorLine = lines[1];
  
  if (!headerLine.includes('|') || !separatorLine.includes('|') || !separatorLine.includes('-')) {
    return null;
  }
  
  // 解析表头
  const headers = headerLine.split('|').map(cell => cell.trim()).filter(cell => cell);
  
  // 解析数据行
  const rows = [];
  for (let i = 2; i < lines.length; i++) {
    const line = lines[i];
    if (line.includes('|')) {
      const cells = line.split('|').map(cell => cell.trim()).filter(cell => cell);
      if (cells.length > 0) {
        rows.push(cells);
      }
    }
  }
  
  if (headers.length === 0) return null;
  
  return {
    headers,
    rows,
    colCount: headers.length,
    rowCount: rows.length + 1 // +1 for header
  };
};

// 检查是否是Markdown内容
const isMarkdownContent = (text) => {
  const markdownPatterns = [
    /^\|.*\|.*$/m, // 表格
    /^#{1,6}\s+/m, // 标题
    /^\*\s+/m, // 无序列表
    /^\d+\.\s+/m, // 有序列表
    /^>\s+/m, // 引用
    /^```/m, // 代码块
    /\*\*.*\*\*/m, // 粗体
    /\*.*\*/m, // 斜体
    /`.*`/m, // 内联代码
  ];
  
  return markdownPatterns.some(pattern => pattern.test(text));
};

// 创建确认对话框
const createConfirmDialog = (content, onConfirm, onCancel) => {
  const overlay = document.createElement('div');
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
  `;
  
  const dialog = document.createElement('div');
  dialog.style.cssText = `
    background: white;
    border-radius: 8px;
    padding: 24px;
    max-width: 500px;
    max-height: 400px;
    overflow: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  `;
  
  const title = document.createElement('h3');
  title.textContent = '检测到Markdown内容';
  title.style.cssText = 'margin: 0 0 16px 0; color: #333;';
  
  const message = document.createElement('p');
  message.textContent = '是否将粘贴的Markdown内容转换为格式化文档？';
  message.style.cssText = 'margin: 0 0 16px 0; color: #666;';
  
  const preview = document.createElement('div');
  preview.style.cssText = `
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 12px;
    margin: 16px 0;
    max-height: 200px;
    overflow: auto;
    font-family: monospace;
    font-size: 12px;
    white-space: pre-wrap;
  `;
  preview.textContent = content.length > 500 ? content.substring(0, 500) + '...' : content;
  
  const buttonContainer = document.createElement('div');
  buttonContainer.style.cssText = 'display: flex; gap: 12px; justify-content: flex-end;';
  
  const cancelButton = document.createElement('button');
  cancelButton.textContent = '保持原文';
  cancelButton.style.cssText = `
    padding: 8px 16px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
  `;
  
  const confirmButton = document.createElement('button');
  confirmButton.textContent = '转换格式';
  confirmButton.style.cssText = `
    padding: 8px 16px;
    border: none;
    background: #1976d2;
    color: white;
    border-radius: 4px;
    cursor: pointer;
  `;
  
  cancelButton.onclick = () => {
    document.body.removeChild(overlay);
    onCancel();
  };
  
  confirmButton.onclick = () => {
    document.body.removeChild(overlay);
    onConfirm();
  };
  
  buttonContainer.appendChild(cancelButton);
  buttonContainer.appendChild(confirmButton);
  
  dialog.appendChild(title);
  dialog.appendChild(message);
  dialog.appendChild(preview);
  dialog.appendChild(buttonContainer);
  
  overlay.appendChild(dialog);
  document.body.appendChild(overlay);
  
  // 默认焦点在确认按钮上
  confirmButton.focus();
};

// 填充表格数据
const fillTableWithData = (editor, tableData) => {
  const { headers, rows } = tableData;

  // 填充表头
  headers.forEach((header, colIndex) => {
    if (colIndex === 0) {
      editor.commands.setTextSelection(editor.state.selection.from);
    } else {
      editor.commands.goToNextCell();
    }
    editor.commands.insertContent(header);
  });

  // 移动到下一行
  editor.commands.goToNextCell();

  // 填充数据行
  rows.forEach((row, rowIndex) => {
    row.forEach((cell, colIndex) => {
      if (colIndex > 0) {
        editor.commands.goToNextCell();
      }
      editor.commands.insertContent(cell);
    });

    // 如果不是最后一行，移动到下一行
    if (rowIndex < rows.length - 1) {
      editor.commands.goToNextCell();
    }
  });
};

// 插入格式化的Markdown
const insertFormattedMarkdown = (editor, text) => {
  // 简单的Markdown转换
  let html = text;

  // 标题
  html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
  html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
  html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');

  // 粗体和斜体
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

  // 内联代码
  html = html.replace(/`(.*?)`/g, '<code>$1</code>');

  // 引用
  html = html.replace(/^> (.*$)/gm, '<blockquote><p>$1</p></blockquote>');

  // 列表
  html = html.replace(/^\* (.*$)/gm, '<ul><li>$1</li></ul>');
  html = html.replace(/^\d+\. (.*$)/gm, '<ol><li>$1</li></ol>');

  // 段落
  html = html.replace(/\n\n/g, '</p><p>');
  html = '<p>' + html + '</p>';

  // 清理HTML
  html = html.replace(/<\/ul>\s*<ul>/g, '');
  html = html.replace(/<\/ol>\s*<ol>/g, '');
  html = html.replace(/<p><\/p>/g, '');

  editor.commands.insertContent(html);
};

// 基于ProseMirror view的格式化Markdown插入
const insertFormattedMarkdownWithView = (view, text) => {
  const { state } = view;
  const { from } = state.selection;
  const schema = state.schema;

  // 简单的Markdown解析和转换
  const lines = text.split('\n');
  const nodes = [];

  let i = 0;
  while (i < lines.length) {
    const line = lines[i].trim();

    if (!line) {
      i++;
      continue;
    }

    // 标题
    if (line.startsWith('# ')) {
      const content = line.substring(2);
      nodes.push(schema.nodes.heading.create({ level: 1 }, schema.text(content)));
    } else if (line.startsWith('## ')) {
      const content = line.substring(3);
      nodes.push(schema.nodes.heading.create({ level: 2 }, schema.text(content)));
    } else if (line.startsWith('### ')) {
      const content = line.substring(4);
      nodes.push(schema.nodes.heading.create({ level: 3 }, schema.text(content)));
    }
    // 引用
    else if (line.startsWith('> ')) {
      const content = line.substring(2);
      const paragraph = schema.nodes.paragraph.create(null, schema.text(content));
      nodes.push(schema.nodes.blockquote.create(null, paragraph));
    }
    // 无序列表
    else if (line.startsWith('* ') || line.startsWith('- ')) {
      const content = line.substring(2);
      const listItem = schema.nodes.listItem.create(null,
        schema.nodes.paragraph.create(null, schema.text(content))
      );
      nodes.push(schema.nodes.bulletList.create(null, listItem));
    }
    // 有序列表
    else if (/^\d+\.\s/.test(line)) {
      const content = line.replace(/^\d+\.\s/, '');
      const listItem = schema.nodes.listItem.create(null,
        schema.nodes.paragraph.create(null, schema.text(content))
      );
      nodes.push(schema.nodes.orderedList.create(null, listItem));
    }
    // 代码块
    else if (line.startsWith('```')) {
      const codeLines = [];
      i++; // 跳过开始的```
      while (i < lines.length && !lines[i].trim().startsWith('```')) {
        codeLines.push(lines[i]);
        i++;
      }
      const codeContent = codeLines.join('\n');
      nodes.push(schema.nodes.codeBlock.create(null, schema.text(codeContent)));
    }
    // 普通段落（处理内联格式）
    else {
      const inlineNodes = parseInlineMarkdown(schema, line);
      nodes.push(schema.nodes.paragraph.create(null, inlineNodes));
    }

    i++;
  }

  // 插入所有节点
  if (nodes.length > 0) {
    const tr = state.tr;
    let pos = from;

    nodes.forEach(node => {
      tr.insert(pos, node);
      pos += node.nodeSize;
    });

    view.dispatch(tr);
  }
};

// 解析内联Markdown格式
const parseInlineMarkdown = (schema, text) => {
  const nodes = [];
  let currentPos = 0;

  // 简单的内联格式解析
  const patterns = [
    { regex: /\*\*(.*?)\*\*/g, mark: 'strong' },
    { regex: /\*(.*?)\*/g, mark: 'em' },
    { regex: /`(.*?)`/g, mark: 'code' },
  ];

  const matches = [];
  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.regex.exec(text)) !== null) {
      matches.push({
        start: match.index,
        end: match.index + match[0].length,
        content: match[1],
        mark: pattern.mark,
        full: match[0]
      });
    }
  });

  // 按位置排序
  matches.sort((a, b) => a.start - b.start);

  // 构建节点
  let pos = 0;
  matches.forEach(match => {
    // 添加前面的普通文本
    if (pos < match.start) {
      const plainText = text.substring(pos, match.start);
      if (plainText) {
        nodes.push(schema.text(plainText));
      }
    }

    // 添加格式化文本
    const mark = schema.marks[match.mark].create();
    nodes.push(schema.text(match.content, [mark]));

    pos = match.end;
  });

  // 添加剩余的普通文本
  if (pos < text.length) {
    const plainText = text.substring(pos);
    if (plainText) {
      nodes.push(schema.text(plainText));
    }
  }

  return nodes.length > 0 ? nodes : [schema.text(text)];
};

// 转换并插入Markdown
const convertAndInsertMarkdown = (editor, text) => {
  // 检查是否是表格
  const tableData = parseMarkdownTable(text);
  if (tableData) {
    // 插入表格
    const { colCount, rowCount } = tableData;
    editor.chain().focus().insertTable({
      rows: rowCount,
      cols: colCount,
      withHeaderRow: true
    }).run();

    // 填充表格内容
    setTimeout(() => {
      fillTableWithData(editor, tableData);
    }, 100);

    return true;
  }

  // 处理其他Markdown格式
  insertFormattedMarkdown(editor, text);
  return true;
};

// 基于ProseMirror view的Markdown转换
const convertAndInsertMarkdownWithView = (view, text) => {
  const { state } = view;
  const { from } = state.selection;

  // 检查是否是表格
  const tableData = parseMarkdownTable(text);
  if (tableData) {
    // 创建表格节点
    const { headers, rows } = tableData;
    const schema = state.schema;

    // 创建表格行
    const tableRows = [];

    // 创建表头行
    const headerCells = headers.map(header =>
      schema.nodes.tableHeader.create(null, schema.text(header))
    );
    const headerRow = schema.nodes.tableRow.create(null, headerCells);
    tableRows.push(headerRow);

    // 创建数据行
    rows.forEach(row => {
      const cells = row.map(cell =>
        schema.nodes.tableCell.create(null, schema.text(cell))
      );
      const dataRow = schema.nodes.tableRow.create(null, cells);
      tableRows.push(dataRow);
    });

    // 创建表格节点
    const table = schema.nodes.table.create(null, tableRows);

    // 插入表格
    const tr = state.tr.insert(from, table);
    view.dispatch(tr);

    return;
  }

  // 处理其他Markdown格式
  insertFormattedMarkdownWithView(view, text);
};

export const MarkdownPasteHandler = Extension.create({
  name: 'markdownPasteHandler',
  
  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('markdownPasteHandler'),
        props: {
          handlePaste: (view, event, slice) => {
            const clipboardData = event.clipboardData;
            if (!clipboardData) return false;
            
            const text = clipboardData.getData('text/plain');
            if (!text || !isMarkdownContent(text)) return false;
            
            // 阻止默认粘贴行为
            event.preventDefault();
            
            // 显示确认对话框
            createConfirmDialog(
              text,
              () => {
                // 用户选择转换格式
                convertAndInsertMarkdownWithView(view, text);
              },
              () => {
                // 用户选择保持原文
                const { state } = view;
                const { from } = state.selection;
                const tr = state.tr.insertText(text, from);
                view.dispatch(tr);
              }
            );
            
            return true;
          },
        },
      }),
    ];
  },
  

});
