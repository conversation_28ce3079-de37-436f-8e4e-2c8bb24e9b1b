/* TipTap编辑器基础样式 */
.tiptap-editor-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
}

.tiptap-editor-toolbar {
  padding: 12px 16px;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.tiptap-editor-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* 编辑器内容样式 */
.ProseMirror {
  outline: none;
  min-height: 200px;
  line-height: 1.6;
  font-size: 14px;
  color: #333;
}

.ProseMirror p {
  margin: 0 0 12px 0;
}

.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  margin: 24px 0 12px 0;
  font-weight: 600;
  line-height: 1.3;
}

.ProseMirror h1 { font-size: 28px; }
.ProseMirror h2 { font-size: 24px; }
.ProseMirror h3 { font-size: 20px; }
.ProseMirror h4 { font-size: 18px; }
.ProseMirror h5 { font-size: 16px; }
.ProseMirror h6 { font-size: 14px; }

/* 表格样式 */
.ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 16px 0;
  overflow: hidden;
  border: 1px solid #ddd;
}

.ProseMirror td,
.ProseMirror th {
  min-width: 1em;
  border: 1px solid #ddd;
  padding: 8px 12px;
  vertical-align: top;
  box-sizing: border-box;
  position: relative;
  word-wrap: break-word;
  overflow-wrap: anywhere;
}

.ProseMirror th {
  font-weight: bold;
  text-align: left;
  background-color: #f8f9fa;
}

.ProseMirror .selectedCell:after {
  z-index: 2;
  position: absolute;
  content: "";
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(200, 200, 255, 0.4);
  pointer-events: none;
}

.ProseMirror .column-resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: -2px;
  width: 4px;
  background-color: #adf;
  pointer-events: none;
}

.ProseMirror.resize-cursor {
  cursor: ew-resize;
  cursor: col-resize;
}

/* 列表样式 */
.ProseMirror ul,
.ProseMirror ol {
  padding-left: 24px;
  margin: 12px 0;
}

.ProseMirror ul {
  list-style-type: disc;
}

.ProseMirror ol {
  list-style-type: decimal;
}

.ProseMirror li {
  margin: 4px 0;
  display: list-item;
}

.ProseMirror ul ul {
  list-style-type: circle;
}

.ProseMirror ul ul ul {
  list-style-type: square;
}

/* 代码样式 */
.ProseMirror code {
  background-color: #f1f3f4;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

.ProseMirror pre {
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 4px;
  padding: 12px;
  margin: 16px 0;
  overflow-x: auto;
}

.ProseMirror pre code {
  background: none;
  padding: 0;
  border-radius: 0;
  font-size: 13px;
}

/* 引用样式 */
.ProseMirror blockquote {
  border-left: 4px solid #1976d2;
  padding-left: 16px;
  margin: 16px 0;
  color: #666;
  font-style: italic;
  background-color: #f8f9fa;
  padding: 12px 16px;
  border-radius: 4px;
}

.ProseMirror blockquote p {
  margin: 0;
}

/* 图片样式 */
.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 8px 0;
}

/* 链接样式 */
.ProseMirror a {
  color: #1976d2;
  text-decoration: none;
}

.ProseMirror a:hover {
  text-decoration: underline;
}

/* 占位符样式 */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

/* 焦点样式 */
.ProseMirror-focused {
  outline: none;
}

/* 选择样式 */
.ProseMirror ::selection {
  background: #b3d4fc;
}

/* 工具栏按钮样式 */
.toolbar-button {
  padding: 6px 12px;
  border: 1px solid #ddd;
  background: #fff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.toolbar-button:hover {
  background: #f8f9fa;
  border-color: #999;
}

.toolbar-button.is-active {
  background: #1976d2;
  color: #fff;
  border-color: #1976d2;
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 气泡菜单样式 */
.bubble-menu {
  display: flex;
  background: #fff;
  padding: 4px;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e1e5e9;
  gap: 2px;
}

.bubble-menu button {
  padding: 6px 8px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.bubble-menu button:hover {
  background: #f8f9fa;
}

.bubble-menu button.is-active {
  background: #e3f2fd;
  color: #1976d2;
}

/* 浮动菜单样式 */
.floating-menu {
  display: flex;
  background: #fff;
  padding: 8px;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e1e5e9;
  gap: 4px;
}

.floating-menu button {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background: #fff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.floating-menu button:hover {
  background: #f8f9fa;
  border-color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tiptap-editor-toolbar {
    padding: 8px 12px;
  }
  
  .tiptap-editor-content {
    padding: 12px;
  }
  
  .ProseMirror {
    font-size: 16px;
  }
  
  .toolbar-button {
    padding: 8px 10px;
    font-size: 16px;
  }
}
