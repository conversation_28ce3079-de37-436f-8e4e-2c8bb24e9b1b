/* 表格编辑菜单样式 */
.table-edit-menu {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  max-width: 250px;
  font-size: 14px;
  user-select: none;
}

.table-edit-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
  font-weight: 600;
  color: #333;
}

.close-button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: #e9ecef;
  color: #333;
}

.table-edit-menu-content {
  padding: 8px 0;
}

.menu-section {
  margin-bottom: 8px;
}

.menu-section:last-child {
  margin-bottom: 0;
}

.menu-section-title {
  padding: 8px 16px 4px 16px;
  font-size: 12px;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.menu-item {
  width: 100%;
  padding: 8px 16px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #333;
  transition: all 0.2s ease;
}

.menu-item:hover {
  background: #f8f9fa;
}

.menu-item.danger {
  color: #dc3545;
}

.menu-item.danger:hover {
  background: #fff5f5;
  color: #c82333;
}

.menu-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

/* 表格悬浮编辑按钮 */
.table-edit-button {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background: #007bff;
  border: 2px solid white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
  transition: all 0.2s ease;
  z-index: 100;
}

.table-edit-button:hover {
  background: #0056b3;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.table-edit-button:active {
  transform: scale(0.95);
}

/* 表格容器样式 */
.table-wrapper {
  position: relative;
  display: inline-block;
}

.table-wrapper:hover .table-edit-button {
  opacity: 1;
}

.table-edit-button {
  opacity: 0;
  transition: opacity 0.2s ease;
}

/* 确保表格在编辑时有正确的定位 */
.ProseMirror table {
  position: relative;
}

.ProseMirror .tableWrapper {
  position: relative;
  margin: 16px 0;
}

.ProseMirror .tableWrapper:hover .table-edit-button {
  opacity: 1;
}
