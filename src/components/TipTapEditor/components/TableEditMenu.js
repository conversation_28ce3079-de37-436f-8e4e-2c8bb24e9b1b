import React, { useState, useRef, useEffect } from 'react';
import './TableEditMenu.css';

const TableEditMenu = ({ editor, tableNode, position, onClose, node, getPos }) => {
  const [isVisible, setIsVisible] = useState(true);
  const menuRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  // 确保编辑器聚焦到表格
  const ensureTableFocus = () => {
    return new Promise((resolve) => {
      // 使用TipTap的原生方式聚焦到表格
      if (node && getPos) {
        try {
          const pos = getPos();
          editor.commands.focus();
          editor.commands.setTextSelection(pos);
          setTimeout(resolve, 100);
        } catch (error) {
          console.warn('Failed to focus table using node position:', error);
          // 回退到简单聚焦
          editor.commands.focus();
          setTimeout(resolve, 100);
        }
      } else {
        // 回退到简单聚焦
        editor.commands.focus();
        setTimeout(resolve, 100);
      }
    });
  };

  const commands = {
    // 行操作
    addRowBefore: async () => {
      console.log('Adding row before'); // 调试日志
      await ensureTableFocus();
      const result = editor.chain().focus().addRowBefore().run();
      console.log('Add row before result:', result); // 调试日志
      onClose();
    },
    addRowAfter: async () => {
      console.log('Adding row after'); // 调试日志
      await ensureTableFocus();
      const result = editor.chain().focus().addRowAfter().run();
      console.log('Add row after result:', result); // 调试日志
      onClose();
    },
    deleteRow: async () => {
      console.log('Deleting row'); // 调试日志
      await ensureTableFocus();
      const result = editor.chain().focus().deleteRow().run();
      console.log('Delete row result:', result); // 调试日志
      onClose();
    },

    // 列操作
    addColumnBefore: async () => {
      console.log('Adding column before'); // 调试日志
      await ensureTableFocus();
      const result = editor.chain().focus().addColumnBefore().run();
      console.log('Add column before result:', result); // 调试日志
      onClose();
    },
    addColumnAfter: async () => {
      console.log('Adding column after'); // 调试日志
      await ensureTableFocus();
      const result = editor.chain().focus().addColumnAfter().run();
      console.log('Add column after result:', result); // 调试日志
      onClose();
    },
    deleteColumn: async () => {
      console.log('Deleting column'); // 调试日志
      await ensureTableFocus();
      const result = editor.chain().focus().deleteColumn().run();
      console.log('Delete column result:', result); // 调试日志
      onClose();
    },

    // 表格操作
    deleteTable: async () => {
      console.log('Deleting table'); // 调试日志
      await ensureTableFocus();
      const result = editor.chain().focus().deleteTable().run();
      console.log('Delete table result:', result); // 调试日志
      onClose();
    },
    toggleHeaderRow: async () => {
      console.log('Toggling header row'); // 调试日志
      await ensureTableFocus();
      const result = editor.chain().focus().toggleHeaderRow().run();
      console.log('Toggle header row result:', result); // 调试日志
      onClose();
    },
    toggleHeaderColumn: async () => {
      console.log('Toggling header column'); // 调试日志
      await ensureTableFocus();
      const result = editor.chain().focus().toggleHeaderColumn().run();
      console.log('Toggle header column result:', result); // 调试日志
      onClose();
    },
  };

  if (!isVisible) return null;

  return (
    <div
      ref={menuRef}
      className="table-edit-menu"
      style={{
        position: 'absolute',
        top: position.top,
        left: position.left,
        zIndex: 1000,
      }}
    >
      <div className="table-edit-menu-header">
        <span>表格编辑</span>
        <button className="close-button" onClick={onClose}>
          ×
        </button>
      </div>

      <div className="table-edit-menu-content">
        {/* 行操作 */}
        <div className="menu-section">
          <div className="menu-section-title">行操作</div>
          <button className="menu-item" onClick={commands.addRowBefore}>
            <span className="menu-icon">⬆️</span>
            在上方插入行
          </button>
          <button className="menu-item" onClick={commands.addRowAfter}>
            <span className="menu-icon">⬇️</span>
            在下方插入行
          </button>
          <button className="menu-item danger" onClick={commands.deleteRow}>
            <span className="menu-icon">🗑️</span>
            删除当前行
          </button>
        </div>

        {/* 列操作 */}
        <div className="menu-section">
          <div className="menu-section-title">列操作</div>
          <button className="menu-item" onClick={commands.addColumnBefore}>
            <span className="menu-icon">⬅️</span>
            在左侧插入列
          </button>
          <button className="menu-item" onClick={commands.addColumnAfter}>
            <span className="menu-icon">➡️</span>
            在右侧插入列
          </button>
          <button className="menu-item danger" onClick={commands.deleteColumn}>
            <span className="menu-icon">🗑️</span>
            删除当前列
          </button>
        </div>

        {/* 表格操作 */}
        <div className="menu-section">
          <div className="menu-section-title">表格操作</div>
          <button className="menu-item" onClick={commands.toggleHeaderRow}>
            <span className="menu-icon">📋</span>
            切换表头行
          </button>
          <button className="menu-item" onClick={commands.toggleHeaderColumn}>
            <span className="menu-icon">📄</span>
            切换表头列
          </button>
          <button className="menu-item danger" onClick={commands.deleteTable}>
            <span className="menu-icon">🗑️</span>
            删除整个表格
          </button>
        </div>
      </div>
    </div>
  );
};

export default TableEditMenu;
