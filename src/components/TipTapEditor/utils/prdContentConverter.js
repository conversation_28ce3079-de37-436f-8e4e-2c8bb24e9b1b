/**
 * PRD内容转换工具
 * 将BSV PRD文档内容转换为TipTap编辑器可用的HTML格式
 */

/**
 * 将PRD章节内容转换为HTML
 * @param {Object} prdContent - PRD文档内容对象
 * @returns {string} HTML格式的内容
 */
export function convertPRDToHTML(prdContent) {
  if (!prdContent || !prdContent.sections) {
    return '<p>暂无内容</p>';
  }

  let html = '';

  // 添加文档标题
  if (prdContent.title) {
    html += `<h1>${prdContent.title}</h1>\n`;
  }

  // 处理每个章节
  prdContent.sections.forEach(section => {
    html += convertSectionToHTML(section);
  });

  return html;
}

/**
 * 将单个章节转换为HTML
 * @param {Object} section - 章节对象
 * @returns {string} HTML格式的章节内容
 */
function convertSectionToHTML(section) {
  if (!section || !section.content) {
    return '';
  }

  let html = '';
  const content = section.content.trim();

  // 根据章节级别添加标题
  if (section.title && section.level > 0) {
    const headingLevel = Math.min(section.level + 1, 6); // 限制在h1-h6范围内
    html += `<h${headingLevel}>${section.title}</h${headingLevel}>\n`;
  }

  // 处理章节内容
  html += convertMarkdownToHTML(content);
  html += '\n';

  return html;
}

/**
 * 将Markdown格式的内容转换为HTML
 * @param {string} markdown - Markdown格式的内容
 * @returns {string} HTML格式的内容
 */
function convertMarkdownToHTML(markdown) {
  if (!markdown) {
    return '';
  }

  let html = '';
  const lines = markdown.split('\n');
  let inTable = false;
  let inCodeBlock = false;
  let codeBlockLanguage = '';

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const trimmedLine = line.trim();

    // 处理代码块
    if (trimmedLine.startsWith('```')) {
      if (!inCodeBlock) {
        // 开始代码块
        inCodeBlock = true;
        codeBlockLanguage = trimmedLine.substring(3).trim();
        html += `<pre><code class="language-${codeBlockLanguage}">`;
      } else {
        // 结束代码块
        inCodeBlock = false;
        html += '</code></pre>\n';
        codeBlockLanguage = '';
      }
      continue;
    }

    // 在代码块内部
    if (inCodeBlock) {
      html += escapeHtml(line) + '\n';
      continue;
    }

    // 处理表格
    if (trimmedLine.includes('|') && !inTable) {
      // 检查是否是表格开始
      const nextLine = i + 1 < lines.length ? lines[i + 1].trim() : '';
      if (nextLine.includes('|') && nextLine.includes('-')) {
        inTable = true;
        html += '<table>\n';
        // 处理表头
        html += '<thead>\n<tr>\n';
        const headers = line.split('|').map(cell => cell.trim()).filter(cell => cell);
        headers.forEach(header => {
          html += `<th>${processInlineFormatting(header)}</th>\n`;
        });
        html += '</tr>\n</thead>\n<tbody>\n';
        i++; // 跳过分隔行
        continue;
      }
    }

    if (inTable) {
      if (trimmedLine.includes('|')) {
        // 表格数据行
        html += '<tr>\n';
        const cells = line.split('|').map(cell => cell.trim()).filter(cell => cell);
        cells.forEach(cell => {
          html += `<td>${processInlineFormatting(cell)}</td>\n`;
        });
        html += '</tr>\n';
      } else {
        // 表格结束
        inTable = false;
        html += '</tbody>\n</table>\n';
        // 处理当前行
        html += processLine(line);
      }
      continue;
    }

    // 处理普通行
    html += processLine(line);
  }

  // 如果表格没有正常结束，补充结束标签
  if (inTable) {
    html += '</tbody>\n</table>\n';
  }

  // 如果代码块没有正常结束，补充结束标签
  if (inCodeBlock) {
    html += '</code></pre>\n';
  }

  return html;
}

/**
 * 处理单行内容
 * @param {string} line - 单行内容
 * @returns {string} 处理后的HTML
 */
function processLine(line) {
  const trimmedLine = line.trim();

  // 空行
  if (!trimmedLine) {
    return '<br>\n';
  }

  // 标题（已在章节级别处理，这里处理内容中的标题）
  if (trimmedLine.startsWith('#')) {
    const level = trimmedLine.match(/^#+/)[0].length;
    const title = trimmedLine.substring(level).trim();
    const headingLevel = Math.min(level, 6);
    return `<h${headingLevel}>${processInlineFormatting(title)}</h${headingLevel}>\n`;
  }

  // 列表项
  if (trimmedLine.match(/^[\-\*\+]\s/)) {
    const content = trimmedLine.substring(2);
    return `<ul><li>${processInlineFormatting(content)}</li></ul>\n`;
  }

  if (trimmedLine.match(/^\d+\.\s/)) {
    const content = trimmedLine.substring(trimmedLine.indexOf('.') + 1).trim();
    return `<ol><li>${processInlineFormatting(content)}</li></ol>\n`;
  }

  // 引用
  if (trimmedLine.startsWith('>')) {
    const content = trimmedLine.substring(1).trim();
    return `<blockquote><p>${processInlineFormatting(content)}</p></blockquote>\n`;
  }

  // 水平分割线
  if (trimmedLine.match(/^[\-\*_]{3,}$/)) {
    return '<hr>\n';
  }

  // 普通段落
  return `<p>${processInlineFormatting(trimmedLine)}</p>\n`;
}

/**
 * 处理行内格式化
 * @param {string} text - 文本内容
 * @returns {string} 处理后的HTML
 */
function processInlineFormatting(text) {
  if (!text) return '';

  let result = text;

  // 粗体 **text** 或 __text__
  result = result.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  result = result.replace(/__(.*?)__/g, '<strong>$1</strong>');

  // 斜体 *text* 或 _text_
  result = result.replace(/\*(.*?)\*/g, '<em>$1</em>');
  result = result.replace(/_(.*?)_/g, '<em>$1</em>');

  // 删除线 ~~text~~
  result = result.replace(/~~(.*?)~~/g, '<s>$1</s>');

  // 内联代码 `code`
  result = result.replace(/`(.*?)`/g, '<code>$1</code>');

  // 链接 [text](url)
  result = result.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

  // HTML转义（避免XSS）
  result = escapeHtml(result);

  return result;
}

/**
 * HTML转义
 * @param {string} text - 需要转义的文本
 * @returns {string} 转义后的文本
 */
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

/**
 * 获取PRD文档的摘要信息
 * @param {Object} prdContent - PRD文档内容
 * @returns {Object} 摘要信息
 */
export function getPRDSummary(prdContent) {
  if (!prdContent || !prdContent.sections) {
    return {
      title: '无标题',
      totalSections: 0,
      totalWords: 0,
      lastModified: null
    };
  }

  const totalWords = prdContent.sections.reduce((count, section) => {
    return count + (section.content ? section.content.length : 0);
  }, 0);

  return {
    title: prdContent.title || '无标题',
    totalSections: prdContent.sections.length,
    totalWords: Math.floor(totalWords / 2), // 粗略估算中文字数
    lastModified: new Date().toLocaleDateString()
  };
}

/**
 * 按章节级别组织内容
 * @param {Object} prdContent - PRD文档内容
 * @returns {Array} 组织后的章节树
 */
export function organizeSectionsByLevel(prdContent) {
  if (!prdContent || !prdContent.sections) {
    return [];
  }

  const organized = [];
  const stack = [];

  prdContent.sections.forEach(section => {
    const item = {
      ...section,
      children: []
    };

    // 根据级别确定父子关系
    while (stack.length > 0 && stack[stack.length - 1].level >= section.level) {
      stack.pop();
    }

    if (stack.length === 0) {
      organized.push(item);
    } else {
      stack[stack.length - 1].children.push(item);
    }

    stack.push(item);
  });

  return organized;
}

export default {
  convertPRDToHTML,
  getPRDSummary,
  organizeSectionsByLevel
};
