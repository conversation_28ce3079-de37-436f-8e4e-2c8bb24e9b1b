// TipTap编辑器内容操作工具

/**
 * 内容模板
 */
export const contentTemplates = {
  // 简单表格模板
  simpleTable: {
    type: 'table',
    content: [
      {
        type: 'tableRow',
        content: [
          { type: 'tableHeader', content: [{ type: 'paragraph', content: [{ type: 'text', text: '姓名' }] }] },
          { type: 'tableHeader', content: [{ type: 'paragraph', content: [{ type: 'text', text: '职位' }] }] },
          { type: 'tableHeader', content: [{ type: 'paragraph', content: [{ type: 'text', text: '部门' }] }] },
        ]
      },
      {
        type: 'tableRow',
        content: [
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '张三' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '前端工程师' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '技术部' }] }] },
        ]
      },
      {
        type: 'tableRow',
        content: [
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '李四' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '产品经理' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '产品部' }] }] },
        ]
      }
    ]
  },

  // 复杂表格模板
  complexTable: {
    type: 'table',
    content: [
      {
        type: 'tableRow',
        content: [
          { type: 'tableHeader', content: [{ type: 'paragraph', content: [{ type: 'text', text: '功能' }] }] },
          { type: 'tableHeader', content: [{ type: 'paragraph', content: [{ type: 'text', text: '状态' }] }] },
          { type: 'tableHeader', content: [{ type: 'paragraph', content: [{ type: 'text', text: '优先级' }] }] },
          { type: 'tableHeader', content: [{ type: 'paragraph', content: [{ type: 'text', text: '负责人' }] }] },
        ]
      },
      {
        type: 'tableRow',
        content: [
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '用户登录' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '✅ 完成' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '高' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '张三' }] }] },
        ]
      },
      {
        type: 'tableRow',
        content: [
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '数据导出' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '🔄 进行中' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '中' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '李四' }] }] },
        ]
      },
      {
        type: 'tableRow',
        content: [
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '报表生成' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '📋 待开始' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '低' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '王五' }] }] },
        ]
      }
    ]
  },

  // 产品表格模板
  productTable: {
    type: 'table',
    content: [
      {
        type: 'tableRow',
        content: [
          { type: 'tableHeader', content: [{ type: 'paragraph', content: [{ type: 'text', text: '产品' }] }] },
          { type: 'tableHeader', content: [{ type: 'paragraph', content: [{ type: 'text', text: '价格' }] }] },
          { type: 'tableHeader', content: [{ type: 'paragraph', content: [{ type: 'text', text: '库存' }] }] },
          { type: 'tableHeader', content: [{ type: 'paragraph', content: [{ type: 'text', text: '状态' }] }] },
        ]
      },
      {
        type: 'tableRow',
        content: [
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: 'iPhone 15 Pro' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '¥8,999' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '50' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '有货' }] }] },
        ]
      },
      {
        type: 'tableRow',
        content: [
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: 'MacBook Air M2' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '¥8,999' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '30' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '有货' }] }] },
        ]
      },
      {
        type: 'tableRow',
        content: [
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: 'iPad Pro' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '¥6,799' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '0' }] }] },
          { type: 'tableCell', content: [{ type: 'paragraph', content: [{ type: 'text', text: '缺货' }] }] },
        ]
      }
    ]
  },

  // 示例文档模板
  sampleDocument: [
    {
      type: 'heading',
      attrs: { level: 1 },
      content: [{ type: 'text', text: 'TipTap编辑器功能演示' }]
    },
    {
      type: 'paragraph',
      content: [
        { type: 'text', text: '这是一个功能强大的富文本编辑器，支持' },
        { type: 'text', marks: [{ type: 'bold' }], text: '粗体' },
        { type: 'text', text: '、' },
        { type: 'text', marks: [{ type: 'italic' }], text: '斜体' },
        { type: 'text', text: '、' },
        { type: 'text', marks: [{ type: 'code' }], text: '代码' },
        { type: 'text', text: '等格式。' }
      ]
    },
    {
      type: 'heading',
      attrs: { level: 2 },
      content: [{ type: 'text', text: '主要特性' }]
    },
    {
      type: 'bulletList',
      content: [
        {
          type: 'listItem',
          content: [{ type: 'paragraph', content: [{ type: 'text', text: 'Markdown语法实时渲染' }] }]
        },
        {
          type: 'listItem',
          content: [{ type: 'paragraph', content: [{ type: 'text', text: '悬浮工具栏支持' }] }]
        },
        {
          type: 'listItem',
          content: [{ type: 'paragraph', content: [{ type: 'text', text: '表格编辑功能' }] }]
        },
        {
          type: 'listItem',
          content: [{ type: 'paragraph', content: [{ type: 'text', text: '事件截获和处理' }] }]
        }
      ]
    }
  ]
};

/**
 * 内容插入工具
 */
export const contentInserter = {
  // 插入模板内容
  insertTemplate: (editor, templateName) => {
    const template = contentTemplates[templateName];
    if (!template) {
      console.warn(`Template "${templateName}" not found`);
      return false;
    }

    try {
      return editor.chain().focus().insertContent(template).run();
    } catch (error) {
      console.error('Failed to insert template:', error);
      return false;
    }
  },

  // 在光标位置插入表格
  insertTableAtCursor: (editor, rows = 3, cols = 3, withHeader = true) => {
    try {
      return editor.chain().focus().insertTable({ rows, cols, withHeaderRow: withHeader }).run();
    } catch (error) {
      console.error('Failed to insert table:', error);
      return false;
    }
  },

  // 插入Markdown内容
  insertMarkdown: (editor, markdown) => {
    try {
      // 简单的Markdown解析和插入
      const lines = markdown.split('\n');
      const content = [];

      for (const line of lines) {
        if (line.startsWith('# ')) {
          content.push({
            type: 'heading',
            attrs: { level: 1 },
            content: [{ type: 'text', text: line.substring(2) }]
          });
        } else if (line.startsWith('## ')) {
          content.push({
            type: 'heading',
            attrs: { level: 2 },
            content: [{ type: 'text', text: line.substring(3) }]
          });
        } else if (line.startsWith('### ')) {
          content.push({
            type: 'heading',
            attrs: { level: 3 },
            content: [{ type: 'text', text: line.substring(4) }]
          });
        } else if (line.trim() === '') {
          // 空行
          continue;
        } else {
          content.push({
            type: 'paragraph',
            content: [{ type: 'text', text: line }]
          });
        }
      }

      return editor.chain().focus().insertContent(content).run();
    } catch (error) {
      console.error('Failed to insert markdown:', error);
      return false;
    }
  },

  // 在指定位置插入内容
  insertContentAtPosition: (editor, pos, content) => {
    try {
      return editor.chain().focus().insertContentAt(pos, content).run();
    } catch (error) {
      console.error('Failed to insert content at position:', error);
      return false;
    }
  },

  // 替换选中内容
  replaceSelection: (editor, content) => {
    try {
      return editor.chain().focus().deleteSelection().insertContent(content).run();
    } catch (error) {
      console.error('Failed to replace selection:', error);
      return false;
    }
  },
};

/**
 * 内容分析工具
 */
export const contentAnalyzer = {
  // 获取文档统计信息
  getDocumentStats: (editor) => {
    const text = editor.getText();
    const html = editor.getHTML();
    const json = editor.getJSON();

    return {
      characters: text.length,
      charactersWithoutSpaces: text.replace(/\s/g, '').length,
      words: text.split(/\s+/).filter(word => word.length > 0).length,
      paragraphs: json.content?.filter(node => node.type === 'paragraph').length || 0,
      headings: json.content?.filter(node => node.type === 'heading').length || 0,
      tables: this.countNodeType(json, 'table'),
      images: this.countNodeType(json, 'image'),
      links: this.countMarks(json, 'link'),
      lists: json.content?.filter(node => 
        node.type === 'bulletList' || node.type === 'orderedList'
      ).length || 0,
    };
  },

  // 递归计算节点类型数量
  countNodeType: (node, nodeType) => {
    let count = 0;
    
    if (node.type === nodeType) {
      count++;
    }
    
    if (node.content) {
      for (const child of node.content) {
        count += this.countNodeType(child, nodeType);
      }
    }
    
    return count;
  },

  // 递归计算标记类型数量
  countMarks: (node, markType) => {
    let count = 0;
    
    if (node.marks) {
      count += node.marks.filter(mark => mark.type === markType).length;
    }
    
    if (node.content) {
      for (const child of node.content) {
        count += this.countMarks(child, markType);
      }
    }
    
    return count;
  },

  // 获取大纲结构
  getOutline: (editor) => {
    const outline = [];
    const json = editor.getJSON();
    
    this.extractHeadings(json, outline);
    
    return outline;
  },

  // 递归提取标题
  extractHeadings: (node, outline, position = 0) => {
    if (node.type === 'heading') {
      outline.push({
        level: node.attrs.level,
        text: this.extractText(node),
        position,
        id: `heading-${position}`,
      });
    }
    
    if (node.content) {
      for (let i = 0; i < node.content.length; i++) {
        this.extractHeadings(node.content[i], outline, position + i);
      }
    }
  },

  // 提取节点文本
  extractText: (node) => {
    if (node.type === 'text') {
      return node.text;
    }
    
    if (node.content) {
      return node.content.map(child => this.extractText(child)).join('');
    }
    
    return '';
  },

  // 获取表格信息
  getTablesInfo: (editor) => {
    const tables = [];
    const json = editor.getJSON();
    
    this.extractTables(json, tables);
    
    return tables;
  },

  // 递归提取表格
  extractTables: (node, tables, position = 0) => {
    if (node.type === 'table') {
      const tableInfo = {
        position,
        rows: 0,
        cols: 0,
        hasHeader: false,
        content: this.extractText(node),
      };
      
      if (node.content) {
        tableInfo.rows = node.content.length;
        if (node.content[0] && node.content[0].content) {
          tableInfo.cols = node.content[0].content.length;
          tableInfo.hasHeader = node.content[0].content.some(cell => 
            cell.type === 'tableHeader'
          );
        }
      }
      
      tables.push(tableInfo);
    }
    
    if (node.content) {
      for (let i = 0; i < node.content.length; i++) {
        this.extractTables(node.content[i], tables, position + i);
      }
    }
  },
};

/**
 * 内容导航工具
 */
export const contentNavigator = {
  // 跳转到标题
  goToHeading: (editor, headingIndex) => {
    const outline = contentAnalyzer.getOutline(editor);
    if (outline[headingIndex]) {
      const position = outline[headingIndex].position;
      editor.commands.focus(position);
      editor.commands.scrollIntoView();
      return true;
    }
    return false;
  },

  // 跳转到表格
  goToTable: (editor, tableIndex) => {
    const tables = contentAnalyzer.getTablesInfo(editor);
    if (tables[tableIndex]) {
      const position = tables[tableIndex].position;
      editor.commands.focus(position);
      editor.commands.scrollIntoView();
      return true;
    }
    return false;
  },

  // 跳转到指定位置
  goToPosition: (editor, position) => {
    try {
      editor.commands.focus(position);
      editor.commands.scrollIntoView();
      return true;
    } catch (error) {
      console.error('Failed to go to position:', error);
      return false;
    }
  },

  // 查找文本
  findText: (editor, searchText) => {
    const text = editor.getText();
    const index = text.indexOf(searchText);
    
    if (index !== -1) {
      // 简单的文本查找，实际实现可能需要更复杂的逻辑
      editor.commands.focus(index);
      editor.commands.scrollIntoView();
      return true;
    }
    
    return false;
  },
};

/**
 * 内容验证工具
 */
export const contentValidator = {
  // 验证表格结构
  validateTable: (tableNode) => {
    const issues = [];
    
    if (!tableNode.content || tableNode.content.length === 0) {
      issues.push('表格没有行');
      return issues;
    }
    
    const firstRowCols = tableNode.content[0].content?.length || 0;
    
    for (let i = 0; i < tableNode.content.length; i++) {
      const row = tableNode.content[i];
      if (row.type !== 'tableRow') {
        issues.push(`第${i + 1}行不是有效的表格行`);
        continue;
      }
      
      const cols = row.content?.length || 0;
      if (cols !== firstRowCols) {
        issues.push(`第${i + 1}行列数不一致，期望${firstRowCols}列，实际${cols}列`);
      }
    }
    
    return issues;
  },

  // 验证文档结构
  validateDocument: (editor) => {
    const json = editor.getJSON();
    const issues = [];
    
    // 检查是否有内容
    if (!json.content || json.content.length === 0) {
      issues.push('文档为空');
      return issues;
    }
    
    // 检查标题层级
    const headings = contentAnalyzer.getOutline(editor);
    for (let i = 1; i < headings.length; i++) {
      const prev = headings[i - 1];
      const curr = headings[i];
      
      if (curr.level > prev.level + 1) {
        issues.push(`标题层级跳跃：从H${prev.level}直接跳到H${curr.level}`);
      }
    }
    
    // 检查表格
    const tables = contentAnalyzer.getTablesInfo(editor);
    tables.forEach((table, index) => {
      if (table.rows === 0) {
        issues.push(`第${index + 1}个表格没有行`);
      }
      if (table.cols === 0) {
        issues.push(`第${index + 1}个表格没有列`);
      }
    });
    
    return issues;
  },
};
