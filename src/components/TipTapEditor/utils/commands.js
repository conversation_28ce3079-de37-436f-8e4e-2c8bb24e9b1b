// TipTap编辑器命令工具函数

/**
 * 表格操作命令
 */
export const tableCommands = {
  // 插入表格
  insertTable: (editor, rows = 3, cols = 3, withHeaderRow = true) => {
    return editor.chain().focus().insertTable({ rows, cols, withHeaderRow }).run();
  },

  // 列操作
  addColumnBefore: (editor) => editor.chain().focus().addColumnBefore().run(),
  addColumnAfter: (editor) => editor.chain().focus().addColumnAfter().run(),
  deleteColumn: (editor) => editor.chain().focus().deleteColumn().run(),

  // 行操作
  addRowBefore: (editor) => editor.chain().focus().addRowBefore().run(),
  addRowAfter: (editor) => editor.chain().focus().addRowAfter().run(),
  deleteRow: (editor) => editor.chain().focus().deleteRow().run(),

  // 表格操作
  deleteTable: (editor) => editor.chain().focus().deleteTable().run(),
  mergeCells: (editor) => editor.chain().focus().mergeCells().run(),
  splitCell: (editor) => editor.chain().focus().splitCell().run(),

  // 表头操作
  toggleHeaderColumn: (editor) => editor.chain().focus().toggleHeaderColumn().run(),
  toggleHeaderRow: (editor) => editor.chain().focus().toggleHeaderRow().run(),
  toggleHeaderCell: (editor) => editor.chain().focus().toggleHeaderCell().run(),

  // 检查表格状态
  isInTable: (editor) => editor.isActive('table'),
  canMergeCells: (editor) => editor.can().mergeCells(),
  canSplitCell: (editor) => editor.can().splitCell(),
  canAddColumnBefore: (editor) => editor.can().addColumnBefore(),
  canAddColumnAfter: (editor) => editor.can().addColumnAfter(),
  canDeleteColumn: (editor) => editor.can().deleteColumn(),
  canAddRowBefore: (editor) => editor.can().addRowBefore(),
  canAddRowAfter: (editor) => editor.can().addRowAfter(),
  canDeleteRow: (editor) => editor.can().deleteRow(),
};

/**
 * 内容插入命令
 */
export const insertCommands = {
  // 在光标位置插入内容
  insertContentAtCursor: (editor, content) => {
    return editor.chain().focus().insertContent(content).run();
  },

  // 在指定位置插入内容
  insertContentAtPosition: (editor, pos, content) => {
    return editor.chain().focus().insertContentAt(pos, content).run();
  },

  // 插入Markdown内容
  insertMarkdown: (editor, markdown) => {
    return editor.chain().focus().insertContent(markdown).run();
  },

  // 插入HTML内容
  insertHTML: (editor, html) => {
    return editor.chain().focus().insertContent(html).run();
  },

  // 插入图片
  insertImage: (editor, src, alt = '', title = '') => {
    return editor.chain().focus().setImage({ src, alt, title }).run();
  },

  // 插入链接
  insertLink: (editor, href, text = '') => {
    if (text) {
      return editor.chain().focus().insertContent(`<a href="${href}">${text}</a>`).run();
    } else {
      return editor.chain().focus().setLink({ href }).run();
    }
  },

  // 插入水平分割线
  insertHorizontalRule: (editor) => {
    return editor.chain().focus().setHorizontalRule().run();
  },

  // 插入代码块
  insertCodeBlock: (editor, language = '') => {
    return editor.chain().focus().insertContent({
      type: 'codeBlock',
      attrs: language ? { language } : {},
    }).run();
  },
};

/**
 * 格式化命令
 */
export const formatCommands = {
  // 文本格式
  toggleBold: (editor) => editor.chain().focus().toggleBold().run(),
  toggleItalic: (editor) => editor.chain().focus().toggleItalic().run(),
  toggleStrike: (editor) => editor.chain().focus().toggleStrike().run(),
  toggleCode: (editor) => editor.chain().focus().toggleCode().run(),
  toggleUnderline: (editor) => editor.chain().focus().toggleUnderline().run(),

  // 标题
  setHeading: (editor, level) => editor.chain().focus().toggleHeading({ level }).run(),
  setParagraph: (editor) => editor.chain().focus().setParagraph().run(),

  // 列表
  toggleBulletList: (editor) => editor.chain().focus().toggleBulletList().run(),
  toggleOrderedList: (editor) => editor.chain().focus().toggleOrderedList().run(),
  toggleTaskList: (editor) => editor.chain().focus().toggleTaskList().run(),

  // 引用和代码
  toggleBlockquote: (editor) => editor.chain().focus().toggleBlockquote().run(),
  toggleCodeBlock: (editor) => editor.chain().focus().toggleCodeBlock().run(),

  // 对齐
  setTextAlign: (editor, alignment) => editor.chain().focus().setTextAlign(alignment).run(),

  // 清除格式
  clearNodes: (editor) => editor.chain().focus().clearNodes().run(),
  unsetAllMarks: (editor) => editor.chain().focus().unsetAllMarks().run(),
};

/**
 * 选择和导航命令
 */
export const navigationCommands = {
  // 选择全部
  selectAll: (editor) => editor.chain().focus().selectAll().run(),

  // 选择节点
  selectNodeForward: (editor) => editor.chain().focus().selectNodeForward().run(),
  selectNodeBackward: (editor) => editor.chain().focus().selectNodeBackward().run(),

  // 移动光标
  focus: (editor, position = 'end') => {
    if (position === 'start') {
      return editor.chain().focus('start').run();
    } else if (position === 'end') {
      return editor.chain().focus('end').run();
    } else if (typeof position === 'number') {
      return editor.chain().focus(position).run();
    }
    return editor.chain().focus().run();
  },

  // 滚动到视图
  scrollIntoView: (editor) => editor.chain().scrollIntoView().run(),

  // 跳转到位置
  goToPosition: (editor, pos) => {
    editor.commands.focus(pos);
    editor.commands.scrollIntoView();
  },
};

/**
 * 撤销重做命令
 */
export const historyCommands = {
  undo: (editor) => editor.chain().focus().undo().run(),
  redo: (editor) => editor.chain().focus().redo().run(),
  canUndo: (editor) => editor.can().undo(),
  canRedo: (editor) => editor.can().redo(),
};

/**
 * 内容操作命令
 */
export const contentCommands = {
  // 获取内容
  getHTML: (editor) => editor.getHTML(),
  getJSON: (editor) => editor.getJSON(),
  getText: (editor) => editor.getText(),
  getMarkdown: (editor) => {
    // 需要安装markdown扩展
    return editor.storage.markdown?.getMarkdown() || '';
  },

  // 设置内容
  setContent: (editor, content, emitUpdate = true) => {
    return editor.commands.setContent(content, emitUpdate);
  },

  // 清空内容
  clearContent: (editor, emitUpdate = true) => {
    return editor.commands.clearContent(emitUpdate);
  },

  // 替换内容
  replaceContent: (editor, content) => {
    return editor.chain().selectAll().deleteSelection().insertContent(content).run();
  },
};

/**
 * 状态检查函数
 */
export const stateCheckers = {
  // 检查是否激活
  isActive: (editor, name, attributes = {}) => editor.isActive(name, attributes),

  // 检查是否可以执行命令
  can: (editor, command) => editor.can()[command](),

  // 检查是否为空
  isEmpty: (editor) => editor.isEmpty,

  // 检查是否可编辑
  isEditable: (editor) => editor.isEditable,

  // 检查是否有焦点
  isFocused: (editor) => editor.isFocused,

  // 获取当前选择
  getSelection: (editor) => {
    const { from, to } = editor.state.selection;
    return {
      from,
      to,
      text: editor.state.doc.textBetween(from, to),
      isEmpty: from === to,
    };
  },

  // 获取光标位置
  getCursorPosition: (editor) => editor.state.selection.head,

  // 获取文档统计
  getStats: (editor) => {
    const text = editor.getText();
    return {
      characters: text.length,
      charactersWithoutSpaces: text.replace(/\s/g, '').length,
      words: text.split(/\s+/).filter(word => word.length > 0).length,
      paragraphs: editor.state.doc.content.childCount,
    };
  },
};

/**
 * 组合命令 - 常用操作的组合
 */
export const compositeCommands = {
  // 插入简单表格
  insertSimpleTable: (editor) => {
    return tableCommands.insertTable(editor, 3, 3, true);
  },

  // 插入复杂表格
  insertComplexTable: (editor) => {
    const tableHTML = `
      <table>
        <thead>
          <tr>
            <th>功能</th>
            <th>状态</th>
            <th>优先级</th>
            <th>负责人</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>用户登录</td>
            <td>✅ 完成</td>
            <td>高</td>
            <td>张三</td>
          </tr>
          <tr>
            <td>数据导出</td>
            <td>🔄 进行中</td>
            <td>中</td>
            <td>李四</td>
          </tr>
          <tr>
            <td>报表生成</td>
            <td>📋 待开始</td>
            <td>低</td>
            <td>王五</td>
          </tr>
        </tbody>
      </table>
    `;
    return insertCommands.insertHTML(editor, tableHTML);
  },

  // 插入产品表格
  insertProductTable: (editor) => {
    const tableHTML = `
      <table>
        <thead>
          <tr>
            <th>产品</th>
            <th>价格</th>
            <th>库存</th>
            <th>状态</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>iPhone 15 Pro</td>
            <td>¥8,999</td>
            <td>50</td>
            <td>有货</td>
          </tr>
          <tr>
            <td>MacBook Air M2</td>
            <td>¥8,999</td>
            <td>30</td>
            <td>有货</td>
          </tr>
          <tr>
            <td>iPad Pro</td>
            <td>¥6,799</td>
            <td>0</td>
            <td>缺货</td>
          </tr>
        </tbody>
      </table>
    `;
    return insertCommands.insertHTML(editor, tableHTML);
  },

  // 格式化选中文本
  formatSelection: (editor, format) => {
    const formats = {
      bold: () => formatCommands.toggleBold(editor),
      italic: () => formatCommands.toggleItalic(editor),
      code: () => formatCommands.toggleCode(editor),
      strike: () => formatCommands.toggleStrike(editor),
    };
    
    return formats[format] ? formats[format]() : false;
  },
};
