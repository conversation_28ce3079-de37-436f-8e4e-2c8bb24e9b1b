// TipTap编辑器事件处理工具

/**
 * 事件类型常量
 */
export const EVENT_TYPES = {
  SELECTION_UPDATE: 'selectionUpdate',
  CONTENT_UPDATE: 'contentUpdate',
  FOCUS: 'focus',
  BLUR: 'blur',
  CREATE: 'create',
  DESTROY: 'destroy',
  COPY: 'copy',
  PASTE: 'paste',
  CUT: 'cut',
  KEYDOWN: 'keydown',
  KEYUP: 'keyup',
  CLICK: 'click',
  DOUBLE_CLICK: 'doubleClick',
  TABLE_CELL_SELECT: 'tableCellSelect',
  IMAGE_SELECT: 'imageSelect',
};

/**
 * 创建事件监听器管理器
 */
export class EventManager {
  constructor() {
    this.listeners = new Map();
    this.logs = [];
  }

  // 添加事件监听器
  on(eventType, callback) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }
    this.listeners.get(eventType).push(callback);
  }

  // 移除事件监听器
  off(eventType, callback) {
    if (this.listeners.has(eventType)) {
      const callbacks = this.listeners.get(eventType);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // 触发事件
  emit(eventType, data) {
    this.log(eventType, data);
    
    if (this.listeners.has(eventType)) {
      this.listeners.get(eventType).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${eventType}:`, error);
        }
      });
    }
  }

  // 记录事件日志
  log(eventType, data) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      type: eventType,
      data: this.sanitizeLogData(data),
    };
    
    this.logs.push(logEntry);
    
    // 保持日志数量在合理范围内
    if (this.logs.length > 1000) {
      this.logs = this.logs.slice(-500);
    }
  }

  // 清理日志数据，避免循环引用
  sanitizeLogData(data) {
    if (!data) return data;
    
    try {
      return JSON.parse(JSON.stringify(data, (key, value) => {
        if (key === 'editor' || key === 'view' || key === 'state') {
          return '[Editor Instance]';
        }
        return value;
      }));
    } catch (error) {
      return { error: 'Failed to serialize data' };
    }
  }

  // 获取事件日志
  getLogs(eventType = null, limit = 100) {
    let logs = this.logs;
    
    if (eventType) {
      logs = logs.filter(log => log.type === eventType);
    }
    
    return logs.slice(-limit);
  }

  // 清空日志
  clearLogs() {
    this.logs = [];
  }
}

/**
 * 编辑器事件处理器工厂
 */
export const createEventHandlers = (eventManager) => {
  return {
    // 选择更新事件
    onSelectionUpdate: ({ editor }) => {
      const { from, to } = editor.state.selection;
      const selectedText = editor.state.doc.textBetween(from, to);
      
      const selectionData = {
        from,
        to,
        text: selectedText,
        isEmpty: from === to,
        length: selectedText.length,
        cursorPosition: editor.state.selection.head,
      };

      eventManager.emit(EVENT_TYPES.SELECTION_UPDATE, {
        editor,
        selection: selectionData,
      });
    },

    // 内容更新事件
    onUpdate: ({ editor }) => {
      const content = {
        html: editor.getHTML(),
        json: editor.getJSON(),
        text: editor.getText(),
        isEmpty: editor.isEmpty,
        length: editor.getText().length,
      };

      eventManager.emit(EVENT_TYPES.CONTENT_UPDATE, {
        editor,
        content,
      });
    },

    // 编辑器创建事件
    onCreate: ({ editor }) => {
      eventManager.emit(EVENT_TYPES.CREATE, {
        editor,
        timestamp: Date.now(),
      });
    },

    // 编辑器销毁事件
    onDestroy: () => {
      eventManager.emit(EVENT_TYPES.DESTROY, {
        timestamp: Date.now(),
      });
    },

    // 焦点事件
    onFocus: ({ editor }) => {
      eventManager.emit(EVENT_TYPES.FOCUS, {
        editor,
        timestamp: Date.now(),
      });
    },

    // 失焦事件
    onBlur: ({ editor }) => {
      eventManager.emit(EVENT_TYPES.BLUR, {
        editor,
        timestamp: Date.now(),
      });
    },
  };
};

/**
 * 键盘事件处理器
 */
export const createKeyboardHandlers = (eventManager) => {
  return {
    // 处理键盘按下事件
    handleKeyDown: (view, event) => {
      const keyData = {
        key: event.key,
        code: event.code,
        ctrlKey: event.ctrlKey,
        metaKey: event.metaKey,
        shiftKey: event.shiftKey,
        altKey: event.altKey,
      };

      eventManager.emit(EVENT_TYPES.KEYDOWN, {
        view,
        event,
        keyData,
      });

      // 处理特殊快捷键
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'c':
            eventManager.emit(EVENT_TYPES.COPY, {
              view,
              selection: view.state.selection,
            });
            break;
          case 'v':
            eventManager.emit(EVENT_TYPES.PASTE, {
              view,
              selection: view.state.selection,
            });
            break;
          case 'x':
            eventManager.emit(EVENT_TYPES.CUT, {
              view,
              selection: view.state.selection,
            });
            break;
        }
      }

      return false; // 不阻止默认行为
    },

    // 处理键盘释放事件
    handleKeyUp: (view, event) => {
      const keyData = {
        key: event.key,
        code: event.code,
        ctrlKey: event.ctrlKey,
        metaKey: event.metaKey,
        shiftKey: event.shiftKey,
        altKey: event.altKey,
      };

      eventManager.emit(EVENT_TYPES.KEYUP, {
        view,
        event,
        keyData,
      });

      return false;
    },
  };
};

/**
 * 鼠标事件处理器
 */
export const createMouseHandlers = (eventManager) => {
  return {
    // 处理点击事件
    handleClick: (view, pos, event) => {
      const clickData = {
        pos,
        button: event.button,
        ctrlKey: event.ctrlKey,
        metaKey: event.metaKey,
        shiftKey: event.shiftKey,
        altKey: event.altKey,
      };

      eventManager.emit(EVENT_TYPES.CLICK, {
        view,
        event,
        clickData,
      });

      return false;
    },

    // 处理双击事件
    handleDoubleClick: (view, pos, event) => {
      const doubleClickData = {
        pos,
        button: event.button,
        ctrlKey: event.ctrlKey,
        metaKey: event.metaKey,
        shiftKey: event.shiftKey,
        altKey: event.altKey,
      };

      eventManager.emit(EVENT_TYPES.DOUBLE_CLICK, {
        view,
        event,
        doubleClickData,
      });

      return false;
    },
  };
};

/**
 * 剪贴板事件处理器
 */
export const createClipboardHandlers = (eventManager) => {
  return {
    // 处理复制事件
    handleCopy: (view, event) => {
      const { from, to } = view.state.selection;
      const selectedText = view.state.doc.textBetween(from, to);

      eventManager.emit(EVENT_TYPES.COPY, {
        view,
        event,
        selection: { from, to },
        text: selectedText,
      });
    },

    // 处理粘贴事件
    handlePaste: (view, event, slice) => {
      const pasteData = {
        text: event.clipboardData?.getData('text/plain') || '',
        html: event.clipboardData?.getData('text/html') || '',
        files: Array.from(event.clipboardData?.files || []),
      };

      eventManager.emit(EVENT_TYPES.PASTE, {
        view,
        event,
        slice,
        pasteData,
      });
    },

    // 处理剪切事件
    handleCut: (view, event) => {
      const { from, to } = view.state.selection;
      const selectedText = view.state.doc.textBetween(from, to);

      eventManager.emit(EVENT_TYPES.CUT, {
        view,
        event,
        selection: { from, to },
        text: selectedText,
      });
    },
  };
};

/**
 * 表格事件处理器
 */
export const createTableHandlers = (eventManager) => {
  return {
    // 表格单元格选择事件
    onTableCellSelect: (cellPos, cellNode) => {
      eventManager.emit(EVENT_TYPES.TABLE_CELL_SELECT, {
        cellPos,
        cellNode,
        cellContent: cellNode.textContent,
      });
    },

    // 检测表格操作
    detectTableOperation: (editor, operation) => {
      const tableOperations = {
        insertTable: '插入表格',
        addColumn: '添加列',
        deleteColumn: '删除列',
        addRow: '添加行',
        deleteRow: '删除行',
        mergeCells: '合并单元格',
        splitCell: '拆分单元格',
      };

      if (tableOperations[operation]) {
        eventManager.emit('tableOperation', {
          editor,
          operation,
          description: tableOperations[operation],
          timestamp: Date.now(),
        });
      }
    },
  };
};

/**
 * 内容分析工具
 */
export const contentAnalyzer = {
  // 分析文档结构
  analyzeDocument: (editor) => {
    const doc = editor.state.doc;
    const analysis = {
      totalNodes: 0,
      nodeTypes: {},
      headings: [],
      tables: [],
      images: [],
      links: [],
    };

    doc.descendants((node, pos) => {
      analysis.totalNodes++;
      
      // 统计节点类型
      if (!analysis.nodeTypes[node.type.name]) {
        analysis.nodeTypes[node.type.name] = 0;
      }
      analysis.nodeTypes[node.type.name]++;

      // 收集标题
      if (node.type.name === 'heading') {
        analysis.headings.push({
          level: node.attrs.level,
          text: node.textContent,
          pos,
        });
      }

      // 收集表格
      if (node.type.name === 'table') {
        analysis.tables.push({
          pos,
          rows: node.childCount,
          content: node.textContent,
        });
      }

      // 收集图片
      if (node.type.name === 'image') {
        analysis.images.push({
          src: node.attrs.src,
          alt: node.attrs.alt,
          pos,
        });
      }

      // 收集链接
      if (node.marks?.some(mark => mark.type.name === 'link')) {
        const linkMark = node.marks.find(mark => mark.type.name === 'link');
        analysis.links.push({
          href: linkMark.attrs.href,
          text: node.textContent,
          pos,
        });
      }
    });

    return analysis;
  },

  // 获取大纲结构
  getOutline: (editor) => {
    const headings = [];
    editor.state.doc.descendants((node, pos) => {
      if (node.type.name === 'heading') {
        headings.push({
          level: node.attrs.level,
          text: node.textContent,
          pos,
          id: `heading-${pos}`,
        });
      }
    });
    return headings;
  },

  // 获取表格信息
  getTablesInfo: (editor) => {
    const tables = [];
    editor.state.doc.descendants((node, pos) => {
      if (node.type.name === 'table') {
        let rows = 0;
        let cols = 0;
        
        node.forEach((rowNode) => {
          if (rowNode.type.name === 'tableRow') {
            rows++;
            if (cols === 0) {
              rowNode.forEach((cellNode) => {
                if (cellNode.type.name === 'tableCell' || cellNode.type.name === 'tableHeader') {
                  cols++;
                }
              });
            }
          }
        });

        tables.push({
          pos,
          rows,
          cols,
          content: node.textContent,
        });
      }
    });
    return tables;
  },
};
