import React, { useState, useRef, useCallback } from 'react';
import TipTapEditor from './TipTapEditor';
import TableMenu from './menus/TableMenu';
import { EventManager, createEventHandlers, EVENT_TYPES, contentAnalyzer } from './utils/events';
import { contentInserter, contentTemplates, contentNavigator } from './utils/content';
import { compositeCommands } from './utils/commands';
import { getPRDContent, getDocumentMetadata } from '../../local_db/dataLoader';
import { convertPRDToHTML, getPRDSummary } from './utils/prdContentConverter';

const TipTapTableTest = () => {
  // 状态管理
  const [content, setContent] = useState('');
  const [logs, setLogs] = useState([]);
  const [editorStats, setEditorStats] = useState({});
  const [outline, setOutline] = useState([]);
  const [tables, setTables] = useState([]);
  const [currentSelection, setCurrentSelection] = useState(null);
  const [isEditorReady, setIsEditorReady] = useState(false);
  const [prdSummary, setPrdSummary] = useState(null);
  const [isLoadingPRD, setIsLoadingPRD] = useState(false);

  // 引用
  const editorRef = useRef(null);
  const eventManagerRef = useRef(new EventManager());

  // 添加日志
  const addLog = useCallback((message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = {
      id: Date.now(),
      timestamp,
      message,
      type,
    };
    
    setLogs(prev => [...prev.slice(-49), logEntry]); // 保持最新50条日志
  }, []);

  // 事件处理器
  const eventHandlers = createEventHandlers(eventManagerRef.current);

  // 编辑器事件回调
  const handleEditorChange = useCallback(({ html, json, text, editor }) => {
    setContent(html);
    
    // 更新统计信息
    const stats = {
      characters: text.length,
      words: text.split(/\s+/).filter(word => word.length > 0).length,
      paragraphs: json.content?.filter(node => node.type === 'paragraph').length || 0,
      headings: json.content?.filter(node => node.type === 'heading').length || 0,
    };
    setEditorStats(stats);

    // 更新大纲
    const newOutline = contentAnalyzer.getOutline(editor);
    setOutline(newOutline);

    // 更新表格信息
    const newTables = contentAnalyzer.getTablesInfo(editor);
    setTables(newTables);

    addLog(`内容更新 - 字符数: ${stats.characters}, 单词数: ${stats.words}`, 'info');
  }, [addLog]);

  const handleSelectionUpdate = useCallback(({ editor, selection }) => {
    setCurrentSelection(selection);
    
    if (!selection.isEmpty) {
      addLog(`选中文本: "${selection.text}" (${selection.from}-${selection.to})`, 'info');
    }
  }, [addLog]);

  const handleEditorCreate = useCallback(({ editor }) => {
    editorRef.current = editor;
    setIsEditorReady(true);
    addLog('TipTap编辑器初始化完成', 'success');
  }, [addLog]);

  const handleEditorFocus = useCallback(() => {
    addLog('编辑器获得焦点', 'info');
  }, [addLog]);

  const handleEditorBlur = useCallback(() => {
    addLog('编辑器失去焦点', 'info');
  }, [addLog]);

  // 表格操作函数
  const insertSimpleTable = useCallback(() => {
    if (!editorRef.current) {
      addLog('编辑器未准备好', 'warning');
      return;
    }

    try {
      addLog('开始插入简单表格', 'info');
      const success = contentInserter.insertTemplate(editorRef.current, 'simpleTable');
      addLog(`简单表格插入${success ? '成功' : '失败'}`, success ? 'success' : 'error');
    } catch (error) {
      addLog(`插入简单表格失败: ${error.message}`, 'error');
    }
  }, [addLog]);

  const insertComplexTable = useCallback(() => {
    if (!editorRef.current) {
      addLog('编辑器未准备好', 'warning');
      return;
    }

    try {
      addLog('开始插入复杂表格', 'info');
      const success = contentInserter.insertTemplate(editorRef.current, 'complexTable');
      addLog(`复杂表格插入${success ? '成功' : '失败'}`, success ? 'success' : 'error');
    } catch (error) {
      addLog(`插入复杂表格失败: ${error.message}`, 'error');
    }
  }, [addLog]);

  const insertProductTable = useCallback(() => {
    if (!editorRef.current) {
      addLog('编辑器未准备好', 'warning');
      return;
    }

    try {
      addLog('开始插入产品表格', 'info');
      const success = contentInserter.insertTemplate(editorRef.current, 'productTable');
      addLog(`产品表格插入${success ? '成功' : '失败'}`, success ? 'success' : 'error');
    } catch (error) {
      addLog(`插入产品表格失败: ${error.message}`, 'error');
    }
  }, [addLog]);

  const insertSampleContent = useCallback(() => {
    if (!editorRef.current) {
      addLog('编辑器未准备好', 'warning');
      return;
    }

    try {
      addLog('开始插入示例文档', 'info');
      const success = editorRef.current.chain().focus().insertContent(contentTemplates.sampleDocument).run();
      addLog(`示例文档插入${success ? '成功' : '失败'}`, success ? 'success' : 'error');
    } catch (error) {
      addLog(`插入示例文档失败: ${error.message}`, 'error');
    }
  }, [addLog]);

  // 加载BSV PRD文档
  const loadBSVPRD = useCallback(async () => {
    if (!editorRef.current) {
      addLog('编辑器未准备好', 'warning');
      return;
    }

    try {
      setIsLoadingPRD(true);
      addLog('开始加载BSV PRD文档...', 'info');

      // 获取PRD内容
      const prdContent = getPRDContent('BSV');
      const metadata = getDocumentMetadata('BSV');

      if (!prdContent) {
        addLog('BSV PRD文档内容为空', 'error');
        return;
      }

      // 转换为HTML格式
      const htmlContent = convertPRDToHTML(prdContent);

      // 获取文档摘要
      const summary = getPRDSummary(prdContent);
      setPrdSummary(summary);

      // 设置编辑器内容
      const success = editorRef.current.chain().focus().setContent(htmlContent).run();

      if (success) {
        addLog(`✅ BSV PRD文档加载成功`, 'success');
        addLog(`📄 文档标题: ${summary.title}`, 'info');
        addLog(`📊 章节数量: ${summary.totalSections}`, 'info');
        addLog(`📝 预估字数: ${summary.totalWords}`, 'info');

        if (metadata) {
          addLog(`📅 文档版本: ${metadata.version}`, 'info');
          addLog(`🔄 最后修改: ${metadata.lastModified}`, 'info');
        }
      } else {
        addLog('❌ BSV PRD文档加载失败', 'error');
      }

    } catch (error) {
      addLog(`❌ 加载BSV PRD文档失败: ${error.message}`, 'error');
      console.error('BSV PRD加载错误:', error);
    } finally {
      setIsLoadingPRD(false);
    }
  }, [addLog]);

  // 导航函数
  const navigateToHeading = useCallback((index) => {
    if (!editorRef.current) return;
    
    try {
      const success = contentNavigator.goToHeading(editorRef.current, index);
      addLog(`跳转到标题${index + 1}${success ? '成功' : '失败'}`, success ? 'success' : 'warning');
    } catch (error) {
      addLog(`跳转失败: ${error.message}`, 'error');
    }
  }, [addLog]);

  const navigateToTable = useCallback((index) => {
    if (!editorRef.current) return;
    
    try {
      const success = contentNavigator.goToTable(editorRef.current, index);
      addLog(`跳转到表格${index + 1}${success ? '成功' : '失败'}`, success ? 'success' : 'warning');
    } catch (error) {
      addLog(`跳转失败: ${error.message}`, 'error');
    }
  }, [addLog]);

  // 清空内容
  const clearContent = useCallback(() => {
    if (!editorRef.current) return;
    
    try {
      editorRef.current.commands.clearContent();
      addLog('内容已清空', 'info');
    } catch (error) {
      addLog(`清空内容失败: ${error.message}`, 'error');
    }
  }, [addLog]);

  // 复制表格测试内容到剪贴板
  const copyTableToClipboard = useCallback(async () => {
    const tableContent = `| 产品名称 | 价格 | 库存 | 状态 |
|---------|------|------|------|
| iPhone 15 | ¥5999 | 50 | 在售 |
| MacBook Pro | ¥12999 | 20 | 在售 |
| AirPods | ¥1299 | 100 | 在售 |`;

    try {
      await navigator.clipboard.writeText(tableContent);
      addLog('✅ 表格Markdown内容已复制到剪贴板');
    } catch (err) {
      addLog('❌ 复制失败: ' + err.message);
    }
  }, [addLog]);

  // 清空日志
  const clearLogs = useCallback(() => {
    setLogs([]);
  }, []);

  // 复制Markdown测试内容到剪贴板
  const copyMarkdownToClipboard = useCallback(async () => {
    const markdownContent = `# 测试Markdown粘贴功能

## 这是一个二级标题

这是一段普通文本，包含**粗体**和*斜体*文字，还有\`内联代码\`。

### 表格示例

| 产品名称 | 价格 | 库存 | 状态 |
|---------|------|------|------|
| iPhone 15 | ¥5999 | 50 | 在售 |
| MacBook Pro | ¥12999 | 20 | 在售 |
| AirPods | ¥1299 | 100 | 在售 |

### 列表示例

* 无序列表项1
* 无序列表项2
* 无序列表项3

1. 有序列表项1
2. 有序列表项2
3. 有序列表项3

### 引用示例

> 这是一个引用块
> 可以包含多行内容

### 代码块示例

\`\`\`javascript
function hello() {
  console.log("Hello, World!");
}
\`\`\`

**使用说明：**
1. 点击此按钮复制上述Markdown内容
2. 在编辑器中粘贴（Ctrl+V）
3. 会弹出对话框询问是否转换格式
4. 选择"转换格式"查看效果`;

    try {
      await navigator.clipboard.writeText(markdownContent);
      addLog('Markdown测试内容已复制到剪贴板，请在编辑器中粘贴测试', 'success');
    } catch (error) {
      addLog(`复制失败: ${error.message}`, 'error');
    }
  }, [addLog]);

  // 样式定义
  const containerStyle = {
    display: 'flex',
    flexDirection: 'column',
    height: '100vh',
    background: '#f5f5f5',
  };

  const headerStyle = {
    padding: '16px 24px',
    background: '#fff',
    borderBottom: '1px solid #e1e5e9',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
  };

  const titleStyle = {
    margin: '0 0 16px 0',
    fontSize: '24px',
    fontWeight: 'bold',
    color: '#333',
  };

  const controlsStyle = {
    display: 'flex',
    gap: '12px',
    flexWrap: 'wrap',
  };

  const buttonStyle = {
    padding: '8px 16px',
    border: '1px solid #ddd',
    background: '#fff',
    borderRadius: '6px',
    cursor: 'pointer',
    fontSize: '14px',
    transition: 'all 0.2s',
    display: 'flex',
    alignItems: 'center',
    gap: '6px',
  };

  const mainStyle = {
    flex: 1,
    display: 'flex',
    gap: '16px',
    padding: '16px',
    overflow: 'hidden',
  };

  const editorSectionStyle = {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    background: '#fff',
    borderRadius: '8px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
    overflow: 'hidden',
  };

  const sidebarStyle = {
    width: '320px',
    display: 'flex',
    flexDirection: 'column',
    gap: '16px',
  };

  const panelStyle = {
    background: '#fff',
    borderRadius: '8px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
    overflow: 'hidden',
  };

  const panelHeaderStyle = {
    padding: '12px 16px',
    background: '#f8f9fa',
    borderBottom: '1px solid #e1e5e9',
    fontWeight: 'bold',
    fontSize: '14px',
    color: '#333',
  };

  const panelContentStyle = {
    padding: '12px 16px',
    maxHeight: '200px',
    overflowY: 'auto',
  };

  const logEntryStyle = (type) => ({
    padding: '6px 8px',
    margin: '2px 0',
    borderRadius: '4px',
    fontSize: '12px',
    background: type === 'error' ? '#ffebee' : 
                type === 'warning' ? '#fff3e0' : 
                type === 'success' ? '#e8f5e8' : '#f8f9fa',
    color: type === 'error' ? '#c62828' : 
           type === 'warning' ? '#ef6c00' : 
           type === 'success' ? '#2e7d32' : '#333',
    borderLeft: `3px solid ${
      type === 'error' ? '#f44336' : 
      type === 'warning' ? '#ff9800' : 
      type === 'success' ? '#4caf50' : '#2196f3'
    }`,
  });

  return (
    <div style={containerStyle}>
      {/* 头部控制区 */}
      <header style={headerStyle}>
        <h1 style={titleStyle}>TipTap 编辑器功能测试</h1>
        <div style={controlsStyle}>
          <button
            style={buttonStyle}
            onClick={insertSimpleTable}
            onMouseEnter={(e) => e.target.style.background = '#f0f0f0'}
            onMouseLeave={(e) => e.target.style.background = '#fff'}
          >
            📊 插入简单表格
          </button>
          <button
            style={buttonStyle}
            onClick={insertComplexTable}
            onMouseEnter={(e) => e.target.style.background = '#f0f0f0'}
            onMouseLeave={(e) => e.target.style.background = '#fff'}
          >
            📋 插入复杂表格
          </button>
          <button
            style={buttonStyle}
            onClick={insertProductTable}
            onMouseEnter={(e) => e.target.style.background = '#f0f0f0'}
            onMouseLeave={(e) => e.target.style.background = '#fff'}
          >
            🛍️ 插入产品表格
          </button>
          <button
            style={buttonStyle}
            onClick={insertSampleContent}
            onMouseEnter={(e) => e.target.style.background = '#f0f0f0'}
            onMouseLeave={(e) => e.target.style.background = '#fff'}
          >
            📄 插入示例文档
          </button>
          <button
            style={{...buttonStyle, background: '#ffebee', borderColor: '#f44336'}}
            onClick={clearContent}
            onMouseEnter={(e) => e.target.style.background = '#ffcdd2'}
            onMouseLeave={(e) => e.target.style.background = '#ffebee'}
          >
            🗑️ 清空内容
          </button>
          <button
            style={{...buttonStyle, background: '#e3f2fd', borderColor: '#1976d2'}}
            onClick={copyMarkdownToClipboard}
            onMouseEnter={(e) => e.target.style.background = '#bbdefb'}
            onMouseLeave={(e) => e.target.style.background = '#e3f2fd'}
          >
            📋 复制Markdown测试内容
          </button>
          <button
            style={{...buttonStyle, background: '#e8f5e8', borderColor: '#4caf50'}}
            onClick={copyTableToClipboard}
            onMouseEnter={(e) => e.target.style.background = '#c8e6c9'}
            onMouseLeave={(e) => e.target.style.background = '#e8f5e8'}
          >
            📊 复制表格测试内容
          </button>
          <button
            style={{
              ...buttonStyle,
              background: isLoadingPRD ? '#f5f5f5' : '#fff3e0',
              borderColor: '#ff9800',
              opacity: isLoadingPRD ? 0.6 : 1,
              cursor: isLoadingPRD ? 'not-allowed' : 'pointer'
            }}
            onClick={loadBSVPRD}
            disabled={isLoadingPRD}
            onMouseEnter={(e) => !isLoadingPRD && (e.target.style.background = '#ffe0b2')}
            onMouseLeave={(e) => !isLoadingPRD && (e.target.style.background = '#fff3e0')}
          >
            {isLoadingPRD ? '⏳ 加载中...' : '📄 加载BSV PRD文档'}
          </button>
        </div>
      </header>

      {/* 主要内容区 */}
      <main style={mainStyle}>
        {/* 编辑器区域 */}
        <section style={editorSectionStyle}>
          <TipTapEditor
            content={content}
            onChange={handleEditorChange}
            onSelectionUpdate={handleSelectionUpdate}
            onCreate={handleEditorCreate}
            onFocus={handleEditorFocus}
            onBlur={handleEditorBlur}
            placeholder="开始输入内容，或使用上方按钮插入表格..."
            style={{ height: '100%' }}
          />
        </section>

        {/* 侧边栏 */}
        <aside style={sidebarStyle}>
          {/* 表格操作面板 */}
          <div style={panelStyle}>
            <div style={panelHeaderStyle}>表格操作</div>
            <div style={{padding: '8px'}}>
              <TableMenu editor={editorRef.current} onLog={addLog} />
              <div style={{
                marginTop: '12px',
                padding: '8px',
                background: '#e3f2fd',
                borderRadius: '4px',
                fontSize: '12px',
                color: '#1976d2',
                lineHeight: '1.4'
              }}>
                <div style={{fontWeight: 'bold', marginBottom: '4px'}}>💡 使用提示:</div>
                <div>• 在表格中按 <kbd style={{background: '#fff', padding: '2px 4px', borderRadius: '2px', border: '1px solid #ddd'}}>Ctrl+Enter</kbd> 可在当前行下方添加新行</div>
                <div>• 使用右侧按钮进行更多表格操作</div>
                <div>• 粘贴Markdown内容时会自动提示转换格式</div>
                <div>• 工具栏按钮单击即可使用，无需双击</div>
              </div>
            </div>
          </div>

          {/* 文档大纲 */}
          <div style={panelStyle}>
            <div style={panelHeaderStyle}>文档大纲 ({outline.length})</div>
            <div style={panelContentStyle}>
              {outline.length > 0 ? (
                outline.map((heading, index) => (
                  <div
                    key={index}
                    style={{
                      padding: '4px 8px',
                      margin: '2px 0',
                      cursor: 'pointer',
                      borderRadius: '4px',
                      paddingLeft: `${8 + (heading.level - 1) * 16}px`,
                      fontSize: '13px',
                      color: '#555',
                      transition: 'background 0.2s',
                    }}
                    onClick={() => navigateToHeading(index)}
                    onMouseEnter={(e) => e.target.style.background = '#f0f0f0'}
                    onMouseLeave={(e) => e.target.style.background = 'transparent'}
                  >
                    H{heading.level} {heading.text}
                  </div>
                ))
              ) : (
                <div style={{color: '#999', fontSize: '12px'}}>暂无标题</div>
              )}
            </div>
          </div>

          {/* 表格列表 */}
          <div style={panelStyle}>
            <div style={panelHeaderStyle}>表格列表 ({tables.length})</div>
            <div style={panelContentStyle}>
              {tables.length > 0 ? (
                tables.map((table, index) => (
                  <div
                    key={index}
                    style={{
                      padding: '6px 8px',
                      margin: '2px 0',
                      cursor: 'pointer',
                      borderRadius: '4px',
                      fontSize: '12px',
                      color: '#555',
                      background: '#f8f9fa',
                      border: '1px solid #e1e5e9',
                      transition: 'background 0.2s',
                    }}
                    onClick={() => navigateToTable(index)}
                    onMouseEnter={(e) => e.target.style.background = '#e3f2fd'}
                    onMouseLeave={(e) => e.target.style.background = '#f8f9fa'}
                  >
                    表格 {index + 1} ({table.rows}×{table.cols})
                    {table.hasHeader && <span style={{color: '#1976d2'}}> 📑</span>}
                  </div>
                ))
              ) : (
                <div style={{color: '#999', fontSize: '12px'}}>暂无表格</div>
              )}
            </div>
          </div>

          {/* 编辑器状态 */}
          <div style={panelStyle}>
            <div style={panelHeaderStyle}>编辑器状态</div>
            <div style={panelContentStyle}>
              <div style={{fontSize: '12px', lineHeight: '1.5'}}>
                <div><strong>状态:</strong> {isEditorReady ? '✅ 就绪' : '⏳ 初始化中'}</div>
                <div><strong>字符数:</strong> {editorStats.characters || 0}</div>
                <div><strong>单词数:</strong> {editorStats.words || 0}</div>
                <div><strong>段落数:</strong> {editorStats.paragraphs || 0}</div>
                <div><strong>标题数:</strong> {editorStats.headings || 0}</div>
                <div><strong>表格数:</strong> {tables.length}</div>
                {currentSelection && !currentSelection.isEmpty && (
                  <div><strong>选中:</strong> {currentSelection.text.substring(0, 20)}...</div>
                )}
              </div>
            </div>
          </div>

          {/* PRD文档摘要 */}
          {prdSummary && (
            <div style={{...panelStyle, marginBottom: '16px'}}>
              <div style={panelHeaderStyle}>
                📄 文档信息
              </div>
              <div style={{...panelContentStyle, maxHeight: 'none'}}>
                <div style={{fontSize: '12px', lineHeight: '1.4'}}>
                  <div style={{marginBottom: '4px'}}>
                    <strong>标题:</strong> {prdSummary.title}
                  </div>
                  <div style={{marginBottom: '4px'}}>
                    <strong>章节数:</strong> {prdSummary.totalSections}
                  </div>
                  <div style={{marginBottom: '4px'}}>
                    <strong>预估字数:</strong> {prdSummary.totalWords}
                  </div>
                  <div>
                    <strong>加载时间:</strong> {prdSummary.lastModified}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 操作日志 */}
          <div style={panelStyle}>
            <div style={panelHeaderStyle}>
              操作日志 ({logs.length})
              <button
                style={{
                  float: 'right',
                  padding: '2px 6px',
                  border: '1px solid #ddd',
                  background: '#fff',
                  borderRadius: '3px',
                  cursor: 'pointer',
                  fontSize: '11px',
                }}
                onClick={clearLogs}
              >
                清空
              </button>
            </div>
            <div style={{...panelContentStyle, maxHeight: '300px'}}>
              {logs.length > 0 ? (
                logs.slice().reverse().map((log) => (
                  <div key={log.id} style={logEntryStyle(log.type)}>
                    <div style={{fontWeight: 'bold'}}>{log.timestamp}</div>
                    <div>{log.message}</div>
                  </div>
                ))
              ) : (
                <div style={{color: '#999', fontSize: '12px'}}>暂无日志</div>
              )}
            </div>
          </div>
        </aside>
      </main>
    </div>
  );
};

export default TipTapTableTest;
