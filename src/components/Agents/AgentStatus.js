import React, { useState } from 'react';

const AgentStatus = ({ 
  agents = [], 
  taskQueue = [], 
  aiStats = {},
  isLoading = false 
}) => {
  const [expandedAgent, setExpandedAgent] = useState(null);

  // 获取Agent类型对应的信息
  const getAgentTypeInfo = (type) => {
    const typeMap = {
      'analysis-agent': { 
        icon: '📊', 
        color: 'bg-blue-100 text-blue-800', 
        label: '分析助手',
        description: '专门负责需求分析和评估'
      },
      'knowledge-agent': { 
        icon: '📚', 
        color: 'bg-green-100 text-green-800', 
        label: '知识助手',
        description: '提供行业知识和标准规范'
      },
      'cases-agent': { 
        icon: '🔍', 
        color: 'bg-purple-100 text-purple-800', 
        label: '案例助手',
        description: '提供相关案例和最佳实践'
      },
      'inspiration-agent': { 
        icon: '💡', 
        color: 'bg-yellow-100 text-yellow-800', 
        label: '灵感助手',
        description: '提供创意建议和灵感激发'
      },
      'dialogue-agent': { 
        icon: '💬', 
        color: 'bg-orange-100 text-orange-800', 
        label: '对话助手',
        description: '进行深度对话和探讨'
      },
      'orchestrator-agent': { 
        icon: '🎭', 
        color: 'bg-indigo-100 text-indigo-800', 
        label: '编排助手',
        description: '协调多Agent协同工作'
      }
    };
    return typeMap[type] || { 
      icon: '🤖', 
      color: 'bg-gray-100 text-gray-800', 
      label: 'Agent',
      description: '智能助手'
    };
  };

  // 获取Agent状态信息
  const getStatusInfo = (status) => {
    const statusMap = {
      'active': { 
        color: 'text-green-600', 
        bgColor: 'bg-green-100',
        label: '在线', 
        icon: '🟢' 
      },
      'inactive': { 
        color: 'text-red-600', 
        bgColor: 'bg-red-100',
        label: '离线', 
        icon: '🔴' 
      },
      'busy': { 
        color: 'text-yellow-600', 
        bgColor: 'bg-yellow-100',
        label: '忙碌', 
        icon: '🟡' 
      },
      'error': { 
        color: 'text-red-600', 
        bgColor: 'bg-red-100',
        label: '错误', 
        icon: '❌' 
      }
    };
    return statusMap[status] || statusMap['inactive'];
  };

  // 格式化时间
  const formatTime = (timestamp) => {
    if (!timestamp) return '未知';
    return new Date(timestamp).toLocaleString();
  };

  // 计算Agent负载
  const getAgentLoad = (agent) => {
    const runningTasks = taskQueue.filter(task => 
      task.agentId === agent.id && task.status === 'running'
    ).length;
    return runningTasks;
  };

  // 切换Agent展开状态
  const toggleAgentExpanded = (agentId) => {
    setExpandedAgent(expandedAgent === agentId ? null : agentId);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-2 text-sm text-gray-600">正在加载Agent状态...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 统计概览 */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="text-sm font-semibold text-gray-800 mb-3">系统概览</h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-lg font-bold text-blue-600">{aiStats.availableAgentCount || 0}</div>
            <div className="text-xs text-gray-600">可用Agent</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-orange-600">{aiStats.runningTasks || 0}</div>
            <div className="text-xs text-gray-600">运行任务</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-600">{aiStats.totalCards || 0}</div>
            <div className="text-xs text-gray-600">总卡片数</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-purple-600">{aiStats.adoptedCards || 0}</div>
            <div className="text-xs text-gray-600">已采纳</div>
          </div>
        </div>
      </div>

      {/* Agent列表 */}
      <div className="space-y-3">
        <h3 className="text-sm font-semibold text-gray-800">Agent状态</h3>
        
        {agents.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <div className="text-4xl mb-2">🤖</div>
            <p className="font-semibold mb-1">暂无可用Agent</p>
            <p className="text-xs">系统正在尝试发现和连接Agent服务</p>
          </div>
        ) : (
          agents.map((agent) => {
            const typeInfo = getAgentTypeInfo(agent.type);
            const statusInfo = getStatusInfo(agent.status);
            const isExpanded = expandedAgent === agent.id;
            const currentLoad = getAgentLoad(agent);

            return (
              <div 
                key={agent.id}
                className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200"
              >
                {/* Agent头部信息 */}
                <div 
                  className="p-3 cursor-pointer"
                  onClick={() => toggleAgentExpanded(agent.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {/* Agent类型图标 */}
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${typeInfo.color}`}>
                        <span className="text-sm">{typeInfo.icon}</span>
                      </div>
                      
                      {/* Agent信息 */}
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h4 className="text-sm font-semibold text-gray-900">
                            {agent.metadata?.name || typeInfo.label}
                          </h4>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusInfo.bgColor} ${statusInfo.color}`}>
                            <span className="mr-1">{statusInfo.icon}</span>
                            {statusInfo.label}
                          </span>
                        </div>
                        <div className="text-xs text-gray-600 mt-1">
                          {typeInfo.description}
                        </div>
                      </div>
                    </div>

                    {/* 负载和展开图标 */}
                    <div className="flex items-center space-x-2">
                      {currentLoad > 0 && (
                        <span className="text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded">
                          {currentLoad} 任务
                        </span>
                      )}
                      <svg 
                        className={`w-4 h-4 text-gray-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* 展开的详细信息 */}
                {isExpanded && (
                  <div className="border-t border-gray-100 p-3 bg-gray-50">
                    <div className="grid grid-cols-2 gap-4 text-xs">
                      <div>
                        <div className="font-medium text-gray-700 mb-1">基本信息</div>
                        <div className="space-y-1">
                          <div>ID: <span className="font-mono text-gray-600">{agent.id}</span></div>
                          <div>版本: <span className="text-gray-600">{agent.metadata?.version || '未知'}</span></div>
                          <div>端点: <span className="font-mono text-gray-600">{agent.endpoint || '未知'}</span></div>
                        </div>
                      </div>
                      <div>
                        <div className="font-medium text-gray-700 mb-1">运行状态</div>
                        <div className="space-y-1">
                          <div>最后心跳: <span className="text-gray-600">{formatTime(agent.lastHeartbeat)}</span></div>
                          <div>任务数: <span className="text-gray-600">{agent.taskCount || 0}</span></div>
                          <div>成功率: <span className="text-gray-600">{Math.round((agent.successRate || 0) * 100)}%</span></div>
                        </div>
                      </div>
                    </div>

                    {/* 能力列表 */}
                    {agent.capabilities && agent.capabilities.length > 0 && (
                      <div className="mt-3">
                        <div className="font-medium text-gray-700 mb-2 text-xs">支持能力</div>
                        <div className="flex flex-wrap gap-1">
                          {agent.capabilities.map((capability, index) => (
                            <span 
                              key={index}
                              className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-600 rounded"
                            >
                              {capability}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })
        )}
      </div>

      {/* 任务队列状态 */}
      {taskQueue.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-gray-800 mb-3">任务队列</h3>
          <div className="space-y-2">
            {taskQueue.slice(0, 3).map((task, index) => (
              <div key={task.id || index} className="flex items-center justify-between text-xs">
                <span className="text-gray-600">任务 #{task.id}</span>
                <span className={`px-2 py-1 rounded ${
                  task.status === 'running' ? 'bg-blue-100 text-blue-600' :
                  task.status === 'pending' ? 'bg-yellow-100 text-yellow-600' :
                  'bg-gray-100 text-gray-600'
                }`}>
                  {task.status}
                </span>
              </div>
            ))}
            {taskQueue.length > 3 && (
              <div className="text-xs text-gray-500 text-center">
                还有 {taskQueue.length - 3} 个任务...
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AgentStatus;
