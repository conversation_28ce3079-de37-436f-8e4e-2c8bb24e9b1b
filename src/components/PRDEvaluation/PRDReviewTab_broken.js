import React, { useState, useEffect, useCallback } from 'react';
import { realPRDContent, mockIssues } from '../../data/realPRDContent';

const PRDReviewTab = ({ document, analysisResults, handleReviewComplete }) => {
  console.log('PRDReviewTab 渲染，参数:', { document: document?.title, analysisResults: analysisResults?.totalIssues });

  const [outlineExpanded, setOutlineExpanded] = useState(false); // 默认隐藏
  const [selectedIssue, setSelectedIssue] = useState(null);
  const [reviewedIssues, setReviewedIssues] = useState(new Set());
  const [acceptedSuggestions, setAcceptedSuggestions] = useState(new Set());
  const [documentSections, setDocumentSections] = useState([]);
  const [allIssues, setAllIssues] = useState([]);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const [reportProgress, setReportProgress] = useState(0);
  const [reviewCompleted, setReviewCompleted] = useState(false);

  // 初始化文档章节和问题数据
  const initializeDocumentSections = useCallback(() => {
    // 使用真实的PRD文档内容
    const sections = realPRDContent.sections.map(section => ({
      ...section,
      issues: mockIssues.filter(issue => issue.section === section.id)
    }));

    setDocumentSections(sections);
    setAllIssues(mockIssues);
  }, []);

  useEffect(() => {
    // 直接初始化，不依赖analysisResults
    initializeDocumentSections();
  }, [initializeDocumentSections]);

  // 处理问题确认
  const handleIssueReview = useCallback((issueId, accepted) => {
    setReviewedIssues(prev => new Set([...prev, issueId]));
    if (accepted) {
      setAcceptedSuggestions(prev => new Set([...prev, issueId]));
    } else {
      setAcceptedSuggestions(prev => {
        const newSet = new Set(prev);
        newSet.delete(issueId);
        return newSet;
      });
    }
  }, []);

  // 滚动到指定章节
  const scrollToSection = useCallback((sectionId) => {
    const element = window.document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, []);

  // 切换大纲显示状态，同时控制主导航
  const toggleOutline = useCallback(() => {
    setOutlineExpanded(prev => {
      const newExpanded = !prev;
      // 通知父组件隐藏/显示主导航
      if (newExpanded) {
        // 展开大纲时隐藏主导航
        const mainNav = window.document.querySelector('[data-main-nav]');
        if (mainNav) {
          mainNav.style.display = 'none';
        }
      } else {
        // 收起大纲时显示主导航
        const mainNav = window.document.querySelector('[data-main-nav]');
        if (mainNav) {
          mainNav.style.display = 'block';
        }
      }
      return newExpanded;
    });
  }, []);

  // 完成评审确认
  const handleCompleteReview = () => {
    setReviewCompleted(true);
    console.log('评审确认完成，等待生成报告');
  };

  // 生成报告
  const handleGenerateReport = async () => {
    setIsGeneratingReport(true);
    setReportProgress(0);
    
    // 模拟报告生成过程
    const steps = [
      "整理评审数据...",
      "生成统计图表...", 
      "编写分析报告...",
      "格式化输出...",
      "完成报告生成..."
    ];

    for (let i = 0; i < steps.length; i++) {
      console.log(`报告生成步骤 ${i + 1}: ${steps[i]}`);
      await new Promise(resolve => setTimeout(resolve, 800));
      setReportProgress(((i + 1) / steps.length) * 100);
    }

    // 生成最终的评审结果
    const reviewResults = {
      documentId: document?.id || 'lca-doc-001',
      reviewTimestamp: new Date().toISOString(),
      totalIssues: allIssues.length,
      reviewedIssues: reviewedIssues.size,
      acceptedSuggestions: acceptedSuggestions.size,
      rejectedSuggestions: reviewedIssues.size - acceptedSuggestions.size,
      reviewedIssueIds: Array.from(reviewedIssues),
      acceptedSuggestionIds: Array.from(acceptedSuggestions),
      overallApproval: reviewedIssues.size > 0 ? (acceptedSuggestions.size / reviewedIssues.size) * 100 : 0
    };

    setIsGeneratingReport(false);
    console.log('报告生成完成，跳转到评审结果页面');
    handleReviewComplete(reviewResults);
  };

  return (
    <div className="h-full flex flex-col">
      {/* 页面标题栏 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
        <div className="flex items-center space-x-4">
          <button
            onClick={toggleOutline}
            className={`p-2 rounded-md transition-colors ${
              outlineExpanded
                ? 'bg-orange-100 text-orange-600'
                : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
            }`}
            title={outlineExpanded ? '隐藏文档大纲' : '显示文档大纲'}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
            </svg>
          </button>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">评审确认</h2>
            <p className="text-sm text-gray-600 mt-1">
              请逐一确认AI分析发现的问题，决定是否采纳相关建议
            </p>
          </div>
        </div>
      </div>

      {/* 主体内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧：文档大纲（可隐藏） */}
        {outlineExpanded && (
          <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <h3 className="font-medium text-gray-900 flex items-center">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                文档大纲
              </h3>
            </div>
            <div className="flex-1 overflow-y-auto p-4">
              <div className="space-y-2">
                {documentSections.map((section) => (
                  <div key={section.id} className="space-y-1">
                    <button
                      onClick={() => scrollToSection(section.id)}
                      className="w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <span className={`font-medium ${
                          section.level === 1 ? 'text-gray-900' :
                          section.level === 2 ? 'text-gray-700 ml-4' :
                          'text-gray-600 ml-8'
                        }`}>
                          {section.title}
                        </span>
                        {section.issues && section.issues.length > 0 && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            {section.issues.length}
                          </span>
                        )}
                      </div>
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* 中间：文档内容 */}
        <div className="flex-1 bg-white flex flex-col">
          <div className="flex-1 overflow-y-auto">
            <div className="p-6 max-w-4xl mx-auto">
            <div className="prose prose-lg max-w-none">
              {documentSections.map((section) => (
                <div key={section.id} id={section.id} className="mb-12">
                  <h2 className={`font-bold mb-6 ${
                    section.level === 1 ? 'text-2xl text-gray-900' :
                    section.level === 2 ? 'text-xl text-gray-800' :
                    'text-lg text-gray-700'
                  }`}>
                    {section.title}
                  </h2>

                  <div className="text-gray-700 leading-relaxed">
                    {/* 渲染表格内容 */}
                    {section.content.includes('|') ? (
                      <div className="overflow-x-auto">
                        <table className="min-w-full border border-gray-300">
                          <tbody>
                            {section.content.split('\n').filter(line => line.includes('|')).map((row, index) => (
                              <tr key={index} className={index === 0 ? 'bg-gray-50' : ''}>
                                {row.split('|').map((cell, cellIndex) => (
                                  <td key={cellIndex} className="border border-gray-300 px-4 py-2 text-sm">
                                    {cell.trim()}
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <div className="whitespace-pre-wrap">{section.content}</div>
                    )}
                  </div>

                  {/* 章节问题标记 */}
                  {section.issues && section.issues.length > 0 && (
                    <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <h4 className="text-sm font-medium text-yellow-800 mb-3 flex items-center">
                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        本章节发现 {section.issues.length} 个问题
                      </h4>
                      <div className="space-y-2">
                        {section.issues.map((issue) => (
                          <button
                            key={issue.id}
                            onClick={() => setSelectedIssue(issue)}
                            className={`w-full text-left px-3 py-2 rounded text-sm transition-colors ${
                              selectedIssue?.id === issue.id
                                ? 'bg-orange-100 text-orange-800 border border-orange-200'
                                : reviewedIssues.has(issue.id)
                                  ? acceptedSuggestions.has(issue.id)
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-gray-100 text-gray-600'
                                  : 'hover:bg-white text-gray-700 border border-yellow-300'
                            }`}
                          >
                            <div className="flex items-center space-x-2">
                              <div className={`w-2 h-2 rounded-full ${
                                issue.type === 'error' ? 'bg-red-500' :
                                issue.type === 'warning' ? 'bg-yellow-500' :
                                'bg-blue-500'
                              }`} />
                              <span className="flex-1">{issue.title}</span>
                              {reviewedIssues.has(issue.id) && (
                                <span className={`text-xs px-2 py-1 rounded-full ${
                                  acceptedSuggestions.has(issue.id)
                                    ? 'bg-green-200 text-green-800'
                                    : 'bg-gray-200 text-gray-600'
                                }`}>
                                  {acceptedSuggestions.has(issue.id) ? '已采纳' : '已拒绝'}
                                </span>
                              )}
                            </div>
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 右侧：问题卡片列表 */}
        <div className="w-96 bg-gray-50 border-l border-gray-200 flex flex-col">
          <div className="p-4 border-b border-gray-200 bg-white">
            <h3 className="font-medium text-gray-900 flex items-center">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              问题列表
              <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                {allIssues.length}
              </span>
            </h3>
          </div>
          <div className="flex-1 overflow-y-auto">
            <div className="p-4 space-y-4">
            {allIssues.map((issue) => {
              // 找到问题所属的章节
              const section = documentSections.find(s => s.id === issue.section);
              const sectionTitle = section ? section.title : '未知章节';

              return (
                <div
                  key={issue.id}
                  className={`bg-white rounded-lg border transition-all ${
                    selectedIssue?.id === issue.id
                      ? 'border-orange-500 shadow-md'
                      : reviewedIssues.has(issue.id)
                        ? acceptedSuggestions.has(issue.id)
                          ? 'border-green-200 bg-green-50'
                          : 'border-gray-200 bg-gray-50'
                        : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
                  }`}
                >
                  {/* 问题卡片头部 - 定位信息 */}
                  <div className="p-3 border-b border-gray-100">
                    <div className="flex items-center justify-between">
                      <button
                        onClick={() => scrollToSection(issue.section)}
                        className="flex items-center space-x-2 text-xs text-blue-600 hover:text-blue-800 transition-colors"
                        title="定位到问题章节"
                      >
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        <span className="truncate max-w-48">{sectionTitle}</span>
                      </button>

                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                          issue.type === 'error' ? 'bg-red-100 text-red-800' :
                          issue.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-blue-100 text-blue-800'
                        }`}>
                          {issue.type === 'error' ? '错误' : issue.type === 'warning' ? '警告' : '建议'}
                        </span>
                        <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                          issue.priority === 'high' ? 'bg-red-100 text-red-800' :
                          issue.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {issue.priority === 'high' ? '高' : issue.priority === 'medium' ? '中' : '低'}
                        </span>
                        {reviewedIssues.has(issue.id) && (
                          <div className={`w-4 h-4 ${acceptedSuggestions.has(issue.id) ? 'text-green-600' : 'text-gray-500'}`}>
                            {acceptedSuggestions.has(issue.id) ? (
                              <svg fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            ) : (
                              <svg fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                              </svg>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* 问题卡片主体内容 */}
                  <div className="p-4">
                    {/* 问题标题 */}
                    <h4 className="font-medium text-gray-900 mb-2 text-sm">{issue.title}</h4>

                    {/* 问题描述 */}
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">{issue.description}</p>

                    {/* 展开/收起按钮 */}
                    {selectedIssue?.id !== issue.id && (
                      <div className="flex justify-center pt-2 border-t border-gray-100">
                        <button
                          onClick={() => setSelectedIssue(issue)}
                          className="flex items-center space-x-1 text-xs text-gray-500 hover:text-gray-700 transition-colors"
                        >
                          <span>展开详情</span>
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </button>
                      </div>
                    )}
                  </div>

                {/* 展开的详细信息 */}
                {selectedIssue?.id === issue.id && (
                  <div className="px-4 pb-4">
                    <div className="pt-3 border-t border-gray-200">
                      {/* 收起按钮 */}
                      <div className="flex justify-center mb-3">
                        <button
                          onClick={() => setSelectedIssue(null)}
                          className="flex items-center space-x-1 text-xs text-gray-500 hover:text-gray-700 transition-colors"
                        >
                          <span>收起详情</span>
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                          </svg>
                        </button>
                      </div>
                      <div className="space-y-3">
                        {/* 修改建议 */}
                        {issue.suggestion && (
                          <div>
                            <h5 className="text-xs font-medium text-gray-900 mb-1">修改建议:</h5>
                            <p className="text-xs text-blue-800 bg-blue-50 p-2 rounded">{issue.suggestion}</p>
                          </div>
                        )}

                        {/* AI推荐 */}
                        {issue.aiRecommendation && (
                          <div>
                            <h5 className="text-xs font-medium text-gray-900 mb-1">AI推荐:</h5>
                            <p className="text-xs text-purple-800 bg-purple-50 p-2 rounded">{issue.aiRecommendation}</p>
                          </div>
                        )}

                        {/* 置信度 */}
                        {issue.confidence && (
                          <div>
                            <h5 className="text-xs font-medium text-gray-900 mb-1">置信度:</h5>
                            <div className="flex items-center space-x-2">
                              <div className="flex-1 bg-gray-200 rounded-full h-1.5">
                                <div
                                  className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                                  style={{ width: `${issue.confidence}%` }}
                                ></div>
                              </div>
                              <span className="text-xs text-gray-600">{issue.confidence}%</span>
                            </div>
                          </div>
                        )}

                        {/* 操作按钮 */}
                        <div className="flex space-x-2 pt-2">
                          {reviewedIssues.has(issue.id) ? (
                            <>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleIssueReview(issue.id, true);
                                }}
                                className={`flex-1 px-3 py-1.5 rounded text-xs font-medium transition-colors ${
                                  acceptedSuggestions.has(issue.id)
                                    ? 'bg-green-100 text-green-800 border border-green-200'
                                    : 'bg-green-500 hover:bg-green-600 text-white'
                                }`}
                              >
                                {acceptedSuggestions.has(issue.id) ? '已采纳' : '采纳'}
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleIssueReview(issue.id, false);
                                }}
                                className={`flex-1 px-3 py-1.5 rounded text-xs font-medium transition-colors ${
                                  !acceptedSuggestions.has(issue.id)
                                    ? 'bg-gray-100 text-gray-800 border border-gray-200'
                                    : 'bg-gray-500 hover:bg-gray-600 text-white'
                                }`}
                              >
                                {!acceptedSuggestions.has(issue.id) ? '已拒绝' : '拒绝'}
                              </button>
                            </>
                          ) : (
                            <>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleIssueReview(issue.id, true);
                                }}
                                className="flex-1 bg-green-500 hover:bg-green-600 text-white px-3 py-1.5 rounded text-xs font-medium transition-colors"
                              >
                                采纳建议
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleIssueReview(issue.id, false);
                                }}
                                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-3 py-1.5 rounded text-xs font-medium transition-colors"
                              >
                                拒绝建议
                              </button>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            );
            })}
            </div>
          </div>
        </div>
      </div>

      {/* 底部操作栏 */}
      <div className="border-t border-gray-200 bg-white p-4">
        <div className="flex items-center justify-between">
          {/* 左侧：进度信息 */}
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">评审进度:</span>
              <span className="text-sm font-medium text-gray-900">
                {reviewedIssues.size}/{allIssues.length}
              </span>
              <div className="w-24 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${allIssues.length > 0 ? (reviewedIssues.size / allIssues.length) * 100 : 0}%` }}
                ></div>
              </div>
              <span className="text-sm text-gray-500">
                {Math.round(allIssues.length > 0 ? (reviewedIssues.size / allIssues.length) * 100 : 0)}%
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">采纳率:</span>
              <span className="text-sm font-medium text-green-600">
                {acceptedSuggestions.size}/{reviewedIssues.size}
              </span>
              <span className="text-sm text-gray-500">
                ({Math.round(reviewedIssues.size > 0 ? (acceptedSuggestions.size / reviewedIssues.size) * 100 : 0)}%)
              </span>
            </div>
          </div>

          {/* 右侧：操作按钮 */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => console.log('保存草稿')}
              className="border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors text-sm"
            >
              保存草稿
            </button>

            {!reviewCompleted ? (
              <button
                onClick={handleCompleteReview}
                disabled={reviewedIssues.size === 0}
                className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-4 py-2 rounded-md transition-colors text-sm"
              >
                完成评审确认
              </button>
            ) : (
              <button
                onClick={handleGenerateReport}
                disabled={isGeneratingReport}
                className="bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-4 py-2 rounded-md transition-colors text-sm"
              >
                {isGeneratingReport ? '生成中...' : '生成报告'}
              </button>
            )}
          </div>
        </div>

        {/* 报告生成进度 */}
        {isGeneratingReport && (
          <div className="mt-4 space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">正在生成评审报告</span>
              <span className="text-sm text-gray-500">{Math.floor(reportProgress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-orange-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${reportProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600">正在整理评审数据并生成详细报告...</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PRDReviewTab;
