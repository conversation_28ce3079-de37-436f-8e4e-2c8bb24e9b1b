import React from 'react';
import { Badge } from './UIComponents';

const StatusBadge = ({ status }) => {
  const statusConfig = {
    completed: {
      variant: "success",
      label: "已通过"
    },
    reviewing: {
      variant: "secondary",
      label: "评审中"
    },
    analyzing: {
      variant: "secondary",
      label: "分析中"
    },
    analyzed: {
      variant: "outline",
      label: "已分析"
    },
    'needs-revision': {
      variant: "destructive",
      label: "需修改"
    },
    uploaded: {
      variant: "outline",
      label: "待评审"
    }
  };

  const config = statusConfig[status] || statusConfig.uploaded;

  return (
    <Badge variant={config.variant}>
      {config.label}
    </Badge>
  );
};

export default StatusBadge;
