import React, { useState, useEffect } from 'react';
import { usePRDUpload } from '../../../hooks/usePRDUpload';

const DocumentUploadModal = ({ isOpen, onClose, uploadedDocuments, setUploadedDocuments, handleStartAnalysis }) => {
  const [uploadStatus, setUploadStatus] = useState('waiting'); // waiting, uploading, success, failed
  const [uploadCount, setUploadCount] = useState(0);
  const [failedCount, setFailedCount] = useState(0);

  const {
    isDragging,
    setIsDragging,
    uploadProgress,
    isUploading,
    handleFileUpload
  } = usePRDUpload();

  // 处理文档添加
  const onDocumentAdded = (newDocuments) => {
    setUploadedDocuments(prev => [...prev, ...newDocuments]);
    setUploadCount(prev => prev + newDocuments.length);
    setUploadStatus('success');
  };

  // 处理文件上传
  const onFileUpload = (files) => {
    setUploadStatus('uploading');
    setUploadCount(0);
    setFailedCount(0);

    // 模拟上传过程，实际应该根据handleFileUpload的结果来设置状态
    handleFileUpload(files, onDocumentAdded);
  };

  // 重置状态
  const resetUploadStatus = () => {
    setUploadStatus('waiting');
    setUploadCount(0);
    setFailedCount(0);
  };

  // 弹窗关闭时重置状态
  useEffect(() => {
    if (!isOpen) {
      resetUploadStatus();
    }
  }, [isOpen]);

  // 获取状态栏文本和样式
  const getStatusBarInfo = () => {
    switch (uploadStatus) {
      case 'waiting':
        return {
          text: '等待上传',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-600',
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          )
        };
      case 'uploading':
        return {
          text: '正在上传...',
          bgColor: 'bg-blue-100',
          textColor: 'text-blue-600',
          icon: (
            <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          )
        };
      case 'success':
        const successText = uploadCount === 1 ? '上传成功1个' : `上传成功${uploadCount}个`;
        return {
          text: successText,
          bgColor: 'bg-green-100',
          textColor: 'text-green-600',
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          )
        };
      case 'failed':
        return {
          text: `上传失败${failedCount}个`,
          bgColor: 'bg-red-100',
          textColor: 'text-red-600',
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          )
        };
      default:
        return {
          text: '等待上传',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-600',
          icon: null
        };
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* 背景遮罩 */}
      <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={onClose}></div>
      
      {/* 弹窗内容 */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] overflow-hidden">
          {/* 弹窗头部 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">文档上传</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* 弹窗主体内容 */}
          <div className="flex flex-col h-full">
            <div className="flex-1 p-6 overflow-y-auto">
              {/* 文件上传区域 */}
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  文档上传
                </h3>
                
                <div
                  className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                    isDragging ? 'border-orange-400 bg-orange-50' : 'border-gray-300'
                  }`}
                  onDragOver={(e) => {
                    e.preventDefault();
                    setIsDragging(true);
                  }}
                  onDragLeave={() => setIsDragging(false)}
                  onDrop={(e) => {
                    e.preventDefault();
                    setIsDragging(false);
                    onFileUpload(e.dataTransfer.files);
                  }}
                >
                  <svg className="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  <p className="text-lg font-medium text-gray-900 mb-2">上传PRD文档</p>
                  <p className="text-sm text-gray-500 mb-4">支持PDF、Word、Markdown格式，最大50MB</p>

                  {isUploading ? (
                    <div className="space-y-2">
                      <div className="w-64 mx-auto bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-orange-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${uploadProgress}%` }}
                        ></div>
                      </div>
                      <p className="text-sm text-gray-600">上传中... {uploadProgress}%</p>
                    </div>
                  ) : (
                    <div className="flex justify-center space-x-3">
                      <button
                        className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md transition-colors"
                        onClick={() => document.getElementById('modal-file-input')?.click()}
                      >
                        选择文件
                      </button>
                      <button className="border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors">
                        拖拽文件到此处
                      </button>
                    </div>
                  )}

                  <input
                    id="modal-file-input"
                    type="file"
                    multiple
                    accept=".pdf,.doc,.docx,.md,.txt"
                    className="hidden"
                    onChange={(e) => e.target.files && onFileUpload(e.target.files)}
                  />
                </div>
              </div>
            </div>

            {/* 底部状态栏 */}
            <div className="border-t border-gray-200 bg-gray-50 px-6 py-4">
              <div className="flex items-center justify-between">
                {/* 状态显示 */}
                <div className="flex items-center space-x-3">
                  {(() => {
                    const statusInfo = getStatusBarInfo();
                    return (
                      <div className={`flex items-center space-x-2 px-3 py-2 rounded-md ${statusInfo.bgColor}`}>
                        {statusInfo.icon}
                        <span className={`text-sm font-medium ${statusInfo.textColor}`}>
                          {statusInfo.text}
                        </span>
                      </div>
                    );
                  })()}
                </div>

                {/* 关闭按钮 */}
                <button
                  onClick={onClose}
                  className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors"
                >
                  关闭
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentUploadModal;
