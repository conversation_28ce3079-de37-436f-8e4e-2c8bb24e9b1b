import React from 'react';

const ReviewProgress = ({ 
  reviewedIssues, 
  totalIssues, 
  acceptedSuggestions,
  isGeneratingReport,
  reportProgress,
  reviewCompleted,
  onSaveDraft, 
  onCompleteReview,
  onGenerateReport
}) => {
  const reviewProgress = totalIssues > 0 ? (reviewedIssues.size / totalIssues) * 100 : 0;
  const acceptanceRate = reviewedIssues.size > 0 ? (acceptedSuggestions.size / reviewedIssues.size) * 100 : 0;

  return (
    <div className="space-y-4">
      {/* 报告生成进度 */}
      {isGeneratingReport && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">正在生成评审报告</span>
              <span className="text-sm text-gray-500">{Math.floor(reportProgress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className="bg-orange-500 h-3 rounded-full transition-all duration-300"
                style={{ width: `${reportProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600">正在整理评审数据并生成详细报告...</p>
          </div>
        </div>
      )}

      {/* 评审进度统计 */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          {/* 左侧：进度信息 */}
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">评审进度:</span>
              <span className="text-sm font-medium text-gray-900">
                {reviewedIssues.size}/{totalIssues}
              </span>
              <div className="w-24 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${reviewProgress}%` }}
                ></div>
              </div>
              <span className="text-sm text-gray-500">
                {Math.round(reviewProgress)}%
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">采纳率:</span>
              <span className="text-sm font-medium text-green-600">
                {acceptedSuggestions.size}/{reviewedIssues.size}
              </span>
              <span className="text-sm text-gray-500">
                ({Math.round(acceptanceRate)}%)
              </span>
            </div>

            {/* 问题类型统计 */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span className="text-xs text-gray-600">错误</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span className="text-xs text-gray-600">警告</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-xs text-gray-600">建议</span>
              </div>
            </div>
          </div>

          {/* 右侧：操作按钮 */}
          <div className="flex items-center space-x-2">
            <button 
              onClick={onSaveDraft}
              className="border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors text-sm"
            >
              保存草稿
            </button>
            
            {!reviewCompleted ? (
              <button
                onClick={onCompleteReview}
                disabled={reviewedIssues.size === 0}
                className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-4 py-2 rounded-md transition-colors text-sm"
              >
                完成评审确认
              </button>
            ) : (
              <button
                onClick={onGenerateReport}
                disabled={isGeneratingReport}
                className="bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-4 py-2 rounded-md transition-colors text-sm"
              >
                {isGeneratingReport ? '生成中...' : '生成报告'}
              </button>
            )}
          </div>
        </div>

        {/* 详细统计信息 */}
        {reviewedIssues.size > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-lg font-semibold text-gray-900">{totalIssues}</div>
                <div className="text-xs text-gray-600">总问题数</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-blue-600">{reviewedIssues.size}</div>
                <div className="text-xs text-gray-600">已评审</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-green-600">{acceptedSuggestions.size}</div>
                <div className="text-xs text-gray-600">已采纳</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-gray-600">
                  {reviewedIssues.size - acceptedSuggestions.size}
                </div>
                <div className="text-xs text-gray-600">已拒绝</div>
              </div>
            </div>
          </div>
        )}

        {/* 评审提示 */}
        {reviewedIssues.size === 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-center text-sm text-gray-500">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              请点击文档中的问题标记开始评审
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReviewProgress;
