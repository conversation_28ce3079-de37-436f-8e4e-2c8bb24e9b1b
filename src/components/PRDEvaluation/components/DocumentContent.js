import React from 'react';
import IssueMarker from './IssueMarker';

const DocumentContent = ({ 
  documentSections, 
  selectedIssue, 
  reviewedIssues, 
  acceptedSuggestions, 
  onIssueClick 
}) => {
  return (
    <div className="flex-1 bg-white border border-gray-200 rounded-lg p-6 overflow-y-auto">
      <div className="prose max-w-none">
        {documentSections.map((section) => (
          <div key={section.id} id={section.id} className="mb-8">
            {/* 章节标题 */}
            <h2 className={`font-bold mb-4 ${
              section.level === 1 ? 'text-2xl text-gray-900' :
              section.level === 2 ? 'text-xl text-gray-800' :
              'text-lg text-gray-700'
            }`}>
              {section.title}
            </h2>
            
            {/* 章节内容 */}
            <div className="relative">
              <div className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                {section.content}
              </div>
              
              {/* 问题标记覆盖层 */}
              {section.issues && section.issues.map((issue) => (
                <IssueMarker
                  key={issue.id}
                  issue={issue}
                  isSelected={selectedIssue?.id === issue.id}
                  isReviewed={reviewedIssues.has(issue.id)}
                  isAccepted={acceptedSuggestions.has(issue.id)}
                  onClick={() => onIssueClick(issue)}
                />
              ))}
            </div>
            
            {/* 章节问题汇总 */}
            {section.issues && section.issues.length > 0 && (
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 mb-2">
                  本章节发现 {section.issues.length} 个问题
                </h4>
                <div className="space-y-1">
                  {section.issues.map((issue) => (
                    <button
                      key={issue.id}
                      onClick={() => onIssueClick(issue)}
                      className={`w-full text-left px-3 py-2 rounded text-sm transition-colors ${
                        selectedIssue?.id === issue.id
                          ? 'bg-orange-100 text-orange-800'
                          : reviewedIssues.has(issue.id)
                            ? acceptedSuggestions.has(issue.id)
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-600'
                            : 'hover:bg-white text-gray-700'
                      }`}
                    >
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${
                          issue.type === 'error' ? 'bg-red-500' :
                          issue.type === 'warning' ? 'bg-yellow-500' :
                          'bg-blue-500'
                        }`} />
                        <span className="flex-1">{issue.title}</span>
                        {reviewedIssues.has(issue.id) && (
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            acceptedSuggestions.has(issue.id)
                              ? 'bg-green-200 text-green-800'
                              : 'bg-gray-200 text-gray-600'
                          }`}>
                            {acceptedSuggestions.has(issue.id) ? '已采纳' : '已拒绝'}
                          </span>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default DocumentContent;
