import React from 'react';

const IssueDetailPanel = ({ 
  selectedIssue, 
  reviewedIssues, 
  acceptedSuggestions, 
  onIssueReview, 
  onClose 
}) => {
  if (!selectedIssue) {
    return (
      <div className="w-96 bg-gray-50 border border-gray-200 rounded-lg p-6 flex items-center justify-center">
        <div className="text-center text-gray-500">
          <svg className="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p className="text-sm">点击左侧问题查看详情</p>
        </div>
      </div>
    );
  }

  const isReviewed = reviewedIssues.has(selectedIssue.id);
  const isAccepted = acceptedSuggestions.has(selectedIssue.id);

  const getTypeColor = (type) => {
    switch (type) {
      case 'error': return 'text-red-600 bg-red-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'suggestion': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getTypeLabel = (type) => {
    switch (type) {
      case 'error': return '错误';
      case 'warning': return '警告';
      case 'suggestion': return '建议';
      default: return '其他';
    }
  };



  return (
    <div className="w-96 bg-white border border-gray-200 rounded-lg p-6 overflow-y-auto">
      {/* 头部 */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">问题详情</h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* 问题基本信息 */}
      <div className="space-y-4">
        {/* 问题类型 */}
        <div className="flex items-center space-x-2">
          <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(selectedIssue.type)}`}>
            {getTypeLabel(selectedIssue.type)}
          </span>
        </div>

        {/* 问题标题 */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-2">问题标题</h4>
          <p className="text-sm text-gray-700">{selectedIssue.title}</p>
        </div>

        {/* 问题描述 */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-2">问题描述</h4>
          <p className="text-sm text-gray-700 leading-relaxed">{selectedIssue.description}</p>
        </div>

        {/* 修改建议 */}
        {selectedIssue.suggestion && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">修改建议</h4>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <p className="text-sm text-blue-800">{selectedIssue.suggestion}</p>
            </div>
          </div>
        )}

        {/* AI推荐 */}
        {selectedIssue.aiRecommendation && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">AI推荐</h4>
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
              <p className="text-sm text-purple-800">{selectedIssue.aiRecommendation}</p>
            </div>
          </div>
        )}

        {/* 置信度 */}
        {selectedIssue.confidence && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">置信度</h4>
            <div className="flex items-center space-x-2">
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${selectedIssue.confidence}%` }}
                ></div>
              </div>
              <span className="text-sm text-gray-600">{selectedIssue.confidence}%</span>
            </div>
          </div>
        )}

        {/* 相关规则 */}
        {selectedIssue.rule && (
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">相关规则</h4>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
              <p className="text-sm text-gray-700">{selectedIssue.rule}</p>
              {selectedIssue.ruleId && (
                <p className="text-xs text-gray-500 mt-1">规则ID: {selectedIssue.ruleId}</p>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 操作按钮 */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        {isReviewed ? (
          <div className="space-y-3">
            <div className={`p-3 rounded-lg ${isAccepted ? 'bg-green-50 border border-green-200' : 'bg-gray-50 border border-gray-200'}`}>
              <div className="flex items-center">
                <div className={`w-5 h-5 mr-2 ${isAccepted ? 'text-green-600' : 'text-gray-500'}`}>
                  {isAccepted ? (
                    <svg fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
                <span className={`text-sm font-medium ${isAccepted ? 'text-green-800' : 'text-gray-600'}`}>
                  {isAccepted ? '已采纳此建议' : '已拒绝此建议'}
                </span>
              </div>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => onIssueReview(selectedIssue.id, true)}
                className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  isAccepted 
                    ? 'bg-green-100 text-green-800 border border-green-200' 
                    : 'bg-green-500 hover:bg-green-600 text-white'
                }`}
              >
                {isAccepted ? '已采纳' : '采纳'}
              </button>
              <button
                onClick={() => onIssueReview(selectedIssue.id, false)}
                className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  !isAccepted 
                    ? 'bg-gray-100 text-gray-800 border border-gray-200' 
                    : 'bg-gray-500 hover:bg-gray-600 text-white'
                }`}
              >
                {!isAccepted ? '已拒绝' : '拒绝'}
              </button>
            </div>
          </div>
        ) : (
          <div className="flex space-x-2">
            <button
              onClick={() => onIssueReview(selectedIssue.id, true)}
              className="flex-1 bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
            >
              采纳建议
            </button>
            <button
              onClick={() => onIssueReview(selectedIssue.id, false)}
              className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
            >
              拒绝建议
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default IssueDetailPanel;
