import React from 'react';

const IssueMarker = ({ 
  issue, 
  isSelected, 
  isReviewed, 
  isAccepted, 
  onClick 
}) => {
  const getMarkerStyle = () => {
    let baseStyle = "absolute left-0 right-0 cursor-pointer transition-all duration-200 ";
    
    if (isSelected) {
      baseStyle += "bg-orange-200 border-l-4 border-orange-500 ";
    } else if (isReviewed) {
      if (isAccepted) {
        baseStyle += "bg-green-100 border-l-4 border-green-500 ";
      } else {
        baseStyle += "bg-gray-100 border-l-4 border-gray-400 ";
      }
    } else {
      switch (issue.type) {
        case 'error':
          baseStyle += "bg-red-100 border-l-4 border-red-500 hover:bg-red-200 ";
          break;
        case 'warning':
          baseStyle += "bg-yellow-100 border-l-4 border-yellow-500 hover:bg-yellow-200 ";
          break;
        case 'suggestion':
          baseStyle += "bg-blue-100 border-l-4 border-blue-500 hover:bg-blue-200 ";
          break;
        default:
          baseStyle += "bg-gray-100 border-l-4 border-gray-500 hover:bg-gray-200 ";
      }
    }
    
    return baseStyle;
  };

  const getIconColor = () => {
    if (isSelected) return "text-orange-600";
    if (isReviewed) {
      return isAccepted ? "text-green-600" : "text-gray-500";
    }
    switch (issue.type) {
      case 'error': return "text-red-600";
      case 'warning': return "text-yellow-600";
      case 'suggestion': return "text-blue-600";
      default: return "text-gray-600";
    }
  };

  const getIcon = () => {
    if (isReviewed) {
      return isAccepted ? (
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
      ) : (
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      );
    }

    switch (issue.type) {
      case 'error':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'suggestion':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  return (
    <div
      className={getMarkerStyle()}
      onClick={onClick}
      style={{
        top: `${(issue.startOffset || 0) * 1.5}em`,
        height: `${((issue.endOffset || 1) - (issue.startOffset || 0)) * 1.5}em`,
        minHeight: '1.5em'
      }}
    >
      <div className="flex items-center h-full px-2">
        <div className={`${getIconColor()} mr-2`}>
          {getIcon()}
        </div>
        <span className="text-xs font-medium truncate">
          {issue.title}
        </span>
      </div>
    </div>
  );
};

export default IssueMarker;
