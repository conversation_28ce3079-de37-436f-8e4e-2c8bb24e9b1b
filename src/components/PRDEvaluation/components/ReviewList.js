import React from 'react';
import { Card, CardHeader, CardTitle, CardContent, Button } from './UIComponents';
import { Upload, FileText } from './Icons';
import EmptyState from './EmptyState';
import DocumentTable from './DocumentTable';

const ReviewList = ({
  documents,
  expandedDocuments,
  onToggleExpansion,
  onStartReview,
  onContinueReview,
  onViewResults,
  onRetryReview,
  onUploadDocument,
  onImportFromEditor
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>评审列表</span>
          <div className="flex space-x-2">
            <Button 
              className="bg-orange-500 hover:bg-orange-600" 
              size="sm" 
              onClick={onUploadDocument}
            >
              <Upload className="w-4 h-4 mr-2" />
              上传文档
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onImportFromEditor}
            >
              <FileText className="w-4 h-4 mr-2" />
              从AI PRD编辑器导入
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {documents.length === 0 ? (
          <EmptyState />
        ) : (
          <DocumentTable 
            documents={documents}
            expandedDocuments={expandedDocuments}
            onToggleExpansion={onToggleExpansion}
            onStartReview={onStartReview}
            onContinueReview={onContinueReview}
            onViewResults={onViewResults}
            onRetryReview={onRetryReview}
          />
        )}
      </CardContent>
    </Card>
  );
};

export default ReviewList;
