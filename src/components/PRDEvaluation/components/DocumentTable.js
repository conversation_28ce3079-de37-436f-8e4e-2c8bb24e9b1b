import React from 'react';
import { Table, TableHeader, TableBody, TableRow, TableHead } from './UIComponents';
import DocumentRowGroup from './DocumentRowGroup';

const DocumentTable = ({
  documents,
  expandedDocuments,
  onToggleExpansion,
  onStartReview,
  onContinueReview,
  onViewResults,
  onRetryReview
}) => {
  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-1/4">文档名称</TableHead>
            <TableHead className="w-20">来源</TableHead>
            <TableHead className="w-24">状态</TableHead>
            <TableHead className="w-20">问题数</TableHead>
            <TableHead className="w-24">修复问题数</TableHead>
            <TableHead className="w-24">产品经理</TableHead>
            <TableHead className="w-24">架构师</TableHead>
            <TableHead className="w-24">测试经理</TableHead>
            <TableHead className="w-24">提交人</TableHead>
            <TableHead className="w-28">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {documents.map((document) => (
            <DocumentRowGroup
              key={document.id}
              document={document}
              isExpanded={expandedDocuments.has(document.id)}
              onToggleExpansion={onToggleExpansion}
              onStartReview={onStartReview}
              onContinueReview={onContinueReview}
              onViewResults={onViewResults}
              onRetryReview={onRetryReview}
            />
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default DocumentTable;
