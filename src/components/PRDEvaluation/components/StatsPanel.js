import React from 'react';
import { FileText, TrendingUp, Target, BarChart } from './Icons';

const StatsPanel = ({ stats }) => {
  const statItems = [
    {
      label: "待评审文档",
      value: stats.pendingReviews,
      bgColor: "bg-orange-100",
      iconColor: "text-orange-600",
      icon: <FileText className="w-4 h-4" />
    },
    {
      label: "评审通过率",
      value: `${stats.passRate}%`,
      bgColor: "bg-green-100",
      iconColor: "text-green-600",
      icon: <TrendingUp className="w-4 h-4" />
    },
    {
      label: "问题采纳率",
      value: `${stats.issueAdoptionRate}%`,
      bgColor: "bg-blue-100",
      iconColor: "text-blue-600",
      icon: <Target className="w-4 h-4" />
    },
    {
      label: "评估规则数",
      value: stats.ruleCount,
      bgColor: "bg-purple-100",
      iconColor: "text-purple-600",
      icon: <BarChart className="w-4 h-4" />
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statItems.map((item, index) => (
        <div key={index} className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{item.label}</p>
                <p className="text-2xl font-bold text-gray-900">{item.value}</p>
              </div>
              <div className={`w-8 h-8 ${item.bgColor} rounded-full flex items-center justify-center`}>
                <div className={item.iconColor}>
                  {item.icon}
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default StatsPanel;
