import React from 'react';
import { MessageSquare, Calendar } from './Icons';

const PageHeader = ({ timeFilter, onTimeFilterChange }) => {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-2">
        {/* 页面图标 */}
        <div className="w-6 h-6 text-orange-500">
          <MessageSquare />
        </div>
        <h2 className="text-2xl font-bold text-gray-900">评审中心</h2>
      </div>
      
      <div className="flex items-center space-x-2">
        {/* 日历图标 */}
        <div className="w-4 h-4 text-gray-500">
          <Calendar />
        </div>
        
        {/* 时间筛选下拉框 */}
        <select
          value={timeFilter}
          onChange={(e) => onTimeFilterChange(e.target.value)}
          className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white"
        >
          <option value="1week">最近一周</option>
          <option value="1month">最近一个月</option>
          <option value="3months">最近三个月</option>
          <option value="6months">最近半年</option>
          <option value="1year">最近一年</option>
        </select>
      </div>
    </div>
  );
};

export default PageHeader;
