import React from 'react';
import { TableRow, <PERSON><PERSON>ell, <PERSON>ge, <PERSON><PERSON> } from './UIComponents';
import { ChevronDown } from './Icons';
import SourceIcon from './SourceIcon';
import StatusBadge from './StatusBadge';
import ActionButtons from './ActionButtons';

const DocumentRowGroup = ({
  document,
  isExpanded,
  onToggleExpansion,
  onStartReview,
  onContinueReview,
  onViewResults,
  onRetryReview
}) => {
  const hasMultipleVersions = document.versions && document.versions.length > 0;

  return (
    <React.Fragment>
      {/* 主文档行 */}
      <TableRow>
        <TableCell className="font-medium">
          <div className="flex items-center space-x-2">
            {hasMultipleVersions && (
              <button
                onClick={() => onToggleExpansion(document.id)}
                className="cursor-pointer hover:text-blue-600"
              >
                <ChevronDown className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
              </button>
            )}
            <span>{document.name || document.title}</span>
            <Badge variant="outline" className="text-xs">
              {document.version || 'v1.0.0'}
            </Badge>
            {hasMultipleVersions && (
              <Badge variant="secondary" className="text-xs">
                {document.versions.length + 1} 版本
              </Badge>
            )}
          </div>
        </TableCell>
        <TableCell>
          <SourceIcon source={document.source || 'manual'} />
        </TableCell>
        <TableCell>
          <StatusBadge status={document.status} />
        </TableCell>
        <TableCell className="text-sm">{document.issues || 0}</TableCell>
        <TableCell className="text-sm">
          {document.fixed !== undefined ? (
            <span className="text-green-600 font-medium">{document.fixed}</span>
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </TableCell>
        <TableCell className="text-sm">{document.productManager || '产品经理'}</TableCell>
        <TableCell className="text-sm">{document.architect || '架构师'}</TableCell>
        <TableCell className="text-sm">{document.testManager || '测试经理'}</TableCell>
        <TableCell className="text-sm">{document.submitter || '提交人'}</TableCell>
        <TableCell>
          <ActionButtons
            document={document}
            onStartReview={onStartReview}
            onContinueReview={onContinueReview}
            onViewResults={onViewResults}
            onRetryReview={onRetryReview}
          />
        </TableCell>
      </TableRow>

      {/* 版本历史行 */}
      {isExpanded && hasMultipleVersions && document.versions.map((version) => (
        <TableRow key={version.id} className="bg-gray-50">
          <TableCell className="pl-8">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <span>└─ {version.fileName || version.name}</span>
              <Badge variant="outline" className="text-xs">
                {version.version}
              </Badge>
            </div>
          </TableCell>
          <TableCell>
            <SourceIcon source={version.source || 'manual'} />
          </TableCell>
          <TableCell>
            <StatusBadge status={version.status} />
          </TableCell>
          <TableCell className="text-sm">{version.issues || 0}</TableCell>
          <TableCell className="text-sm">
            {version.fixed !== undefined ? (
              <span className="text-green-600 font-medium">{version.fixed}</span>
            ) : (
              <span className="text-gray-400">-</span>
            )}
          </TableCell>
          <TableCell className="text-sm">{version.productManager || '产品经理'}</TableCell>
          <TableCell className="text-sm">{version.architect || '架构师'}</TableCell>
          <TableCell className="text-sm">{version.testManager || '测试经理'}</TableCell>
          <TableCell className="text-sm">{version.submitter || '提交人'}</TableCell>
          <TableCell>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onViewResults(version.id)}
              className="text-blue-600"
            >
              查看
            </Button>
          </TableCell>
        </TableRow>
      ))}
    </React.Fragment>
  );
};

export default DocumentRowGroup;
