import React from 'react';

const DocumentOutline = ({ 
  documentSections, 
  selectedIssue, 
  onSectionClick, 
  onIssueClick 
}) => {
  return (
    <div className="w-80 bg-white border border-gray-200 rounded-lg p-4 overflow-y-auto">
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
        </svg>
        文档大纲
      </h3>
      
      <div className="space-y-2">
        {documentSections.map((section) => (
          <div key={section.id} className="space-y-1">
            {/* 章节标题 */}
            <button
              onClick={() => onSectionClick(section.id)}
              className="w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors"
            >
              <div className="flex items-center justify-between">
                <span className={`font-medium ${
                  section.level === 1 ? 'text-gray-900' : 
                  section.level === 2 ? 'text-gray-700 ml-4' : 
                  'text-gray-600 ml-8'
                }`}>
                  {section.title}
                </span>
                {section.issues && section.issues.length > 0 && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    {section.issues.length}
                  </span>
                )}
              </div>
            </button>
            
            {/* 章节下的问题列表 */}
            {section.issues && section.issues.length > 0 && (
              <div className="ml-6 space-y-1">
                {section.issues.map((issue) => (
                  <button
                    key={issue.id}
                    onClick={() => onIssueClick(issue)}
                    className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                      selectedIssue?.id === issue.id
                        ? 'bg-orange-100 text-orange-800 border border-orange-200'
                        : 'hover:bg-gray-50 text-gray-600'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${
                        issue.type === 'error' ? 'bg-red-500' :
                        issue.type === 'warning' ? 'bg-yellow-500' :
                        'bg-blue-500'
                      }`} />
                      <span className="truncate">{issue.title}</span>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
      
      {/* 大纲统计 */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="text-sm text-gray-600 space-y-1">
          <div className="flex justify-between">
            <span>总章节数:</span>
            <span>{documentSections.length}</span>
          </div>
          <div className="flex justify-between">
            <span>发现问题:</span>
            <span>{documentSections.reduce((total, section) => total + (section.issues?.length || 0), 0)}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentOutline;
