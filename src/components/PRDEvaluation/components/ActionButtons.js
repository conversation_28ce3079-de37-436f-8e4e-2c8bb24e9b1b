import React from 'react';
import { Button } from './UIComponents';

const ActionButtons = ({
  document,
  onStartReview,
  onContinueReview,
  onViewResults,
  onRetryReview
}) => {
  const buttonConfig = {
    uploaded: {
      label: "开始评审",
      onClick: () => onStartReview(document.id),
      className: "text-blue-600"
    },
    reviewing: {
      label: "继续评审",
      onClick: () => onContinueReview(document.id),
      className: "text-orange-600"
    },
    analyzing: {
      label: "分析中",
      onClick: null,
      className: "text-gray-400",
      disabled: true
    },
    analyzed: {
      label: "开始评审",
      onClick: () => onStartReview(document.id),
      className: "text-blue-600"
    },
    completed: {
      label: "查看结果",
      onClick: () => onViewResults(document.id),
      className: "text-green-600"
    },
    'needs-revision': {
      label: "重新评审",
      onClick: () => onRetryReview(document.id),
      className: "text-red-600"
    }
  };

  const config = buttonConfig[document.status];

  if (!config || !config.onClick) {
    return (
      <div className="flex space-x-1">
        <Button
          variant="ghost"
          size="sm"
          disabled={true}
          className="text-gray-400"
        >
          {config?.label || "无操作"}
        </Button>
      </div>
    );
  }

  return (
    <div className="flex space-x-1">
      <Button
        variant="ghost"
        size="sm"
        onClick={config.onClick}
        className={config.className}
        disabled={config.disabled}
      >
        {config.label}
      </Button>
    </div>
  );
};

export default ActionButtons;
