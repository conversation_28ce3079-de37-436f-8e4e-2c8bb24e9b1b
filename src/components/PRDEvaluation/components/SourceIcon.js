import React from 'react';
import { Bot, Upload, Search } from './Icons';

const SourceIcon = ({ source }) => {
  const sourceConfig = {
    ai: {
      icon: Bot,
      color: "text-blue-500",
      bgColor: "text-blue-600",
      label: "AI"
    },
    manual: {
      icon: Upload,
      color: "text-green-500",
      bgColor: "text-green-600",
      label: "上传"
    },
    'self-review': {
      icon: Search,
      color: "text-purple-500",
      bgColor: "text-purple-600",
      label: "自评"
    }
  };

  const config = sourceConfig[source] || sourceConfig.manual;
  const IconComponent = config.icon;

  return (
    <div className="flex items-center space-x-1">
      <div className={`w-4 h-4 ${config.color}`}>
        <IconComponent />
      </div>
      <span className={`text-xs ${config.bgColor}`}>{config.label}</span>
    </div>
  );
};

export default SourceIcon;
