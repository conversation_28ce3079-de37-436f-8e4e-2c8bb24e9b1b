import React, { useState, useEffect, useCallback } from 'react';
import { realPRDContent, mockIssues } from '../../data/realPRDContent';
import './PRDReviewTab.css';

const PRDReviewTab = ({ document, analysisResults, handleReviewComplete, targetProblemId, onReturnToResults }) => {
  console.log('PRDReviewTab 渲染，参数:', { document: document?.title, analysisResults: analysisResults?.totalIssues });

  const [outlineExpanded, setOutlineExpanded] = useState(false); // 默认隐藏
  const [selectedIssue, setSelectedIssue] = useState(null);
  const [reviewedIssues, setReviewedIssues] = useState(new Set());
  const [acceptedSuggestions, setAcceptedSuggestions] = useState(new Set());
  const [documentSections, setDocumentSections] = useState([]);
  const [allIssues, setAllIssues] = useState([]);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const [reportProgress, setReportProgress] = useState(0);
  const [reviewCompleted, setReviewCompleted] = useState(false);
  const [highlightedIssueId, setHighlightedIssueId] = useState(null);
  const [isFromResultsTab, setIsFromResultsTab] = useState(false);

  // 初始化文档章节和问题数据
  const initializeDocumentSections = useCallback(() => {
    // 使用真实的PRD文档内容
    const sections = realPRDContent.sections.map(section => ({
      ...section,
      issues: mockIssues.filter(issue => issue.section === section.id)
    }));

    setDocumentSections(sections);
    setAllIssues(mockIssues);
  }, []);

  // 滚动到指定章节
  const scrollToSection = useCallback((sectionId) => {
    console.log('点击大纲章节，定位到:', sectionId);

    // 查找目标章节元素
    const targetElement = window.document.getElementById(sectionId);
    if (!targetElement) {
      console.log('未找到目标章节元素:', sectionId);
      return;
    }

    // 查找文档内容滚动容器
    const contentContainer = targetElement.closest('.overflow-y-auto');

    if (contentContainer) {
      // 计算目标元素在容器中的准确位置
      const elementOffsetTop = targetElement.offsetTop;

      // 计算目标滚动位置（元素顶部距离容器顶部20px）
      const targetPosition = Math.max(0, elementOffsetTop - 20);

      // 执行滚动到章节起始处
      try {
        contentContainer.scrollTo({
          top: targetPosition,
          behavior: 'smooth'
        });

        console.log('大纲定位到:', sectionId, '位置:', targetPosition);
      } catch (error) {
        // 回退方案：直接设置scrollTop
        contentContainer.scrollTop = targetPosition;
      }

      // 添加章节高亮效果
      targetElement.classList.add('outline-section-highlight', 'highlight-pulse');

      // 移除高亮效果
      setTimeout(() => {
        targetElement.classList.remove('highlight-pulse');
      }, 2000);

      // 3秒后移除章节高亮
      setTimeout(() => {
        targetElement.classList.remove('outline-section-highlight');
      }, 3000);
    } else {
      // 备用方案：使用标准的scrollIntoView
      targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, []);

  // 滚动到右侧问题卡片并展开
  const scrollToIssueCard = useCallback((issue) => {
    console.log('点击左侧问题，定位到右侧问题卡片:', issue.id);

    // 设置选中的问题（展开卡片）
    setSelectedIssue(issue);

    // 滚动到问题卡片
    setTimeout(() => {
      const element = window.document.getElementById(`issue-card-${issue.id}`);
      console.log('查找右侧问题卡片元素:', `issue-card-${issue.id}`, element);

      if (element) {
        // 滚动到问题卡片，确保在右侧面板中可见
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // 高亮显示问题卡片
        setHighlightedIssueId(issue.id);
        console.log('问题卡片已高亮显示:', issue.id);

        // 3秒后移除高亮效果
        setTimeout(() => setHighlightedIssueId(null), 3000);
      } else {
        console.warn('未找到右侧问题卡片元素:', `issue-card-${issue.id}`);
      }
    }, 200);
  }, []);

  useEffect(() => {
    // 直接初始化，不依赖analysisResults
    initializeDocumentSections();
  }, [initializeDocumentSections]);

  // 处理从评审结果页面导航过来的问题定位
  useEffect(() => {
    if (targetProblemId && allIssues.length > 0) {
      const targetIssue = allIssues.find(issue => issue.id === targetProblemId);
      if (targetIssue) {
        setIsFromResultsTab(true);
        setSelectedIssue(targetIssue); // 展开问题卡片
        setHighlightedIssueId(targetProblemId);

        // 滚动到对应章节和问题卡片
        setTimeout(() => {
          scrollToSection(targetIssue.section);
          scrollToIssueCard(targetIssue);

          // 确保右侧问题列表中的问题卡片也展开
          setTimeout(() => {
            const issueCardElement = window.document.getElementById(`issue-card-${targetIssue.id}`);
            if (issueCardElement) {
              // 滚动到问题卡片
              issueCardElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
              });

              // 高亮显示问题卡片
              setHighlightedIssueId(targetIssue.id);
              setTimeout(() => setHighlightedIssueId(null), 3000);
            }
          }, 200);
        }, 100);
      }
    }
  }, [targetProblemId, allIssues, scrollToSection, scrollToIssueCard]);

  // 处理问题确认
  const handleIssueReview = useCallback((issueId, accepted) => {
    setReviewedIssues(prev => new Set([...prev, issueId]));
    if (accepted) {
      setAcceptedSuggestions(prev => new Set([...prev, issueId]));
    } else {
      setAcceptedSuggestions(prev => {
        const newSet = new Set(prev);
        newSet.delete(issueId);
        return newSet;
      });
    }
  }, []);



  // 获取问题的完整章节路径
  const getSectionPath = useCallback((issue) => {
    if (issue.sectionPath && issue.sectionPath.length > 0) {
      return issue.sectionPath.join(' > ');
    }

    // 如果没有预定义的路径，从documentSections中查找
    const section = documentSections.find(s => s.id === issue.section);
    return section ? section.title : '未知章节';
  }, [documentSections]);

  // 精确定位到原文段落并高亮
  const highlightTargetText = useCallback((issue) => {
    if (!issue.targetText) {
      console.log('没有目标文本，跳过高亮');
      return;
    }

    console.log('开始高亮目标文本:', issue.targetText);

    // 设置高亮的问题ID
    setHighlightedIssueId(issue.id);

    // 延迟一下确保DOM更新后再查找和高亮
    setTimeout(() => {
      // 查找包含目标文本的元素
      const sectionElement = window.document.getElementById(issue.section);
      console.log('查找章节元素:', issue.section, sectionElement);

      if (sectionElement) {
        // 使用更简单的方法查找包含目标文本的元素
        const findTextInElement = (element, targetText) => {
          // 检查当前元素的文本内容
          if (element.textContent && element.textContent.includes(targetText)) {
            // 如果是文本节点的父元素，直接返回
            if (element.children.length === 0 || element.tagName === 'P' || element.tagName === 'DIV') {
              return element;
            }

            // 递归查找子元素
            for (let child of element.children) {
              const result = findTextInElement(child, targetText);
              if (result) return result;
            }
          }
          return null;
        };

        const targetElement = findTextInElement(sectionElement, issue.targetText);
        console.log('查找到的目标元素:', targetElement);

        if (targetElement) {
          console.log('找到目标元素，开始高亮');
          // 添加高亮CSS类
          targetElement.classList.add('highlight-text');

          // 滚动到目标位置
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });

          // 3秒后移除高亮效果
          setTimeout(() => {
            targetElement.classList.remove('highlight-text');
            setHighlightedIssueId(null);
          }, 3000);
        } else {
          console.log('未找到具体文本，高亮整个章节');
          // 如果找不到具体文本，就高亮整个章节
          sectionElement.classList.add('highlight-text');
          sectionElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });

          setTimeout(() => {
            sectionElement.classList.remove('highlight-text');
            setHighlightedIssueId(null);
          }, 3000);
        }
      } else {
        console.log('未找到章节元素:', issue.section);
      }
    }, 200);
  }, []);

  // 切换大纲显示状态，同时控制主导航
  const toggleOutline = useCallback(() => {
    setOutlineExpanded(prev => {
      const newExpanded = !prev;
      // 通知父组件隐藏/显示主导航
      if (newExpanded) {
        // 展开大纲时隐藏主导航
        const mainNav = window.document.querySelector('[data-main-nav]');
        if (mainNav) {
          mainNav.style.display = 'none';
        }
      } else {
        // 收起大纲时显示主导航
        const mainNav = window.document.querySelector('[data-main-nav]');
        if (mainNav) {
          mainNav.style.display = 'block';
        }
      }
      return newExpanded;
    });
  }, []);

  // 完成评审确认
  const handleCompleteReview = () => {
    setReviewCompleted(true);
    console.log('评审确认完成，等待生成报告');
  };

  // 生成报告
  const handleGenerateReport = async () => {
    setIsGeneratingReport(true);
    setReportProgress(0);

    // 模拟报告生成过程
    const steps = [
      "整理评审数据...",
      "生成统计图表...",
      "编写分析报告...",
      "格式化输出...",
      "完成报告生成..."
    ];

    for (let i = 0; i < steps.length; i++) {
      console.log(`报告生成步骤 ${i + 1}: ${steps[i]}`);
      await new Promise(resolve => setTimeout(resolve, 800));
      setReportProgress(((i + 1) / steps.length) * 100);
    }

    // 生成最终的评审结果
    const reviewResults = {
      documentId: document?.id || 'lca-doc-001',
      reviewTimestamp: new Date().toISOString(),
      totalIssues: allIssues.length,
      reviewedIssues: reviewedIssues.size,
      acceptedSuggestions: acceptedSuggestions.size,
      rejectedSuggestions: reviewedIssues.size - acceptedSuggestions.size,
      reviewedIssueIds: Array.from(reviewedIssues),
      acceptedSuggestionIds: Array.from(acceptedSuggestions),
      overallApproval: reviewedIssues.size > 0 ? (acceptedSuggestions.size / reviewedIssues.size) * 100 : 0
    };

    setIsGeneratingReport(false);
    console.log('报告生成完成，跳转到评审结果页面');
    handleReviewComplete(reviewResults);
  };

  return (
    <div className="h-full flex flex-col">
      {/* 主体内容区域 - 三栏布局 */}
      <div className="flex-1 flex overflow-hidden min-w-0 prd-review-layout">
        {/* 左侧：文档大纲（固定位置，独立滚动） */}
        {outlineExpanded && (
          <div className="prd-review-left-panel w-72 flex-shrink-0 bg-white border-r border-gray-200 flex flex-col overflow-hidden">
            {/* 大纲标题 - 固定不滚动 */}
            <div className="flex-shrink-0 p-4 border-b border-gray-200 bg-white">
              <h3 className="font-medium text-gray-900 flex items-center">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                文档大纲
              </h3>
            </div>

            {/* 大纲内容 - 独立滚动区域 */}
            <div className="flex-1 overflow-y-auto">
              <div className="p-4">
                <div className="space-y-1">
                  {documentSections.map((section) => {
                    // 计算缩进级别
                    const indentLevel = section.level;
                    const indentClass =
                      indentLevel === 0 ? '' :
                      indentLevel === 1 ? 'ml-0' :
                      indentLevel === 2 ? 'ml-4' :
                      'ml-8';

                    return (
                      <div key={section.id}>
                        <button
                          onClick={() => scrollToSection(section.id)}
                          className={`w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 transition-colors ${indentClass}`}
                        >
                          <div className="flex items-center justify-between">
                            <span className={`${
                              indentLevel === 0 ? 'text-lg font-bold text-gray-900' :
                              indentLevel === 1 ? 'text-base font-semibold text-gray-900' :
                              indentLevel === 2 ? 'text-sm font-medium text-gray-700' :
                              'text-sm text-gray-600'
                            }`}>
                              {section.title}
                            </span>
                            {section.issues && section.issues.length > 0 && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 ml-2">
                                {section.issues.length}
                              </span>
                            )}
                          </div>
                        </button>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 中间：文档内容 */}
        <div className="flex-1 min-w-0 bg-white flex flex-col overflow-hidden">
          <div className="flex-1 overflow-y-auto">
            <div className="p-6 min-w-0">
              <div className="prose prose-lg max-w-none min-w-0">
                {documentSections.map((section) => {
                  // 根据层级确定标题样式和间距
                  const getTitleElement = () => {
                    const titleClasses =
                      section.level === 0 ? 'text-3xl font-bold text-gray-900 mb-8 pb-4 border-b-2 border-gray-200' :
                      section.level === 1 ? 'text-2xl font-bold text-gray-900 mb-6 mt-8' :
                      section.level === 2 ? 'text-xl font-semibold text-gray-800 mb-4 mt-6' :
                      'text-lg font-medium text-gray-700 mb-3 mt-4';

                    const marginBottom =
                      section.level === 0 ? 'mb-8' :
                      section.level === 1 ? 'mb-8' :
                      section.level === 2 ? 'mb-6' :
                      'mb-4';

                    return (
                      <div className={`${marginBottom}`}>
                        {section.level === 0 ? (
                          <div className="flex items-baseline mb-8 pb-4 border-b-2 border-gray-200">
                            <button
                              onClick={toggleOutline}
                              className={`mr-4 p-2 rounded-md transition-colors flex-shrink-0 ${
                                outlineExpanded
                                  ? 'bg-orange-100 text-orange-600'
                                  : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
                              }`}
                              title={outlineExpanded ? '隐藏文档大纲' : '显示文档大纲'}
                            >
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                              </svg>
                            </button>
                            <h1 className="text-3xl font-bold text-gray-900">{section.title}</h1>
                          </div>
                        ) : section.level === 1 ? (
                          <h2 className={titleClasses}>{section.title}</h2>
                        ) : section.level === 2 ? (
                          <h3 className={titleClasses}>{section.title}</h3>
                        ) : (
                          <h4 className={titleClasses}>{section.title}</h4>
                        )}
                      </div>
                    );
                  };

                  return (
                    <div key={section.id} id={section.id} className="mb-8">
                      {getTitleElement()}

                      <div className="text-gray-700 leading-relaxed">
                      {/* 渲染表格内容 */}
                      {section.content.includes('|') ? (
                        <div className="table-container">
                          <table className="prd-content-table border border-gray-300">
                            <tbody>
                              {section.content.split('\n').filter(line => line.includes('|')).map((row, index) => (
                                <tr key={index} className={index === 0 ? 'bg-gray-50' : ''}>
                                  {row.split('|').map((cell, cellIndex) => (
                                    <td key={cellIndex} className="border border-gray-300 px-2 py-2 text-xs">
                                      <div className="break-words overflow-wrap-anywhere">
                                        {cell.trim()}
                                      </div>
                                    </td>
                                  ))}
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <div className="whitespace-pre-wrap">{section.content}</div>
                      )}
                    </div>

                    {/* 章节问题标记 */}
                    {section.issues && section.issues.length > 0 && (
                      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <h4 className="text-sm font-medium text-yellow-800 mb-3 flex items-center">
                          <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          本章节发现 {section.issues.length} 个问题
                        </h4>
                        <div className="space-y-2">
                          {section.issues.map((issue) => (
                            <button
                              key={issue.id}
                              onClick={() => scrollToIssueCard(issue)}
                              className={`w-full text-left px-3 py-2 rounded text-sm transition-colors ${
                                selectedIssue?.id === issue.id
                                  ? 'bg-orange-100 text-orange-800 border border-orange-200'
                                  : reviewedIssues.has(issue.id)
                                    ? acceptedSuggestions.has(issue.id)
                                      ? 'bg-green-100 text-green-800'
                                      : 'bg-gray-100 text-gray-600'
                                    : 'hover:bg-white text-gray-700 border border-yellow-300'
                              }`}
                            >
                              <div className="flex items-center space-x-2">
                                <div className={`w-2 h-2 rounded-full ${
                                  issue.type === 'error' ? 'bg-red-500' :
                                  issue.type === 'warning' ? 'bg-yellow-500' :
                                  'bg-blue-500'
                                }`} />
                                <span className="flex-1">{issue.title}</span>
                                {reviewedIssues.has(issue.id) && (
                                  <span className={`text-xs px-2 py-1 rounded-full ${
                                    acceptedSuggestions.has(issue.id)
                                      ? 'bg-green-200 text-green-800'
                                      : 'bg-gray-200 text-gray-600'
                                  }`}>
                                    {acceptedSuggestions.has(issue.id) ? '已采纳' : '已拒绝'}
                                  </span>
                                )}
                              </div>
                            </button>
                          ))}
                        </div>
                      </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        {/* 右侧：问题卡片列表 - 固定位置，独立滚动 */}
        <div className="prd-review-right-panel w-96 flex-shrink-0 bg-gray-50 border-l border-gray-200 flex flex-col overflow-hidden">
          {/* 问题列表标题 - 固定不滚动 */}
          <div className="flex-shrink-0 p-4 border-b border-gray-200 bg-white">
            <h3 className="font-medium text-gray-900 flex items-center">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              问题列表
              <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                {allIssues.length}
              </span>
            </h3>
          </div>

          {/* 问题卡片内容 - 独立滚动区域 */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-4 space-y-4">
              {allIssues.map((issue) => {
                // 获取完整的章节路径
                const sectionPath = getSectionPath(issue);

                return (
                  <div
                    key={issue.id}
                    id={`issue-card-${issue.id}`}
                    className={`bg-white rounded-lg border transition-all duration-300 ${
                      highlightedIssueId === issue.id
                        ? 'border-orange-500 shadow-lg bg-orange-50 scale-[1.02]'
                        : selectedIssue?.id === issue.id
                          ? 'border-orange-500 shadow-md'
                          : reviewedIssues.has(issue.id)
                            ? acceptedSuggestions.has(issue.id)
                              ? 'border-green-200 bg-green-50'
                              : 'border-red-200 bg-red-50'
                            : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
                    }`}
                  >
                    {/* 问题卡片头部 - 定位信息 */}
                    <div className="p-3 border-b border-gray-100">
                      <div className="flex items-center justify-between">
                        <div className="flex flex-col space-y-1">
                          <button
                            onClick={() => {
                              scrollToSection(issue.section);
                              highlightTargetText(issue);
                            }}
                            className="flex items-center space-x-2 text-xs text-blue-600 hover:text-blue-800 transition-colors"
                            title="精确定位到问题原文"
                          >
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <span className="truncate max-w-48 section-path">{sectionPath}</span>
                          </button>
                          {issue.targetText && (
                            <div className="text-xs text-gray-500 ml-5 truncate max-w-48 target-text-preview">
                              "{issue.targetText.substring(0, 35)}..."
                            </div>
                          )}
                        </div>

                        <div className="flex items-center space-x-2">
                          <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                            issue.type === 'error' ? 'bg-red-100 text-red-800' :
                            issue.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-blue-100 text-blue-800'
                          }`}>
                            {issue.type === 'error' ? '错误' : issue.type === 'warning' ? '警告' : '建议'}
                          </span>
                          {/* 状态显示 */}
                          <div className="flex items-center space-x-2">
                            {reviewedIssues.has(issue.id) ? (
                              <>
                                <div className={`w-4 h-4 ${acceptedSuggestions.has(issue.id) ? 'text-green-600' : 'text-red-600'}`}>
                                  {acceptedSuggestions.has(issue.id) ? (
                                    <svg fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                    </svg>
                                  ) : (
                                    <svg fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                    </svg>
                                  )}
                                </div>
                                <span className={`text-xs font-medium ${acceptedSuggestions.has(issue.id) ? 'text-green-600' : 'text-red-600'}`}>
                                  {acceptedSuggestions.has(issue.id) ? '已确认' : '已拒绝'}
                                </span>
                              </>
                            ) : (
                              <span className="text-xs font-medium text-gray-500">待确认</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 问题卡片主体内容 */}
                    <div className="p-4">
                      {/* 问题标题 */}
                      <h4 className="font-medium text-gray-900 mb-2 text-sm">{issue.title}</h4>

                      {/* 问题描述 */}
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">{issue.description}</p>

                      {/* 展开/收起按钮 */}
                      {selectedIssue?.id !== issue.id && (
                        <div className="flex justify-center pt-2 border-t border-gray-100">
                          <button
                            onClick={() => setSelectedIssue(issue)}
                            className="flex items-center space-x-1 text-xs text-gray-500 hover:text-gray-700 transition-colors"
                          >
                            <span>展开详情</span>
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                            </svg>
                          </button>
                        </div>
                      )}
                    </div>

                    {/* 展开的详细信息 */}
                    {selectedIssue?.id === issue.id && (
                      <div className="px-4 pb-4">
                        <div className="pt-3 border-t border-gray-200">
                          {/* 收起按钮 */}
                          <div className="flex justify-center mb-3">
                            <button
                              onClick={() => setSelectedIssue(null)}
                              className="flex items-center space-x-1 text-xs text-gray-500 hover:text-gray-700 transition-colors"
                            >
                              <span>收起详情</span>
                              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                              </svg>
                            </button>
                          </div>

                          <div className="space-y-3">
                            {/* 修改建议 */}
                            {issue.suggestion && (
                              <div>
                                <h5 className="text-xs font-medium text-gray-900 mb-1">修改建议:</h5>
                                <p className="text-xs text-blue-800 bg-blue-50 p-2 rounded">{issue.suggestion}</p>
                              </div>
                            )}

                            {/* AI推荐 */}
                            {issue.aiRecommendation && (
                              <div>
                                <h5 className="text-xs font-medium text-gray-900 mb-1">AI推荐:</h5>
                                <p className="text-xs text-purple-800 bg-purple-50 p-2 rounded">{issue.aiRecommendation}</p>
                              </div>
                            )}

                            {/* 置信度 */}
                            {issue.confidence && (
                              <div>
                                <h5 className="text-xs font-medium text-gray-900 mb-1">置信度:</h5>
                                <div className="flex items-center space-x-2">
                                  <div className="flex-1 bg-gray-200 rounded-full h-1.5">
                                    <div
                                      className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                                      style={{ width: `${issue.confidence}%` }}
                                    ></div>
                                  </div>
                                  <span className="text-xs text-gray-600">{issue.confidence}%</span>
                                </div>
                              </div>
                            )}

                            {/* 操作按钮 */}
                            <div className="flex space-x-2 pt-2">
                              {reviewedIssues.has(issue.id) ? (
                                <>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleIssueReview(issue.id, true);
                                    }}
                                    className={`flex-1 px-3 py-1.5 rounded text-xs font-medium transition-colors ${
                                      acceptedSuggestions.has(issue.id)
                                        ? 'bg-green-100 text-green-800 border border-green-200'
                                        : 'bg-green-500 hover:bg-green-600 text-white'
                                    }`}
                                  >
                                    {acceptedSuggestions.has(issue.id) ? '已确认' : '确认'}
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleIssueReview(issue.id, false);
                                    }}
                                    className={`flex-1 px-3 py-1.5 rounded text-xs font-medium transition-colors ${
                                      !acceptedSuggestions.has(issue.id)
                                        ? 'bg-gray-100 text-gray-800 border border-gray-200'
                                        : 'bg-gray-500 hover:bg-gray-600 text-white'
                                    }`}
                                  >
                                    {!acceptedSuggestions.has(issue.id) ? '已拒绝' : '拒绝'}
                                  </button>
                                </>
                              ) : (
                                <>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleIssueReview(issue.id, true);
                                    }}
                                    className="flex-1 bg-green-500 hover:bg-green-600 text-white px-3 py-1.5 rounded text-xs font-medium transition-colors"
                                  >
                                    确认问题
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleIssueReview(issue.id, false);
                                    }}
                                    className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-3 py-1.5 rounded text-xs font-medium transition-colors"
                                  >
                                    拒绝问题
                                  </button>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* 底部操作栏 */}
      <div className="flex-shrink-0 border-t border-gray-200 bg-white p-4">
        <div className="flex items-center justify-between">
          {/* 左侧：进度信息 */}
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">评审进度:</span>
              <span className="text-sm font-medium text-gray-900">
                {reviewedIssues.size}/{allIssues.length}
              </span>
              <div className="w-24 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${allIssues.length > 0 ? (reviewedIssues.size / allIssues.length) * 100 : 0}%` }}
                ></div>
              </div>
              <span className="text-sm text-gray-500">
                {Math.round(allIssues.length > 0 ? (reviewedIssues.size / allIssues.length) * 100 : 0)}%
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">采纳率:</span>
              <span className="text-sm font-medium text-green-600">
                {acceptedSuggestions.size}/{reviewedIssues.size}
              </span>
              <span className="text-sm text-gray-500">
                ({Math.round(reviewedIssues.size > 0 ? (acceptedSuggestions.size / reviewedIssues.size) * 100 : 0)}%)
              </span>
            </div>
          </div>

          {/* 右侧：操作按钮 */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => console.log('保存草稿')}
              className="border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors text-sm"
            >
              保存草稿
            </button>

            {isFromResultsTab ? (
              <button
                onClick={() => {
                  if (onReturnToResults) {
                    onReturnToResults(targetProblemId);
                  }
                }}
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition-colors text-sm"
              >
                返回章节评审
              </button>
            ) : !reviewCompleted ? (
              <button
                onClick={handleCompleteReview}
                disabled={reviewedIssues.size === 0}
                className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-4 py-2 rounded-md transition-colors text-sm"
              >
                完成评审确认
              </button>
            ) : (
              <button
                onClick={handleGenerateReport}
                disabled={isGeneratingReport}
                className="bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-4 py-2 rounded-md transition-colors text-sm"
              >
                {isGeneratingReport ? '生成中...' : '生成报告'}
              </button>
            )}
          </div>
        </div>

        {/* 报告生成进度 */}
        {isGeneratingReport && (
          <div className="mt-4 space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">正在生成评审报告</span>
              <span className="text-sm text-gray-500">{Math.floor(reportProgress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-orange-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${reportProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600">正在整理评审数据并生成详细报告...</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PRDReviewTab;