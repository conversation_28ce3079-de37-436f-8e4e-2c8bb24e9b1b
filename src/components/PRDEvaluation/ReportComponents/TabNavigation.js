import React from 'react';

/**
 * Tab导航组件
 * 提供四个主要Tab页面的导航
 */
const TabNavigation = ({ activeTab, onTabChange }) => {
  const tabs = [
    { id: 'highlights', label: '评审总结', icon: '📋' },
    { id: 'chapters', label: '章节评审详细', icon: '📖' },
    { id: 'suggestions', label: '建议转行动', icon: '✅' },
    { id: 'context', label: '评审上下文', icon: '📊' }
  ];

  return (
    <div className="border-b-2 border-gray-200 mb-6">
      <div className="flex overflow-x-auto scrollbar-hide">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`
              flex items-center gap-2 px-6 py-3 font-semibold text-sm whitespace-nowrap
              border-b-2 transition-all duration-200
              ${activeTab === tab.id 
                ? 'text-blue-600 border-blue-600' 
                : 'text-gray-600 border-transparent hover:text-blue-600 hover:border-blue-300'
              }
            `}
          >
            <span className="text-base">{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>
    </div>
  );
};

export default TabNavigation;
