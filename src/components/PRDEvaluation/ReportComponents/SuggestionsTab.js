import React, { useState } from 'react';
import SeverityBadge from './SeverityBadge';

/**
 * 建议转行动Tab组件
 * 显示改进建议列表和采纳状态管理
 */
const SuggestionsTab = ({
  problems,
  adoptionStatus,
  onAdoptionToggle,
  onCopyToEditor,
  onGenerateCards,
  onProblemNavigation
}) => {
  const [selectAll, setSelectAll] = useState(false);
  const [selectedSuggestions, setSelectedSuggestions] = useState(new Set());

  if (!problems) return null;

  // 处理问题ID点击事件
  const handleProblemClick = (e, problemId) => {
    e.preventDefault();
    if (onProblemNavigation) {
      onProblemNavigation(problemId);
    }
  };

  // 按优先级排序问题
  const sortedProblems = Object.entries(problems).sort(([, a], [, b]) => {
    const severityOrder = { '严重': 0, '重要': 1, '警告': 2, '建议': 3 };
    return severityOrder[a.severity] - severityOrder[b.severity];
  });

  // 处理全选/取消全选
  const handleSelectAll = (checked) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedSuggestions(new Set(Object.keys(problems)));
    } else {
      setSelectedSuggestions(new Set());
    }
  };

  // 处理单个建议选择
  const handleSuggestionSelect = (problemId, checked) => {
    const newSelected = new Set(selectedSuggestions);
    if (checked) {
      newSelected.add(problemId);
    } else {
      newSelected.delete(problemId);
    }
    setSelectedSuggestions(newSelected);
    setSelectAll(newSelected.size === Object.keys(problems).length);
  };

  // 处理采纳状态切换
  const handleAdoptionToggle = (problemId) => {
    const currentStatus = adoptionStatus[problemId];
    onAdoptionToggle(problemId, !currentStatus);
  };

  // 复制到AI编辑器
  const handleCopyToEditor = () => {
    const selectedProblemsData = Array.from(selectedSuggestions).map(id => ({
      id,
      problem: problems[id],
      adopted: adoptionStatus[id]
    }));
    
    if (onCopyToEditor) {
      onCopyToEditor(selectedProblemsData);
    }
    
    // 显示反馈
    alert(`已复制 ${selectedProblemsData.length} 个建议到AI编辑器`);
  };

  // 生成建议卡片
  const handleGenerateCards = () => {
    const selectedProblemsData = Array.from(selectedSuggestions).map(id => ({
      id,
      problem: problems[id],
      adopted: adoptionStatus[id]
    }));
    
    if (onGenerateCards) {
      onGenerateCards(selectedProblemsData);
    }
    
    // 显示反馈
    alert(`已生成 ${selectedProblemsData.length} 个建议卡片`);
  };

  return (
    <div>
      <h2 className="text-2xl font-bold text-gray-900 border-l-4 border-blue-600 pl-4 mb-6">
        建议转行动
      </h2>
      
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full text-sm text-left text-gray-600">
            <thead className="text-xs text-gray-700 uppercase bg-gray-100">
              <tr>
                <th scope="col" className="px-4 py-3 w-12">
                  <input 
                    type="checkbox" 
                    checked={selectAll}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  />
                </th>
                <th scope="col" className="px-4 py-3">建议 (点击ID可定位问题)</th>
                <th scope="col" className="px-4 py-3">改进方案</th>
                <th scope="col" className="px-4 py-3">采纳状态</th>
              </tr>
            </thead>
            <tbody>
              {sortedProblems.map(([problemId, problem]) => {
                const isAdopted = adoptionStatus[problemId];
                const isSelected = selectedSuggestions.has(problemId);
                
                return (
                  <tr key={problemId} className="border-b hover:bg-gray-50">
                    <td className="px-4 py-3 align-top">
                      <input 
                        type="checkbox"
                        checked={isSelected}
                        onChange={(e) => handleSuggestionSelect(problemId, e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      />
                    </td>
                    <td className="px-4 py-3">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <SeverityBadge severity={problem.severity} size="small" />
                          <span className="font-semibold">{problem.title}</span>
                          <a
                            href={`#problem-${problemId}`}
                            className="text-blue-600 hover:underline font-mono text-xs cursor-pointer"
                            onClick={(e) => handleProblemClick(e, problemId)}
                          >
                            [{problemId}]
                          </a>
                        </div>
                        <p className="text-xs text-gray-500 leading-relaxed">
                          {problem.details && problem.details.length > 100 
                            ? problem.details.substring(0, 100) + '...'
                            : problem.details
                          }
                        </p>
                      </div>
                    </td>
                    <td className="px-4 py-3 align-top">
                      <div className="text-xs space-y-1">
                        {problem.solution.specific && problem.solution.specific.length > 0 ? (
                          <ul className="list-disc list-inside space-y-1">
                            {problem.solution.specific.map((item, index) => (
                              <li key={index} className="leading-relaxed">
                                {item}
                              </li>
                            ))}
                          </ul>
                        ) : (
                          <p className="text-gray-500">暂无具体修改内容。</p>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-3 align-top">
                      <button
                        onClick={() => handleAdoptionToggle(problemId)}
                        className={`
                          inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition-colors
                          ${isAdopted 
                            ? 'bg-green-100 text-green-800 hover:bg-green-200' 
                            : 'bg-red-100 text-red-800 hover:bg-red-200'
                          }
                        `}
                      >
                        {isAdopted ? '✅ 已确认' : '❌ 暂不接受'}
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {/* 空状态 */}
        {sortedProblems.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <svg 
              className="w-12 h-12 mx-auto mb-4 text-gray-300" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" 
              />
            </svg>
            <p>暂无改进建议</p>
          </div>
        )}
      </div>

      {/* 操作按钮 */}
      <div className="mt-6 flex flex-wrap gap-4 justify-end">
        <button 
          onClick={handleCopyToEditor}
          disabled={selectedSuggestions.size === 0}
          className={`
            flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors
            ${selectedSuggestions.size > 0
              ? 'text-gray-600 hover:text-gray-900 bg-gray-100 hover:bg-gray-200'
              : 'text-gray-400 bg-gray-50 cursor-not-allowed'
            }
          `}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
            <path strokeLinecap="round" strokeLinejoin="round" d="M8 5H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7l3-3m-3 3l-3-3m3 3V5" />
          </svg>
          一键拷贝到AI编辑器
        </button>
        
        <button 
          onClick={handleGenerateCards}
          disabled={selectedSuggestions.size === 0}
          className={`
            flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors
            ${selectedSuggestions.size > 0
              ? 'text-blue-600 hover:text-blue-800 bg-blue-100 hover:bg-blue-200'
              : 'text-gray-400 bg-gray-50 cursor-not-allowed'
            }
          `}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
            <path strokeLinecap="round" strokeLinejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          一键生成建议卡片
        </button>
      </div>

      {/* 操作反馈区域 */}
      <div className="mt-4 h-8">
        {selectedSuggestions.size > 0 && (
          <p className="text-sm text-blue-600">
            已选择 {selectedSuggestions.size} 个建议
          </p>
        )}
      </div>
    </div>
  );
};

export default SuggestionsTab;
