import React, { useState, useEffect } from 'react';
import ProblemDetailCard from './ProblemDetailCard';

/**
 * 章节详情Tab组件 - 1比1复刻HTML版本
 * 完全按照assessment-report-v2.6.html中的章节评审Tab实现
 */
const ChapterDetailsTab = ({
  chapters,
  problems,
  adoptionStatus,
  onAdoptionToggle,
  targetProblemId,
  onTargetReached,
  onNavigateToPRD
}) => {
  const [expandedChapters, setExpandedChapters] = useState(new Set());

  // 自动展开包含目标问题的章节
  useEffect(() => {
    if (targetProblemId && chapters && problems) {
      // 找到包含目标问题的章节
      const targetChapter = chapters.find(chapter =>
        chapter.problems && chapter.problems.includes(targetProblemId)
      );

      if (targetChapter) {
        const chapterKey = targetChapter.problems.join('-');
        const newExpanded = new Set(expandedChapters);
        newExpanded.add(chapterKey);
        setExpandedChapters(newExpanded);

        // 通知父组件目标已到达
        if (onTargetReached) {
          onTargetReached();
        }
      }
    }
  }, [targetProblemId, chapters, problems, expandedChapters, onTargetReached]);

  // 早期返回检查
  if (!chapters || !problems) {
    return (
      <div>
        <h2 className="text-2xl font-bold text-gray-900 border-l-4 border-blue-600 pl-4 mb-6">
          章节评审详细
        </h2>
        <div className="bg-white rounded-lg border border-gray-200 p-8 text-center text-gray-500">
          暂无章节数据
        </div>
      </div>
    );
  }

  // 切换章节详情展开状态
  const toggleChapterDetails = (chapterKey) => {
    const newExpanded = new Set(expandedChapters);
    if (newExpanded.has(chapterKey)) {
      newExpanded.delete(chapterKey);
    } else {
      newExpanded.add(chapterKey);
    }
    setExpandedChapters(newExpanded);
  };

  // 获取章节的主要问题信息（复刻HTML逻辑）
  const getChapterMainProblem = (chapter) => {
    if (!chapter.problems || chapter.problems.length === 0) {
      return null;
    }
    return problems[chapter.problems[0]]; // HTML中取第一个问题作为主要问题
  };

  // 判断章节是否通过（复刻HTML逻辑）
  const isChapterPassed = (chapter) => {
    if (!chapter.problems || chapter.problems.length === 0) {
      return true;
    }
    // HTML逻辑：如果有严重或重要问题，则不通过
    return !chapter.problems.some(problemId => {
      const problem = problems[problemId];
      return problem && ['严重', '重要'].includes(problem.severity);
    });
  };

  // 获取章节问题维度（复刻HTML逻辑）
  const getChapterDimensions = (chapter) => {
    if (!chapter.problems || chapter.problems.length === 0) {
      return '-';
    }
    const dimensions = [...new Set(
      chapter.problems.map(problemId => problems[problemId]?.dimension).filter(Boolean)
    )];
    return dimensions.join(', ');
  };

  // 获取严重程度标签HTML（复刻HTML逻辑）
  const getSeverityTagHtml = (severity) => {
    const severityConfig = {
      '严重': { color: 'bg-red-100 text-red-800', icon: '🔴' },
      '重要': { color: 'bg-yellow-100 text-yellow-800', icon: '🟡' },
      '警告': { color: 'bg-orange-100 text-orange-800', icon: '🟠' },
      '建议': { color: 'bg-blue-100 text-blue-800', icon: '🔵' }
    };

    const config = severityConfig[severity] || { color: 'bg-gray-100 text-gray-800', icon: '⚪' };
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        {config.icon} {severity}
      </span>
    );
  };

  return (
    <div>
      <h2 className="text-2xl font-bold text-gray-900 border-l-4 border-blue-600 pl-4 mb-6">
        章节评审详细
      </h2>

      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full text-sm text-left text-gray-600 table-interactive">
            <thead className="text-xs text-gray-700 uppercase bg-gray-100">
              <tr>
                <th scope="col" className="px-6 py-3">章节名称</th>
                <th scope="col" className="px-6 py-3">问题描述</th>
                <th scope="col" className="px-6 py-3">问题等级</th>
                <th scope="col" className="px-6 py-3">问题维度</th>
                <th scope="col" className="px-6 py-3">评价结果</th>
                <th scope="col" className="px-6 py-3 w-32">操作</th>
              </tr>
            </thead>
            <tbody>
              {chapters.map((chapter, index) => {
                const hasProblems = chapter.problems && chapter.problems.length > 0;
                const mainProblem = getChapterMainProblem(chapter);
                const isPassed = isChapterPassed(chapter);
                const chapterKey = chapter.problems && chapter.problems.length > 0
                  ? chapter.problems.join('-')
                  : `chapter-${index}-${chapter.name.replace(/\s+/g, '-')}`;
                const isExpanded = expandedChapters.has(chapterKey);

                // 复刻HTML逻辑：问题描述处理
                let problemInfo;
                if (hasProblems && mainProblem) {
                  problemInfo = mainProblem.title.length > 15
                    ? mainProblem.title.substring(0, 15) + '...'
                    : mainProblem.title;
                } else {
                  problemInfo = '无问题';
                }

                // 复刻HTML逻辑：行样式
                const rowClass = hasProblems ? 'chapter-row border-b' : 'chapter-row border-b bg-green-50';

                return (
                  <React.Fragment key={chapterKey}>
                    {/* 主行 - 完全复刻HTML结构 */}
                    <tr className={rowClass} id={`chapter-row-${chapterKey}`}>
                      <td className="px-6 py-4 font-semibold">{chapter.name}</td>
                      <td className="px-6 py-4">{problemInfo}</td>
                      <td className="px-6 py-4">
                        {hasProblems && mainProblem ? getSeverityTagHtml(mainProblem.severity) : '-'}
                      </td>
                      <td className="px-6 py-4">{getChapterDimensions(chapter)}</td>
                      <td className="px-6 py-4">
                        {isPassed ? (
                          <span className="status-pass">✅通过</span>
                        ) : (
                          <span className="status-fail">❌不通过</span>
                        )}
                      </td>
                      <td className="px-6 py-4">
                        {hasProblems && (
                          <button
                            className="toggle-button view-details-btn"
                            onClick={() => toggleChapterDetails(chapterKey)}
                          >
                            <span>{isExpanded ? '收起详情' : '查看详情'}</span>
                            <svg
                              className={`w-4 h-4 arrow transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                              strokeWidth="2"
                              stroke="currentColor"
                            >
                              <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
                            </svg>
                          </button>
                        )}
                      </td>
                    </tr>

                    {/* 详情行 - 复刻HTML结构 */}
                    {isExpanded && hasProblems && (
                      <tr className={`detail-row ${isExpanded ? '' : 'hidden'}`}>
                        <td colSpan="6" className="p-4">
                          <div className="space-y-4">
                            {chapter.problems.map(problemId => {
                              const problem = problems[problemId];
                              if (!problem) return null;

                              return (
                                <ProblemDetailCard
                                  key={problemId}
                                  problemId={problemId}
                                  problem={problem}
                                  adoptionStatus={adoptionStatus}
                                  onAdoptionToggle={onAdoptionToggle}
                                  onNavigateToPRD={onNavigateToPRD}
                                />
                              );
                            })}
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                );
              })}
            </tbody>
          </table>
        </div>

        {/* 空状态 - 复刻HTML逻辑 */}
        {chapters.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <p>暂无章节数据</p>
          </div>
        )}
      </div>

      {/* 添加CSS样式以复刻HTML效果 */}
      <style jsx>{`
        .chapter-row:hover {
          background-color: #f9fafb;
        }
        .toggle-button {
          display: flex;
          align-items: center;
          color: #3b82f6;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: color 0.2s;
        }
        .toggle-button:hover {
          color: #1d4ed8;
        }
        .arrow {
          margin-left: 0.25rem;
        }
        .status-pass {
          color: #059669;
          font-weight: 500;
        }
        .status-fail {
          color: #dc2626;
          font-weight: 500;
        }
        .detail-row {
          background-color: #f9fafb;
        }
        .table-interactive {
          border-collapse: collapse;
        }
        .highlight {
          background-color: #fef3c7 !important;
          border: 2px solid #f59e0b !important;
          border-radius: 8px;
          transition: all 0.3s ease;
          animation: pulse 1s ease-in-out 2;
        }
        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.02); }
        }
      `}</style>
    </div>
  );
};

export default ChapterDetailsTab;
