import React from 'react';

/**
 * 评审总结Tab组件
 * 显示报告摘要、核心发现、文档亮点和维度评分
 */
const HighlightsTab = ({
  documentInfo,
  statistics,
  highlights,
  onProblemNavigation
}) => {
  if (!documentInfo || !statistics) return null;

  // 处理问题ID点击事件
  const handleProblemClick = (e, problemId) => {
    e.preventDefault();
    if (onProblemNavigation) {
      onProblemNavigation(problemId);
    }
  };

  // 生成改进任务列表
  const generateTaskList = () => {
    const tasks = highlights?.improvements || {};
    
    return (
      <div className="space-y-4 text-sm">
        {/* 紧急任务 */}
        {tasks.urgent && tasks.urgent.length > 0 && (
          <div className="flex items-start">
            <div className="flex-shrink-0 w-28">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-red-100 text-red-800">
                <span className="mr-1.5">🚨</span> 紧急任务
              </span>
              <span className="block text-xs text-gray-500 mt-1">1周内完成</span>
            </div>
            <div className="ml-4 flex-grow">
              {tasks.urgent.map(task => (
                <p key={task.id} className="font-medium text-gray-800">
                  【<a
                    href={`#problem-${task.id}`}
                    className="text-blue-600 hover:underline cursor-pointer"
                    onClick={(e) => handleProblemClick(e, task.id)}
                  >
                    {task.id}
                  </a>】
                  {task.title}
                </p>
              ))}
            </div>
          </div>
        )}

        {/* 高优先级任务 */}
        {tasks.high && tasks.high.length > 0 && (
          <div className="flex items-start">
            <div className="flex-shrink-0 w-28">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                <span className="mr-1.5">🔥</span> 高优先级
              </span>
              <span className="block text-xs text-gray-500 mt-1">2周内完成</span>
            </div>
            <div className="ml-4 flex-grow">
              {tasks.high.map(task => (
                <p key={task.id} className="font-medium text-gray-800 mt-1">
                  【<a
                    href={`#problem-${task.id}`}
                    className="text-blue-600 hover:underline cursor-pointer"
                    onClick={(e) => handleProblemClick(e, task.id)}
                  >
                    {task.id}
                  </a>】
                  {task.title}
                </p>
              ))}
            </div>
          </div>
        )}

        {/* 中优先级任务 */}
        {tasks.medium && tasks.medium.length > 0 && (
          <div className="flex items-start">
            <div className="flex-shrink-0 w-28">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-orange-100 text-orange-800">
                <span className="mr-1.5">🔶</span> 中优先级
              </span>
              <span className="block text-xs text-gray-500 mt-1">3-4周内完成</span>
            </div>
            <div className="ml-4 flex-grow">
              {tasks.medium.map(task => (
                <p key={task.id} className="font-medium text-gray-800 mt-1">
                  【<a
                    href={`#problem-${task.id}`}
                    className="text-blue-600 hover:underline cursor-pointer"
                    onClick={(e) => handleProblemClick(e, task.id)}
                  >
                    {task.id}
                  </a>】
                  {task.title}
                </p>
              ))}
            </div>
          </div>
        )}

        {/* 低优先级任务 */}
        {tasks.low && tasks.low.length > 0 && (
          <div className="flex items-start">
            <div className="flex-shrink-0 w-28">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                <span className="mr-1.5">🔹</span> 低优先级
              </span>
              <span className="block text-xs text-gray-500 mt-1">后续优化</span>
            </div>
            <div className="ml-4 flex-grow">
              {tasks.low.map(task => (
                <p key={task.id} className="font-medium text-gray-800 mt-1">
                  【<a
                    href={`#problem-${task.id}`}
                    className="text-blue-600 hover:underline cursor-pointer"
                    onClick={(e) => handleProblemClick(e, task.id)}
                  >
                    {task.id}
                  </a>】
                  {task.title}
                </p>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-8">
      {/* 报告摘要与核心发现 */}
      <section>
        <h2 className="text-2xl font-bold text-gray-900 border-l-4 border-blue-600 pl-4 mb-6">
          报告摘要与核心发现
        </h2>
        
        <p className="text-gray-600 mb-4 leading-relaxed">
          本次{documentInfo.title}评审综合得分 
          <span className={`font-bold mx-1 ${documentInfo.reviewResultClass}`}>
            {documentInfo.overallScore}/100分
          </span>
          ，评审结果为 
          <span className={`font-bold mx-1 ${documentInfo.reviewResultClass}`}>
            {documentInfo.reviewResult}
          </span>
          。该文档在部分方面表现出色，但仍存在需要改进的地方。
        </p>

        <div className="mt-6">
          <h4 className="font-semibold mb-3">核心改进任务列表 (按严重程度排序)</h4>
          {generateTaskList()}
        </div>

        <div className="mt-6 pt-4 border-t border-gray-200">
          <p className="text-sm text-gray-600">
            <strong>整改成功标准:</strong> 所有🔴严重和🟡重要问题全部关闭，综合评分达到 
            <span className="font-bold text-green-600">80分</span> 以上。
          </p>
        </div>
      </section>



      {/* 文档亮点与优势 - 1比1复刻HTML版本 */}
      <section className="mt-8">
        <h2 className="text-2xl font-bold text-gray-900 border-l-4 border-blue-600 pl-4 mb-6">
          文档亮点与优势
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {highlights?.strengths && highlights.strengths.map((strength, index) => (
            <div key={index} className="bg-white rounded-lg border-t-4 border-green-500 p-6 shadow-sm">
              <div className="flex items-center mb-3">
                <span className="bg-green-100 text-green-800 text-sm font-bold px-3 py-1 rounded-full mr-3">
                  {strength.score}
                </span>
                <h3 className="font-semibold text-lg">{strength.title}</h3>
              </div>
              <p className="text-gray-600 text-sm mb-2">
                {strength.description}
              </p>
              <p className="text-sm">
                <strong className="text-green-700">价值:</strong> {strength.value}
              </p>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
};

export default HighlightsTab;
