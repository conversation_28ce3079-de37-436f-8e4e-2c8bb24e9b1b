import React from 'react';

/**
 * 问题详情卡片组件
 * 显示单个问题的详细信息和解决方案
 */
const ProblemDetailCard = ({
  problemId,
  problem,
  adoptionStatus,
  onAdoptionToggle,
  onNavigateToPRD
}) => {
  if (!problem) return null;

  // 添加防御性编程，确保adoptionStatus存在
  const isAdopted = adoptionStatus && adoptionStatus[problemId];
  
  // 获取严重程度对应的边框颜色
  const getBorderColor = (severity) => {
    const colorMap = {
      '严重': 'border-l-red-500',
      '重要': 'border-l-yellow-500', 
      '警告': 'border-l-orange-500',
      '建议': 'border-l-blue-500'
    };
    return colorMap[severity] || 'border-l-gray-500';
  };

  // 获取按钮样式
  const getButtonStyle = (severity) => {
    const styleMap = {
      '严重': 'bg-red-500 hover:bg-red-600',
      '重要': 'bg-yellow-500 hover:bg-yellow-600',
      '警告': 'bg-orange-500 hover:bg-orange-600', 
      '建议': 'bg-blue-500 hover:bg-blue-600'
    };
    return styleMap[severity] || 'bg-gray-500 hover:bg-gray-600';
  };

  const handleNavigateToPRD = () => {
    console.log(`导航到PRD章节: ${problem.location}`);
    if (onNavigateToPRD) {
      onNavigateToPRD(problemId, problem);
    }
  };

  const handleToggleAdoption = () => {
    if (onAdoptionToggle) {
      onAdoptionToggle(problemId, !isAdopted);
    }
  };

  return (
    <div 
      id={`problem-${problemId}`}
      className={`
        bg-white rounded-lg border-l-4 shadow-sm p-6 transition-all duration-300
        ${getBorderColor(problem.severity)}
        hover:shadow-md
      `}
    >
      {/* 问题头部 */}
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-bold text-gray-900 mb-2">
            {problemId}: {problem.title}
          </h3>
          <p className="text-sm text-gray-500">
            发现位置: {problem.location}
          </p>
        </div>
        
        <button
          onClick={handleNavigateToPRD}
          className={`
            flex items-center gap-2 px-3 py-1 text-sm font-semibold rounded-full 
            text-white transition duration-200 ${getButtonStyle(problem.severity)}
          `}
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-4 w-4" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor" 
            strokeWidth="2"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" 
            />
          </svg>
          <span>转到PRD对应章节</span>
        </button>
      </div>

      {/* 问题分析部分 */}
      <div className="mb-6">
        <h4 className="text-base font-bold text-gray-900 mb-4">详细问题分析</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h5 className="text-sm font-semibold text-gray-800 mb-2">问题详情</h5>
            <p className="text-sm text-gray-600 leading-relaxed">
              {problem.details}
            </p>
          </div>
          
          <div>
            <h5 className="text-sm font-semibold text-gray-800 mb-2">引用原文摘要</h5>
            <p className="text-sm text-gray-700 italic border-l-2 border-gray-300 pl-3">
              {problem.originalExcerpt && problem.originalExcerpt.length > 100 
                ? problem.originalExcerpt.substring(0, 100) + '...'
                : problem.originalExcerpt || '原文摘要缺失。'
              }
            </p>
          </div>
          
          <div>
            <h5 className="text-sm font-semibold text-gray-800 mb-2">期望状态</h5>
            <p className="text-sm text-gray-600 leading-relaxed">
              {problem.expectedState || '暂无期望状态描述。'}
            </p>
          </div>
          
          <div>
            <h5 className="text-sm font-semibold text-gray-800 mb-2">影响分析</h5>
            <p className="text-sm text-gray-600 leading-relaxed">
              {problem.impact}
            </p>
          </div>
        </div>
      </div>

      {/* 改进方案部分 */}
      <div className="pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-base font-bold text-gray-900">改进方案</h4>
          <span className={`
            inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
            ${isAdopted 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'
            }
          `}>
            {isAdopted ? '✅ 已确认' : '❌ 暂不接受'}
          </span>
        </div>

        <div className="space-y-4">
          <div>
            <h5 className="text-sm font-semibold text-gray-800 mb-2">修改思路</h5>
            <p className="text-sm text-gray-600 leading-relaxed">
              {problem.solution.idea || '暂无修改思路。'}
            </p>
          </div>

          <div>
            <h5 className="text-sm font-semibold text-gray-800 mb-2">具体修改</h5>
            <ul className="text-sm text-gray-600 space-y-1 pl-4">
              {problem.solution.specific && problem.solution.specific.length > 0 ? (
                problem.solution.specific.map((item, index) => (
                  <li key={index} className="list-disc">
                    {item}
                  </li>
                ))
              ) : (
                <li className="list-disc">暂无具体修改内容。</li>
              )}
            </ul>
          </div>

          <div>
            <h5 className="text-sm font-semibold text-gray-800 mb-2">验证方法</h5>
            <p className="text-sm text-gray-600 leading-relaxed">
              {problem.solution.verification || '暂无验证方法。'}
            </p>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="mt-6 pt-4 border-t border-gray-200 flex justify-end">
          <button
            onClick={handleToggleAdoption}
            className={`
              px-4 py-2 rounded-lg text-white font-semibold transition-colors
              ${isAdopted 
                ? 'bg-red-500 hover:bg-red-600' 
                : 'bg-green-500 hover:bg-green-600'
              }
            `}
          >
            {isAdopted ? '暂不接受' : '确认建议'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProblemDetailCard;
