import React from 'react';

/**
 * 维度评分卡片组件
 * 显示单个维度的评分和相关问题
 */
const DimensionScoreCard = ({
  dimensionName,
  scoreData,
  problems = [],
  showDetails = false,
  onProblemNavigation
}) => {
  if (!scoreData) return null;

  const { score, color } = scoreData;

  // 处理问题ID点击事件
  const handleProblemClick = (e, problemId) => {
    e.preventDefault();
    if (onProblemNavigation) {
      onProblemNavigation(problemId);
    }
  };
  
  // 获取关键问题（严重、重要、警告）
  const keyProblems = problems.filter(p => 
    ['严重', '重要', '警告'].includes(p.severity)
  );

  // 获取颜色类
  const getColorClasses = (color) => {
    const colorMap = {
      'red': {
        stroke: '#dc2626',
        text: 'text-red-600'
      },
      'amber': {
        stroke: '#f59e0b', 
        text: 'text-amber-600'
      },
      'green': {
        stroke: '#10b981',
        text: 'text-green-600'
      }
    };
    return colorMap[color] || colorMap['amber'];
  };

  const colorClasses = getColorClasses(color);

  // 渲染问题见解内容
  const renderProblemInsights = () => {
    if (keyProblems.length === 0) {
      return (
        <div className="text-left space-y-1 text-xs px-2">
          <p className="text-gray-500">无关键问题。</p>
          <p className="text-gray-500 mt-1">此维度表现良好，得分{score}。</p>
        </div>
      );
    }

    if (keyProblems.length === 1) {
      const singleProblem = keyProblems[0];
      return (
        <div className="text-left space-y-1 text-xs px-2">
          <p className="font-semibold text-gray-800">问题详情:</p>
          <p className="text-gray-600">
            {singleProblem.details && singleProblem.details.length > 50 
              ? singleProblem.details.substring(0, 50) + '...'
              : singleProblem.details || singleProblem.title
            }
          </p>
          <div className="h-4"></div>
          <p className="mt-2 text-gray-500">
            问题编号:
            <a
              href={`#problem-${singleProblem.id}`}
              className="text-blue-600 hover:underline ml-1 cursor-pointer"
              onClick={(e) => handleProblemClick(e, singleProblem.id)}
            >
              {singleProblem.id}
            </a>
          </p>
        </div>
      );
    }

    // 多个问题的情况
    return (
      <ul className="list-disc list-inside text-left space-y-1 text-xs px-2">
        {keyProblems.slice(0, 3).map(p => (
          <li key={p.id}>
            {p.title && p.title.length > 15 ? p.title.substring(0, 15) + '...' : p.title}
            <a
              href={`#problem-${p.id}`}
              className="text-blue-600 hover:underline ml-1 cursor-pointer"
              onClick={(e) => handleProblemClick(e, p.id)}
            >
              [{p.id}]
            </a>
          </li>
        ))}
        {keyProblems.length > 3 && (
          <li>
            <span className="text-gray-500">...更多</span>
          </li>
        )}
      </ul>
    );
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 flex flex-col items-center text-center">
      <h3 className="font-semibold text-lg mb-2 text-gray-700">
        {dimensionName}
      </h3>
      
      {/* 圆形进度条 */}
      <div className="relative w-24 h-24 mb-4">
        <svg className="w-full h-full transform -rotate-90" viewBox="0 0 36 36">
          {/* 背景圆 */}
          <path 
            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" 
            fill="none" 
            stroke="#e6e6e6" 
            strokeWidth="3"
          />
          {/* 进度圆 */}
          <path 
            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831" 
            fill="none" 
            stroke={colorClasses.stroke}
            strokeWidth="3" 
            strokeDasharray={`${score}, 100`}
            strokeLinecap="round"
          />
        </svg>
        
        {/* 中心文字 */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <span className={`text-2xl font-bold ${colorClasses.text}`}>
            {score}
          </span>
          <span className="text-xs text-gray-500">/ 100</span>
        </div>
      </div>

      <p className="mb-2 text-sm text-gray-600">
        问题数: <span className="font-bold">{problems.length}</span>
      </p>

      {/* 问题见解部分 */}
      {showDetails && (
        <div className="w-full mt-4 pt-4 border-t border-gray-200">
          <h5 className="font-semibold mb-2 text-sm">关键问题见解:</h5>
          {renderProblemInsights()}
        </div>
      )}
    </div>
  );
};

export default DimensionScoreCard;
