import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import ProblemDetailCard from '../ProblemDetailCard';

// Mock data
const mockProblem = {
  id: 'P001',
  type: 'critical',
  title: '产品架构/系统架构图完全缺失',
  description: '文档中没有提供产品架构或系统架构图，这对于理解产品的整体设计和技术实现至关重要。',
  location: '2.2 产品架构/系统架构',
  suggestion: '建议添加详细的产品架构图和系统架构图',
  impact: '高',
  effort: '中'
};

describe('ProblemDetailCard', () => {
  const mockOnAdoptionToggle = jest.fn();
  const mockOnNavigateToPRD = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders problem detail card correctly', () => {
    render(
      <ProblemDetailCard
        problemId="P001"
        problem={mockProblem}
        adoptionStatus={false}
        onAdoptionToggle={mockOnAdoptionToggle}
        onNavigateToPRD={mockOnNavigateToPRD}
      />
    );

    expect(screen.getByText('P001')).toBeInTheDocument();
    expect(screen.getByText('产品架构/系统架构图完全缺失')).toBeInTheDocument();
    expect(screen.getByText('2.2 产品架构/系统架构')).toBeInTheDocument();
  });

  test('displays problem severity correctly', () => {
    render(
      <ProblemDetailCard
        problemId="P001"
        problem={mockProblem}
        adoptionStatus={false}
        onAdoptionToggle={mockOnAdoptionToggle}
        onNavigateToPRD={mockOnNavigateToPRD}
      />
    );

    expect(screen.getByText('严重')).toBeInTheDocument();
  });

  test('shows navigate to PRD button', () => {
    render(
      <ProblemDetailCard
        problemId="P001"
        problem={mockProblem}
        adoptionStatus={false}
        onAdoptionToggle={mockOnAdoptionToggle}
        onNavigateToPRD={mockOnNavigateToPRD}
      />
    );

    expect(screen.getByText('转到PRD对应章节')).toBeInTheDocument();
  });

  test('handles navigate to PRD button click', () => {
    render(
      <ProblemDetailCard
        problemId="P001"
        problem={mockProblem}
        adoptionStatus={false}
        onAdoptionToggle={mockOnAdoptionToggle}
        onNavigateToPRD={mockOnNavigateToPRD}
      />
    );

    const navigateButton = screen.getByText('转到PRD对应章节');
    fireEvent.click(navigateButton);

    expect(mockOnNavigateToPRD).toHaveBeenCalledWith('P001', mockProblem);
  });

  test('handles adoption toggle correctly', () => {
    render(
      <ProblemDetailCard
        problemId="P001"
        problem={mockProblem}
        adoptionStatus={false}
        onAdoptionToggle={mockOnAdoptionToggle}
        onNavigateToPRD={mockOnNavigateToPRD}
      />
    );

    const adoptButton = screen.getByText('采纳建议');
    fireEvent.click(adoptButton);

    expect(mockOnAdoptionToggle).toHaveBeenCalledWith('P001', true);
  });

  test('shows adopted state correctly', () => {
    render(
      <ProblemDetailCard
        problemId="P001"
        problem={mockProblem}
        adoptionStatus={true}
        onAdoptionToggle={mockOnAdoptionToggle}
        onNavigateToPRD={mockOnNavigateToPRD}
      />
    );

    expect(screen.getByText('已采纳')).toBeInTheDocument();
  });

  test('displays problem details when expanded', () => {
    render(
      <ProblemDetailCard
        problemId="P001"
        problem={mockProblem}
        adoptionStatus={false}
        onAdoptionToggle={mockOnAdoptionToggle}
        onNavigateToPRD={mockOnNavigateToPRD}
      />
    );

    expect(screen.getByText(mockProblem.description)).toBeInTheDocument();
    expect(screen.getByText(mockProblem.suggestion)).toBeInTheDocument();
  });
});
