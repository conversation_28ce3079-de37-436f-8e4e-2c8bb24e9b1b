import React, { useState } from 'react';

/**
 * 评审上下文Tab组件
 * 显示评审相关的上下文信息和规则说明
 */
const ReviewContextTab = ({ documentInfo, statistics, chapters }) => {
  const [showRules, setShowRules] = useState(false);

  if (!documentInfo || !statistics) return null;

  return (
    <div>
      <h2 className="text-2xl font-bold text-gray-900 border-l-4 border-blue-600 pl-4 mb-6">
        评审上下文
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 文档信息 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="font-semibold text-lg mb-4 flex items-center">
            <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            文档信息
          </h3>
          <div className="space-y-3 text-gray-700">
            <div className="flex justify-between">
              <span className="font-medium">文档名称:</span>
              <span className="text-right">{documentInfo.title}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">版本:</span>
              <span>{documentInfo.version}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">变更状态:</span>
              <span>变更</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">综合评分:</span>
              <span className={`font-bold ${documentInfo.reviewResultClass}`}>
                {documentInfo.overallScore}/100
              </span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">评审结果:</span>
              <span className={`font-bold ${documentInfo.reviewResultClass}`}>
                {documentInfo.reviewResult}
              </span>
            </div>
          </div>
        </div>

        {/* 评审范围 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="font-semibold text-lg mb-4 flex items-center">
            <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
            </svg>
            评审范围
          </h3>
          <div className="space-y-3 text-gray-700">
            <div className="flex justify-between">
              <span className="font-medium">评审章节数量:</span>
              <span>
                <a href="#chapters" className="text-blue-600 hover:underline">
                  {chapters?.length || 0}个章节
                </a>
              </span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">发现问题数量:</span>
              <span className="font-bold text-red-600">
                {statistics.totalIssues}个问题
              </span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">评审规则数量:</span>
              <span>
                <button 
                  onClick={() => setShowRules(!showRules)}
                  className="text-blue-600 hover:underline"
                >
                  27个规则
                </button>
              </span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">需求模版:</span>
              <span>
                <button className="text-blue-600 hover:underline">
                  汽车行业标准PRD模版V2.1
                </button>
              </span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">评审重点:</span>
              <span className="text-right text-sm">
                PRD的整体质量、对于产品设计的价值和易于被其他理解，被AI Coding理解
              </span>
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="font-semibold text-lg mb-4 flex items-center">
            <svg className="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            评审统计
          </h3>
          <div className="space-y-3 text-gray-700">
            <div className="flex justify-between">
              <span className="font-medium">建议采纳率:</span>
              <span className="font-bold text-green-600">
                {statistics.acceptanceRate}%
              </span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">已采纳建议:</span>
              <span>{statistics.acceptedSuggestions}条</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">关键问题:</span>
              <span className="font-bold text-red-600">
                {statistics.criticalIssues}个
              </span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">预期改进分数:</span>
              <span className="font-bold text-blue-600">
                +{statistics.improvementPotential}分
              </span>
            </div>
          </div>
        </div>

        {/* 问题分级与评分规则 */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 col-span-1 md:col-span-2">
          <details className="group" open={showRules}>
            <summary 
              className="flex items-center justify-between cursor-pointer font-semibold text-lg"
              onClick={(e) => {
                e.preventDefault();
                setShowRules(!showRules);
              }}
            >
              <span className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                问题分级与评分规则 (点击展开)
              </span>
              <svg 
                className={`w-5 h-5 text-gray-500 transition-transform duration-200 ${showRules ? 'rotate-180' : ''}`}
                xmlns="http://www.w3.org/2000/svg" 
                fill="none" 
                viewBox="0 0 24 24" 
                strokeWidth="2" 
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
              </svg>
            </summary>
            
            {showRules && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                  <div className="flex items-start space-x-3">
                    <span className="w-6 h-6 rounded-full bg-red-500 flex-shrink-0 flex items-center justify-center">
                      <span className="text-white text-xs font-bold">严</span>
                    </span>
                    <div>
                      <strong className="text-red-600">严重:</strong>
                      <p className="text-sm text-gray-600">阻塞开发推进，必须修复</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <span className="w-6 h-6 rounded-full bg-yellow-500 flex-shrink-0 flex items-center justify-center">
                      <span className="text-white text-xs font-bold">重</span>
                    </span>
                    <div>
                      <strong className="text-yellow-600">重要:</strong>
                      <p className="text-sm text-gray-600">影响质量或效率，建议修复</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <span className="w-6 h-6 rounded-full bg-orange-500 flex-shrink-0 flex items-center justify-center">
                      <span className="text-white text-xs font-bold">警</span>
                    </span>
                    <div>
                      <strong className="text-orange-600">警告:</strong>
                      <p className="text-sm text-gray-600">可能影响后续扩展或维护，建议优化</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <span className="w-6 h-6 rounded-full bg-blue-500 flex-shrink-0 flex items-center justify-center">
                      <span className="text-white text-xs font-bold">建</span>
                    </span>
                    <div>
                      <strong className="text-blue-600">建议:</strong>
                      <p className="text-sm text-gray-600">优化改进，提升文档质量</p>
                    </div>
                  </div>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-sm text-gray-600">
                    <strong>评分规则:</strong> 最终得分 = 85 (基础分) + 亮点加分 (最高+15) - 问题扣分 (严重-8, 重要-4, 警告-2, 建议-1)
                  </p>
                </div>
              </div>
            )}
          </details>
        </div>
      </div>
    </div>
  );
};

export default ReviewContextTab;
