import React, { useEffect, useState } from 'react';

/**
 * 粘性概览组件
 * 显示评审结果概览和问题分布，支持粘性滚动
 */
const StickyOverview = ({ documentInfo, statistics }) => {
  const [isSticky, setIsSticky] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      // 查找最近的可滚动父容器
      const scrollContainer = document.querySelector('.h-full.overflow-y-auto') || window;
      const scrollTop = scrollContainer === window
        ? window.pageYOffset || document.documentElement.scrollTop
        : scrollContainer.scrollTop;
      setIsSticky(scrollTop > 200); // 滚动超过200px时变为粘性
    };

    // 查找滚动容器并添加事件监听
    const scrollContainer = document.querySelector('.h-full.overflow-y-auto');
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
      return () => scrollContainer.removeEventListener('scroll', handleScroll);
    } else {
      window.addEventListener('scroll', handleScroll);
      return () => window.removeEventListener('scroll', handleScroll);
    }
  }, []);

  if (!documentInfo || !statistics) return null;

  const { problemDistribution } = statistics;
  const totalProblems = Object.values(problemDistribution || {}).reduce((sum, count) => sum + count, 0);

  // 计算问题分布百分比
  const getPercentage = (count) => totalProblems > 0 ? (count / totalProblems * 100) : 0;

  return (
    <section className="mb-5">
      <h2 className="text-2xl font-bold text-gray-900 border-l-4 border-blue-600 pl-4 mb-6">
        {documentInfo.title.includes('BSV') ? 'BSV 特性需求文档评审概览' : 'PRD文档评审概览'}
      </h2>
      
      <div className={`transition-all duration-300 ${isSticky ? 'sticky top-0 z-50 bg-gray-50 py-4 shadow-md' : ''}`}>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 左列：评审结果 & 综合评分 */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 flex items-center gap-6">
            <div className="flex-1 text-center">
              <p className="text-gray-500 text-sm">评审结果</p>
              <p className={`text-2xl font-bold mt-1 ${documentInfo.reviewResultClass}`}>
                {documentInfo.reviewResult}
              </p>
            </div>
            <div className="border-l border-gray-300 h-16"></div>
            <div className="flex-1 text-center">
              <p className="text-gray-500 text-sm">综合评分</p>
              <p className="text-gray-900 text-2xl font-bold mt-1">
                {documentInfo.overallScore}
                <span className="text-base text-gray-500">/100</span>
              </p>
            </div>
          </div>

          {/* 右列：问题分布 */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-xl font-bold mb-4 text-center">
              问题分布 (共{totalProblems}个)
            </h3>
            
            {/* 问题分布条 */}
            <div className="h-6 flex rounded-md overflow-hidden mb-4">
              {problemDistribution?.['严重'] > 0 && (
                <div 
                  className="bg-red-600 flex items-center justify-center text-white text-xs font-semibold"
                  style={{ width: `${getPercentage(problemDistribution['严重'])}%` }}
                >
                  {problemDistribution['严重']}
                </div>
              )}
              {problemDistribution?.['重要'] > 0 && (
                <div 
                  className="bg-yellow-600 flex items-center justify-center text-white text-xs font-semibold"
                  style={{ width: `${getPercentage(problemDistribution['重要'])}%` }}
                >
                  {problemDistribution['重要']}
                </div>
              )}
              {problemDistribution?.['警告'] > 0 && (
                <div 
                  className="bg-orange-500 flex items-center justify-center text-white text-xs font-semibold"
                  style={{ width: `${getPercentage(problemDistribution['警告'])}%` }}
                >
                  {problemDistribution['警告']}
                </div>
              )}
              {problemDistribution?.['建议'] > 0 && (
                <div 
                  className="bg-blue-600 flex items-center justify-center text-white text-xs font-semibold"
                  style={{ width: `${getPercentage(problemDistribution['建议'])}%` }}
                >
                  {problemDistribution['建议']}
                </div>
              )}
            </div>

            {/* 图例 */}
            <div className="flex justify-around text-xs text-gray-700">
              <div className="flex items-center">
                <span className="w-2 h-2 rounded-full bg-red-600 mr-1"></span>
                严重: {problemDistribution?.['严重'] || 0}
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 rounded-full bg-yellow-600 mr-1"></span>
                重要: {problemDistribution?.['重要'] || 0}
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 rounded-full bg-orange-500 mr-1"></span>
                警告: {problemDistribution?.['警告'] || 0}
              </div>
              <div className="flex items-center">
                <span className="w-2 h-2 rounded-full bg-blue-600 mr-1"></span>
                建议: {problemDistribution?.['建议'] || 0}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default StickyOverview;
