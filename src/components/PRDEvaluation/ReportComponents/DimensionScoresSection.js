import React from 'react';
import DimensionScoreCard from './DimensionScoreCard';

/**
 * 维度评分分析组件
 * 独立的维度评分展示模块，放置在Tab导航外面
 */
const DimensionScoresSection = ({
  dimensionScores,
  problems,
  showDetailedScores,
  onToggleDetailedScores,
  onProblemNavigation
}) => {
  if (!dimensionScores) return null;

  // 根据问题维度分组
  const problemsByDimension = {};
  Object.entries(problems || {}).forEach(([id, problem]) => {
    if (!problemsByDimension[problem.dimension]) {
      problemsByDimension[problem.dimension] = [];
    }
    problemsByDimension[problem.dimension].push({ ...problem, id });
  });

  return (
    <section className="mb-5">
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-900">维度评分分析</h3>
          <button
            onClick={onToggleDetailedScores}
            className="flex items-center text-blue-600 hover:text-blue-800 transition duration-200"
          >
            <span className="mr-1">{showDetailedScores ? '收起' : '展开'}</span>
            <svg 
              className={`w-5 h-5 transition-transform duration-300 ${showDetailedScores ? 'rotate-180' : ''}`}
              xmlns="http://www.w3.org/2000/svg" 
              fill="none" 
              viewBox="0 0 24 24" 
              strokeWidth="2" 
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          {Object.entries(dimensionScores || {}).map(([dimensionName, scoreData]) => (
            <DimensionScoreCard
              key={dimensionName}
              dimensionName={dimensionName}
              scoreData={scoreData}
              problems={problemsByDimension[dimensionName] || []}
              showDetails={showDetailedScores}
              onProblemNavigation={onProblemNavigation}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default DimensionScoresSection;
