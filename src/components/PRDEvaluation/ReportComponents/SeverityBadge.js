import React from 'react';

/**
 * 严重程度标签组件
 * 显示问题的严重程度标签
 */
const SeverityBadge = ({ severity, size = 'normal' }) => {
  const getSeverityConfig = (severity) => {
    const configs = {
      '严重': { 
        icon: '🔴', 
        bgColor: 'bg-red-100', 
        textColor: 'text-red-800',
        borderColor: 'border-red-200'
      },
      '重要': { 
        icon: '🟡', 
        bgColor: 'bg-yellow-100', 
        textColor: 'text-yellow-800',
        borderColor: 'border-yellow-200'
      },
      '警告': { 
        icon: '🟠', 
        bgColor: 'bg-orange-100', 
        textColor: 'text-orange-800',
        borderColor: 'border-orange-200'
      },
      '建议': { 
        icon: '🔵', 
        bgColor: 'bg-blue-100', 
        textColor: 'text-blue-800',
        borderColor: 'border-blue-200'
      }
    };
    return configs[severity] || configs['建议'];
  };

  const config = getSeverityConfig(severity);
  const sizeClasses = size === 'small' ? 'px-2 py-0.5 text-xs' : 'px-3 py-1 text-sm';

  return (
    <span className={`
      inline-flex items-center gap-1 font-medium rounded-full border
      ${config.bgColor} ${config.textColor} ${config.borderColor} ${sizeClasses}
    `}>
      <span className="text-xs">{config.icon}</span>
      {severity}
    </span>
  );
};

export default SeverityBadge;
