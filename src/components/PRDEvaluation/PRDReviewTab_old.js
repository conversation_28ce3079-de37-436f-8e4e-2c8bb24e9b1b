import React, { useState, useEffect } from 'react';
import DocumentOutline from './components/DocumentOutline';
import DocumentContent from './components/DocumentContent';
import IssueDetailPanel from './components/IssueDetailPanel';
import ReviewProgress from './components/ReviewProgress';

const PRDReviewTab = ({ document, analysisResults, handleReviewComplete }) => {
  console.log('PRDReviewTab 渲染，参数:', { document: document?.title, analysisResults: analysisResults?.totalIssues });

  const [outlineExpanded, setOutlineExpanded] = useState(true);
  const [selectedIssue, setSelectedIssue] = useState(null);
  const [reviewedIssues, setReviewedIssues] = useState(new Set());
  const [acceptedSuggestions, setAcceptedSuggestions] = useState(new Set());
  const [documentSections, setDocumentSections] = useState([]);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const [reportProgress, setReportProgress] = useState(0);
  const [reviewCompleted, setReviewCompleted] = useState(false);

  // 初始化文档章节
  const initializeDocumentSections = useCallback(() => {
    const sections = [
      {
        id: "section-1",
        title: "LCA 特性需求文档",
        level: 1,
        content: `LCA 特性需求文档

编制/变更日志

序号 | 版本 | PRD ID | 编制变更及内容 | 编制人 | 审核人 | 批准人 | 编制日期
1 | V1.0.0 | S-123-07 | 初次创建 | 王建军 | | | 2024-11-25
2 | V1.0.1 | S-123-07 | 新增基础软件下LCA算法可视化展现的说明 | 王建军 | | | 2025-03-21
3 | V1.0.2 | S-123-07 | 增加Usecase 3.2.1.2, 增加大下坡时的LCA/ADW功能台，不涉及功能需求变更 | 王建军 | | | 2025-05-09`,
        issues: analysisResults?.issues.filter(issue => issue.section === "section-1") || []
      },
      {
        id: "section-2",
        title: "1. 文档综述",
        level: 1,
        content: `1.1 文档背景

本文档用于吉利集团的变道安全辅助管理（LCA）功能需求文档，详细描述变道安全辅助管理（LCA）的功能分析、应用场景设计等。

本目标实现程序中，通过详细设计计算规则进行算法实现，约束到核心或改变，通过功能点。

1.2 文档范围

本文档介绍了变道安全辅助管理（Lane Change Assist, LCA）的产品需求、具体的功能需求设计及其功能说明。`,
        issues: analysisResults?.issues.filter(issue => issue.section === "section-2") || []
      },
      {
        id: "section-3",
        title: "1.3 术语解释",
        level: 2,
        content: `序号 | 术语 | 定义
1 | BSD | Blind Spot Detection 盲区检测
2 | CWW | Closing Vehicle Warning 接近车辆警告
3 | CSD | Center Stack Display 中控显示屏
4 | DIM | Driver Information Module 驾驶员信息模块（仪表）
5 | HUD | Head Up Display 抬头显示
6 | TTC | Time To Collision 碰撞时间`,
        issues: analysisResults?.issues.filter(issue => issue.section === "section-3") || []
      }
    ];
    setDocumentSections(sections);
  }, [analysisResults]);

  useEffect(() => {
    if (document && analysisResults) {
      initializeDocumentSections();
    }
  }, [document, analysisResults, initializeDocumentSections]);

  // 处理问题确认
  const handleIssueReview = useCallback((issueId, accepted) => {
    setReviewedIssues(prev => new Set([...prev, issueId]));
    if (accepted) {
      setAcceptedSuggestions(prev => new Set([...prev, issueId]));
    } else {
      setAcceptedSuggestions(prev => {
        const newSet = new Set(prev);
        newSet.delete(issueId);
        return newSet;
      });
    }
  }, []);

  // 滚动到指定章节
  const scrollToSection = useCallback((sectionId) => {
    const element = window.document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, []);



  // 处理问题确认
  const handleIssueReview = (issueId, accepted) => {
    setReviewedIssues(prev => new Set([...prev, issueId]));
    if (accepted) {
      setAcceptedSuggestions(prev => new Set([...prev, issueId]));
    } else {
      setAcceptedSuggestions(prev => {
        const newSet = new Set(prev);
        newSet.delete(issueId);
        return newSet;
      });
    }
  };

  // 完成评审确认
  const handleCompleteReview = () => {
    setReviewCompleted(true);
    console.log('评审确认完成，等待生成报告');
  };

  // 生成报告
  const handleGenerateReport = async () => {
    setIsGeneratingReport(true);
    setReportProgress(0);

    // 模拟报告生成过程
    const steps = [
      "整理评审数据...",
      "生成统计图表...",
      "编写分析报告...",
      "格式化输出...",
      "完成报告生成..."
    ];

    for (let i = 0; i < steps.length; i++) {
      console.log(`报告生成步骤 ${i + 1}: ${steps[i]}`);
      await new Promise(resolve => setTimeout(resolve, 800));
      setReportProgress(((i + 1) / steps.length) * 100);
    }

    // 生成最终的评审结果
    const reviewResults = {
      documentId: document.id,
      reviewTimestamp: new Date().toISOString(),
      totalIssues: analysisResults?.totalIssues || 0,
      reviewedIssues: reviewedIssues.size,
      acceptedSuggestions: acceptedSuggestions.size,
      rejectedSuggestions: reviewedIssues.size - acceptedSuggestions.size,
      reviewedIssueIds: Array.from(reviewedIssues),
      acceptedSuggestionIds: Array.from(acceptedSuggestions),
      overallApproval: reviewedIssues.size > 0 ? (acceptedSuggestions.size / reviewedIssues.size) * 100 : 0
    };

    setIsGeneratingReport(false);
    console.log('报告生成完成，跳转到评审结果页面');
    handleReviewComplete(reviewResults);
  };

  // 滚动到指定章节
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          <button
            className="border border-gray-300 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-50 transition-colors"
            onClick={() => setOutlineExpanded(!outlineExpanded)}
          >
            <svg className="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
            </svg>
            {outlineExpanded ? "隐藏大纲" : "显示大纲"}
          </button>
          <h2 className="text-xl font-semibold text-gray-900">
            {document?.title || "智能驾驶系统PRD"}
          </h2>
        </div>
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
          v1.2.0 汽车行业
        </span>
      </div>

      {/* 分析结果摘要 */}
      {analysisResults && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">智能分析结果摘要</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{analysisResults.overallScore}</div>
              <div className="text-sm text-gray-600">总体评分</div>
            </div>
            <div className="text-center p-3 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{analysisResults.errorCount}</div>
              <div className="text-sm text-gray-600">错误</div>
            </div>
            <div className="text-center p-3 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">{analysisResults.warningCount}</div>
              <div className="text-sm text-gray-600">警告</div>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{analysisResults.suggestionCount}</div>
              <div className="text-sm text-gray-600">建议</div>
            </div>
          </div>
          <div className="text-sm text-gray-600">
            请逐一确认下方标记的问题，选择采纳或拒绝相应的修改建议。
          </div>
        </div>
      )}

      {/* 三栏布局主体 */}
      <div className="flex gap-6 h-[700px]">
        {/* 左栏：文档大纲导航 */}
        {outlineExpanded && (
          <DocumentOutline
            documentSections={documentSections}
            selectedIssue={selectedIssue}
            onSectionClick={scrollToSection}
            onIssueClick={setSelectedIssue}
          />
        )}

        {/* 中栏：文档内容 */}
        <DocumentContent
          documentSections={documentSections}
          selectedIssue={selectedIssue}
          reviewedIssues={reviewedIssues}
          acceptedSuggestions={acceptedSuggestions}
          onIssueClick={setSelectedIssue}
        />

        {/* 右栏：问题详情面板 */}
        <IssueDetailPanel
          selectedIssue={selectedIssue}
          reviewedIssues={reviewedIssues}
          acceptedSuggestions={acceptedSuggestions}
          onIssueReview={handleIssueReview}
          onClose={() => setSelectedIssue(null)}
        />
      </div>

      {/* 底部操作栏 */}
      <ReviewProgress
        reviewedIssues={reviewedIssues}
        totalIssues={analysisResults?.totalIssues || 0}
        acceptedSuggestions={acceptedSuggestions}
        isGeneratingReport={isGeneratingReport}
        reportProgress={reportProgress}
        reviewCompleted={reviewCompleted}
        onSaveDraft={() => console.log('保存草稿')}
        onCompleteReview={handleCompleteReview}
        onGenerateReport={handleGenerateReport}
      />
    </div>
  );
};

export default PRDReviewTab;
