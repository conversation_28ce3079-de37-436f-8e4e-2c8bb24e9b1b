import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import PRDReviewTab from '../PRDReviewTab';

// Mock data
const mockDocument = {
  id: 'doc-003',
  title: 'BSV特性需求文档',
  content: 'Mock document content'
};

const mockAnalysisResults = {
  issues: [
    {
      id: 'P001',
      type: 'critical',
      title: '产品架构/系统架构图完全缺失',
      description: '文档中没有提供产品架构或系统架构图',
      section: '2.2',
      location: '2.2 产品架构/系统架构'
    }
  ]
};

describe('PRDReviewTab', () => {
  const mockHandleReviewComplete = jest.fn();
  const mockOnReturnToResults = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders PRD review tab correctly', () => {
    render(
      <PRDReviewTab
        document={mockDocument}
        analysisResults={mockAnalysisResults}
        handleReviewComplete={mockHandleReviewComplete}
      />
    );

    expect(screen.getByText('BSV特性需求文档')).toBeInTheDocument();
  });

  test('shows return button when navigated from results', () => {
    render(
      <PRDReviewTab
        document={mockDocument}
        analysisResults={mockAnalysisResults}
        handleReviewComplete={mockHandleReviewComplete}
        targetProblemId="P001"
        onReturnToResults={mockOnReturnToResults}
      />
    );

    // Should show return button instead of complete review button
    expect(screen.getByText('返回章节评审')).toBeInTheDocument();
  });

  test('shows complete review button in normal mode', () => {
    render(
      <PRDReviewTab
        document={mockDocument}
        analysisResults={mockAnalysisResults}
        handleReviewComplete={mockHandleReviewComplete}
      />
    );

    expect(screen.getByText('完成评审确认')).toBeInTheDocument();
  });

  test('handles return to results correctly', () => {
    render(
      <PRDReviewTab
        document={mockDocument}
        analysisResults={mockAnalysisResults}
        handleReviewComplete={mockHandleReviewComplete}
        targetProblemId="P001"
        onReturnToResults={mockOnReturnToResults}
      />
    );

    const returnButton = screen.getByText('返回章节评审');
    fireEvent.click(returnButton);

    expect(mockOnReturnToResults).toHaveBeenCalledWith('P001');
  });

  test('renders basic elements', () => {
    render(
      <PRDReviewTab
        document={mockDocument}
        analysisResults={mockAnalysisResults}
        handleReviewComplete={mockHandleReviewComplete}
      />
    );

    // Basic rendering test
    expect(screen.getByText('BSV特性需求文档')).toBeInTheDocument();
  });

  test('disables complete button when no issues reviewed', () => {
    render(
      <PRDReviewTab
        document={mockDocument}
        analysisResults={mockAnalysisResults}
        handleReviewComplete={mockHandleReviewComplete}
      />
    );

    const completeButton = screen.getByText('完成评审确认');
    expect(completeButton).toBeDisabled();
  });
});
