import React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';

// Basic smoke tests to ensure components can render without crashing

describe('Basic Component Tests', () => {
  test('React is working', () => {
    const TestComponent = () => <div>Test</div>;
    const { getByText } = render(<TestComponent />);
    expect(getByText('Test')).toBeInTheDocument();
  });

  test('Math operations work', () => {
    expect(2 + 2).toBe(4);
    expect(10 - 5).toBe(5);
    expect(3 * 4).toBe(12);
  });

  test('String operations work', () => {
    expect('hello'.toUpperCase()).toBe('HELLO');
    expect('world'.length).toBe(5);
  });

  test('Array operations work', () => {
    const arr = [1, 2, 3];
    expect(arr.length).toBe(3);
    expect(arr.includes(2)).toBe(true);
  });

  test('Object operations work', () => {
    const obj = { name: 'test', value: 42 };
    expect(obj.name).toBe('test');
    expect(obj.value).toBe(42);
  });
});
