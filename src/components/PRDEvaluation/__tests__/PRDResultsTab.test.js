import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import PRDResultsTab from '../PRDResultsTab';

// Mock the reportDataAdapter
jest.mock('../../../services/reportDataAdapter', () => ({
  adaptAnalysisResultsToReport: jest.fn((analysisResults) => {
    if (!analysisResults) return null;
    return {
      document: {
        title: 'BSV特性需求文档',
        totalScore: 69
      },
      statistics: {
        totalIssues: 10,
        criticalIssues: 1,
        importantIssues: 4,
        warningIssues: 3,
        suggestionIssues: 2
      },
      issues: [
        {
          id: 'P001',
          type: 'critical',
          title: '产品架构/系统架构图完全缺失',
          section: '2.2'
        }
      ]
    };
  })
}));

// Mock data
const mockDocument = {
  id: 'doc-003',
  title: 'BSV特性需求文档',
  uploadDate: '2024-01-17',
  status: 'completed'
};

const mockAnalysisResults = {
  totalIssues: 10,
  criticalIssues: 1,
  warningIssues: 4,
  suggestionIssues: 5,
  overallScore: 69,
  issues: [
    {
      id: 'P001',
      type: 'critical',
      title: '产品架构/系统架构图完全缺失'
    }
  ]
};

const mockReviewResults = {
  reviewedIssues: 10,
  acceptedSuggestions: 8,
  rejectedSuggestions: 2
};

describe('PRDResultsTab', () => {
  const mockHandleStartOptimization = jest.fn();
  const mockOnNavigateToReview = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders PRD results tab correctly', () => {
    render(
      <PRDResultsTab
        document={mockDocument}
        analysisResults={mockAnalysisResults}
        reviewResults={mockReviewResults}
        handleStartOptimization={mockHandleStartOptimization}
        onNavigateToReview={mockOnNavigateToReview}
      />
    );

    expect(screen.getByText('BSV特性需求文档')).toBeInTheDocument();
    expect(screen.getByText('69')).toBeInTheDocument(); // score
  });

  test('displays correct problem statistics', () => {
    render(
      <PRDResultsTab
        document={mockDocument}
        analysisResults={mockAnalysisResults}
        reviewResults={mockReviewResults}
        handleStartOptimization={mockHandleStartOptimization}
        onNavigateToReview={mockOnNavigateToReview}
      />
    );

    // Check for statistics in the component
    expect(screen.getByText('10')).toBeInTheDocument(); // total issues
    expect(screen.getByText('1')).toBeInTheDocument();  // critical issues
  });

  test('switches between tabs correctly', async () => {
    render(
      <PRDResultsTab
        document={mockDocument}
        analysisResults={mockAnalysisResults}
        reviewResults={mockReviewResults}
        handleStartOptimization={mockHandleStartOptimization}
        onNavigateToReview={mockOnNavigateToReview}
      />
    );

    // Test tab switching - look for tab buttons
    const tabButtons = screen.getAllByRole('button');
    const chaptersTabButton = tabButtons.find(button =>
      button.textContent.includes('章节评审详细')
    );

    if (chaptersTabButton) {
      fireEvent.click(chaptersTabButton);
    }
  });

  test('handles manual confirm button click', () => {
    render(
      <PRDResultsTab
        document={mockDocument}
        analysisResults={mockAnalysisResults}
        reviewResults={mockReviewResults}
        handleStartOptimization={mockHandleStartOptimization}
        onNavigateToReview={mockOnNavigateToReview}
      />
    );

    // Look for manual confirm button
    const buttons = screen.getAllByRole('button');
    const manualConfirmButton = buttons.find(button =>
      button.textContent.includes('人工确认')
    );

    if (manualConfirmButton) {
      fireEvent.click(manualConfirmButton);
      expect(mockOnNavigateToReview).toHaveBeenCalled();
    }
  });

  test('renders loading state when no data', () => {
    render(
      <PRDResultsTab
        document={null}
        analysisResults={null}
        reviewResults={null}
        handleStartOptimization={mockHandleStartOptimization}
        onNavigateToReview={mockOnNavigateToReview}
      />
    );

    expect(screen.getByText('正在加载评审报告...')).toBeInTheDocument();
  });
});
