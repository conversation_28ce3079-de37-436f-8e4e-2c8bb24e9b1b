/* PRD评审确认页面样式 */

/* 高亮文本动画效果 */
.highlight-text {
  background-color: #fef3c7 !important;
  border: 2px solid #f59e0b !important;
  border-radius: 4px !important;
  padding: 2px 4px !important;
  transition: all 0.3s ease !important;
  animation: highlightPulse 3s ease-in-out;
}

@keyframes highlightPulse {
  0% {
    background-color: #fef3c7;
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
  }
  50% {
    background-color: #fde68a;
    box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);
  }
  100% {
    background-color: #fef3c7;
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
  }
}

/* 问题卡片高亮效果 */
.issue-card-highlight {
  animation: cardHighlight 2s ease-in-out;
}

@keyframes cardHighlight {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 20px 25px -5px rgba(245, 158, 11, 0.3);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
}

/* 章节高亮效果 */
.outline-section-highlight {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-left: 4px solid #3b82f6 !important;
  padding-left: 16px !important;
  margin-left: -20px !important;
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
}

.highlight-pulse {
  animation: sectionHighlightPulse 2s ease-in-out;
}

@keyframes sectionHighlightPulse {
  0% {
    background-color: rgba(59, 130, 246, 0.1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    background-color: rgba(59, 130, 246, 0.2);
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
  }
  100% {
    background-color: rgba(59, 130, 246, 0.1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* 章节路径显示优化 */
.section-path {
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 500;
}

.target-text-preview {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 11px;
  background-color: #f3f4f6;
  border-radius: 3px;
  padding: 1px 4px;
}

/* 三栏布局优化 */
.prd-review-layout {
  min-width: 0;
  width: 100%;
}

/* 表格样式优化 */
.prd-content-table {
  table-layout: fixed;
  width: 100%;
  border-collapse: collapse;
}

.prd-content-table td {
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: anywhere;
  hyphens: auto;
  vertical-align: top;
}

/* 确保表格内容不会溢出 */
.table-container {
  width: 100%;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  /* 调整三栏宽度比例 */
  .prd-review-left-panel {
    width: 18rem; /* w-72 */
  }

  .prd-review-right-panel {
    width: 22rem; /* w-88 */
  }
}

@media (max-width: 1400px) {
  .prd-content-table {
    font-size: 0.75rem;
  }

  .prd-content-table td {
    padding: 0.375rem 0.5rem;
  }

  /* 进一步调整三栏宽度 */
  .prd-review-left-panel {
    width: 16rem; /* w-64 */
  }

  .prd-review-right-panel {
    width: 20rem; /* w-80 */
  }
}

@media (max-width: 1200px) {
  .prd-content-table {
    font-size: 0.7rem;
  }

  .prd-content-table td {
    padding: 0.25rem 0.375rem;
  }

  /* 小屏幕下调整布局 */
  .prd-review-left-panel {
    width: 14rem; /* w-56 */
  }

  .prd-review-right-panel {
    width: 18rem; /* w-72 */
  }
}

/* 超小屏幕下的布局调整 */
@media (max-width: 1024px) {
  .prd-review-layout {
    flex-direction: column;
    height: auto;
  }

  .prd-review-left-panel,
  .prd-review-right-panel {
    width: 100%;
    max-height: 300px;
  }
}
