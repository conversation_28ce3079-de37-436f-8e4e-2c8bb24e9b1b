import React, { useEffect, useMemo } from 'react';
import { usePRDEvaluation } from '../../hooks/usePRDEvaluation';
import PRDUploadTab from './PRDUploadTab';
import PRDAnalysisTab from './PRDAnalysisTab';
import PRDReviewTab from './PRDReviewTab';
import PRDResultsTab from './PRDResultsTab';
import PRDOptimizationTab from './PRDOptimizationTab';

const PRDEvaluationPage = ({ onTabChange, onModuleTitleChange }) => {
  const {
    activeTab,
    setActiveTab,
    hasStartedAnalysis,
    hasCompletedAnalysis,
    hasCompletedReview,
    hasStartedOptimization,
    hasCompletedOptimization,
    currentDocument,
    analysisResults,
    reviewResults,
    uploadedDocuments,
    setUploadedDocuments,
    handleStartAnalysis,
    handleAnalysisComplete,
    handleEnterReview,
    handleReviewComplete,
    handleStartOptimization,
    handleCompleteOptimization,
    resetEvaluationState,
    enableTestMode
  } = usePRDEvaluation();

  // 导航状态管理
  const [navigationState, setNavigationState] = React.useState({
    targetProblemId: null,
    targetProblem: null,
    fromTab: null
  });

  // 当tab切换时，通知父组件更新面包屑导航
  const handleTabChange = React.useCallback((tabId) => {
    setActiveTab(tabId);
    if (onTabChange) {
      onTabChange(tabId);
    }
  }, [setActiveTab, onTabChange]);

  // 处理从评审结果导航到评审确认
  const handleNavigateToReview = React.useCallback((problemId, problem) => {
    console.log('导航到评审确认页面:', problemId, problem);
    setNavigationState({
      targetProblemId: problemId,
      targetProblem: problem,
      fromTab: 'results'
    });
    setActiveTab('review');
  }, [setActiveTab]);

  // 处理从评审确认返回到评审结果
  const handleReturnToResults = React.useCallback((problemId) => {
    console.log('返回评审结果页面:', problemId);
    setNavigationState({
      targetProblemId: problemId,
      targetProblem: null,
      fromTab: 'review'
    });
    setActiveTab('results');

    // 通知PRDResultsTab进行问题定位
    if (problemId) {
      // 使用事件系统通知PRDResultsTab
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('returnFromReview', {
          detail: { problemId }
        }));
      }, 100);
    }
  }, [setActiveTab]);

  // 初始化时通知父组件当前tab状态
  React.useEffect(() => {
    if (onTabChange) {
      onTabChange(activeTab);
    }
  }, [activeTab, onTabChange]);

  // 调试信息
  console.log('主组件状态:', {
    activeTab,
    hasStartedAnalysis,
    hasCompletedAnalysis,
    hasCompletedReview,
    hasStartedOptimization,
    hasCompletedOptimization,
    currentDocument: currentDocument?.title
  });

  // Tab配置
  const tabs = useMemo(() => [
    { id: 'upload', label: '评审中心', enabled: true },
    { id: 'analysis', label: '智能分析', enabled: hasStartedAnalysis },
    { id: 'review', label: '评审确认', enabled: hasCompletedAnalysis },
    { id: 'results', label: '评审结果', enabled: hasCompletedReview },
    { id: 'optimization', label: '自检优化', enabled: hasStartedOptimization }
  ], [hasStartedAnalysis, hasCompletedAnalysis, hasCompletedReview, hasStartedOptimization]);

  // 获取步骤状态
  const getStepStatus = (index) => {
    const stepStates = [
      true, // 上传步骤始终可用
      hasStartedAnalysis,
      hasCompletedAnalysis,
      hasCompletedReview,
      hasCompletedOptimization
    ];

    if (activeTab === tabs[index].id) return 'current';
    if (stepStates[index]) return 'completed';
    return 'pending';
  };

  // 键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Ctrl/Cmd + 数字键切换Tab
      if ((event.ctrlKey || event.metaKey) && event.key >= '1' && event.key <= '5') {
        event.preventDefault();
        const tabIndex = parseInt(event.key) - 1;
        const targetTab = tabs[tabIndex];
        if (targetTab && targetTab.enabled) {
          handleTabChange(targetTab.id);
        }
      }

      // Ctrl/Cmd + R 重置流程
      if ((event.ctrlKey || event.metaKey) && event.key === 'r' && hasCompletedOptimization) {
        event.preventDefault();
        if (window.confirm('确定要重新开始新的评估流程吗？')) {
          resetEvaluationState();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [tabs, handleTabChange, hasCompletedOptimization, resetEvaluationState]);

  // 渲染Tab内容
  const renderTabContent = () => {
    switch (activeTab) {
      case 'upload':
        return (
          <PRDUploadTab
            uploadedDocuments={uploadedDocuments}
            setUploadedDocuments={setUploadedDocuments}
            handleStartAnalysis={handleStartAnalysis}
          />
        );
      case 'analysis':
        return currentDocument ? (
          <PRDAnalysisTab
            document={currentDocument}
            handleAnalysisComplete={handleAnalysisComplete}
            handleEnterReview={handleEnterReview}
          />
        ) : null;
      case 'review':
        return currentDocument && analysisResults ? (
          <PRDReviewTab
            document={currentDocument}
            analysisResults={analysisResults}
            handleReviewComplete={handleReviewComplete}
            targetProblemId={navigationState.targetProblemId}
            onReturnToResults={handleReturnToResults}
          />
        ) : null;
      case 'results':
        return currentDocument && analysisResults && reviewResults ? (
          <PRDResultsTab
            document={currentDocument}
            analysisResults={analysisResults}
            reviewResults={reviewResults}
            handleStartOptimization={handleStartOptimization}
            onNavigateToReview={handleNavigateToReview}
          />
        ) : null;
      case 'optimization':
        return currentDocument && reviewResults && analysisResults ? (
          <PRDOptimizationTab
            document={currentDocument}
            reviewResults={reviewResults}
            analysisResults={analysisResults}
            handleCompleteOptimization={handleCompleteOptimization}
          />
        ) : null;
      default:
        return (
          <PRDUploadTab
            uploadedDocuments={uploadedDocuments}
            setUploadedDocuments={setUploadedDocuments}
            handleStartAnalysis={handleStartAnalysis}
          />
        );
    }
  };

  return (
    <div className="h-full bg-gray-50 overflow-hidden flex flex-col">
      <div className="flex-shrink-0 p-6 pb-0">
        <div className="w-full px-4">


          {/* 流程完成状态提示 */}
          {hasCompletedOptimization && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div>
                    <h3 className="text-sm font-medium text-green-800">
                      🎉 PRD智能评估流程已完成！
                    </h3>
                    <p className="text-sm text-green-700 mt-1">
                      文档《{currentDocument?.title}》已完成完整的智能评估、人工评审和自检优化流程。
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => {
                    if (window.confirm('确定要重新开始新的评估流程吗？当前数据将被清除。')) {
                      resetEvaluationState();
                    }
                  }}
                  className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md text-sm transition-colors"
                >
                  重新开始
                </button>
              </div>
            </div>
          )}

          {/* 数据导出提示 */}
          {hasCompletedOptimization && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <div>
                    <h3 className="text-sm font-medium text-blue-800">
                      建议导出评估数据
                    </h3>
                    <p className="text-sm text-blue-700 mt-1">
                      您可以导出完整的评估报告、优化建议等数据，用于后续的文档改进工作。
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => {
                    const exportData = {
                      document: currentDocument,
                      analysisResults,
                      reviewResults,
                      timestamp: new Date().toISOString()
                    };
                    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `PRD评估报告_${currentDocument?.title}_${new Date().toISOString().split('T')[0]}.json`;
                    a.click();
                    URL.revokeObjectURL(url);
                  }}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm transition-colors"
                >
                  导出完整报告
                </button>
              </div>
            </div>
          )}

          {/* 简洁流程导航 */}
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="flex">
              {tabs.map((tab, index) => {
                const isActive = activeTab === tab.id;
                const isCompleted = getStepStatus(index) === 'completed';
                const isEnabled = tab.enabled;

                return (
                  <div key={tab.id} className="flex-1 relative">
                    <button
                      onClick={() => isEnabled && handleTabChange(tab.id)}
                      disabled={!isEnabled}
                      className={`w-full py-2 px-2 flex items-center justify-center space-x-2 relative transition-all duration-300 ${
                        isActive
                          ? 'bg-orange-500 text-white'
                          : isCompleted
                            ? 'bg-green-50 text-green-700 hover:bg-green-100'
                            : isEnabled
                              ? 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                              : 'bg-gray-50 text-gray-400 cursor-not-allowed'
                      }`}
                    >
                      {/* 步骤编号 */}
                      <div className={`inline-flex items-center justify-center w-5 h-5 rounded-full text-xs font-bold ${
                        isActive
                          ? 'bg-white text-orange-500'
                          : isCompleted
                            ? 'bg-green-500 text-white'
                            : isEnabled
                              ? 'bg-gray-300 text-gray-600'
                              : 'bg-gray-200 text-gray-400'
                      }`}>
                        {isCompleted ? (
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        ) : (
                          index + 1
                        )}
                      </div>

                      {/* 步骤标题 */}
                      <div className="text-xs font-medium truncate">{tab.label}</div>
                    </button>

                    {/* 简化箭头连接线 */}
                    {index < tabs.length - 1 && (
                      <div className="absolute top-1/2 right-0 transform translate-x-1/2 -translate-y-1/2 z-10">
                        <div className={`w-0 h-0 border-l-[8px] border-r-0 border-t-[6px] border-b-[6px] border-t-transparent border-b-transparent ${
                          isActive
                            ? 'border-l-orange-500'
                            : isCompleted
                              ? 'border-l-green-50'
                              : 'border-l-gray-50'
                        }`}></div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Tab内容区域 - 不可滚动 */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full w-full px-4">
          {renderTabContent()}
        </div>
      </div>

      {/* 流程统计信息 */}
      {currentDocument && (
        <div className="bg-gray-50 border-t border-gray-200 p-4">
          <div className="w-full px-4">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div className="flex items-center space-x-6">
                <span>当前文档: {currentDocument.title}</span>
                {analysisResults && (
                  <span>发现问题: {analysisResults.totalIssues} 个</span>
                )}
                {reviewResults && (
                  <span>已评审: {reviewResults.reviewedIssues} 个</span>
                )}
                {reviewResults && (
                  <span>采纳建议: {reviewResults.acceptedSuggestions} 个</span>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <div className="flex items-center space-x-1">
                  {[1, 2, 3, 4, 5].map((step) => {
                    const isCompleted =
                      (step === 1 && hasStartedAnalysis) ||
                      (step === 2 && hasCompletedAnalysis) ||
                      (step === 3 && hasCompletedReview) ||
                      (step === 4 && hasStartedOptimization) ||
                      (step === 5 && hasCompletedOptimization);

                    return (
                      <div
                        key={step}
                        className={`w-2 h-2 rounded-full ${
                          isCompleted ? 'bg-green-500' : 'bg-gray-300'
                        }`}
                      />
                    );
                  })}
                </div>
                <span className="text-xs">
                  {hasCompletedOptimization ? '流程完成' :
                   hasStartedOptimization ? '自检优化中' :
                   hasCompletedReview ? '等待优化' :
                   hasCompletedAnalysis ? '评审确认中' :
                   hasStartedAnalysis ? '智能分析中' : '等待开始'}
                </span>
                <span className="text-xs text-gray-400 ml-4">
                  快捷键: Ctrl+1-5 切换页面
                  {hasCompletedOptimization && ', Ctrl+R 重新开始'}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PRDEvaluationPage;
