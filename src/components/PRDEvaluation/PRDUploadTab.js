import React, { useState } from 'react';
import DocumentUploadModal from './components/DocumentUploadModal';
import PageHeader from './components/PageHeader';
import StatsPanel from './components/StatsPanel';
import ReviewList from './components/ReviewList';
import { transformToReviewDocument, generateMockStats, generateMockReviewData } from '../../utils/dataTransform';

const PRDUploadTab = ({ uploadedDocuments, setUploadedDocuments, handleStartAnalysis }) => {
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);

  // 新增状态 (内部使用)
  const [reviewStats] = useState(generateMockStats());
  const [timeFilter, setTimeFilter] = useState('1month');
  const [expandedReviewDocuments, setExpandedReviewDocuments] = useState(new Set());

  // 转换现有数据格式为新格式，如果没有数据则使用模拟数据进行演示
  const reviewDocuments = uploadedDocuments.length > 0
    ? uploadedDocuments.map(transformToReviewDocument)
    : generateMockReviewData();

  // 事件处理函数
  const handleToggleExpansion = (documentId) => {
    setExpandedReviewDocuments(prev => {
      const newSet = new Set(prev);
      if (newSet.has(documentId)) {
        newSet.delete(documentId);
      } else {
        newSet.add(documentId);
      }
      return newSet;
    });
  };

  const handleStartReview = (documentId) => {
    // 找到对应的文档并调用现有的开始分析函数
    const document = uploadedDocuments.find(doc => doc.id === documentId);
    if (document) {
      handleStartAnalysis(document);
    }
  };

  const handleContinueReview = (documentId) => {
    // 跳转到评审确认Tab的逻辑
    console.log('继续评审:', documentId);
  };

  const handleViewResults = (documentId) => {
    // 跳转到评估报告Tab的逻辑
    console.log('查看结果:', documentId);
  };

  const handleRetryReview = (documentId) => {
    // 重新开始评审的逻辑
    console.log('重新评审:', documentId);
  };

  const handleUploadDocument = () => {
    // 触发文件上传的逻辑 (复用现有的上传弹窗)
    setIsUploadModalOpen(true);
  };

  const handleImportFromEditor = () => {
    // 从AI PRD编辑器导入的逻辑
    console.log('从编辑器导入');
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-6 space-y-6">
        {/* 页面标题区域 */}
        <PageHeader
          timeFilter={timeFilter}
          onTimeFilterChange={setTimeFilter}
        />

        {/* 统计面板区域 */}
        <StatsPanel stats={reviewStats} />

        {/* 评审列表区域 */}
        <ReviewList
          documents={reviewDocuments}
          expandedDocuments={expandedReviewDocuments}
          onToggleExpansion={handleToggleExpansion}
          onStartReview={handleStartReview}
          onContinueReview={handleContinueReview}
          onViewResults={handleViewResults}
          onRetryReview={handleRetryReview}
          onUploadDocument={handleUploadDocument}
          onImportFromEditor={handleImportFromEditor}
        />
      </div>

      {/* 文档上传弹窗 */}
      <DocumentUploadModal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        uploadedDocuments={uploadedDocuments}
        setUploadedDocuments={setUploadedDocuments}
        handleStartAnalysis={handleStartAnalysis}
      />
    </div>
  );
};

export default PRDUploadTab;
