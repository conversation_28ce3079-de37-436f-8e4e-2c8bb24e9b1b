import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { adaptAnalysisResultsToReport } from '../../services/reportDataAdapter';

// 导入子组件
import StickyOverview from './ReportComponents/StickyOverview';
import DimensionScoresSection from './ReportComponents/DimensionScoresSection';
import TabNavigation from './ReportComponents/TabNavigation';
import HighlightsTab from './ReportComponents/HighlightsTab';
import ChapterDetailsTab from './ReportComponents/ChapterDetailsTab';
import SuggestionsTab from './ReportComponents/SuggestionsTab';
import ReviewContextTab from './ReportComponents/ReviewContextTab';
import ActionButtons from './ReportComponents/ActionButtons';

const PRDResultsTab = ({ document, analysisResults, reviewResults, handleStartOptimization, onNavigateToReview }) => {
  // 状态管理
  const [activeTab, setActiveTab] = useState('highlights');
  const [showDetailedScores, setShowDetailedScores] = useState(false);
  const [adoptionStatus, setAdoptionStatus] = useState({});
  const [targetProblemId, setTargetProblemId] = useState(null); // 用于问题定位

  // 使用数据适配器转换数据
  const reportData = useMemo(() => {
    return adaptAnalysisResultsToReport(analysisResults, reviewResults);
  }, [analysisResults, reviewResults]);

  // 处理从评审确认页面返回后的问题定位
  const handleReturnFromReview = useCallback((problemId) => {
    console.log('从评审确认页面返回，定位问题:', problemId);
    // 使用与handleProblemNavigation完全相同的逻辑，确保效果一致
    if (problemId) {
      setTimeout(() => {
        // 1. 切换到章节评审详细Tab
        setActiveTab('chapters');

        // 2. 设置目标问题ID，用于自动展开
        setTargetProblemId(problemId);

        // 3. 使用与handleProblemNavigation相同的智能定位逻辑
        const executeSmartNavigation = () => {
          const doc = window.document;
          if (typeof window !== 'undefined' && doc && doc.getElementById) {
            const findAndScrollToElement = () => {
              const allChapterRows = doc.querySelectorAll('[id^="chapter-row-"]');

              if (allChapterRows.length === 0) {
                requestAnimationFrame(() => {
                  setTimeout(findAndScrollToElement, 50);
                });
                return;
              }

              // 查找包含指定问题ID的章节行
              const findChapterRowByProblemId = (problemId) => {
                const chapterRows = doc.querySelectorAll('[id^="chapter-row-"]');
                for (const row of chapterRows) {
                  const chapterKey = row.id.replace('chapter-row-', '');
                  if (chapterKey.includes(problemId)) {
                    return row;
                  }
                }
                return null;
              };

              // 找到包含目标问题的章节行并定位
              const chapterRowElement = findChapterRowByProblemId(problemId);

              if (chapterRowElement) {
                // 使用与handleProblemNavigation相同的滚动逻辑
                // 获取Tab内容区域（评审结果Tab的内容区域）
                const tabContentCandidates = [
                  doc.querySelector('[role="tabpanel"]'),
                  doc.querySelector('.tab-content'),
                  doc.querySelector('[data-tab-content]'),
                  chapterRowElement.closest('.overflow-x-auto')?.parentElement,
                  chapterRowElement.closest('.h-full'),
                  chapterRowElement.closest('[class*="overflow"]'),
                  doc.querySelector('.max-w-7xl')?.parentElement
                ].filter(Boolean);

                const tabContentArea = tabContentCandidates[0];

                if (tabContentArea) {
                  // 计算章节行相对于Tab内容区域的位置
                  const tabRect = tabContentArea.getBoundingClientRect();
                  const rowRect = chapterRowElement.getBoundingClientRect();
                  const scrollTop = tabContentArea.scrollTop + (rowRect.top - tabRect.top);

                  // 平滑滚动到章节行位置（Tab内容区域顶部）
                  tabContentArea.scrollTo({
                    top: scrollTop,
                    behavior: 'smooth'
                  });

                  // 立即验证滚动效果
                  const verifyScroll = () => {
                    const newScrollTop = tabContentArea.scrollTop;
                    const scrollSuccess = Math.abs(newScrollTop - scrollTop) < 10;

                    if (!scrollSuccess) {
                      // 备用方案：直接使用scrollIntoView滚动到顶部
                      chapterRowElement.scrollIntoView({
                        behavior: 'auto',
                        block: 'start'  // 滚动到Tab页面顶部
                      });
                    }
                  };

                  requestAnimationFrame(verifyScroll);
                } else {
                  // 备用方案：使用标准的scrollIntoView滚动到顶部
                  chapterRowElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'  // 滚动到Tab页面顶部
                  });
                }

                // 高亮章节行
                chapterRowElement.classList.add('highlight');
                setTimeout(() => {
                  chapterRowElement.classList.remove('highlight');
                }, 5000);
              }
            };
            findAndScrollToElement();
          }
        };
        executeSmartNavigation();
      }, 100);
    }
  }, [setActiveTab, setTargetProblemId]);

  // 初始化采纳状态
  useEffect(() => {
    if (reportData?.adoptionStatus) {
      setAdoptionStatus(reportData.adoptionStatus);
    }
  }, [reportData]);

  // 监听从评审确认页面返回的事件
  useEffect(() => {
    const handleReturnEvent = (event) => {
      const { problemId } = event.detail;
      console.log('收到返回事件，定位问题:', problemId);
      handleReturnFromReview(problemId);
    };

    window.addEventListener('returnFromReview', handleReturnEvent);
    return () => {
      window.removeEventListener('returnFromReview', handleReturnEvent);
    };
  }, [handleReturnFromReview]);

  // 如果没有数据，显示加载状态
  if (!reportData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载评审报告...</p>
        </div>
      </div>
    );
  }

  // 事件处理函数
  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
  };

  const handleToggleDetailedScores = () => {
    setShowDetailedScores(!showDetailedScores);
  };

  const handleAdoptionToggle = (problemId, newStatus) => {
    setAdoptionStatus(prev => ({
      ...prev,
      [problemId]: newStatus
    }));
  };

  const handleManualConfirm = () => {
    // 返回到评审确认页面
    if (onNavigateToReview) {
      onNavigateToReview();
    } else {
      alert('返回人工确认页面功能开发中...');
    }
  };

  // 处理导航到PRD章节
  const handleNavigateToPRD = (problemId, problem) => {
    console.log('导航到PRD章节:', problemId, problem);
    if (onNavigateToReview) {
      onNavigateToReview(problemId, problem);
    }
  };



  const handleAIOptimize = () => {
    // 跳转到AI优化页面
    if (handleStartOptimization) {
      handleStartOptimization();
    }
  };

  const handleExport = (format) => {
    // TODO: 实现导出功能
  };

  const handleShare = (userIds) => {
    // TODO: 实现分享功能
  };

  const handleCopyToEditor = (selectedProblems) => {
    // TODO: 实现复制到AI编辑器功能
  };

  const handleGenerateCards = (selectedProblems) => {
    // TODO: 实现生成建议卡片功能
  };

  // 问题定位功能 - 复刻HTML版本的逻辑
  const handleProblemNavigation = (problemId) => {
    // 1. 切换到章节评审详细Tab
    setActiveTab('chapters');

    // 2. 设置目标问题ID，用于自动展开
    setTargetProblemId(problemId);

    // 3. 智能等待Tab渲染完成后立即定位
    const executeSmartNavigation = () => {
      // 确保document和getElementById可用
      const doc = window.document || document;
      if (typeof window !== 'undefined' && doc && doc.getElementById) {
        const findAndScrollToElement = () => {
          // 先检查章节行是否存在
          const doc = window.document || document;
          const allChapterRows = doc.querySelectorAll('[id^="chapter-row-"]');

          // 如果章节行还没渲染，等待一下再尝试
          if (allChapterRows.length === 0) {
            // 使用requestAnimationFrame等待下一帧，然后再尝试
            requestAnimationFrame(() => {
              setTimeout(findAndScrollToElement, 50); // 短暂等待确保渲染完成
            });
            return;
          }

          // 参考HTML版本的实现：先找到章节行，展开章节，然后定位到章节行并高亮
          const chapterRowElement = findChapterRowByProblemId(problemId);

          if (chapterRowElement) {

            // 获取Tab内容区域（评审结果Tab的内容区域）
            const doc = window.document || document;

            // 尝试多种方式查找Tab内容区域
            const tabContentCandidates = [
              doc.querySelector('[role="tabpanel"]'),
              doc.querySelector('.tab-content'),
              doc.querySelector('[data-tab-content]'),
              chapterRowElement.closest('.overflow-x-auto')?.parentElement,
              chapterRowElement.closest('.h-full'),
              chapterRowElement.closest('[class*="overflow"]'),
              doc.querySelector('.max-w-7xl')?.parentElement
            ].filter(Boolean);

            const tabContentArea = tabContentCandidates[0];

            if (tabContentArea) {

              // 计算章节行相对于Tab内容区域的位置
              const tabRect = tabContentArea.getBoundingClientRect();
              const rowRect = chapterRowElement.getBoundingClientRect();
              const scrollTop = tabContentArea.scrollTop + (rowRect.top - tabRect.top);

              // 平滑滚动到章节行位置（Tab内容区域顶部）
              tabContentArea.scrollTo({
                top: scrollTop,
                behavior: 'smooth'
              });

              // 立即验证滚动效果（无延迟）
              const verifyScroll = () => {
                const newScrollTop = tabContentArea.scrollTop;
                const scrollSuccess = Math.abs(newScrollTop - scrollTop) < 10;

                // 如果滚动失败，立即尝试备用方案
                if (!scrollSuccess) {
                  // 直接使用scrollIntoView（无动画，立即定位）
                  chapterRowElement.scrollIntoView({
                    behavior: 'auto',  // 改为auto，立即定位
                    block: 'start'
                  });
                }
              };

              // 使用requestAnimationFrame确保DOM更新后立即验证
              requestAnimationFrame(verifyScroll);

            } else {
              // 备用方案：使用标准的scrollIntoView
              chapterRowElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
              });
            }

            // 立即高亮章节行5秒
            chapterRowElement.classList.add('highlight');

            // 5秒后移除高亮
            setTimeout(() => {
              chapterRowElement.classList.remove('highlight');
            }, 5000);

          } else {
            // 章节行未找到，可能是DOM还没完全渲染
            // 这种情况下不执行任何操作，等待下次重试
          }
        };

        // 查找包含指定问题ID的章节行
        const findChapterRowByProblemId = (problemId) => {
          // 查找所有章节行
          const doc = window.document || document;
          const chapterRows = doc.querySelectorAll('[id^="chapter-row-"]');

          for (const row of chapterRows) {
            const chapterKey = row.id.replace('chapter-row-', '');
            // 检查章节key是否包含问题ID
            if (chapterKey.includes(problemId)) {
              return row;
            }
          }

          return null;
        };

        findAndScrollToElement();
      }
    };

    // 执行智能导航，等待DOM渲染完成
    executeSmartNavigation();
  };

  // 渲染Tab内容
  const renderTabContent = () => {
    switch (activeTab) {
      case 'highlights':
        return (
          <HighlightsTab
            documentInfo={reportData.documentInfo}
            statistics={reportData.statistics}
            highlights={reportData.highlights}
            onProblemNavigation={handleProblemNavigation}
          />
        );
      case 'chapters':
        return (
          <ChapterDetailsTab
            chapters={reportData.chapters}
            problems={reportData.problems}
            adoptionStatus={adoptionStatus}
            onAdoptionToggle={handleAdoptionToggle}
            targetProblemId={targetProblemId}
            onTargetReached={() => setTargetProblemId(null)}
            onNavigateToPRD={handleNavigateToPRD}
          />
        );
      case 'suggestions':
        return (
          <SuggestionsTab
            problems={reportData.problems}
            adoptionStatus={adoptionStatus}
            onAdoptionToggle={handleAdoptionToggle}
            onCopyToEditor={handleCopyToEditor}
            onGenerateCards={handleGenerateCards}
            onProblemNavigation={handleProblemNavigation}
          />
        );
      case 'context':
        return (
          <ReviewContextTab
            documentInfo={reportData.documentInfo}
            statistics={reportData.statistics}
            chapters={reportData.chapters}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8 bg-gray-50 min-h-full">
        {/* 粘性概览 */}
        <StickyOverview
          documentInfo={reportData.documentInfo}
          statistics={reportData.statistics}
        />

        {/* 维度评分分析 */}
        <DimensionScoresSection
          dimensionScores={reportData.dimensionScores}
          problems={reportData.problems}
          showDetailedScores={showDetailedScores}
          onToggleDetailedScores={handleToggleDetailedScores}
          onProblemNavigation={handleProblemNavigation}
        />

        {/* Tab导航和内容 */}
        <section className="mb-5">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <TabNavigation
              activeTab={activeTab}
              onTabChange={handleTabChange}
            />

            {/* Tab内容区域 */}
            <div className="tab-content-area">
              {renderTabContent()}
            </div>
          </div>
        </section>

        {/* 全局操作按钮 */}
        <ActionButtons
          onManualConfirm={handleManualConfirm}
          onAIOptimize={handleAIOptimize}
          onExport={handleExport}
          onShare={handleShare}
        />
      </div>
    </div>
  );
};

export default PRDResultsTab;
