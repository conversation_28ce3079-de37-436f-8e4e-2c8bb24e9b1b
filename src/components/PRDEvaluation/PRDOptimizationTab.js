import React, { useState, useEffect, useCallback } from 'react';

const PRDOptimizationTab = ({ document, analysisResults, reviewResults, handleCompleteOptimization }) => {
  console.log('PRDOptimizationTab 渲染，参数:', {
    document: document?.title,
    analysisResults: analysisResults?.totalIssues,
    reviewResults: reviewResults?.acceptedSuggestions
  });

  const [optimizationData, setOptimizationData] = useState(null);
  const [isRunningOptimization, setIsRunningOptimization] = useState(false);
  const [optimizationProgress, setOptimizationProgress] = useState(0);
  const [selectedOptimization, setSelectedOptimization] = useState("all");


  // 生成优化数据
  const generateOptimizationData = useCallback(async () => {
    setIsRunningOptimization(true);
    setOptimizationProgress(0);

    // 模拟优化分析过程
    const steps = [
      "分析评审结果...",
      "计算采纳率指标...",
      "生成规则优化建议...",
      "预测改进效果...",
      "生成优化报告..."
    ];

    for (let i = 0; i < steps.length; i++) {
      console.log(`优化步骤 ${i + 1}: ${steps[i]}`);
      await new Promise(resolve => setTimeout(resolve, 800));
      setOptimizationProgress(((i + 1) / steps.length) * 100);
    }

    // 生成优化数据
    const data = {
      documentId: document?.id,
      optimizationTimestamp: new Date().toISOString(),
      humanAdoptionRate: Math.round((reviewResults.acceptedSuggestions / reviewResults.totalIssues) * 100),
      aiAccuracyRate: 87,
      suggestionModificationRate: 23,
      ruleOptimizationSuggestions: generateRuleOptimizations(),
      intelligentSuggestions: generateIntelligentSuggestions(),
      performanceMetrics: {
        beforeOptimization: analysisResults.overallScore,
        afterOptimization: analysisResults.overallScore + 12,
        improvementPotential: 15,
        confidenceLevel: 89
      }
    };

    console.log('优化数据生成完成:', data);
    setOptimizationData(data);
    setIsRunningOptimization(false);
  }, [document, reviewResults, analysisResults]);

  // 启动时自动生成优化数据
  useEffect(() => {
    if (reviewResults && analysisResults && !optimizationData) {
      console.log('开始生成优化数据...');
      generateOptimizationData();
    }
  }, [reviewResults, analysisResults, optimizationData, generateOptimizationData]);

  // 生成规则优化建议
  const generateRuleOptimizations = () => {
    return [
      {
        id: "rule-opt-1",
        ruleId: "rule-001",
        ruleName: "功能完整性检查",
        currentVersion: "v1.2",
        suggestedChanges: "增加对用户故事格式的检查，提高功能描述的标准化程度",
        reason: "当前规则对功能描述的检查过于宽泛，导致一些不规范的描述被遗漏",
        expectedImprovement: "预计可提高功能完整性检查准确率15%",
        priority: "high",
        status: "pending",
        testResults: [],
        confidence: 92
      },
      {
        id: "rule-opt-2",
        ruleId: "rule-002",
        ruleName: "场景完整性检查",
        currentVersion: "v1.1",
        suggestedChanges: "优化异常场景识别算法，增加边缘案例检测",
        reason: "用户反馈显示该规则对复杂场景的识别能力不足",
        expectedImprovement: "预计可提高场景覆盖率20%",
        priority: "medium",
        status: "pending",
        testResults: [],
        confidence: 85
      },
      {
        id: "rule-opt-3",
        ruleId: "rule-003",
        ruleName: "质量标准检查",
        currentVersion: "v1.0",
        suggestedChanges: "更新性能指标阈值，适应当前行业标准",
        reason: "当前阈值设置过于保守，导致误报率较高",
        expectedImprovement: "预计可降低误报率30%",
        priority: "low",
        status: "pending",
        testResults: [],
        confidence: 78
      }
    ];
  };

  // 生成智能建议
  const generateIntelligentSuggestions = () => {
    return [
      {
        id: "suggestion-1",
        type: "template",
        title: "推荐使用标准PRD模板",
        content: "基于评审结果，建议采用汽车行业标准PRD模板，可提高文档规范性",
        priority: "high",
        confidence: 92,
        estimatedImprovement: 18,
        status: "pending",
        category: "structure",
        actionable: true,
        relatedSections: ["section-1", "section-3"]
      },
      {
        id: "suggestion-2",
        type: "content",
        title: "补充用户画像描述",
        content: "建议在用户需求章节增加详细的用户画像和使用场景描述",
        priority: "medium",
        confidence: 85,
        estimatedImprovement: 12,
        status: "pending",
        category: "content",
        actionable: true,
        relatedSections: ["section-2"]
      },
      {
        id: "suggestion-3",
        type: "quality",
        title: "增加验收标准",
        content: "为每个功能点添加明确的验收标准和测试用例",
        priority: "medium",
        confidence: 78,
        estimatedImprovement: 15,
        status: "pending",
        category: "quality",
        actionable: true,
        relatedSections: ["section-3", "section-4"]
      }
    ];
  };

  // 运行规则优化
  const handleRunRuleOptimization = () => {
    setSelectedOptimization("rules");
    console.log('切换到规则优化页面');
  };

  // 导出优化建议
  const handleExportOptimization = () => {
    try {
      const exportData = {
        documentTitle: document?.title || 'PRD文档',
        timestamp: new Date().toISOString(),
        optimizationData
      };

      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);

      const downloadLink = window.document.createElement('a');
      downloadLink.href = url;
      downloadLink.download = `PRD优化建议_${document?.title || 'document'}_${new Date().toISOString().split('T')[0]}.json`;

      window.document.body.appendChild(downloadLink);
      downloadLink.click();
      window.document.body.removeChild(downloadLink);
      URL.revokeObjectURL(url);

      console.log('优化建议导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      alert('导出失败，请重试');
    }
  };

  // 生成新版本
  const handleGenerateNewVersion = () => {
    alert('生成新版本功能开发中...');
  };

  // 处理规则优化状态变更
  const handleRuleStatusChange = (ruleId, newStatus) => {
    setOptimizationData(prev => ({
      ...prev,
      ruleOptimizationSuggestions: prev.ruleOptimizationSuggestions.map(rule =>
        rule.id === ruleId ? { ...rule, status: newStatus } : rule
      )
    }));
  };

  // 处理智能建议状态变更
  const handleSuggestionStatusChange = (suggestionId, newStatus) => {
    setOptimizationData(prev => ({
      ...prev,
      intelligentSuggestions: prev.intelligentSuggestions.map(suggestion =>
        suggestion.id === suggestionId ? { ...suggestion, status: newStatus } : suggestion
      )
    }));
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">
          自检优化 - {document?.title || "智能驾驶系统PRD"}
        </h2>
        <div className="flex space-x-2">
          <button
            onClick={handleExportOptimization}
            className="border border-gray-300 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-50 transition-colors flex items-center"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            导出优化建议
          </button>
          <button
            onClick={handleGenerateNewVersion}
            className="border border-gray-300 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-50 transition-colors flex items-center"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            生成新版本
          </button>
        </div>
      </div>

      {/* 优化进度 */}
      {isRunningOptimization && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">自检优化进度</span>
              <span className="text-sm text-gray-500">{Math.floor(optimizationProgress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-orange-500 h-3 rounded-full transition-all duration-300"
                style={{ width: `${optimizationProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600">正在分析评审结果并生成优化建议...</p>
          </div>
        </div>
      )}

      {/* 加载状态 */}
      {!optimizationData && !isRunningOptimization && (
        <div className="flex items-center justify-center h-64">
          <div className="w-8 h-8 border-4 border-orange-500 border-t-transparent rounded-full animate-spin"></div>
          <span className="ml-2 text-gray-600">正在生成优化建议...</span>
        </div>
      )}

      {/* PRD自检概览 */}
      {optimizationData && (
        <>
          <div className="grid grid-cols-4 gap-4">
            <div className="bg-white border border-blue-200 rounded-lg p-6 text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {optimizationData.humanAdoptionRate}%
              </div>
              <div className="text-sm text-blue-700">人工采纳率</div>
              <div className="text-xs text-blue-600 mt-1">
                {reviewResults?.acceptedSuggestions}/{reviewResults?.totalIssues} 建议被采纳
              </div>
            </div>

            <div className="bg-white border border-green-200 rounded-lg p-6 text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {optimizationData.aiAccuracyRate}%
              </div>
              <div className="text-sm text-green-700">AI准确率</div>
              <div className="text-xs text-green-600 mt-1">
                基于历史数据评估
              </div>
            </div>

            <div className="bg-white border border-orange-200 rounded-lg p-6 text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">
                {optimizationData.suggestionModificationRate}%
              </div>
              <div className="text-sm text-orange-700">建议修改率</div>
              <div className="text-xs text-orange-600 mt-1">
                需要优化的建议比例
              </div>
            </div>

            <div className="bg-white border border-purple-200 rounded-lg p-6 text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">
                +{optimizationData.performanceMetrics.improvementPotential}
              </div>
              <div className="text-sm text-purple-700">改进潜力</div>
              <div className="text-xs text-purple-600 mt-1">
                预期质量提升分数
              </div>
            </div>
          </div>

          {/* 优化建议选项卡 */}
          <div className="bg-white border border-gray-200 rounded-lg">
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-6">
                <button
                  onClick={() => setSelectedOptimization("all")}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    selectedOptimization === "all"
                      ? 'border-orange-500 text-orange-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  全部建议
                </button>
                <button
                  onClick={() => setSelectedOptimization("rules")}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    selectedOptimization === "rules"
                      ? 'border-orange-500 text-orange-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  规则优化
                </button>
                <button
                  onClick={() => setSelectedOptimization("intelligent")}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    selectedOptimization === "intelligent"
                      ? 'border-orange-500 text-orange-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  智能建议
                </button>
              </nav>
            </div>

            <div className="p-6">
              {/* 全部建议 */}
              {selectedOptimization === "all" && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* 规则优化概览 */}
                    <div className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="font-medium">规则优化建议</h3>
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                          {optimizationData.ruleOptimizationSuggestions.length} 条
                        </span>
                      </div>
                      <div className="space-y-3">
                        {optimizationData.ruleOptimizationSuggestions.slice(0, 3).map((rule) => (
                          <div key={rule.id} className="border border-gray-200 rounded p-3">
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-medium text-sm">{rule.ruleName}</span>
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                rule.priority === 'high' ? 'bg-red-100 text-red-800' :
                                rule.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-blue-100 text-blue-800'
                              }`}>
                                {rule.priority === 'high' ? '高' : rule.priority === 'medium' ? '中' : '低'}
                              </span>
                            </div>
                            <p className="text-xs text-gray-600">{rule.reason}</p>
                          </div>
                        ))}
                        <button
                          onClick={handleRunRuleOptimization}
                          className="w-full border border-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-50 transition-colors"
                        >
                          查看全部规则优化
                        </button>
                      </div>
                    </div>

                    {/* 智能建议概览 */}
                    <div className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="font-medium">智能建议</h3>
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                          {optimizationData.intelligentSuggestions.length} 条
                        </span>
                      </div>
                      <div className="space-y-3">
                        {optimizationData.intelligentSuggestions.map((suggestion) => (
                          <div key={suggestion.id} className="border border-gray-200 rounded p-3">
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-medium text-sm">{suggestion.title}</span>
                              <div className="flex items-center space-x-2">
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  suggestion.priority === 'high' ? 'bg-red-100 text-red-800' :
                                  suggestion.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-blue-100 text-blue-800'
                                }`}>
                                  {suggestion.priority === 'high' ? '高' : suggestion.priority === 'medium' ? '中' : '低'}
                                </span>
                                <span className="text-xs text-gray-500">+{suggestion.estimatedImprovement}分</span>
                              </div>
                            </div>
                            <p className="text-xs text-gray-600">{suggestion.content}</p>
                            <div className="mt-2 flex items-center justify-between">
                              <span className="text-xs text-gray-500">置信度: {suggestion.confidence}%</span>
                              <button
                                onClick={() => handleSuggestionStatusChange(suggestion.id, 'accepted')}
                                className="text-xs text-green-600 hover:text-green-800"
                              >
                                采纳建议
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 规则优化详细页面 */}
              {selectedOptimization === "rules" && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">规则优化建议</h3>
                    <button
                      onClick={handleRunRuleOptimization}
                      className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded transition-colors"
                    >
                      运行规则优化
                    </button>
                  </div>
                  {optimizationData.ruleOptimizationSuggestions.map((rule) => (
                    <div key={rule.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h4 className="font-medium">{rule.ruleName}</h4>
                            <span className="text-sm text-gray-500">({rule.currentVersion})</span>
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              rule.priority === 'high' ? 'bg-red-100 text-red-800' :
                              rule.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-blue-100 text-blue-800'
                            }`}>
                              {rule.priority === 'high' ? '高优先级' : rule.priority === 'medium' ? '中优先级' : '低优先级'}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{rule.reason}</p>
                          <div className="bg-blue-50 p-3 rounded mb-2">
                            <h5 className="text-sm font-medium text-blue-900 mb-1">建议修改:</h5>
                            <p className="text-sm text-blue-800">{rule.suggestedChanges}</p>
                          </div>
                          <div className="bg-green-50 p-3 rounded">
                            <h5 className="text-sm font-medium text-green-900 mb-1">预期效果:</h5>
                            <p className="text-sm text-green-800">{rule.expectedImprovement}</p>
                          </div>
                        </div>
                        <div className="ml-4 flex flex-col space-y-2">
                          <span className="text-xs text-gray-500">置信度: {rule.confidence}%</span>
                          <button
                            onClick={() => handleRuleStatusChange(rule.id, 'accepted')}
                            className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm transition-colors"
                          >
                            采纳
                          </button>
                          <button
                            onClick={() => handleRuleStatusChange(rule.id, 'rejected')}
                            className="border border-gray-300 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-50 transition-colors"
                          >
                            拒绝
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* 智能建议详细页面 */}
              {selectedOptimization === "intelligent" && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">智能建议</h3>
                  {optimizationData.intelligentSuggestions.map((suggestion) => (
                    <div key={suggestion.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h4 className="font-medium">{suggestion.title}</h4>
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              suggestion.type === 'template' ? 'bg-purple-100 text-purple-800' :
                              suggestion.type === 'content' ? 'bg-blue-100 text-blue-800' :
                              'bg-green-100 text-green-800'
                            }`}>
                              {suggestion.category}
                            </span>
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              suggestion.priority === 'high' ? 'bg-red-100 text-red-800' :
                              suggestion.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-blue-100 text-blue-800'
                            }`}>
                              {suggestion.priority === 'high' ? '高优先级' : suggestion.priority === 'medium' ? '中优先级' : '低优先级'}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mb-3">{suggestion.content}</p>
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span>预期提升: +{suggestion.estimatedImprovement}分</span>
                            <span>置信度: {suggestion.confidence}%</span>
                            <span>相关章节: {suggestion.relatedSections.join(', ')}</span>
                          </div>
                        </div>
                        <div className="ml-4 flex flex-col space-y-2">
                          <button
                            onClick={() => handleSuggestionStatusChange(suggestion.id, 'accepted')}
                            className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm transition-colors"
                          >
                            采纳
                          </button>
                          <button
                            onClick={() => handleSuggestionStatusChange(suggestion.id, 'rejected')}
                            className="border border-gray-300 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-50 transition-colors"
                          >
                            拒绝
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* 完成优化按钮 */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                自检优化已完成，您可以根据建议进行相应的改进
              </div>
              <button
                onClick={handleCompleteOptimization}
                className="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-md transition-colors flex items-center"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                完成优化
              </button>
            </div>
          </div>
        </>
      )}
      </div>
    </div>
  );
};

export default PRDOptimizationTab;
