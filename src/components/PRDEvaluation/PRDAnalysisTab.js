import React, { useState, useEffect, useCallback } from 'react';

const PRDAnalysisTab = ({ document, handleAnalysisComplete, handleEnterReview }) => {
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState("");
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [analysisResults, setAnalysisResults] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [stepDetails, setStepDetails] = useState("");

  // 分析步骤定义
  const analysisSteps = [
    { 
      id: 1, 
      name: "文档解析", 
      description: "解析文档结构和内容", 
      duration: 2000,
      details: [
        "正在解析文档格式...",
        "提取文档章节结构...",
        "识别关键内容区域...",
        "构建文档语义树..."
      ]
    },
    { 
      id: 2, 
      name: "内容分析", 
      description: "分析需求完整性和逻辑性", 
      duration: 3000,
      details: [
        "分析需求完整性...",
        "检查逻辑一致性...",
        "评估内容质量...",
        "识别潜在缺陷..."
      ]
    },
    { 
      id: 3, 
      name: "规则匹配", 
      description: "应用评审规则进行问题检测", 
      duration: 4000,
      details: [
        "加载评审规则库...",
        "执行规则匹配...",
        "检测违规项目...",
        "计算置信度分数..."
      ]
    },
    { 
      id: 4, 
      name: "问题分类", 
      description: "对发现的问题进行分类和优先级排序", 
      duration: 2000,
      details: [
        "问题类型分类...",
        "优先级评估...",
        "影响范围分析...",
        "修复难度评估..."
      ]
    },
    { 
      id: 5, 
      name: "建议生成", 
      description: "生成修改建议和优化方案", 
      duration: 3000,
      details: [
        "生成修改建议...",
        "制定优化方案...",
        "评估改进效果...",
        "准备实施指导..."
      ]
    },
    {
      id: 6,
      name: "报告草稿生成",
      description: "生成初步分析报告草稿",
      duration: 1000,
      details: [
        "汇总分析结果...",
        "生成评分草稿...",
        "准备可视化数据...",
        "完成报告草稿..."
      ]
    }
  ];

  const startAnalysis = useCallback(async () => {
    console.log('开始智能分析...');
    setIsAnalyzing(true);
    setAnalysisProgress(0);
    setCurrentStepIndex(0);
    setStepDetails("");

    let totalProgress = 0;
    const stepProgress = 100 / analysisSteps.length;

    for (let stepIndex = 0; stepIndex < analysisSteps.length; stepIndex++) {
      const step = analysisSteps[stepIndex];
      console.log(`执行步骤 ${stepIndex + 1}: ${step.name}`);
      setCurrentStep(step.name);
      setCurrentStepIndex(stepIndex);

      // 模拟分析过程中的详细步骤
      const stepDuration = step.duration;
      const detailSteps = step.details.length;
      const detailDuration = stepDuration / detailSteps;

      for (let detailIndex = 0; detailIndex < detailSteps; detailIndex++) {
        setStepDetails(step.details[detailIndex]);
        console.log(`  - ${step.details[detailIndex]}`);

        // 每个详细步骤的进度更新 - 更平滑的进度
        const updateCount = 10; // 减少更新次数，提高性能
        const detailProgress = detailDuration / updateCount;

        for (let i = 0; i < updateCount; i++) {
          await new Promise(resolve => setTimeout(resolve, detailProgress));
          const currentDetailProgress = (detailIndex + (i + 1) / updateCount) / detailSteps * stepProgress;
          const newProgress = Math.min(totalProgress + currentDetailProgress, 100);
          setAnalysisProgress(newProgress);
          console.log(`进度更新: ${newProgress.toFixed(1)}%`);
        }
      }

      totalProgress += stepProgress;
      // 确保每个步骤完成后进度正确
      setAnalysisProgress(Math.min(totalProgress, 100));
    }

    // 确保最终进度为100%
    setAnalysisProgress(100);
    console.log('所有分析步骤完成，进度: 100%');

    // 短暂延迟，让用户看到100%的进度
    await new Promise(resolve => setTimeout(resolve, 500));

    // 生成分析结果
    console.log('生成分析结果...');
    const results = generateAnalysisResults(document);
    setAnalysisResults(results);
    setIsAnalyzing(false);
    setCurrentStep("分析完成");
    setStepDetails("智能分析已完成，生成了报告草稿，请进入评审确认");

    console.log('分析完成，结果:', results);

    // 通知父组件分析完成
    handleAnalysisComplete(results);
  }, [handleAnalysisComplete]);

  // 开始分析
  useEffect(() => {
    console.log('智能分析页面 useEffect:', { document: document?.title, isAnalyzing });
    if (document && !isAnalyzing) {
      console.log('开始执行分析...');
      startAnalysis();
    }
  }, [document, isAnalyzing, startAnalysis]);

  // 生成模拟分析结果
  const generateAnalysisResults = (doc) => {
    // 根据文档标题和内容生成更真实的问题
    const baseIssues = [
      {
        id: "issue-1",
        type: "error",
        title: "缺少关键功能描述",
        description: "产品功能模块缺少详细的功能描述和用户场景，无法准确评估实现复杂度",
        section: "section-3",
        sectionTitle: "3. 产品功能",
        suggestion: "建议补充详细的功能描述，包括用户操作流程、预期结果和异常处理机制",
        priority: "high",
        confidence: 92,
        rule: "功能完整性检查",
        ruleId: "rule-001",
        impact: "高",
        category: "功能性"
      },
      {
        id: "issue-2",
        type: "warning",
        title: "用户场景描述不够具体",
        description: "用户场景描述过于简略，缺少极端情况和边缘案例的考虑，可能导致开发遗漏",
        section: "section-2",
        sectionTitle: "2. 用户需求",
        suggestion: "建议增加更多具体的用户场景，包括异常情况的处理和用户行为分析",
        priority: "medium",
        confidence: 78,
        rule: "场景完整性检查",
        ruleId: "rule-002",
        impact: "中",
        category: "用户体验"
      },
      {
        id: "issue-3",
        type: "suggestion",
        title: "建议增加性能指标",
        description: "缺少明确的性能指标和质量标准，难以进行系统性能评估和优化",
        section: "section-4",
        sectionTitle: "4. 非功能需求",
        suggestion: "建议添加具体的性能指标，如响应时间（<200ms）、并发用户数（>1000）、可用性（>99.9%）等",
        priority: "low",
        confidence: 65,
        rule: "质量标准检查",
        ruleId: "rule-003",
        impact: "低",
        category: "性能"
      },
      {
        id: "issue-4",
        type: "error",
        title: "安全需求描述不完整",
        description: "缺少数据安全、用户隐私保护等关键安全需求的详细描述",
        section: "section-4",
        sectionTitle: "4. 非功能需求",
        suggestion: "建议补充数据加密、访问控制、隐私保护等安全需求的具体实现方案",
        priority: "high",
        confidence: 88,
        rule: "安全性检查",
        ruleId: "rule-004",
        impact: "高",
        category: "安全性"
      },
      {
        id: "issue-5",
        type: "warning",
        title: "接口定义不够清晰",
        description: "API接口定义缺少详细的参数说明和错误码定义",
        section: "section-5",
        sectionTitle: "5. 技术架构",
        suggestion: "建议补充完整的API文档，包括请求参数、响应格式、错误码和调用示例",
        priority: "medium",
        confidence: 82,
        rule: "接口规范检查",
        ruleId: "rule-005",
        impact: "中",
        category: "技术架构"
      }
    ];

    // 随机选择3-5个问题，模拟真实的分析结果
    const selectedIssues = baseIssues.slice(0, 3 + Math.floor(Math.random() * 3));

    // 计算动态评分
    const errorCount = selectedIssues.filter(i => i.type === "error").length;
    const warningCount = selectedIssues.filter(i => i.type === "warning").length;
    const suggestionCount = selectedIssues.filter(i => i.type === "suggestion").length;
    
    // 根据问题数量和类型计算总体评分
    const baseScore = 90;
    const errorPenalty = errorCount * 15;
    const warningPenalty = warningCount * 8;
    const suggestionPenalty = suggestionCount * 3;
    const overallScore = Math.max(baseScore - errorPenalty - warningPenalty - suggestionPenalty, 30);

    return {
      documentId: doc.id,
      analysisTimestamp: new Date().toISOString(),
      overallScore,
      totalIssues: selectedIssues.length,
      errorCount,
      warningCount,
      suggestionCount,
      issues: selectedIssues,
      sectionScores: {
        "section-1": Math.max(85 - errorCount * 5, 50),
        "section-2": Math.max(72 - warningCount * 3, 40),
        "section-3": Math.max(68 - errorCount * 8, 30),
        "section-4": Math.max(80 - (errorCount + warningCount) * 4, 35),
        "section-5": Math.max(90 - suggestionCount * 2, 60)
      },
      completenessScore: Math.max(75 - errorCount * 10, 30),
      clarityScore: Math.max(80 - warningCount * 5, 40),
      consistencyScore: Math.max(82 - (errorCount + warningCount) * 3, 45),
      feasibilityScore: Math.max(78 - errorCount * 6, 35),
      analysisMetrics: {
        documentsAnalyzed: 1,
        rulesApplied: 15,
        processingTime: "2.3秒",
        confidenceLevel: 87
      }
    };
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-6 space-y-6">
      {/* 分析进度 */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          {isAnalyzing ? (
            <>
              <div className="w-5 h-5 mr-2 border-2 border-orange-500 border-t-transparent rounded-full animate-spin"></div>
              智能分析进行中
            </>
          ) : (
            <>
              <svg className="w-5 h-5 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              智能分析已完成
            </>
          )}
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">分析进度</span>
            <span className={`text-sm font-semibold ${
              analysisProgress === 100 ? 'text-green-600' : 'text-orange-600'
            }`}>
              {Math.round(analysisProgress)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-4 shadow-inner">
            <div
              className={`h-4 rounded-full transition-all duration-500 ease-out ${
                analysisProgress === 100 ? 'bg-green-500' : 'bg-orange-500'
              }`}
              style={{ width: `${analysisProgress}%` }}
            >
              {analysisProgress > 10 && (
                <div className="h-full bg-gradient-to-r from-transparent to-white opacity-30 rounded-full"></div>
              )}
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              {isAnalyzing ? (
                <div className="w-4 h-4 border-2 border-orange-500 border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              )}
              <span className="text-sm font-medium text-gray-700">{currentStep}</span>
            </div>
            {stepDetails && isAnalyzing && (
              <div className="ml-6 text-xs text-gray-500 italic">
                {stepDetails}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 分析步骤 */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">分析步骤</h3>
        
        <div className="space-y-4">
          {analysisSteps.map((step, index) => {
            const isCompleted = index < currentStepIndex || (!isAnalyzing && analysisResults);
            const isCurrent = index === currentStepIndex && isAnalyzing;

            return (
              <div key={step.id} className={`border rounded-lg p-4 transition-all duration-300 ${
                isCurrent ? 'border-orange-500 bg-orange-50' :
                isCompleted ? 'border-green-500 bg-green-50' :
                'border-gray-200 bg-gray-50'
              }`}>
                <div className="flex items-start space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                    isCompleted ? 'bg-green-500 text-white' :
                    isCurrent ? 'bg-orange-500 text-white' :
                    'bg-gray-200 text-gray-500'
                  }`}>
                    {isCompleted ? (
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    ) : isCurrent ? (
                      <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                    ) : (
                      <span className="text-sm font-medium">{step.id}</span>
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div className="font-medium text-gray-900">{step.name}</div>
                      <div className="text-xs text-gray-400">
                        {isCompleted ? '已完成' : isCurrent ? '进行中' : '等待中'}
                      </div>
                    </div>
                    <div className="text-sm text-gray-500 mt-1">{step.description}</div>
                    
                    {/* 显示当前步骤的详细信息 */}
                    {isCurrent && stepDetails && (
                      <div className="mt-2 p-2 bg-white rounded border border-orange-200">
                        <div className="text-xs text-orange-600 font-medium">正在执行:</div>
                        <div className="text-xs text-gray-600 mt-1">{stepDetails}</div>
                      </div>
                    )}
                    
                    {/* 显示已完成步骤的结果 */}
                    {isCompleted && !isCurrent && (
                      <div className="mt-2 text-xs text-green-600">
                        ✓ 步骤已完成，用时 {(step.duration / 1000).toFixed(1)}s
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* 分析结果预览 */}
      {analysisResults && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">分析结果预览</h3>
            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
              分析完成
            </span>
          </div>

          {/* 总体评分 */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <div className="text-3xl font-bold text-orange-600">
                {analysisResults.overallScore}
              </div>
              <div className="text-sm text-gray-600">总体评分</div>
              <div className="text-xs text-gray-500 mt-1">
                {analysisResults.overallScore >= 80 ? '优秀' :
                 analysisResults.overallScore >= 60 ? '良好' : '需改进'}
              </div>
            </div>
            <div className="text-center p-3 bg-red-50 rounded-lg">
              <div className="text-3xl font-bold text-red-600">
                {analysisResults.errorCount}
              </div>
              <div className="text-sm text-gray-600">错误</div>
              <div className="text-xs text-gray-500 mt-1">必须修复</div>
            </div>
            <div className="text-center p-3 bg-yellow-50 rounded-lg">
              <div className="text-3xl font-bold text-yellow-600">
                {analysisResults.warningCount}
              </div>
              <div className="text-sm text-gray-600">警告</div>
              <div className="text-xs text-gray-500 mt-1">建议修复</div>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-3xl font-bold text-blue-600">
                {analysisResults.suggestionCount}
              </div>
              <div className="text-sm text-gray-600">建议</div>
              <div className="text-xs text-gray-500 mt-1">可选优化</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-3xl font-bold text-green-600">
                {analysisResults.analysisMetrics.confidenceLevel}%
              </div>
              <div className="text-sm text-gray-600">置信度</div>
              <div className="text-xs text-gray-500 mt-1">分析准确性</div>
            </div>
          </div>

          {/* 主要问题 */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium">主要问题</h4>
              <span className="text-xs text-gray-500">
                显示前 {Math.min(3, analysisResults.issues.length)} 个问题
              </span>
            </div>
            <div className="space-y-3">
              {analysisResults.issues.slice(0, 3).map((issue) => (
                <div key={issue.id} className={`border-l-4 p-3 rounded-r-lg ${
                  issue.type === 'error' ? 'border-red-500 bg-red-50' :
                  issue.type === 'warning' ? 'border-yellow-500 bg-yellow-50' :
                  'border-blue-500 bg-blue-50'
                }`}>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className={`inline-flex px-2 py-0.5 text-xs font-semibold rounded-full ${
                          issue.type === 'error' ? 'bg-red-100 text-red-800' :
                          issue.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-blue-100 text-blue-800'
                        }`}>
                          {issue.type === 'error' ? '错误' : issue.type === 'warning' ? '警告' : '建议'}
                        </span>
                        <span className="text-xs px-2 py-0.5 bg-gray-200 rounded-full">
                          {issue.sectionTitle}
                        </span>
                      </div>
                      <div className="font-medium text-gray-900 text-sm mb-1">{issue.title}</div>
                      <div className="text-xs text-gray-600 mb-2">{issue.description}</div>
                      <div className="text-xs text-blue-600">💡 {issue.suggestion}</div>
                    </div>
                    <div className="ml-3 text-right">
                      <div className="text-xs text-gray-500">置信度</div>
                      <div className="text-sm font-medium text-gray-700">{issue.confidence}%</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* 分析统计 */}
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <div className="text-xs text-gray-600 space-y-1">
                <div>📊 分析统计: 应用了 {analysisResults.analysisMetrics.rulesApplied} 条评审规则</div>
                <div>⏱️ 处理时间: {analysisResults.analysisMetrics.processingTime}</div>
                <div>🎯 整体置信度: {analysisResults.analysisMetrics.confidenceLevel}%</div>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-2">
            <button className="border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors">
              查看详细报告
            </button>
            <button
              className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md transition-colors"
              onClick={() => handleEnterReview()}
            >
              进入评审确认
            </button>
          </div>
        </div>
      )}
      </div>
    </div>
  );
};

export default PRDAnalysisTab;
