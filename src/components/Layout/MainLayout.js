import React, { useState, useEffect, useCallback, useMemo } from 'react';
import Sidebar from './Sidebar';
import Header from './Header';
import PRDEditor from '../PRDEditor/PRDEditor';
import PRDEvaluationPage from '../PRDEvaluation';
import TableRenderingTestSimple from '../TableRenderingTestSimple';
import MilkdownTableTest from '../MilkdownTableTest/MilkdownTableTest';
import TipTapTableTest from '../TipTapEditor/TipTapTableTest';

const MainLayout = () => {
  const [activeModule, setActiveModule] = useState('dashboard');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [currentTab, setCurrentTab] = useState(null);
  const [moduleTitle, setModuleTitle] = useState(null);
  const [user] = useState({
    name: '管理员',
    role: 'TTPO',
    avatar: null
  });

  // 路由映射配置
  const routeMap = useMemo(() => ({
    'dashboard': '/',
    'requirement-analysis': '/requirement-analysis',
    'requirement-management': '/requirement-management',
    'detail-analysis': '/detail-analysis',
    'prd-evaluation': '/prd-evaluation',
    'baseline-management': '/baseline-management',
    'business-knowledge': '/business-knowledge',
    'system-knowledge': '/system-knowledge',
    'requirement-rules': '/requirement-rules',
    'business-analysis': '/business-analysis',
    'template-management': '/template-management',
    'table-test': '/table-test',
    'milkdown-table-test': '/milkdown-table-test',
    'tiptap-table-test': '/tiptap-table-test'
  }), []);

  // 反向路由映射
  const pathToModuleMap = useMemo(() =>
    Object.fromEntries(
      Object.entries(routeMap).map(([module, path]) => [path, module])
    ), [routeMap]
  );

  // 更新URL路径
  const updateUrlPath = useCallback((moduleId) => {
    const path = routeMap[moduleId] || '/';
    window.history.pushState(null, null, path);
  }, [routeMap]);

  // 处理模块切换
  const handleModuleChange = (moduleId) => {
    setActiveModule(moduleId);
    updateUrlPath(moduleId);
    // 重置tab状态和模块标题
    setCurrentTab(null);
    setModuleTitle(null);
  };

  // 处理tab切换（用于PRD智能评估等模块）
  const handleTabChange = (tabId) => {
    setCurrentTab(tabId);
  };

  // 处理模块标题更新
  const handleModuleTitleChange = (title) => {
    setModuleTitle(title);
  };

  // 处理浏览器前进/后退
  const handlePopState = useCallback(() => {
    const currentPath = window.location.pathname;
    const moduleId = pathToModuleMap[currentPath] || 'dashboard';
    setActiveModule(moduleId);
  }, [pathToModuleMap]);

  // 初始化路由
  useEffect(() => {
    // 页面加载时根据URL设置当前模块
    const currentPath = window.location.pathname;
    const moduleId = pathToModuleMap[currentPath] || 'dashboard';
    setActiveModule(moduleId);

    // 监听浏览器前进/后退
    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [handlePopState, pathToModuleMap]);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const renderContent = () => {
    switch (activeModule) {
      case 'requirement-analysis':
        return <PRDEditor />;
      case 'dashboard':
        return (
          <div className="flex items-center justify-center h-full bg-gray-50">
            <div className="text-center">
              <div className="w-24 h-24 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
                <svg className="w-12 h-12 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-800 mb-2">欢迎使用 Intelli-req</h2>
              <p className="text-gray-600">智能需求管理平台</p>
              <p className="text-sm text-gray-500 mt-4">请从左侧菜单选择功能模块</p>
            </div>
          </div>
        );
      case 'requirement-management':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold mb-4">需求管理</h1>
            <div className="bg-white rounded-lg shadow p-6">
              <p className="text-gray-600">需求管理功能开发中...</p>
            </div>
          </div>
        );
      case 'detail-analysis':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold mb-4">详细分析</h1>
            <div className="bg-white rounded-lg shadow p-6">
              <p className="text-gray-600">详细分析功能开发中...</p>
            </div>
          </div>
        );
      case 'prd-evaluation':
        return (
          <PRDEvaluationPage
            onTabChange={handleTabChange}
            onModuleTitleChange={handleModuleTitleChange}
          />
        );
      case 'baseline-management':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold mb-4">任务设计与分发</h1>
            <div className="bg-white rounded-lg shadow p-6">
              <p className="text-gray-600">任务设计与分发功能开发中...</p>
            </div>
          </div>
        );
      case 'business-knowledge':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold mb-4">业务知识</h1>
            <div className="bg-white rounded-lg shadow p-6">
              <p className="text-gray-600">业务知识管理功能开发中...</p>
            </div>
          </div>
        );
      case 'system-knowledge':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold mb-4">系统知识</h1>
            <div className="bg-white rounded-lg shadow p-6">
              <p className="text-gray-600">系统知识管理功能开发中...</p>
            </div>
          </div>
        );
      case 'requirement-rules':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold mb-4">需求评审规则</h1>
            <div className="bg-white rounded-lg shadow p-6">
              <p className="text-gray-600">需求评审规则管理功能开发中...</p>
            </div>
          </div>
        );
      case 'business-analysis':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold mb-4">需求生成规则</h1>
            <div className="bg-white rounded-lg shadow p-6">
              <p className="text-gray-600">需求生成规则管理功能开发中...</p>
            </div>
          </div>
        );
      case 'template-management':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold mb-4">模版管理</h1>
            <div className="bg-white rounded-lg shadow p-6">
              <p className="text-gray-600">模版管理功能开发中...</p>
            </div>
          </div>
        );
      case 'table-test':
        return <TableRenderingTestSimple />;
      case 'milkdown-table-test':
        return <MilkdownTableTest />;
      case 'tiptap-table-test':
        return <TipTapTableTest />;
      default:
        return (
          <div className="flex items-center justify-center h-full">
            <p className="text-gray-500">页面不存在</p>
          </div>
        );
    }
  };

  return (
    <div className="h-screen flex bg-white">
      {/* 左侧导航栏 */}
      <Sidebar
        activeModule={activeModule}
        onModuleChange={handleModuleChange}
        collapsed={sidebarCollapsed}
        onToggleCollapse={toggleSidebar}
      />

      {/* 右侧主内容区 */}
      <div className="flex-1 flex flex-col">
        {/* 顶部头部 */}
        <Header
          user={user}
          onToggleSidebar={toggleSidebar}
          sidebarCollapsed={sidebarCollapsed}
          activeModule={activeModule}
          currentTab={currentTab}
          moduleTitle={moduleTitle}
        />

        {/* 主内容区域 */}
        <main className="flex-1 overflow-y-auto">
          {renderContent()}
        </main>
      </div>
    </div>
  );
};

export default MainLayout;
