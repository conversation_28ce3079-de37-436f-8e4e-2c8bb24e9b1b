import React from 'react';

const Sidebar = ({ activeModule, onModuleChange, collapsed, onToggleCollapse }) => {
  const menuItems = [
    {
      id: 'ai-capability',
      title: 'AI智能需求能力',
      icon: '🤖',
      children: [
        { id: 'requirement-analysis', title: '需求辅助分析', icon: '📊' },
        { id: 'requirement-management', title: '需求文档管理', icon: '📄' },
        { id: 'detail-analysis', title: '需求拆解', icon: '🔍' },
        { id: 'prd-evaluation', title: 'PRD智能评估', icon: '📋' },
        { id: 'baseline-management', title: '任务设计与分发', icon: '🔄' }
      ]
    },
    {
      id: 'knowledge',
      title: '知识库',
      icon: '📚',
      children: [
        { id: 'business-knowledge', title: '业务知识', icon: '💼' },
        { id: 'system-knowledge', title: '系统知识', icon: '⚙️' },
        { id: 'requirement-rules', title: '需求评审规则', icon: '📏' },
        { id: 'business-analysis', title: '需求生成规则', icon: '📈' },
        { id: 'template-management', title: '模版管理', icon: '📝' }
      ]
    },
    {
      id: 'debug',
      title: '调试工具',
      icon: '🔧',
      children: [
        { id: 'table-test', title: '表格渲染测试', icon: '📊' },
        { id: 'milkdown-table-test', title: 'Milkdown表格测试', icon: '🧪' }
      ]
    }
  ];

  const handleItemClick = (itemId) => {
    onModuleChange(itemId);
  };

  return (
    <div className={`${collapsed ? 'w-16' : 'w-64'} bg-white shadow-lg flex flex-col transition-all duration-300 ease-in-out border-r border-gray-200`}>
      {/* Logo区域 */}
      <div className="p-4 border-b border-gray-200">
        <div className={`flex items-center ${collapsed ? 'justify-center' : 'space-x-3'}`}>
          <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center flex-shrink-0">
            <span className="text-white font-bold text-sm">TE</span>
          </div>
          {!collapsed && (
            <div>
              <h1 className="text-lg font-bold text-gray-800">Team Evolve</h1>
              <p className="text-xs text-gray-500">管理员 TTPO</p>
            </div>
          )}
        </div>
      </div>

      {/* 导航菜单 */}
      <nav className="flex-1 overflow-y-auto py-4">
        {menuItems.map((section) => (
          <div key={section.id} className="mb-6">
            {/* 分组标题 */}
            {!collapsed && (
              <div className="px-4 mb-2">
                <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider flex items-center">
                  <span className="mr-2">{section.icon}</span>
                  {section.title}
                </h3>
              </div>
            )}

            {/* 菜单项 */}
            <ul className="space-y-1">
              {section.children.map((item) => (
                <li key={item.id}>
                  <button
                    onClick={() => handleItemClick(item.id)}
                    className={`w-full text-left ${collapsed ? 'px-2 py-3' : 'px-4 py-2'} text-sm flex items-center ${collapsed ? 'justify-center' : 'space-x-3'} hover:bg-orange-50 transition-colors ${
                      activeModule === item.id
                        ? 'bg-white text-orange-600 border-r-2 border-orange-500 font-medium'
                        : 'text-gray-700'
                    }`}
                    title={collapsed ? item.title : ''}
                  >
                    <span className={`text-base ${collapsed ? 'text-lg' : ''} ${activeModule === item.id ? 'text-orange-500' : ''}`}>{item.icon}</span>
                    {!collapsed && <span>{item.title}</span>}
                  </button>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </nav>

      {/* 底部信息 */}
      <div className={`${collapsed ? 'p-2' : 'p-4'} border-t border-gray-200`}>
        {collapsed ? (
          <div className="flex justify-center">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          </div>
        ) : (
          <div className="text-xs text-gray-500 text-center">
            <p>AI智能需求管理</p>
            <p className="flex items-center justify-center mt-1">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
              当前AI模型状态: 正常
            </p>
          </div>
        )}
      </div>

      {/* 折叠/展开按钮 */}
      <div className="p-2 border-t border-gray-200 flex justify-center">
        <button
          onClick={onToggleCollapse}
          className="p-2 text-orange-500 hover:bg-orange-50 rounded-lg transition-colors"
          title={collapsed ? "展开侧边栏" : "收起侧边栏"}
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            {collapsed ? (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
            ) : (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
            )}
          </svg>
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
