import React from 'react';

const Breadcrumb = ({ activeModule, currentTab, moduleTitle }) => {
  // 模块名称映射
  const moduleNames = {
    'dashboard': '首页',
    'requirement-analysis': '需求辅助分析',
    'requirement-management': '需求文档管理',
    'detail-analysis': '需求拆解',
    'prd-evaluation': 'PRD智能评估',
    'baseline-management': '任务设计与分发',
    'business-knowledge': '业务知识',
    'system-knowledge': '系统知识',
    'requirement-rules': '需求评审规则',
    'business-analysis': '需求生成规则',
    'template-management': '模版管理'
  };

  // PRD智能评估的tab名称映射
  const prdEvaluationTabs = {
    'upload': '文档上传',
    'analysis': '智能分析',
    'review': '评审确认',
    'results': '评审结果',
    'optimization': '自检优化'
  };

  // 获取当前模块名称
  const getCurrentModuleName = () => {
    if (moduleTitle) {
      return moduleTitle;
    }
    return moduleNames[activeModule] || '未知模块';
  };

  // 获取当前tab名称（仅对PRD智能评估模块）
  const getCurrentTabName = () => {
    if (activeModule === 'prd-evaluation' && currentTab) {
      return prdEvaluationTabs[currentTab] || currentTab;
    }
    return null;
  };

  // 构建面包屑路径
  const buildBreadcrumbPath = () => {
    const path = [
      { name: '首页', isActive: false }
    ];

    const moduleName = getCurrentModuleName();
    const tabName = getCurrentTabName();

    if (activeModule === 'dashboard') {
      // 如果是首页，只显示首页
      path[0].isActive = true;
    } else {
      // 添加模块名称
      path.push({ name: moduleName, isActive: !tabName });

      // 如果有tab，添加tab名称
      if (tabName) {
        path.push({ name: tabName, isActive: true });
      }
    }

    return path;
  };

  const breadcrumbPath = buildBreadcrumbPath();

  return (
    <div className="flex items-center space-x-2 text-sm text-gray-600">
      {breadcrumbPath.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          )}
          <span className={`${
            item.isActive 
              ? 'text-orange-600 font-medium' 
              : 'text-gray-600 hover:text-gray-800'
          } transition-colors`}>
            {item.name}
          </span>
        </React.Fragment>
      ))}
    </div>
  );
};

export default Breadcrumb;
