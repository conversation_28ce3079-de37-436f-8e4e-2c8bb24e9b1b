import React, { useState, useEffect, useCallback, useRef } from 'react';
import MilkdownEditor from '../Editor/MilkdownEditor';
import SimpleEditor from '../Editor/SimpleEditor';
import EnhancedMilkdownEditor from '../Editor/EnhancedMilkdownEditor';
import SectionManager from '../Editor/SectionManager';
import PRDContentRenderer from '../Common/PRDContentRenderer';
import SaveIndicator from '../Common/SaveIndicator';
import ModeToggle from '../Common/ModeToggle';
import OutlineNavigation from '../Common/OutlineNavigation';
import AICard from '../AICards/AICard';
import HistoryCard from '../AICards/HistoryCard';
import AgentStatus from '../Agents/AgentStatus';
import ErrorBoundary from '../Common/ErrorBoundary';
import { SafeEditor } from '../Editor/EditorFallback';
import useContentState from '../../hooks/useContentState';
import useAutoSave from '../../hooks/useAutoSave';
import { useOutlineSync } from '../../hooks/useOutlineSync';
import { useAICards } from '../../hooks/useAICards';
import { DEFAULT_PRD_CONTENT } from '../../constants/defaultContent';
import { exportContent } from '../../utils/storageUtils';
import { realPRDContent } from '../../data/realPRDContent';
import { updateSectionContent, generateMarkdownFromSections, generateOutlineFromSections } from '../../utils/contentSync';
import './PRDEditor.css';
import '../Editor/EnhancedMilkdownEditor.css';
import '../../App.css';

const PRDEditor = () => {
  // 模式和UI状态
  const [mode, setMode] = useState('preview'); // 默认预览模式以展示BSV文档
  const [activeTab, setActiveTab] = useState('cards');
  const [expandedCard, setExpandedCard] = useState(null);
  // const [chatInputs, setChatInputs] = useState({}); // 暂时不使用
  const [editorError, setEditorError] = useState(false);
  const [documentSections, setDocumentSections] = useState([]);
  const [sectionOutline, setSectionOutline] = useState([]); // 基于章节数据的大纲

  // 新增：章节管理状态
  const [showSectionManager, setShowSectionManager] = useState(false);

  // 固定使用增强编辑器
  const editorType = 'enhanced';

  // 内容状态管理 - 使用真实BSV PRD数据
  const {
    content,
    outline,
    isModified,
    lastSaved,
    metadata,
    // 新增：章节管理相关
    sections,
    editHistory,
    canUndo,
    canRedo,
    // 原有方法
    updateContent,
    saveContentManually,
    resetContent,
    // 新增：章节操作方法
    addSection,
    deleteSection,
    moveSection,
    updateSectionLevel,
    duplicateSection,
    reorderSections,
    undoLastAction,
    redoLastAction
  } = useContentState(DEFAULT_PRD_CONTENT, true); // 启用真实PRD数据

  // 自动保存
  const { isSaving, lastAutoSaved, saveError } = useAutoSave(content, isModified, true);

  // 大纲同步
  const { activeSection: currentActiveSection, handleOutlineClick } = useOutlineSync();

  // AI卡片系统
  const {
    cards,
    isLoading: aiLoading,
    availableAgents,
    taskQueue,
    handleAIInteraction,
    executeMultiAgentAnalysis,
    executeAIAnalysis,
    updateCard,
    reAnalyzeCard,
    adoptCard,
    ignoreCard,
    deleteCard,
    markAsRead,
    restoreCard,
    getStats,
    addCard
  } = useAICards();

  // 同步章节数据到本地状态（保持向后兼容）
  useEffect(() => {
    if (sections && sections.length > 0) {
      setDocumentSections(sections);
      // 同时生成基于章节数据的大纲
      const newOutline = generateOutlineFromSections(sections);
      setSectionOutline(newOutline);
    }
  }, [sections]);

  // 错误处理
  useEffect(() => {
    const handleError = (error) => {
      console.error('编辑器错误:', error);
      if (error.message && error.message.includes('setEditorFactory')) {
        setEditorError(true);
      }
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  // 模式切换
  const handleModeToggle = useCallback((newMode) => {
    setMode(newMode);
  }, []);

  // 编辑器类型固定为增强编辑器，移除切换功能

  // 处理内容变化 - 防止重复更新
  const lastHandledContentRef = useRef(content);

  // 当content变化时更新ref
  useEffect(() => {
    lastHandledContentRef.current = content;
  }, [content]);

  const handleContentChange = useCallback((newContent) => {
    // 防止重复处理相同内容
    if (newContent === lastHandledContentRef.current) {
      console.log('🔄 内容未变化，跳过更新');
      return;
    }

    console.log('📝 处理内容变化，长度:', newContent?.length || 0);
    lastHandledContentRef.current = newContent;
    updateContent(newContent);
  }, [updateContent]);

  // 处理章节内容变更（用于统一渲染器）
  const handleSectionContentChange = useCallback((sectionId, field, value) => {
    const updatedSections = updateSectionContent(documentSections, sectionId, field, value);
    setDocumentSections(updatedSections);

    // 同步更新大纲数据
    const newOutline = generateOutlineFromSections(updatedSections);
    setSectionOutline(newOutline);

    // 同步更新到编辑器内容
    const newMarkdown = generateMarkdownFromSections(updatedSections);
    updateContent(newMarkdown);


  }, [documentSections, updateContent]);

  // ==================== 章节管理处理函数 ====================

  // 处理新增章节
  const handleAddSection = useCallback((newSection, position = 'end') => {
    addSection(newSection, position);
  }, [addSection]);

  // 处理删除章节
  const handleDeleteSection = useCallback((sectionId) => {
    deleteSection(sectionId);
  }, [deleteSection]);

  // 处理移动章节
  const handleMoveSection = useCallback((sectionId, direction) => {
    moveSection(sectionId, direction);
  }, [moveSection]);

  // 处理重新排序章节
  const handleReorderSections = useCallback((fromIndex, toIndex) => {
    reorderSections(fromIndex, toIndex);
  }, [reorderSections]);

  // 处理章节层级调整
  const handleUpdateSectionLevel = useCallback((sectionId, newLevel) => {
    updateSectionLevel(sectionId, newLevel);
  }, [updateSectionLevel]);

  // 处理复制章节
  const handleDuplicateSection = useCallback((sectionId) => {
    duplicateSection(sectionId);
  }, [duplicateSection]);

  // 处理文本选中（用于智能卡片创建）
  const handleTextSelection = useCallback((selectedText, range) => {
    console.log('📝 文本选中:', {
      text: selectedText.substring(0, 50) + '...',
      length: selectedText.length
    });
  }, []);

  // 处理AI交互 - 现在使用真实的Agent系统
  const handleAIInteractionWrapper = useCallback(async (selectedText, analysisType) => {
    console.log('🚀 handleAIInteractionWrapper 被调用');
    console.log('📝 选中文本:', selectedText);
    console.log('🔍 分析类型:', analysisType);
    console.log('🔧 handleAIInteraction函数:', typeof handleAIInteraction);

    try {
      const result = await handleAIInteraction(selectedText, analysisType, {
        documentContext: content,
        documentId: 'current-document',
        timestamp: Date.now()
      });
      console.log('✅ AI交互完成:', result);
    } catch (error) {
      console.error('❌ AI交互处理失败:', error);
    }
  }, [handleAIInteraction, content]);

  // 处理智能卡片创建
  const handleCreateCard = useCallback((cardData) => {
    console.log('🎯 创建智能卡片:', cardData);
    console.log('📊 当前卡片数量:', cards.length);
    console.log('🔧 handleAIInteraction函数:', typeof handleAIInteraction);

    // 直接调用AI交互系统，避免循环依赖
    handleAIInteraction(cardData.selectedText, cardData.analysisType || cardData.type, {
      documentContext: content,
      documentId: 'current-document',
      timestamp: Date.now()
    }).then(result => {
      console.log('✅ AI交互完成:', result);
    }).catch(error => {
      console.error('❌ AI交互处理失败:', error);
    });
  }, [handleAIInteraction, cards.length, content]);

  // 处理多Agent协同分析
  const handleMultiAgentAnalysis = useCallback(async (selectedText) => {
    try {
      await executeMultiAgentAnalysis(selectedText, {
        documentContext: content,
        documentId: 'current-document',
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('多Agent分析失败:', error);
    }
  }, [executeMultiAgentAnalysis, content]);

  // 导出功能
  const handleExport = useCallback(() => {
    const success = exportContent(content, `PRD-${new Date().toISOString().split('T')[0]}.md`);
    if (success) {
      console.log('文档导出成功');
    } else {
      console.error('文档导出失败');
    }
  }, [content]);

  // 快捷键支持
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case '1':
            e.preventDefault();
            setMode('edit');
            break;
          case '2':
            e.preventDefault();
            setMode('preview');
            break;
          case 's':
            e.preventDefault();
            saveContentManually();
            break;
          default:
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [saveContentManually]);

  // 处理大纲点击（仅在预览模式下）
  const handleOutlineItemClick = (item) => {
    if (mode !== 'preview') {
      return;
    }
    handleOutlineClick(item);
  };

  // 切换卡片展开状态
  const toggleCardExpanded = (cardId) => {
    setExpandedCard(expandedCard === cardId ? null : cardId);
    markAsRead(cardId);
  };

  // 处理卡片操作
  const handleCardAction = (cardId, action) => {
    switch (action) {
      case 'adopt':
        adoptCard(cardId);
        setExpandedCard(null);
        break;
      case 'ignore':
        ignoreCard(cardId);
        setExpandedCard(null);
        break;
      case 'delete':
        deleteCard(cardId);
        setExpandedCard(null);
        break;
      default:
        console.warn('未知的卡片操作:', action);
        break;
    }
  };

  // 处理卡片定位
  const handleCardLocate = useCallback((card) => {
    console.log('智能卡片定位请求:', card.selectedText);

    if (!card.selectedText) {
      console.warn('卡片没有选中文本，无法定位');
      return;
    }

    // 查找预览容器
    const previewContainer = document.querySelector('.preview-content');
    if (!previewContainer) {
      console.warn('未找到预览容器');
      return;
    }

    // 多层级搜索算法
    const searchStrategies = [
      // 1. 精确匹配
      (text, searchText) => {
        const index = text.indexOf(searchText);
        return index !== -1 ? { index, length: searchText.length } : null;
      },

      // 2. 模糊匹配（去除多余空格）
      (text, searchText) => {
        const normalizedText = text.replace(/\s+/g, ' ');
        const normalizedSearch = searchText.replace(/\s+/g, ' ');
        const index = normalizedText.indexOf(normalizedSearch);
        return index !== -1 ? { index, length: normalizedSearch.length } : null;
      },

      // 3. 关键词搜索（取前20个字符）
      (text, searchText) => {
        const keywords = searchText.substring(0, 20).trim();
        const index = text.indexOf(keywords);
        return index !== -1 ? { index, length: keywords.length } : null;
      },

      // 4. 分行匹配
      (text, searchText) => {
        const lines = searchText.split('\n');
        for (const line of lines) {
          if (line.trim().length > 5) {
            const index = text.indexOf(line.trim());
            if (index !== -1) {
              return { index, length: line.trim().length };
            }
          }
        }
        return null;
      },

      // 5. 中文关键词提取
      (text, searchText) => {
        const chineseWords = searchText.match(/[\u4e00-\u9fa5]{2,}/g);
        if (chineseWords && chineseWords.length > 0) {
          for (const word of chineseWords) {
            const index = text.indexOf(word);
            if (index !== -1) {
              return { index, length: word.length };
            }
          }
        }
        return null;
      }
    ];

    // 获取预览容器的文本内容
    const containerText = previewContainer.textContent || previewContainer.innerText || '';

    // 尝试各种搜索策略
    let matchResult = null;
    for (const strategy of searchStrategies) {
      matchResult = strategy(containerText, card.selectedText);
      if (matchResult) {
        console.log('找到匹配文本，使用策略:', searchStrategies.indexOf(strategy) + 1);
        break;
      }
    }

    if (!matchResult) {
      console.warn('未找到匹配的文本内容');
      return;
    }

    // 查找包含目标文本的DOM元素
    const walker = document.createTreeWalker(
      previewContainer,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );

    let currentPos = 0;
    let targetNode = null;
    let targetOffset = 0;

    while (walker.nextNode()) {
      const node = walker.currentNode;
      const nodeText = node.textContent || '';
      const nodeLength = nodeText.length;

      if (currentPos <= matchResult.index && currentPos + nodeLength > matchResult.index) {
        targetNode = node;
        targetOffset = matchResult.index - currentPos;
        break;
      }

      currentPos += nodeLength;
    }

    if (targetNode) {
      // 找到包含目标文本的元素
      const targetElement = targetNode.parentElement;

      // 计算滚动位置
      const elementOffsetTop = targetElement.offsetTop;
      const targetPosition = Math.max(0, elementOffsetTop - 100);

      // 滚动到目标位置
      try {
        previewContainer.scrollTo({
          top: targetPosition,
          behavior: 'smooth'
        });

        console.log('卡片定位成功，滚动到位置:', targetPosition);
      } catch (error) {
        previewContainer.scrollTop = targetPosition;
      }

      // 高亮显示目标文本
      const range = document.createRange();
      range.setStart(targetNode, targetOffset);
      range.setEnd(targetNode, Math.min(targetOffset + matchResult.length, targetNode.textContent.length));

      // 创建高亮标记
      const mark = document.createElement('mark');
      mark.className = 'card-locate-highlight';
      mark.style.cssText = `
        background-color: #fef3c7 !important;
        border: 2px solid #f59e0b !important;
        border-radius: 4px !important;
        padding: 2px 4px !important;
        animation: highlightPulse 3s ease-in-out;
      `;

      try {
        range.surroundContents(mark);

        // 3秒后移除高亮
        setTimeout(() => {
          if (mark.parentNode) {
            const parent = mark.parentNode;
            parent.insertBefore(document.createTextNode(mark.textContent), mark);
            parent.removeChild(mark);
          }
        }, 3000);
      } catch (error) {
        console.warn('无法添加高亮标记:', error);
      }
    } else {
      console.warn('未找到包含目标文本的DOM节点');
    }
  }, []);

  // 处理编辑器错误
  const handleEditorError = (error) => {
    console.error('编辑器错误:', error);
    setEditorError(true);
    setTimeout(() => setEditorError(false), 3000);
  };

  // 获取AI统计信息
  const aiStats = getStats();

  // 渲染编辑器组件
  const renderEditor = () => {
    if (editorError) {
      return (
        <SimpleEditor
          content={content}
          onChange={handleContentChange}
          className="h-full"
        />
      );
    }
    
    return (
      <MilkdownEditor
        content={content}
        onChange={handleContentChange}
        onError={handleEditorError}
        className="h-full"
      />
    );
  };

  return (
    <div className="w-full h-full flex flex-col font-sans text-slate-800 antialiased bg-slate-50">
      {/* 三栏式主布局 */}
      <div className="flex-grow flex min-h-0">
        
        {/* 左侧边栏 - 文档控制与导航中心 */}
        <aside className="w-72 bg-white border-r border-slate-200 flex-shrink-0 flex flex-col shadow-sm">
          {/* 文档控制中心 */}
          <div className="p-6 border-b border-slate-200">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div className="flex-1">
                <h1 className="text-lg font-bold text-slate-800">AI PRD 编辑器</h1>
                <p className="text-sm text-slate-500">共享停车 App</p>
              </div>
            </div>
            
            {/* 编辑器状态提示 */}
            {editorError && (
              <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  💡 当前使用简化编辑器模式
                </p>
              </div>
            )}
            
            {/* 文档健康度 */}
            <div className="text-sm">
              <div className="flex items-center justify-between mb-2">
                <span className="text-slate-600">文档健康度</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-lg font-bold text-green-600">86</span>
                </div>
                <div>
                  <div className="text-green-600 font-semibold">状态良好</div>
                  <div className="text-xs text-slate-500">
                    {metadata ? `${metadata.wordCount} 字` : '计算中...'}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 大纲导航 - 使用章节数据生成的大纲 */}
          <div className="flex-1 p-4 overflow-y-auto">
            <OutlineNavigation
              outline={sectionOutline}
              activeSection={currentActiveSection}
              onItemClick={mode === 'preview' ? handleOutlineItemClick : undefined}
            />
          </div>
        </aside>

        {/* 中间内容区 - 编辑与预览 */}
        <main className="flex-1 flex flex-col bg-white">
          {/* 顶部工具栏 */}
          <div className="flex-shrink-0 h-16 border-b border-slate-200 flex justify-between items-center px-6">
            <div className="flex items-center space-x-4">
              <h2 className="text-lg font-semibold text-slate-700">
                内容编辑区 {editorError && <span className="text-sm text-yellow-600">(简化模式)</span>}
              </h2>
              
              {/* 保存状态指示器 */}
              <SaveIndicator
                isModified={isModified}
                isSaving={isSaving}
                lastSaved={lastSaved || lastAutoSaved}
                saveError={saveError}
                onManualSave={saveContentManually}
              />
            </div>
            
            <div className="flex items-center space-x-4">
              {/* 章节管理按钮 */}
              <button
                onClick={() => setShowSectionManager(!showSectionManager)}
                className={`flex items-center space-x-2 px-3 py-2 text-sm font-semibold rounded-lg transition-all ${
                  showSectionManager
                    ? 'text-blue-600 bg-blue-50 border border-blue-200'
                    : 'text-slate-600 border border-slate-300 hover:bg-slate-50'
                }`}
                title="章节管理"
              >
                <span>📋</span>
                <span>章节管理</span>
                {canUndo && (
                  <span className="ml-1 px-1.5 py-0.5 text-xs bg-blue-100 text-blue-600 rounded">
                    {editHistory.length}
                  </span>
                )}
              </button>

              {/* 撤销重做按钮 */}
              {(canUndo || canRedo) && (
                <div className="flex items-center space-x-1">
                  <button
                    onClick={undoLastAction}
                    disabled={!canUndo}
                    className={`p-2 text-sm rounded transition-all ${
                      canUndo
                        ? 'text-slate-600 hover:bg-slate-100'
                        : 'text-slate-400 cursor-not-allowed'
                    }`}
                    title="撤销"
                  >
                    ↶
                  </button>
                  <button
                    onClick={redoLastAction}
                    disabled={!canRedo}
                    className={`p-2 text-sm rounded transition-all ${
                      canRedo
                        ? 'text-slate-600 hover:bg-slate-100'
                        : 'text-slate-400 cursor-not-allowed'
                    }`}
                    title="重做"
                  >
                    ↷
                  </button>
                </div>
              )}

              {/* 编辑器类型固定为增强编辑器 */}

              {/* 模式切换按钮 */}
              <ModeToggle mode={mode} onToggle={handleModeToggle} />
              
              {/* 导出按钮 */}
              <button 
                onClick={handleExport}
                className="flex items-center space-x-2 px-4 py-2 text-sm font-semibold text-slate-600 border border-slate-300 rounded-lg hover:bg-slate-50 transition-all"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span>导出</span>
              </button>

              {/* 新建PRD按钮 */}
              <button 
                onClick={() => resetContent(DEFAULT_PRD_CONTENT)}
                className="flex items-center space-x-2 px-4 py-2 text-sm font-semibold text-white bg-orange-500 rounded-lg hover:bg-orange-600 transition-all"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <span>新建 PRD</span>
              </button>
            </div>
          </div>

          {/* 章节管理面板 */}
          {showSectionManager && (
            <div className="border-b border-slate-200 bg-slate-50" style={{ maxHeight: '40vh', minHeight: '300px' }}>
              <div className="h-full overflow-hidden">
                <SectionManager
                  sections={sections}
                  onAddSection={handleAddSection}
                  onDeleteSection={handleDeleteSection}
                  onMoveSection={handleMoveSection}
                  onReorderSections={handleReorderSections}
                  onUpdateSection={handleSectionContentChange}
                />
              </div>
            </div>
          )}

          {/* 内容展示区域 */}
          <div className="flex-1 relative overflow-hidden">
            {mode === 'edit' ? (
              <div className="h-full overflow-hidden prd-content-container" style={{
                width: '100%',
                minWidth: '600px', // 设置最小宽度确保内容显示清晰
                maxWidth: '100%'
              }}>
                {/* 增强编辑器 - 使用错误边界保护 */}
                <ErrorBoundary level="component">
                  <SafeEditor
                    EditorComponent={EnhancedMilkdownEditor}
                    content={content}
                    onChange={handleContentChange}
                    onSave={saveContentManually}
                    placeholder="开始编写PRD内容..."
                    showToolbar={true}
                    enableTable={true}
                    enableUpload={true}
                    enableMenu={true}
                    enableSlash={true}
                    className="w-full h-full"
                    style={{
                      width: '100%',
                      height: '100%',
                      minWidth: '0', // 允许收缩
                      maxWidth: '100%'
                    }}
                    fallbackProps={{
                      placeholder: "增强编辑器暂时不可用，已切换到基础编辑模式"
                    }}
                  />
                </ErrorBoundary>
              </div>
            ) : (
              <div className="h-full overflow-y-auto p-6 prd-content-container" style={{ maxWidth: '100%' }}>
                <PRDContentRenderer
                  sections={documentSections}
                  mode="preview"
                  onTextSelection={handleTextSelection}
                  onCreateCard={handleCreateCard}
                  className="max-w-none"
                />
              </div>
            )}
          </div>
        </main>

        {/* 右侧AI助手侧边栏 */}
        <aside className="w-96 bg-slate-50 border-l border-slate-200 flex flex-col shadow-sm">
          <div className="flex-shrink-0 h-14 border-b border-slate-200 flex justify-between items-center px-4">
            <div className="flex items-center space-x-2">
              <svg className="w-6 h-6 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <h2 className="text-lg font-semibold text-slate-700">AI 助手</h2>
            </div>
          </div>
          
          <div className="flex-1 overflow-hidden flex flex-col">
            {/* Tab切换 */}
            <div className="flex-shrink-0 p-3 border-b border-slate-200 bg-slate-50">
              <div className="flex space-x-1">
                <button 
                  onClick={() => {
                    setActiveTab('cards');
                    setExpandedCard(null);
                  }}
                  className={`flex-1 flex items-center justify-center text-center py-2 text-sm font-semibold rounded-md transition-all ${
                    activeTab === 'cards' ? 'bg-white text-orange-500 shadow-sm' : 'text-slate-500 hover:bg-white/50'
                  }`}
                >
                  <span>智能卡片</span>
                  {cards.filter(card => card.status !== 'adopted' && card.status !== 'ignored' && card.hasUnread).length > 0 && (
                    <span className="ml-2 bg-red-100 text-red-600 text-xs font-bold px-2 py-0.5 rounded-full">
                      {cards.filter(card => card.status !== 'adopted' && card.status !== 'ignored' && card.hasUnread).length}
                    </span>
                  )}
                </button>
                <button 
                  onClick={() => {
                    setActiveTab('history');
                    setExpandedCard(null);
                  }}
                  className={`flex-1 flex items-center justify-center text-center py-2 text-sm font-semibold rounded-md transition-all ${
                    activeTab === 'history' ? 'bg-white text-orange-500 shadow-sm' : 'text-slate-500 hover:bg-white/50'
                  }`}
                >
                  <span>历史</span>
                  <span className="ml-2 text-xs text-slate-600">
                    ({aiStats.adoptedCards + aiStats.ignoredCards})
                  </span>
                </button>
                <button 
                  onClick={() => {
                    setActiveTab('agents');
                    setExpandedCard(null);
                  }}
                  className={`flex-1 flex items-center justify-center text-center py-2 text-sm font-semibold rounded-md transition-all ${
                    activeTab === 'agents' ? 'bg-white text-orange-500 shadow-sm' : 'text-slate-500 hover:bg-white/50'
                  }`}
                >
                  <span>Agent</span>
                  <span className="ml-2 text-xs text-green-600">
                    ({aiStats.availableAgentCount})
                  </span>
                </button>
              </div>
            </div>

            {/* AI卡片内容区 */}
            <div className="flex-1 overflow-y-auto">
              {activeTab === 'cards' ? (
                <div className="p-4 space-y-4">
                  {cards.filter(card => card.status !== 'adopted' && card.status !== 'ignored').length === 0 ? (
                    <div className="text-center text-slate-500 py-8">
                      <svg className="w-12 h-12 mx-auto text-slate-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <p className="font-semibold mb-2">暂无待处理的AI分析卡片</p>
                      <p className="text-sm">在预览模式下选择文本并使用AI工具</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {cards
                        .filter(card => card.status !== 'adopted' && card.status !== 'ignored')
                        .map((card) => (
                          <AICard
                            key={card.id}
                            card={card}
                            isExpanded={expandedCard === card.id}
                            onToggleExpanded={toggleCardExpanded}
                            onAdopt={adoptCard}
                            onIgnore={ignoreCard}
                            onDelete={deleteCard}
                            onMarkAsRead={markAsRead}
                            onLocate={handleCardLocate}
                          />
                        ))
                      }
                    </div>
                  )}
                </div>
              ) : activeTab === 'history' ? (
                <div className="p-4 space-y-3">
                  {cards.filter(card => card.status === 'adopted' || card.status === 'ignored').length === 0 ? (
                    <div className="text-center text-slate-500 py-8">
                      <svg className="w-12 h-12 mx-auto text-slate-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <p className="font-semibold mb-2">暂无历史记录</p>
                      <p className="text-sm">采纳或忽略的卡片将显示在这里</p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {cards
                        .filter(card => card.status === 'adopted' || card.status === 'ignored')
                        .sort((a, b) => {
                          // 按操作时间倒序排列
                          const timeA = a.adoptedAt || a.ignoredAt || 0;
                          const timeB = b.adoptedAt || b.ignoredAt || 0;
                          return timeB - timeA;
                        })
                        .map((card) => (
                          <HistoryCard
                            key={card.id}
                            card={card}
                            isExpanded={expandedCard === card.id}
                            onToggleExpanded={toggleCardExpanded}
                            onRestore={restoreCard}
                            onDelete={deleteCard}
                          />
                        ))
                      }
                    </div>
                  )}
                </div>
              ) : (
                <div className="p-4">
                  <AgentStatus
                    agents={availableAgents}
                    taskQueue={taskQueue}
                    aiStats={aiStats}
                    isLoading={aiLoading}
                  />
                </div>
              )}
            </div>
          </div>
        </aside>
      </div>
    </div>
  );
};

export default PRDEditor;
