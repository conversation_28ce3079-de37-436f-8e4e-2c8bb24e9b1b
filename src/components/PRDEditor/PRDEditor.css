/* PRD编辑器样式 - 与PRD智能评估保持一致 */

/* 导入PRD智能评估的样式 */
@import '../PRDEvaluation/PRDReviewTab.css';

/* PRD编辑器特有样式 */
.prd-editor-layout {
  min-width: 0;
  width: 100%;
}

/* 左侧面板样式 - 与PRD智能评估保持一致 */
.prd-editor-left-panel {
  width: 18rem; /* w-72 = 288px */
  flex-shrink: 0;
  background-color: white;
  border-right: 1px solid #e5e7eb;
}

/* 右侧面板样式 - 与PRD智能评估保持一致 */
.prd-editor-right-panel {
  width: 24rem; /* w-96 = 384px */
  flex-shrink: 0;
  background-color: #f8fafc;
  border-left: 1px solid #e5e7eb;
}

/* 中间内容区域 */
.prd-editor-content {
  flex: 1;
  overflow-y: auto;
  background-color: white;
  padding: 1.5rem;
}

/* 编辑模式特殊样式 */
.prd-editor-edit-mode .editable-content {
  border: 1px dashed transparent;
  border-radius: 4px;
  padding: 8px;
  transition: all 0.2s ease;
}

.prd-editor-edit-mode .editable-content:hover {
  border-color: #e5e7eb;
  background-color: #f9fafb;
}

.prd-editor-edit-mode .editable-content:focus {
  outline: none;
  border-color: #3b82f6;
  background-color: #eff6ff;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 工具栏样式 */
.prd-editor-toolbar {
  height: 4rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1.5rem;
  background-color: white;
}

/* 模式切换按钮样式 */
.mode-toggle-button {
  display: flex;
  align-items: center;
  space-x: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.mode-toggle-button:hover {
  background-color: #f9fafb;
}

.mode-toggle-button.active {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* 大纲导航样式增强 */
.outline-navigation {
  padding: 1rem;
}

.outline-item {
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 0.25rem;
}

.outline-item:hover {
  background-color: #f3f4f6;
}

.outline-item.active {
  background-color: #dbeafe;
  color: #1d4ed8;
  font-weight: 500;
}

.outline-item.level-1 {
  font-weight: 600;
  font-size: 0.875rem;
}

.outline-item.level-2 {
  padding-left: 1.5rem;
  font-size: 0.8125rem;
}

.outline-item.level-3 {
  padding-left: 2.25rem;
  font-size: 0.75rem;
  color: #6b7280;
}

/* AI助手面板样式 */
.ai-assistant-panel {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.ai-assistant-header {
  height: 3.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1rem;
  background-color: white;
}

.ai-assistant-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

/* AI卡片样式 */
.ai-card {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
  transition: all 0.2s ease;
}

.ai-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.ai-card.expanded {
  border-color: #3b82f6;
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.1);
}

/* 响应式设计 - 与PRD智能评估保持一致 */
@media (max-width: 1600px) {
  .prd-editor-left-panel {
    width: 16rem; /* w-64 */
  }
  
  .prd-editor-right-panel {
    width: 22rem; /* w-88 */
  }
}

@media (max-width: 1400px) {
  .prd-editor-left-panel {
    width: 14rem; /* w-56 */
  }
  
  .prd-editor-right-panel {
    width: 20rem; /* w-80 */
  }
  
  .prd-editor-content {
    padding: 1rem;
  }
}

@media (max-width: 1200px) {
  .prd-editor-left-panel {
    width: 12rem; /* w-48 */
  }
  
  .prd-editor-right-panel {
    width: 18rem; /* w-72 */
  }
  
  .prd-editor-content {
    padding: 0.75rem;
  }
}

@media (max-width: 1024px) {
  .prd-editor-layout {
    flex-direction: column;
    height: auto;
  }
  
  .prd-editor-left-panel,
  .prd-editor-right-panel {
    width: 100%;
    max-height: 300px;
  }
  
  .prd-editor-content {
    padding: 1rem;
  }
}

/* 文档健康度指示器 */
.document-health {
  display: flex;
  align-items: center;
  space-x: 0.75rem;
}

.health-score {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.125rem;
}

.health-score.good {
  background-color: #dcfce7;
  color: #16a34a;
}

.health-score.warning {
  background-color: #fef3c7;
  color: #d97706;
}

.health-score.error {
  background-color: #fee2e2;
  color: #dc2626;
}

/* 保存指示器样式 */
.save-indicator {
  display: flex;
  align-items: center;
  space-x: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.save-indicator.saving {
  color: #3b82f6;
}

.save-indicator.error {
  color: #dc2626;
}

.save-indicator.saved {
  color: #16a34a;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* 滚动条样式 */
.prd-editor-content::-webkit-scrollbar,
.ai-assistant-content::-webkit-scrollbar,
.outline-navigation::-webkit-scrollbar {
  width: 6px;
}

.prd-editor-content::-webkit-scrollbar-track,
.ai-assistant-content::-webkit-scrollbar-track,
.outline-navigation::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.prd-editor-content::-webkit-scrollbar-thumb,
.ai-assistant-content::-webkit-scrollbar-thumb,
.outline-navigation::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.prd-editor-content::-webkit-scrollbar-thumb:hover,
.ai-assistant-content::-webkit-scrollbar-thumb:hover,
.outline-navigation::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
