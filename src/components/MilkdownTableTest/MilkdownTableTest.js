import React, { useState, useRef, useEffect } from 'react';
import { Editor, rootCtx, defaultValueCtx, editorViewCtx } from '@milkdown/core';
import { commonmark } from '@milkdown/preset-commonmark';
import { gfm } from '@milkdown/preset-gfm';
import { nord } from '@milkdown/theme-nord';
import { replaceAll } from '@milkdown/utils';

/**
 * MilkdownEditor表格功能测试页面
 * 专门用于测试表格插入、实时渲染、预览与编辑同步等功能
 */
const MilkdownTableTest = () => {
  // 状态管理
  const [content, setContent] = useState('# MilkdownEditor表格功能测试\n\n这是一个专门测试表格功能的页面。\n\n## 测试内容\n\n1. 动态插入表格\n2. 实时渲染\n3. 位置控制\n4. 预览同步\n\n---\n\n');
  const [mode, setMode] = useState('edit'); // edit | preview
  const [editorReady, setEditorReady] = useState(false);
  const [logs, setLogs] = useState([]);
  
  // 引用
  const editorInstanceRef = useRef(null);
  const containerRef = useRef(null);
  const isInitializing = useRef(false);

  // 日志记录函数
  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev.slice(-20), { // 保留最近20条日志
      id: Date.now(),
      timestamp,
      message,
      type
    }]);
    console.log(`[${timestamp}] ${message}`);
  };

  // 初始化编辑器
  useEffect(() => {
    if (!containerRef.current || editorInstanceRef.current || isInitializing.current) {
      return;
    }

    isInitializing.current = true;
    addLog('开始初始化MilkdownEditor', 'info');

    const initEditor = async () => {
      try {
        // 清空容器
        containerRef.current.innerHTML = '';
        addLog('正在配置编辑器...', 'info');

        const editor = await Editor.make()
          .config((ctx) => {
            // 设置根容器
            ctx.set(rootCtx, containerRef.current);
            // 设置初始内容
            ctx.set(defaultValueCtx, content);
            addLog('编辑器配置完成', 'info');
          })
          .use(nord)
          .use(commonmark)
          .use(gfm)
          .create();

        editorInstanceRef.current = editor;
        setEditorReady(true);
        isInitializing.current = false;
        addLog('MilkdownEditor初始化成功', 'success');

        // 设置内容变化监听
        try {
          editor.action((ctx) => {
            const view = ctx.get(editorViewCtx);
            if (view) {
              // 监听文档变化
              const originalDispatch = view.dispatch;
              view.dispatch = (tr) => {
                originalDispatch.call(view, tr);
                if (tr.docChanged) {
                  const newContent = tr.doc.textContent;
                  if (newContent !== content) {
                    addLog(`内容变化: ${newContent.length} 字符`, 'info');
                    setContent(newContent);
                  }
                }
              };
              addLog('内容监听器设置成功', 'info');
            }
          });
        } catch (e) {
          addLog(`设置监听器失败: ${e.message}`, 'warning');
        }

        // 延迟聚焦
        setTimeout(() => {
          if (containerRef.current) {
            const editorElement = containerRef.current.querySelector('.milkdown');
            if (editorElement) {
              editorElement.focus();
              addLog('编辑器已聚焦', 'info');
            } else {
              addLog('未找到编辑器元素', 'warning');
            }
          }
        }, 200);

      } catch (error) {
        isInitializing.current = false;
        addLog(`编辑器初始化失败: ${error.message}`, 'error');
        addLog(`错误堆栈: ${error.stack}`, 'error');
        console.error('Editor initialization failed:', error);

        // 尝试备用初始化方案
        addLog('尝试备用初始化方案...', 'info');
        try {
          await initEditorFallback();
        } catch (fallbackError) {
          addLog(`备用方案也失败: ${fallbackError.message}`, 'error');
        }
      }
    };

    // 备用初始化方案 - 只使用基础功能
    const initEditorFallback = async () => {
      try {
        containerRef.current.innerHTML = '';
        addLog('使用备用方案初始化...', 'info');

        const editor = await Editor.make()
          .config((ctx) => {
            ctx.set(rootCtx, containerRef.current);
            ctx.set(defaultValueCtx, content);
          })
          .use(commonmark) // 只使用基础Markdown
          .create();

        editorInstanceRef.current = editor;
        setEditorReady(true);
        isInitializing.current = false;
        addLog('备用方案初始化成功（基础功能）', 'success');

      } catch (error) {
        addLog(`备用方案失败: ${error.message}`, 'error');
        throw error;
      }
    };

    initEditor();

    // 清理函数
    return () => {
      if (editorInstanceRef.current) {
        try {
          editorInstanceRef.current.destroy();
          addLog('编辑器已销毁', 'info');
        } catch (e) {
          addLog(`编辑器销毁失败: ${e.message}`, 'error');
        }
        editorInstanceRef.current = null;
      }
      isInitializing.current = false;
    };
  }, []);

  // 监听内容变化，确保编辑器和状态同步
  useEffect(() => {
    if (!editorInstanceRef.current || !editorReady) {
      return;
    }

    // 当外部content变化时，更新编辑器内容
    try {
      editorInstanceRef.current.action((ctx) => {
        const view = ctx.get(editorViewCtx);
        if (view && view.state) {
          const currentMarkdown = view.state.doc.textContent;
          if (currentMarkdown !== content) {
            addLog('外部内容变化，更新编辑器', 'info');
            ctx.set(defaultValueCtx, content);
          }
        }
      });
    } catch (error) {
      addLog(`内容同步失败: ${error.message}`, 'error');
    }

  }, [content, editorReady]);

  // 获取当前光标位置
  const getCurrentCursorPosition = () => {
    if (!editorInstanceRef.current || !editorReady) {
      addLog('编辑器未准备好，无法获取光标位置', 'warning');
      return null;
    }

    try {
      let position = null;
      editorInstanceRef.current.action((ctx) => {
        const view = ctx.get(editorViewCtx);
        if (view && view.state) {
          position = view.state.selection.head;
          addLog(`当前光标位置: ${position}`, 'info');
        } else {
          addLog('无法获取编辑器视图', 'warning');
        }
      });
      return position;
    } catch (error) {
      addLog(`获取光标位置失败: ${error.message}`, 'error');
      return null;
    }
  };

  // 使用Milkdown正确的方式在光标位置插入Markdown内容
  const insertMarkdownContent = (markdownContent) => {
    if (!editorInstanceRef.current || !editorReady) {
      addLog('编辑器未准备好，无法插入内容', 'warning');
      return false;
    }

    try {
      addLog('使用Milkdown API在光标位置插入Markdown内容', 'info');
      addLog(`插入内容预览: ${markdownContent.substring(0, 50)}...`, 'info');

      let success = false;
      editorInstanceRef.current.action((ctx) => {
        const view = ctx.get(editorViewCtx);
        if (view && view.state) {
          const { state } = view;
          const cursorPos = state.selection.head;

          addLog(`当前光标位置: ${cursorPos}`, 'info');

          // 获取当前完整内容
          const currentContent = state.doc.textContent;

          // 在光标位置插入新内容
          const beforeCursor = currentContent.substring(0, cursorPos);
          const afterCursor = currentContent.substring(cursorPos);
          const newContent = beforeCursor + markdownContent + afterCursor;

          addLog(`插入前内容长度: ${currentContent.length}`, 'info');
          addLog(`插入后内容长度: ${newContent.length}`, 'info');

          // 使用replaceAll命令重新设置整个文档内容
          ctx.set(defaultValueCtx, newContent);
          success = true;
        }
      });

      if (success) {
        addLog('Markdown内容插入成功', 'success');

        // 延迟检查渲染结果
        setTimeout(() => {
          checkTableRendering();
        }, 300);
      }

      return success;
    } catch (error) {
      addLog(`插入Markdown内容失败: ${error.message}`, 'error');
      return false;
    }
  };

  // 在指定位置插入内容（保留原有方法作为备用）
  const insertContentAtPosition = (insertContent, position = null) => {
    if (!editorInstanceRef.current || !editorReady) {
      addLog('编辑器未准备好，无法插入内容', 'warning');
      return false;
    }

    try {
      let success = false;
      editorInstanceRef.current.action((ctx) => {
        const view = ctx.get(editorViewCtx);
        if (view && view.state) {
          const { state, dispatch } = view;
          const insertPos = position !== null ? position : state.selection.head;

          addLog(`准备在位置 ${insertPos} 插入内容`, 'info');
          addLog(`插入内容预览: ${insertContent.substring(0, 50)}...`, 'info');

          const tr = state.tr.insertText(insertContent, insertPos);
          dispatch(tr);
          success = true;

          addLog(`内容插入成功，位置: ${insertPos}`, 'success');
        } else {
          addLog('无法获取编辑器视图', 'warning');
        }
      });
      return success;
    } catch (error) {
      addLog(`插入内容失败: ${error.message}`, 'error');
      return false;
    }
  };

  // 测试表格插入 - 使用Milkdown API
  const testTableInsertion = () => {
    const tableMarkdown = '\n\n| 姓名 | 年龄 | 城市 |\n|------|------|------|\n| 张三 | 25 | 北京 |\n| 李四 | 30 | 上海 |\n| 王五 | 28 | 广州 |\n\n';

    addLog('开始测试表格插入（使用Milkdown API）', 'info');
    const success = insertMarkdownContent(tableMarkdown);

    if (success) {
      addLog('表格插入测试完成', 'success');
    }
  };

  // 测试在文档末尾插入表格 - 使用Milkdown API
  const testTableInsertionAtEnd = () => {
    const tableMarkdown = '\n\n| 产品 | 价格 | 库存 |\n|------|------|------|\n| iPhone | 6999 | 100 |\n| iPad | 3999 | 50 |\n| MacBook | 12999 | 20 |\n\n';

    addLog('开始测试在文档末尾插入表格（使用Milkdown API）', 'info');
    const success = insertMarkdownContent(tableMarkdown);

    if (success) {
      addLog('文档末尾表格插入完成', 'success');
    }
  };

  // 测试插入简单文本 - 使用Milkdown API
  const testTextInsertion = () => {
    const text = '\n\n**这是一段测试文本**\n\n这段文本用于验证编辑器的基本插入功能是否正常工作。\n\n';

    addLog('开始测试文本插入（使用Milkdown API）', 'info');
    const success = insertMarkdownContent(text);

    if (success) {
      addLog('文本插入测试完成', 'success');
    }
  };

  // 测试复杂表格插入
  const testComplexTableInsertion = () => {
    const complexTable = `

## 复杂表格测试

| 功能 | 状态 | 优先级 | 负责人 | 完成时间 |
|------|------|--------|--------|----------|
| 用户登录 | ✅ 完成 | 高 | 张三 | 2024-01-15 |
| 数据导出 | 🔄 进行中 | 中 | 李四 | 2024-01-20 |
| 报表生成 | ⏳ 待开始 | 低 | 王五 | 2024-01-25 |
| 权限管理 | ✅ 完成 | 高 | 赵六 | 2024-01-10 |

`;

    addLog('开始测试复杂表格插入', 'info');
    const success = insertMarkdownContent(complexTable);

    if (success) {
      addLog('复杂表格插入测试完成', 'success');
    }
  };

  // 检查表格渲染情况
  const checkTableRendering = () => {
    if (!containerRef.current) {
      addLog('容器引用不可用', 'warning');
      return;
    }

    addLog('开始检查表格渲染情况', 'info');

    // 检查编辑器容器
    const editorContainer = containerRef.current.querySelector('.milkdown');
    if (!editorContainer) {
      addLog('未找到编辑器容器(.milkdown)', 'warning');
      return;
    }

    // 检查表格元素
    const tables = editorContainer.querySelectorAll('table');
    const tableCount = tables.length;

    addLog(`检查到 ${tableCount} 个表格元素`, 'info');

    if (tableCount > 0) {
      tables.forEach((table, index) => {
        const rows = table.querySelectorAll('tr').length;
        const cells = table.querySelectorAll('th, td').length;
        const headers = table.querySelectorAll('th').length;
        addLog(`表格 ${index + 1}: ${rows} 行，${cells} 个单元格，${headers} 个表头`, 'info');
      });
    } else {
      addLog('未发现表格元素，检查Markdown内容', 'warning');

      // 检查是否有表格Markdown语法
      const markdownTables = content.match(/\|.*\|/g);
      if (markdownTables) {
        addLog(`发现 ${markdownTables.length} 行表格Markdown语法`, 'info');
        addLog('表格可能未正确渲染，尝试强制重渲染', 'warning');
      }
    }

    // 检查其他元素
    const allElements = editorContainer.querySelectorAll('*');
    addLog(`编辑器容器内共有 ${allElements.length} 个DOM元素`, 'info');
  };

  // 强制重新渲染
  const forceRerender = () => {
    if (!editorInstanceRef.current || !editorReady) {
      addLog('编辑器未准备好，无法强制重新渲染', 'warning');
      return;
    }

    try {
      addLog('开始强制重新渲染', 'info');

      editorInstanceRef.current.action((ctx) => {
        const view = ctx.get(editorViewCtx);
        if (view && view.state) {
          // 获取当前Markdown内容
          const currentMarkdown = content;
          addLog(`当前内容长度: ${currentMarkdown.length}`, 'info');

          // 重新设置内容
          ctx.set(defaultValueCtx, currentMarkdown);

          // 强制视图更新
          view.updateState(view.state);

          addLog('强制重新渲染完成', 'success');

          // 延迟检查渲染结果
          setTimeout(() => {
            checkTableRendering();
          }, 200);
        } else {
          addLog('无法获取编辑器视图', 'warning');
        }
      });
    } catch (error) {
      addLog(`强制重新渲染失败: ${error.message}`, 'error');
    }
  };

  // 重新加载编辑器
  const reloadEditor = () => {
    addLog('开始重新加载编辑器', 'info');

    if (editorInstanceRef.current) {
      try {
        editorInstanceRef.current.destroy();
        addLog('旧编辑器已销毁', 'info');
      } catch (e) {
        addLog(`销毁旧编辑器失败: ${e.message}`, 'warning');
      }
      editorInstanceRef.current = null;
    }

    setEditorReady(false);
    isInitializing.current = false;

    // 延迟重新初始化
    setTimeout(() => {
      if (containerRef.current) {
        containerRef.current.innerHTML = '';
        addLog('准备重新初始化编辑器', 'info');

        // 手动触发初始化
        const initEditor = async () => {
          try {
            isInitializing.current = true;
            addLog('手动重新初始化开始', 'info');

            const editor = await Editor.make()
              .config((ctx) => {
                ctx.set(rootCtx, containerRef.current);
                ctx.set(defaultValueCtx, content);
              })
              .use(commonmark)
              .create();

            editorInstanceRef.current = editor;
            setEditorReady(true);
            isInitializing.current = false;
            addLog('手动重新初始化成功', 'success');

          } catch (error) {
            isInitializing.current = false;
            addLog(`手动重新初始化失败: ${error.message}`, 'error');
          }
        };

        initEditor();
      }
    }, 100);
  };

  // 尝试不同的初始化方案
  const tryDifferentInit = async () => {
    addLog('尝试不同的初始化方案', 'info');

    const methods = [
      {
        name: '完整功能',
        init: async () => {
          return await Editor.make()
            .config((ctx) => {
              ctx.set(rootCtx, containerRef.current);
              ctx.set(defaultValueCtx, content);
            })
            .use(nord)
            .use(commonmark)
            .use(gfm)
            .create();
        }
      },
      {
        name: '无主题',
        init: async () => {
          return await Editor.make()
            .config((ctx) => {
              ctx.set(rootCtx, containerRef.current);
              ctx.set(defaultValueCtx, content);
            })
            .use(commonmark)
            .use(gfm)
            .create();
        }
      },
      {
        name: '仅基础',
        init: async () => {
          return await Editor.make()
            .config((ctx) => {
              ctx.set(rootCtx, containerRef.current);
              ctx.set(defaultValueCtx, content);
            })
            .use(commonmark)
            .create();
        }
      }
    ];

    for (const method of methods) {
      try {
        addLog(`尝试${method.name}方案...`, 'info');

        if (editorInstanceRef.current) {
          editorInstanceRef.current.destroy();
          editorInstanceRef.current = null;
        }

        containerRef.current.innerHTML = '';
        const editor = await method.init();

        editorInstanceRef.current = editor;
        setEditorReady(true);
        addLog(`${method.name}方案成功！`, 'success');
        return;

      } catch (error) {
        addLog(`${method.name}方案失败: ${error.message}`, 'warning');
      }
    }

    addLog('所有初始化方案都失败了', 'error');
  };

  // 清空日志
  const clearLogs = () => {
    setLogs([]);
  };

  // 切换模式
  const toggleMode = () => {
    const newMode = mode === 'edit' ? 'preview' : 'edit';
    setMode(newMode);
    addLog(`切换到${newMode === 'edit' ? '编辑' : '预览'}模式`, 'info');
  };

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* 标题栏 */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <h1 className="text-2xl font-bold text-gray-900">
          MilkdownEditor表格功能测试
        </h1>
        <p className="text-gray-600 text-sm">
          专门测试表格插入、实时渲染、预览与编辑同步等功能
        </p>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 左侧：编辑器区域 */}
        <div className="flex-1 flex flex-col bg-white border-r border-gray-200">
          <div className="flex-1 flex flex-col">
              {/* 工具栏 */}
              <div className="border-b border-gray-200 p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <span className={`px-2 py-1 rounded text-sm ${
                      editorReady 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {editorReady ? '编辑器就绪' : '编辑器初始化中...'}
                    </span>
                    
                    <button
                      onClick={toggleMode}
                      className={`px-3 py-1 rounded text-sm font-medium ${
                        mode === 'edit'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-purple-100 text-purple-800'
                      }`}
                    >
                      {mode === 'edit' ? '编辑模式' : '预览模式'}
                    </button>
                  </div>

                  <div className="flex items-center space-x-2 flex-wrap gap-2">
                    <button
                      onClick={testTableInsertion}
                      disabled={!editorReady}
                      className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      插入简单表格
                    </button>

                    <button
                      onClick={testComplexTableInsertion}
                      disabled={!editorReady}
                      className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      插入复杂表格
                    </button>

                    <button
                      onClick={testTableInsertionAtEnd}
                      disabled={!editorReady}
                      className="px-3 py-1 bg-purple-500 text-white rounded text-sm hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      插入产品表格
                    </button>

                    <button
                      onClick={testTextInsertion}
                      disabled={!editorReady}
                      className="px-3 py-1 bg-indigo-500 text-white rounded text-sm hover:bg-indigo-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      插入文本
                    </button>

                    <button
                      onClick={checkTableRendering}
                      disabled={!editorReady}
                      className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      检查渲染
                    </button>

                    <button
                      onClick={forceRerender}
                      disabled={!editorReady}
                      className="px-3 py-1 bg-orange-500 text-white rounded text-sm hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      强制重渲染
                    </button>

                    <button
                      onClick={reloadEditor}
                      className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
                    >
                      重新加载编辑器
                    </button>

                    <button
                      onClick={tryDifferentInit}
                      className="px-3 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600"
                    >
                      尝试不同方案
                    </button>
                  </div>
                </div>
              </div>

              {/* 编辑器内容区域 */}
              <div className="relative flex-1 overflow-hidden">
                {mode === 'edit' ? (
                  <div
                    ref={containerRef}
                    className="h-full p-4 prose max-w-none overflow-y-auto"
                    style={{ height: 'calc(100vh - 200px)' }}
                  />
                ) : (
                  <div className="h-full p-4 prose max-w-none bg-gray-50 overflow-y-auto" style={{ height: 'calc(100vh - 200px)' }}>
                    <div className="text-sm text-gray-600 mb-4">
                      预览模式 - Markdown源码显示
                    </div>
                    <div className="bg-white p-4 rounded border">
                      <pre className="whitespace-pre-wrap font-mono text-sm">
                        {content || '暂无内容'}
                      </pre>
                    </div>
                    <div className="mt-4 text-xs text-gray-500">
                      内容长度: {content.length} 字符
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

        {/* 右侧：日志和控制面板 */}
        <div className="w-80 bg-gray-50 flex flex-col">
          {/* 状态信息 */}
          <div className="bg-white border-b border-gray-200 p-4">
            <h3 className="text-lg font-semibold mb-3">状态信息</h3>
            <div className="space-y-2 text-sm">
              <div>编辑器状态: <span className={editorReady ? 'text-green-600' : 'text-yellow-600'}>
                {editorReady ? '就绪' : '初始化中'}
              </span></div>
              <div>当前模式: <span className="text-blue-600">{mode === 'edit' ? '编辑' : '预览'}</span></div>
              <div>内容长度: <span className="text-gray-600">{content.length} 字符</span></div>
            </div>
          </div>

          {/* 操作日志 */}
          <div className="flex-1 bg-white flex flex-col">
            <div className="border-b border-gray-200 p-4 flex items-center justify-between">
              <h3 className="text-lg font-semibold">操作日志</h3>
              <button
                onClick={clearLogs}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                清空
              </button>
            </div>
            <div className="flex-1 p-4 overflow-y-auto">
              {logs.length === 0 ? (
                <p className="text-gray-500 text-sm">暂无日志</p>
              ) : (
                <div className="space-y-1">
                  {logs.map(log => (
                    <div key={log.id} className="text-xs">
                      <span className="text-gray-400">{log.timestamp}</span>
                      <span className={`ml-2 ${
                        log.type === 'error' ? 'text-red-600' :
                        log.type === 'success' ? 'text-green-600' :
                        log.type === 'warning' ? 'text-yellow-600' :
                        'text-gray-600'
                      }`}>
                        {log.message}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MilkdownTableTest;
