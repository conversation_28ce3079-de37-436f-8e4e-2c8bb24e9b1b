import React, { useState } from 'react';

const AICard = ({
  card,
  isExpanded,
  onToggleExpanded,
  onAdopt,
  onIgnore,
  onDelete,
  onMarkAsRead,
  onLocate
}) => {
  const [isProcessing, setIsProcessing] = useState(false);

  // 获取卡片类型对应的图标和颜色
  const getCardTypeInfo = (type) => {
    const typeMap = {
      'dialogue': { icon: '💬', color: 'bg-orange-100 text-orange-800', label: '对话分析' },
      'inspire': { icon: '💡', color: 'bg-yellow-100 text-yellow-800', label: '灵感激发' },
      'evaluate': { icon: '📊', color: 'bg-blue-100 text-blue-800', label: '评估分析' },
      'knowledge': { icon: '📚', color: 'bg-green-100 text-green-800', label: '知识补充' },
      'cases': { icon: '🔍', color: 'bg-purple-100 text-purple-800', label: '案例分析' },
      'collaboration': { icon: '🤝', color: 'bg-indigo-100 text-indigo-800', label: '协同分析' },
      'error': { icon: '❌', color: 'bg-red-100 text-red-800', label: '分析失败' }
    };
    return typeMap[type] || typeMap['evaluate'];
  };

  // 获取状态对应的样式
  const getStatusInfo = (status) => {
    const statusMap = {
      'loading': { color: 'text-blue-600', label: '分析中...', icon: '⏳' },
      'completed': { color: 'text-green-600', label: '已完成', icon: '✅' },
      'failed': { color: 'text-red-600', label: '失败', icon: '❌' },
      'adopted': { color: 'text-purple-600', label: '已采纳', icon: '✨' },
      'ignored': { color: 'text-gray-600', label: '已忽略', icon: '🚫' }
    };
    return statusMap[status] || statusMap['completed'];
  };

  const typeInfo = getCardTypeInfo(card.type);
  const statusInfo = getStatusInfo(card.status);

  const handleAction = async (action) => {
    setIsProcessing(true);
    try {
      switch (action) {
        case 'adopt':
          await onAdopt(card.id);
          break;
        case 'ignore':
          await onIgnore(card.id);
          break;
        case 'delete':
          await onDelete(card.id);
          break;
        default:
          break;
      }
    } catch (error) {
      console.error('卡片操作失败:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCardClick = () => {
    if (card.hasUnread) {
      onMarkAsRead(card.id);
    }
    onToggleExpanded(card.id);
  };

  return (
    <div 
      className={`bg-white rounded-lg border transition-all duration-200 ${
        card.hasUnread 
          ? 'border-orange-200 shadow-md ring-1 ring-orange-100' 
          : 'border-gray-200 shadow-sm hover:shadow-md'
      } ${isExpanded ? 'ring-2 ring-blue-200' : ''}`}
    >
      {/* 卡片头部 */}
      <div 
        className="p-4 cursor-pointer"
        onClick={handleCardClick}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1">
            {/* 类型标签和状态 */}
            <div className="flex items-center space-x-2 mb-2">
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${typeInfo.color}`}>
                <span className="mr-1">{typeInfo.icon}</span>
                {typeInfo.label}
              </span>
              <span className={`text-xs ${statusInfo.color} flex items-center`}>
                <span className="mr-1">{statusInfo.icon}</span>
                {statusInfo.label}
              </span>
              {card.hasUnread && (
                <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
              )}
            </div>

            {/* 卡片标题 */}
            <h3 className="text-sm font-semibold text-gray-900 mb-1">
              {card.title}
            </h3>

            {/* 选中的文本预览 */}
            {card.selectedText && (
              <div className="text-xs text-gray-600 bg-gray-50 rounded px-2 py-1 mb-2">
                <span className="font-medium">选中文本: </span>
                <span className="italic">
                  {card.selectedText.length > 50 
                    ? `${card.selectedText.substring(0, 50)}...` 
                    : card.selectedText
                  }
                </span>
              </div>
            )}

            {/* 时间戳 */}
            <div className="text-xs text-gray-500">
              {card.timestamp}
            </div>
          </div>

          {/* 展开/收起图标 */}
          <div className="ml-2">
            <svg 
              className={`w-4 h-4 text-gray-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
      </div>

      {/* 展开的内容 */}
      {isExpanded && (
        <div className="border-t border-gray-100">
          {/* AI响应内容 */}
          <div className="p-4">
            <div className="prose prose-sm max-w-none">
              <div className="text-sm text-gray-700 whitespace-pre-wrap">
                {card.aiResponse}
              </div>
            </div>

            {/* 标签 */}
            {card.tags && card.tags.length > 0 && (
              <div className="mt-3 flex flex-wrap gap-1">
                {card.tags.map((tag, index) => (
                  <span 
                    key={index}
                    className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            )}

            {/* 置信度显示 */}
            {card.metadata?.confidence && (
              <div className="mt-3">
                <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                  <span>置信度</span>
                  <span>{Math.round(card.metadata.confidence * 100)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-1">
                  <div 
                    className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                    style={{ width: `${card.metadata.confidence * 100}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>

          {/* 定位按钮 - 始终显示 */}
          {card.selectedText && onLocate && (
            <div className="px-4 pb-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onLocate(card);
                }}
                className="w-full px-3 py-2 text-xs font-medium text-blue-600 bg-blue-50 rounded hover:bg-blue-100 transition-colors flex items-center justify-center space-x-1"
                title="定位到文档中的对应位置"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span>📍 定位</span>
              </button>
            </div>
          )}

          {/* 操作按钮 */}
          {card.status !== 'adopted' && card.status !== 'ignored' && (
            <div className="px-4 pb-4">
              <div className="flex space-x-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAction('adopt');
                  }}
                  disabled={isProcessing}
                  className="flex-1 px-3 py-2 text-xs font-medium text-white bg-green-500 rounded hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isProcessing ? '处理中...' : '✨ 采纳'}
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAction('ignore');
                  }}
                  disabled={isProcessing}
                  className="flex-1 px-3 py-2 text-xs font-medium text-gray-700 bg-gray-100 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isProcessing ? '处理中...' : '🚫 忽略'}
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAction('delete');
                  }}
                  disabled={isProcessing}
                  className="px-3 py-2 text-xs font-medium text-red-600 bg-red-50 rounded hover:bg-red-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isProcessing ? '...' : '🗑️'}
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AICard;
