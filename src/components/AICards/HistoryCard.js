import React, { useState } from 'react';

const HistoryCard = ({ 
  card, 
  isExpanded, 
  onToggleExpanded,
  onRestore,
  onDelete 
}) => {
  const [isProcessing, setIsProcessing] = useState(false);

  // 获取卡片类型对应的图标和颜色
  const getCardTypeInfo = (type) => {
    const typeMap = {
      'dialogue': { icon: '💬', color: 'bg-orange-100 text-orange-800', label: '对话分析' },
      'inspire': { icon: '💡', color: 'bg-yellow-100 text-yellow-800', label: '灵感激发' },
      'evaluate': { icon: '📊', color: 'bg-blue-100 text-blue-800', label: '评估分析' },
      'knowledge': { icon: '📚', color: 'bg-green-100 text-green-800', label: '知识补充' },
      'cases': { icon: '🔍', color: 'bg-purple-100 text-purple-800', label: '案例分析' },
      'collaboration': { icon: '🤝', color: 'bg-indigo-100 text-indigo-800', label: '协同分析' },
      'error': { icon: '❌', color: 'bg-red-100 text-red-800', label: '分析失败' }
    };
    return typeMap[type] || typeMap['evaluate'];
  };

  // 获取状态对应的样式和操作时间
  const getStatusInfo = (status, card) => {
    const statusMap = {
      'adopted': { 
        color: 'text-green-600', 
        label: '已采纳', 
        icon: '✨',
        time: card.adoptedAt ? new Date(card.adoptedAt).toLocaleString() : ''
      },
      'ignored': { 
        color: 'text-gray-600', 
        label: '已忽略', 
        icon: '🚫',
        time: card.ignoredAt ? new Date(card.ignoredAt).toLocaleString() : ''
      }
    };
    return statusMap[status] || statusMap['adopted'];
  };

  const typeInfo = getCardTypeInfo(card.type);
  const statusInfo = getStatusInfo(card.status, card);

  const handleAction = async (action) => {
    setIsProcessing(true);
    try {
      switch (action) {
        case 'restore':
          await onRestore(card.id);
          break;
        case 'delete':
          await onDelete(card.id);
          break;
        default:
          break;
      }
    } catch (error) {
      console.error('历史卡片操作失败:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCardClick = () => {
    onToggleExpanded(card.id);
  };

  // 格式化AI响应内容，用于紧凑显示
  const getCompactResponse = (response) => {
    if (!response) return '';
    // 移除多余的换行和空格，限制长度
    const cleaned = response.replace(/\n+/g, ' ').replace(/\s+/g, ' ').trim();
    return cleaned.length > 100 ? `${cleaned.substring(0, 100)}...` : cleaned;
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
      {/* 卡片头部 - 紧凑显示 */}
      <div 
        className="p-3 cursor-pointer"
        onClick={handleCardClick}
      >
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            {/* 类型标签和状态 */}
            <div className="flex items-center space-x-2 mb-2">
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${typeInfo.color}`}>
                <span className="mr-1">{typeInfo.icon}</span>
                {typeInfo.label}
              </span>
              <span className={`text-xs ${statusInfo.color} flex items-center`}>
                <span className="mr-1">{statusInfo.icon}</span>
                {statusInfo.label}
              </span>
            </div>

            {/* 卡片标题 - 紧凑显示 */}
            <h3 className="text-xs font-semibold text-gray-900 mb-1 line-clamp-1">
              {card.title}
            </h3>

            {/* AI响应预览 - 2行截断 */}
            <div className="text-xs text-gray-600 line-clamp-2 mb-2">
              {getCompactResponse(card.aiResponse)}
            </div>

            {/* 时间信息 */}
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>创建: {card.timestamp}</span>
              {statusInfo.time && (
                <span>{statusInfo.label}: {statusInfo.time}</span>
              )}
            </div>
          </div>

          {/* 展开/收起图标 */}
          <div className="ml-2 flex-shrink-0">
            <svg 
              className={`w-4 h-4 text-gray-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
      </div>

      {/* 展开的详细内容 */}
      {isExpanded && (
        <div className="border-t border-gray-100">
          {/* 选中的文本 */}
          {card.selectedText && (
            <div className="p-3 bg-gray-50 border-b border-gray-100">
              <div className="text-xs font-medium text-gray-700 mb-1">原始选中文本:</div>
              <div className="text-xs text-gray-600 bg-white rounded px-2 py-1 border">
                {card.selectedText}
              </div>
            </div>
          )}

          {/* 完整AI响应内容 */}
          <div className="p-3">
            <div className="text-xs font-medium text-gray-700 mb-2">AI分析结果:</div>
            <div className="prose prose-sm max-w-none">
              <div className="text-xs text-gray-700 whitespace-pre-wrap break-words">
                {card.aiResponse}
              </div>
            </div>

            {/* 标签 */}
            {card.tags && card.tags.length > 0 && (
              <div className="mt-3 flex flex-wrap gap-1">
                {card.tags.map((tag, index) => (
                  <span 
                    key={index}
                    className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            )}

            {/* 置信度显示 */}
            {card.metadata?.confidence && (
              <div className="mt-3">
                <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                  <span>置信度</span>
                  <span>{Math.round(card.metadata.confidence * 100)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-1">
                  <div 
                    className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                    style={{ width: `${card.metadata.confidence * 100}%` }}
                  ></div>
                </div>
              </div>
            )}

            {/* 元数据信息 */}
            {card.metadata && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <div className="text-xs text-gray-500 space-y-1">
                  {card.metadata.processingTime && (
                    <div>处理时间: {Math.round(card.metadata.processingTime / 1000)}秒</div>
                  )}
                  {card.metadata.completedAt && (
                    <div>完成时间: {new Date(card.metadata.completedAt).toLocaleString()}</div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="px-3 pb-3">
            <div className="flex space-x-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleAction('restore');
                }}
                disabled={isProcessing}
                className="flex-1 px-3 py-2 text-xs font-medium text-blue-600 bg-blue-50 rounded hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isProcessing ? '处理中...' : '🔄 恢复到智能卡片'}
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleAction('delete');
                }}
                disabled={isProcessing}
                className="px-3 py-2 text-xs font-medium text-red-600 bg-red-50 rounded hover:bg-red-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isProcessing ? '...' : '🗑️'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HistoryCard;
