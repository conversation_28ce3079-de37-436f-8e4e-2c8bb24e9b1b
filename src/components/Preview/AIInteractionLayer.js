import React from 'react';

const AIInteractionLayer = ({ 
  selectedText, 
  selectionRect, 
  onAIInteraction, 
  onClearSelection 
}) => {
  if (!selectedText || !selectionRect) {
    return null;
  }

  // AI功能按钮配置 - 支持5种类型：对话、灵感、评估、知识、案例
  const aiActions = [
    {
      id: 'dialogue',
      label: '对话',
      icon: '💬',
      color: 'bg-orange-500 hover:bg-orange-600',
      description: '与AI就此内容进行深度对话和探讨'
    },
    {
      id: 'inspire',
      label: '灵感',
      icon: '💡',
      color: 'bg-yellow-500 hover:bg-yellow-600',
      description: '基于这段内容提供创意建议'
    },
    {
      id: 'evaluate',
      label: '评估',
      icon: '📊',
      color: 'bg-blue-500 hover:bg-blue-600',
      description: '评估这段内容的质量和完整性'
    },
    {
      id: 'knowledge',
      label: '知识',
      icon: '📚',
      color: 'bg-green-500 hover:bg-green-600',
      description: '补充相关的行业知识和案例'
    },
    {
      id: 'cases',
      label: '案例',
      icon: '🔍',
      color: 'bg-purple-500 hover:bg-purple-600',
      description: '提供相关的实际案例分析'
    }
  ];

  // 计算工具栏位置 - 调整宽度以适应5个按钮
  const toolbarStyle = {
    position: 'absolute',
    top: selectionRect.top - 50,
    left: Math.max(10, selectionRect.centerX - 150), // 调整宽度约300px，居中显示
    zIndex: 1000,
    pointerEvents: 'auto'
  };

  // 如果工具栏会超出顶部，则显示在选区下方
  if (selectionRect.top < 60) {
    toolbarStyle.top = selectionRect.top + selectionRect.height + 10;
  }

  return (
    <div className="ai-interaction-overlay">
      {/* 工具栏 */}
      <div 
        className="ai-toolbar"
        style={toolbarStyle}
      >
        {aiActions.map((action) => (
          <button
            key={action.id}
            onClick={() => onAIInteraction(action.id)}
            className={`ai-toolbar-btn ${action.color} text-white`}
            title={action.description}
          >
            <span className="mr-1">{action.icon}</span>
            <span className="text-xs font-medium">{action.label}</span>
          </button>
        ))}
        
        {/* 关闭按钮 */}
        <button
          onClick={onClearSelection}
          className="ai-toolbar-btn bg-gray-500 hover:bg-gray-600 text-white ml-1"
          title="关闭"
        >
          <span className="text-xs">✕</span>
        </button>
      </div>

      {/* 选区高亮遮罩 */}
      <div
        className="selection-highlight"
        style={{
          position: 'absolute',
          top: selectionRect.top,
          left: selectionRect.left,
          width: selectionRect.width,
          height: selectionRect.height,
          background: 'rgba(255, 107, 53, 0.1)',
          border: '1px solid rgba(255, 107, 53, 0.3)',
          borderRadius: '2px',
          pointerEvents: 'none',
          zIndex: 999
        }}
      />
    </div>
  );
};

export default AIInteractionLayer; 