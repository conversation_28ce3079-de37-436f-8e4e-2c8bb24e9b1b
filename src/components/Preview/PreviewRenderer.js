import React, { useMemo, useRef, useEffect, useState, useCallback } from 'react';
import { marked } from 'marked';
import DOMPurify from 'dompurify';
import AIInteractionLayer from './AIInteractionLayer';
import './preview.css';

// 配置marked选项，禁用默认的ID生成
marked.setOptions({
  breaks: true,
  gfm: true,
  mangle: false,
  headerIds: false // 禁用默认的ID生成
});

const PreviewRenderer = ({ 
  content = '', 
  onSectionSelect, 
  onAIInteraction,
  className = '' 
}) => {
  const previewRef = useRef(null);
  const [selectedText, setSelectedText] = useState('');
  const [selectionRect, setSelectionRect] = useState(null);
  const [hoveredSection, setHoveredSection] = useState(null);

  // 渲染HTML内容
  const htmlContent = useMemo(() => {
    console.log('=== PreviewRenderer 开始渲染 ===');
    console.log('输入内容长度:', content.length);
    console.log('输入内容前100字符:', content.substring(0, 100));
    
    if (!content) return '<div class="empty-content">暂无内容</div>';
    
    try {
      // 使用 marked.parse 而不是 marked
      console.log('1. 开始 Markdown 解析...');
      const rawHtml = marked.parse(content);
      console.log('2. Markdown解析完成，原始HTML长度:', rawHtml.length);
      console.log('3. 原始HTML前200字符:', rawHtml.substring(0, 200));
      
      // 创建一个临时的 DOM 元素来修改 HTML
      console.log('4. 创建临时DOM元素...');
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = rawHtml;
      
      // 为所有标题元素添加 ID
      console.log('5. 开始为标题添加ID...');
      let headingIndex = 0;
      const headings = tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');
      console.log('6. 找到标题数量:', headings.length);
      
      headings.forEach((heading, index) => {
        const text = heading.textContent.trim();
        const id = `heading-${headingIndex}-${text.toLowerCase().replace(/[^a-z0-9]/g, '-')}`;
        
        // 强制移除任何现有的ID属性
        heading.removeAttribute('id');
        
        // 设置新的ID
        heading.setAttribute('id', id);
        
        console.log(`7.${index + 1} 设置标题ID:`, { 
          tagName: heading.tagName,
          text, 
          id, 
          headingIndex,
          finalId: heading.id,
          originalHTML: heading.outerHTML.substring(0, 100)
        });
        headingIndex++;
      });
      
      // 获取修改后的 HTML
      console.log('8. 获取修改后的HTML...');
      const modifiedHtml = tempDiv.innerHTML;
      console.log('9. 修改后HTML长度:', modifiedHtml.length);
      console.log('10. 修改后HTML前300字符:', modifiedHtml.substring(0, 300));
      
      // 再次检查并修正可能残留的user-content-前缀
      console.log('10.5 检查并修正残留的user-content-前缀...');
      const finalDiv = document.createElement('div');
      finalDiv.innerHTML = modifiedHtml;
      const finalHeadings = finalDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');
      let hasUserContentPrefix = false;
      
      finalHeadings.forEach((heading, index) => {
        if (heading.id && heading.id.startsWith('user-content-')) {
          hasUserContentPrefix = true;
          const newId = heading.id.replace('user-content-', '');
          heading.id = newId;
          console.log(`10.5.${index + 1} 修正ID:`, {
            oldId: heading.id,
            newId: newId,
            text: heading.textContent.trim()
          });
        }
      });
      
      const finalHtml = hasUserContentPrefix ? finalDiv.innerHTML : modifiedHtml;
      console.log('10.6 最终修正后的HTML长度:', finalHtml.length);
      
      // 清理 HTML
      console.log('11. 开始DOMPurify清理...');
      const cleanHtml = DOMPurify.sanitize(finalHtml, {
        ADD_ATTR: ['id', 'class', 'data-line'],
        ALLOWED_TAGS: [
          'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
          'p', 'br', 'strong', 'em', 'u', 'strike',
          'ul', 'ol', 'li',
          'table', 'thead', 'tbody', 'tr', 'th', 'td',
          'blockquote', 'code', 'pre',
          'hr', 'a', 'img',
          'div', 'span'
        ],
        KEEP_CONTENT: true,
        RETURN_DOM: false,
        RETURN_DOM_FRAGMENT: false,
        RETURN_DOM_IMPORT: false,
        RETURN_TRUSTED_TYPE: false,
        SANITIZE_DOM: true,
        SANITIZE_NAMED_PROPS: true,
        WHOLE_DOCUMENT: false
      });
      console.log('12. DOMPurify清理完成，最终HTML长度:', cleanHtml.length);
      console.log('13. 最终HTML前300字符:', cleanHtml.substring(0, 300));
      
      console.log('=== PreviewRenderer 渲染完成 ===');
      return cleanHtml;
    } catch (error) {
      console.error('Markdown解析错误:', error);
      return '<div class="error-content">内容解析出错，请检查Markdown格式</div>';
    }
  }, [content]);

  // 在组件挂载后检查标题元素
  useEffect(() => {
    console.log('=== useEffect: 检查DOM中的标题元素 ===');
    
    // 延迟检查，确保DOM已更新
    setTimeout(() => {
      console.log('14. 开始检查DOM中的标题元素...');
      const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      console.log('15. DOM中找到的标题数量:', headings.length);
      
      // 检查并修正任何残留的user-content-前缀
      let fixedCount = 0;
      Array.from(headings).forEach((h, index) => {
        if (h.id && h.id.startsWith('user-content-')) {
          const oldId = h.id;
          const newId = h.id.replace('user-content-', '');
          h.id = newId;
          fixedCount++;
          console.log(`15.${index + 1} 修正DOM中的ID:`, {
            oldId: oldId,
            newId: newId,
            text: h.textContent.trim()
          });
        }
        
        console.log(`16.${index + 1} DOM标题元素:`, {
          id: h.id,
          text: h.textContent.trim(),
          tagName: h.tagName,
          className: h.className,
          hasId: !!h.id,
          outerHTML: h.outerHTML.substring(0, 150)
        });
      });
      
      console.log('15.修正总数:', fixedCount);
      
      // 特别检查预览容器内的标题
      if (previewRef.current) {
        const previewHeadings = previewRef.current.querySelectorAll('h1, h2, h3, h4, h5, h6');
        console.log('17. 预览容器内的标题数量:', previewHeadings.length);
        Array.from(previewHeadings).forEach((h, index) => {
          console.log(`18.${index + 1} 预览容器内标题:`, {
            id: h.id,
            text: h.textContent.trim(),
            tagName: h.tagName,
            hasId: !!h.id
          });
        });
      }
      
      console.log('=== DOM检查完成 ===');
    }, 100);
  }, [htmlContent]);

  // 清除选择
  const clearSelection = useCallback(() => {
    setSelectedText('');
    setSelectionRect(null);
    window.getSelection()?.removeAllRanges();
  }, []);

  // 处理文本选择
  const handleMouseUp = useCallback(() => {
    const selection = window.getSelection();
    const selectedText = selection.toString().trim();
    
    if (selectedText.length > 5) {
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      const previewRect = previewRef.current?.getBoundingClientRect();
      
      if (previewRect) {
        setSelectedText(selectedText);
        setSelectionRect({
          top: rect.top - previewRect.top + previewRef.current.scrollTop,
          left: rect.left - previewRect.left,
          width: rect.width,
          height: rect.height,
          centerX: rect.left - previewRect.left + rect.width / 2,
          centerY: rect.top - previewRect.top + rect.height / 2
        });
      }
    } else {
      clearSelection();
    }
  }, [clearSelection]);

  // 处理段落悬浮
  const handleMouseMove = useCallback((e) => {
    const element = e.target.closest('h1, h2, h3, p, blockquote, li, td');
    if (element && element !== hoveredSection) {
      setHoveredSection(element);
      if (onSectionSelect) {
        onSectionSelect(element);
      }
    }
  }, [hoveredSection, onSectionSelect]);

  // 处理鼠标离开
  const handleMouseLeave = useCallback(() => {
    setHoveredSection(null);
  }, []);

  // 处理AI交互
  const handleAIInteraction = useCallback((action) => {
    if (selectedText && onAIInteraction) {
      onAIInteraction(selectedText, action, selectionRect);
    }
    clearSelection();
  }, [selectedText, onAIInteraction, selectionRect, clearSelection]);

  // 点击其他区域时清除选择
  const handleDocumentClick = useCallback((e) => {
    if (previewRef.current && !previewRef.current.contains(e.target)) {
      clearSelection();
    }
  }, [clearSelection]);

  useEffect(() => {
    document.addEventListener('click', handleDocumentClick);
    return () => {
      document.removeEventListener('click', handleDocumentClick);
    };
  }, [handleDocumentClick]);

  return (
    <div className={`preview-container ${className}`} ref={previewRef}>
      <div 
        className="preview-content prose prose-slate max-w-none"
        dangerouslySetInnerHTML={{ __html: htmlContent }}
        onMouseUp={handleMouseUp}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
      />
      
      {/* AI交互层 */}
      <AIInteractionLayer
        selectedText={selectedText}
        selectionRect={selectionRect}
        onAIInteraction={handleAIInteraction}
        onClearSelection={clearSelection}
      />
    </div>
  );
};

export default PreviewRenderer; 