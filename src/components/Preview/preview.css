/* 预览容器 */
.preview-container {
  position: relative;
  height: 100%;
  background: white;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.preview-content {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
}

/* 空内容样式 */
.preview-content .empty-content {
  text-align: center;
  color: #9ca3af;
  font-style: italic;
  padding: 60px 20px;
  font-size: 16px;
}

.preview-content .error-content {
  text-align: center;
  color: #ef4444;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

/* 覆盖prose样式以匹配主题 */
.preview-content.prose h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 32px 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 3px solid #ff6b35;
  scroll-margin-top: 80px;
}

.preview-content.prose h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin: 24px 0 12px 0;
  scroll-margin-top: 80px;
}

.preview-content.prose h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #4b5563;
  margin: 20px 0 8px 0;
  scroll-margin-top: 80px;
}

.preview-content.prose p {
  margin: 12px 0;
  line-height: 1.7;
  color: #374151;
}

.preview-content.prose strong {
  font-weight: 700;
  color: #1f2937;
}

.preview-content.prose em {
  font-style: italic;
  color: #6b7280;
}

.preview-content.prose blockquote {
  border-left: 4px solid #ff6b35;
  background: #fef3e2;
  padding: 16px 20px;
  margin: 20px 0;
  font-style: italic;
  border-radius: 0 6px 6px 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.preview-content.prose blockquote p {
  margin: 0;
  color: #92400e;
}

.preview-content.prose ul,
.preview-content.prose ol {
  margin: 16px 0;
  padding-left: 24px;
}

.preview-content.prose li {
  margin: 4px 0;
  line-height: 1.6;
}

.preview-content.prose table {
  border-collapse: collapse;
  width: 100%;
  margin: 20px 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.preview-content.prose th,
.preview-content.prose td {
  border: 1px solid #e5e7eb;
  padding: 12px 16px;
  text-align: left;
}

.preview-content.prose th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.preview-content.prose tbody tr:nth-child(even) {
  background: #f9fafb;
}

.preview-content.prose tbody tr:hover {
  background: #f3f4f6;
}

.preview-content.prose code {
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
  color: #d97706;
  border: 1px solid #e5e7eb;
}

.preview-content.prose pre {
  background: #1f2937;
  color: #f9fafb;
  padding: 16px 20px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 20px 0;
  border: 1px solid #374151;
}

.preview-content.prose pre code {
  background: transparent;
  padding: 0;
  color: inherit;
  border: none;
}

.preview-content.prose hr {
  border: none;
  border-top: 2px solid #e5e7eb;
  margin: 32px 0;
  border-radius: 1px;
}

/* AI交互层样式 */
.ai-interaction-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
}

.ai-toolbar {
  position: absolute;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 6px;
  display: flex;
  gap: 4px;
  pointer-events: auto;
  animation: fadeInUp 0.2s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ai-toolbar-btn {
  padding: 6px 10px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  white-space: nowrap;
  min-width: 50px;
  justify-content: center;
}

.ai-toolbar-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 选区高亮样式 */
.selection-highlight {
  animation: highlightPulse 0.3s ease-out;
}

@keyframes highlightPulse {
  0% {
    background: rgba(255, 107, 53, 0.3);
  }
  100% {
    background: rgba(255, 107, 53, 0.1);
  }
}

/* 段落悬浮效果 */
.preview-content h1:hover,
.preview-content h2:hover,
.preview-content h3:hover,
.preview-content p:hover,
.preview-content blockquote:hover,
.preview-content li:hover {
  background: rgba(255, 107, 53, 0.02);
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s ease;
}

/* 滚动条样式 */
.preview-content::-webkit-scrollbar {
  width: 8px;
}

.preview-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.preview-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.preview-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .preview-content {
    padding: 16px 20px;
  }
  
  .ai-toolbar {
    padding: 4px;
    gap: 2px;
  }
  
  .ai-toolbar-btn {
    padding: 4px 8px;
    font-size: 11px;
    min-width: 40px;
  }
} 