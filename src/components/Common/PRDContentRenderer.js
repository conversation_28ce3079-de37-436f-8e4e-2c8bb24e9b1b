import React, { useState, useCallback, useEffect } from 'react';
import SmartCardMenu from './SmartCardMenu';
import './PRDContentRenderer.css';

const PRDContentRenderer = ({
  sections,
  mode = 'preview', // 'edit' | 'preview'
  onContentChange,
  onTextSelection,
  onCreateCard,
  className = ''
}) => {
  const [selectedText, setSelectedText] = useState('');
  const [selectionPosition, setSelectionPosition] = useState(null);
  const [showCardMenu, setShowCardMenu] = useState(false);

  // 处理文本选中
  const handleTextSelection = useCallback((event) => {
    if (mode !== 'preview') return;

    // 如果点击的是智能卡片菜单，不处理文本选中
    if (event && event.target && event.target.closest('.smart-card-menu')) {
      return;
    }

    // 添加延迟，避免与按钮点击事件冲突
    setTimeout(() => {
      const selection = window.getSelection();
      const selectedText = selection.toString().trim();



      if (selectedText && selectedText.length > 10) {
        const range = selection.getRangeAt(0);
        const rect = range.getBoundingClientRect();

        setSelectedText(selectedText);
        setSelectionPosition({
          x: rect.left + rect.width / 2,
          y: rect.top - 10
        });
        setShowCardMenu(true);

        onTextSelection?.(selectedText, range);
      } else {
        // 只有在没有选中文本时才关闭菜单
        if (!selectedText) {
          setShowCardMenu(false);
        }
      }
    }, 50); // 50ms延迟
  }, [mode, onTextSelection, showCardMenu]);

  // 处理内容编辑
  const handleContentEdit = useCallback((sectionId, field, value) => {
    onContentChange?.(sectionId, field, value);
  }, [onContentChange]);

  // 渲染表格内容
  const renderTableContent = (content, sectionId, isEditable) => {
    const lines = content.split('\n');
    const tableLines = lines.filter(line => line.includes('|') && line.trim() !== '');
    
    if (tableLines.length === 0) return null;

    return (
      <div
        className="table-container"
        onMouseUp={mode === 'preview' ? (e) => handleTextSelection(e) : undefined}
      >
        <table className="prd-content-table border border-gray-300">
          <tbody>
            {tableLines.map((row, rowIndex) => {
              const cells = row.split('|').map(cell => cell.trim()).filter(cell => cell !== '');
              const isHeaderRow = rowIndex === 0 || row.includes('---');

              if (row.includes('---')) return null; // 跳过分隔行

              return (
                <tr key={rowIndex} className={isHeaderRow ? 'bg-gray-50 font-medium' : ''}>
                  {cells.map((cell, cellIndex) => (
                    <td key={cellIndex} className="border border-gray-300 px-2 py-2 text-xs">
                      {isEditable ? (
                        <div
                          contentEditable
                          className="break-words overflow-wrap-anywhere outline-none min-h-4"
                          onBlur={(e) => handleContentEdit(sectionId, 'cell', {
                            rowIndex,
                            cellIndex,
                            value: e.target.textContent
                          })}
                          suppressContentEditableWarning={true}
                          dangerouslySetInnerHTML={{ __html: cell }}
                        />
                      ) : (
                        <div
                          className="break-words overflow-wrap-anywhere"
                          dangerouslySetInnerHTML={{ __html: cell }}
                        />
                      )}
                    </td>
                  ))}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    );
  };

  // 渲染普通文本内容
  const renderTextContent = (content, sectionId, isEditable) => {
    return (
      <div 
        className={`whitespace-pre-wrap ${isEditable ? 'editable-content' : ''}`}
        contentEditable={isEditable}
        onBlur={isEditable ? (e) => handleContentEdit(sectionId, 'content', e.target.textContent) : undefined}
        onMouseUp={mode === 'preview' ? (e) => handleTextSelection(e) : undefined}
        suppressContentEditableWarning={true}
      >
        {content}
      </div>
    );
  };

  // 渲染章节内容
  const renderSectionContent = (section, isEditable = false) => {
    if (!section.content) return null;
    
    // 检查是否包含表格
    if (section.content.includes('|') && section.content.includes('---')) {
      return renderTableContent(section.content, section.id, isEditable);
    } else {
      return renderTextContent(section.content, section.id, isEditable);
    }
  };

  // 获取标题元素
  const getTitleElement = (section, isEditable = false) => {
    const titleProps = {
      id: section.id, // 添加ID属性，确保大纲导航能找到对应元素
      className: `font-bold text-gray-900 mb-4 border-b border-gray-200 pb-2 ${
        section.level === 0 ? 'text-3xl' :
        section.level === 1 ? 'text-2xl' :
        section.level === 2 ? 'text-xl' :
        'text-lg'
      } ${isEditable ? 'editable-title' : ''}`,
      contentEditable: isEditable,
      onBlur: isEditable ? (e) => handleContentEdit(section.id, 'title', e.target.textContent) : undefined,
      suppressContentEditableWarning: true
    };

    const titleText = section.title;

    switch (section.level) {
      case 0: return <h1 {...titleProps}>{titleText}</h1>;
      case 1: return <h2 {...titleProps}>{titleText}</h2>;
      case 2: return <h3 {...titleProps}>{titleText}</h3>;
      case 3: return <h4 {...titleProps}>{titleText}</h4>;
      default: return <h5 {...titleProps}>{titleText}</h5>;
    }
  };

  // 处理点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = () => {
      if (showCardMenu) {
        setShowCardMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showCardMenu]);

  if (!sections || sections.length === 0) {
    return (
      <div className={`prd-content-renderer ${className}`}>
        <div className="text-center text-gray-500 py-8">
          暂无内容
        </div>
      </div>
    );
  }

  return (
    <div
      className={`prd-content-renderer ${className}`}
      onMouseUp={mode === 'preview' ? (e) => handleTextSelection(e) : undefined}
    >
      <div className="prose max-w-none">
        {sections.map((section) => (
          <div key={section.id} id={section.id} className="mb-8 section-content">
            {/* 章节标题 */}
            {getTitleElement(section, mode === 'edit')}

            {/* 章节内容 */}
            <div className="section-body">
              <div className="text-gray-700 leading-relaxed">
                {renderSectionContent(section, mode === 'edit')}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 智能卡片创建菜单 */}
      {showCardMenu && selectionPosition && (
        <SmartCardMenu
          position={selectionPosition}
          selectedText={selectedText}
          onCreateCard={onCreateCard}
          onClose={() => setShowCardMenu(false)}
        />
      )}
    </div>
  );
};

export default PRDContentRenderer;
