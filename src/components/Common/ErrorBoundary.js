import React from 'react';

/**
 * 全局错误边界组件
 * 捕获React组件树中的JavaScript错误，记录错误并显示降级UI
 */
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    // 更新state，下次渲染将显示降级UI
    return {
      hasError: true,
      errorId: Date.now().toString(36) + Math.random().toString(36).substr(2)
    };
  }

  componentDidCatch(error, errorInfo) {
    // 记录错误详情
    this.setState({
      error,
      errorInfo
    });

    // 错误上报
    this.reportError(error, errorInfo);
  }

  reportError = (error, errorInfo) => {
    const errorReport = {
      id: this.state.errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // 发送错误报告到控制台（生产环境可发送到错误监控服务）
    console.error('Error Boundary Caught:', errorReport);

    // 可以集成第三方错误监控服务
    if (window.errorReportingService) {
      window.errorReportingService.report(errorReport);
    }
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      const { fallback: Fallback, level = 'component' } = this.props;

      // 如果提供了自定义降级组件，使用它
      if (Fallback) {
        return (
          <Fallback
            error={this.state.error}
            errorInfo={this.state.errorInfo}
            onRetry={this.handleRetry}
            onReload={this.handleReload}
          />
        );
      }

      // 默认错误UI
      return (
        <div className="error-boundary-container">
          {level === 'app' ? (
            <AppLevelErrorFallback
              error={this.state.error}
              errorId={this.state.errorId}
              onRetry={this.handleRetry}
              onReload={this.handleReload}
            />
          ) : (
            <ComponentLevelErrorFallback
              error={this.state.error}
              errorId={this.state.errorId}
              onRetry={this.handleRetry}
            />
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * 应用级错误降级UI
 */
const AppLevelErrorFallback = ({ error, errorId, onRetry, onReload }) => (
  <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div className="sm:mx-auto sm:w-full sm:max-w-md">
      <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="mt-4 text-lg font-medium text-gray-900">应用出现错误</h3>
          <p className="mt-2 text-sm text-gray-500">
            很抱歉，应用遇到了意外错误。我们已经记录了这个问题。
          </p>
          <p className="mt-1 text-xs text-gray-400">
            错误ID: {errorId}
          </p>
        </div>

        <div className="mt-6 space-y-3">
          <button
            onClick={onRetry}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            重试
          </button>
          <button
            onClick={onReload}
            className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            刷新页面
          </button>
        </div>

        {process.env.NODE_ENV === 'development' && error && (
          <details className="mt-4">
            <summary className="text-sm text-gray-500 cursor-pointer">错误详情 (开发模式)</summary>
            <pre className="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto max-h-32">
              {error.stack}
            </pre>
          </details>
        )}
      </div>
    </div>
  </div>
);

/**
 * 组件级错误降级UI
 */
const ComponentLevelErrorFallback = ({ error, errorId, onRetry }) => (
  <div className="bg-red-50 border border-red-200 rounded-md p-4">
    <div className="flex">
      <div className="flex-shrink-0">
        <svg className="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <div className="ml-3 flex-1">
        <h3 className="text-sm font-medium text-red-800">组件加载失败</h3>
        <p className="mt-1 text-sm text-red-700">
          这个组件遇到了错误，请尝试重新加载。
        </p>
        <p className="mt-1 text-xs text-red-600">错误ID: {errorId}</p>
        <div className="mt-3">
          <button
            onClick={onRetry}
            className="text-sm bg-red-100 text-red-800 px-3 py-1 rounded hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500"
          >
            重试
          </button>
        </div>
      </div>
    </div>
  </div>
);

export default ErrorBoundary;

/**
 * 高阶组件：为组件添加错误边界
 */
export const withErrorBoundary = (Component, fallback) => {
  const WrappedComponent = (props) => (
    <ErrorBoundary fallback={fallback}>
      <Component {...props} />
    </ErrorBoundary>
  );
  
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

/**
 * Hook：错误处理
 */
export const useErrorHandler = () => {
  const [error, setError] = React.useState(null);

  const handleError = React.useCallback((error) => {
    setError(error);
    console.error('Handled Error:', error);
  }, []);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  // 如果有错误，抛出它让ErrorBoundary捕获
  if (error) {
    throw error;
  }

  return { handleError, clearError };
};
