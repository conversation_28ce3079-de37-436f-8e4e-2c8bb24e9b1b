/* SmartCardMenu 样式优化 */

.smart-card-menu {
  /* 确保菜单在所有情况下都有固定宽度 */
  min-width: 320px;
  max-width: 320px;
  
  /* 优化阴影效果 */
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* 动画效果 */
  animation: smartCardMenuFadeIn 0.15s ease-out;
  transform-origin: top center;
}

@keyframes smartCardMenuFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-5px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 菜单项悬停效果优化 */
.smart-card-menu button:hover {
  transform: translateX(2px);
  transition: all 0.15s ease-out;
}

/* 文本省略和换行控制 */
.smart-card-menu .truncate-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.smart-card-menu .break-words {
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* 选中文本预览区域优化 */
.smart-card-menu .text-preview {
  line-height: 1.4;
  max-height: 4rem; /* 约3行文字 */
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .smart-card-menu {
    min-width: 280px;
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .smart-card-menu {
    min-width: calc(100vw - 40px);
    max-width: calc(100vw - 40px);
  }
}

/* 卡片类型按钮优化 */
.smart-card-menu .card-type-button {
  border-radius: 8px;
  transition: all 0.15s ease-out;
}

.smart-card-menu .card-type-button:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 图标动画 */
.smart-card-menu .card-type-icon {
  transition: transform 0.15s ease-out;
}

.smart-card-menu .card-type-button:hover .card-type-icon {
  transform: scale(1.1);
}

/* 关闭按钮优化 */
.smart-card-menu .close-button {
  border-radius: 4px;
  transition: all 0.15s ease-out;
}

.smart-card-menu .close-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(1.1);
}

/* 滚动条样式 */
.smart-card-menu .text-preview::-webkit-scrollbar {
  width: 4px;
}

.smart-card-menu .text-preview::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.smart-card-menu .text-preview::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.smart-card-menu .text-preview::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
