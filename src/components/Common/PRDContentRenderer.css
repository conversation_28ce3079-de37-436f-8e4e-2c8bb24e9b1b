/* PRD内容渲染器样式 - 与PRD智能评估保持一致 */

.prd-content-renderer {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

/* 章节内容样式 */
.section-content {
  scroll-margin-top: 80px;
}

.section-body p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.section-body p:last-child {
  margin-bottom: 0;
}

/* 表格样式 - 与PRD智能评估完全一致 */
.table-container {
  width: 100%;
  overflow-x: auto;
  margin: 1rem 0;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background: white;
}

.prd-content-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
  table-layout: fixed;
}

.prd-content-table td {
  padding: 0.75rem;
  border-bottom: 1px solid #f3f4f6;
  border-right: 1px solid #f3f4f6;
  vertical-align: top;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: anywhere;
}

.prd-content-table tr:last-child td {
  border-bottom: none;
}

.prd-content-table td:last-child {
  border-right: none;
}

.prd-content-table tr:nth-child(even) {
  background-color: #f9fafb;
}

.prd-content-table tr:hover {
  background-color: #f3f4f6;
}

/* 编辑模式样式 */
.editable-content {
  border: 1px dashed transparent;
  border-radius: 4px;
  padding: 8px;
  transition: all 0.2s ease;
  min-height: 1.5rem;
}

.editable-content:hover {
  border-color: #e5e7eb;
  background-color: #f9fafb;
}

.editable-content:focus {
  outline: none;
  border-color: #3b82f6;
  background-color: #eff6ff;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.editable-title {
  border: 1px dashed transparent;
  border-radius: 4px;
  padding: 4px 8px;
  margin: -4px -8px;
  transition: all 0.2s ease;
}

.editable-title:hover {
  border-color: #e5e7eb;
  background-color: #f9fafb;
}

.editable-title:focus {
  outline: none;
  border-color: #3b82f6;
  background-color: #eff6ff;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 文本选中高亮 */
.prd-content-renderer ::selection {
  background-color: #fef3c7;
  color: #92400e;
}

/* 响应式设计 - 与PRD智能评估保持一致 */
@media (max-width: 1600px) {
  .prd-content-table {
    font-size: 0.8125rem;
  }
  
  .prd-content-table td {
    padding: 0.625rem;
  }
}

@media (max-width: 1400px) {
  .prd-content-table {
    font-size: 0.75rem;
  }
  
  .prd-content-table td {
    padding: 0.5rem;
  }
}

@media (max-width: 1200px) {
  .prd-content-table {
    font-size: 0.7rem;
  }
  
  .prd-content-table td {
    padding: 0.375rem;
  }
}

@media (max-width: 1024px) {
  .prd-content-table {
    font-size: 0.65rem;
  }
  
  .prd-content-table td {
    padding: 0.25rem;
  }
}

/* 智能卡片菜单动画 */
.smart-card-menu {
  animation: fadeInUp 0.2s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 章节高亮效果 - 与PRD智能评估保持一致 */
.section-content.highlight {
  background-color: #fef3c7;
  border-left: 4px solid #f59e0b;
  padding-left: 1rem;
  margin-left: -1rem;
  border-radius: 0.25rem;
  animation: highlight-pulse 2s ease-in-out;
}

@keyframes highlight-pulse {
  0%, 100% {
    background-color: #fef3c7;
  }
  50% {
    background-color: #fde68a;
  }
}

/* 确保长文本正确换行 */
.section-body {
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
}

/* 代码块样式（如果有的话） */
.section-body code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 列表样式 */
.section-body ul, .section-body ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.section-body li {
  margin-bottom: 0.5rem;
}

/* 链接样式 */
.section-body a {
  color: #3b82f6;
  text-decoration: underline;
}

.section-body a:hover {
  color: #1d4ed8;
}

/* 强调文本样式 */
.section-body strong {
  font-weight: 600;
  color: #1f2937;
}

.section-body em {
  font-style: italic;
  color: #4b5563;
}
