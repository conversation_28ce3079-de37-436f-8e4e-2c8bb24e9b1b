import React from 'react';

const ModeToggle = ({ mode, onToggle, className = '' }) => {
  return (
    <div className={`mode-toggle ${className}`}>
      <div className="flex bg-slate-100 rounded-lg p-1">
        <button 
          onClick={() => onToggle('edit')} 
          className={`px-4 py-2 text-sm font-semibold rounded-md transition-all flex items-center space-x-2 ${
            mode === 'edit' 
              ? 'bg-orange-500 text-white shadow-sm' 
              : 'text-slate-600 hover:text-slate-800 hover:bg-slate-200'
          }`}
          title="编辑模式 (Ctrl+1)"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
          <span>编辑</span>
        </button>
        
        <button 
          onClick={() => onToggle('preview')} 
          className={`px-4 py-2 text-sm font-semibold rounded-md transition-all flex items-center space-x-2 ${
            mode === 'preview' 
              ? 'bg-orange-500 text-white shadow-sm' 
              : 'text-slate-600 hover:text-slate-800 hover:bg-slate-200'
          }`}
          title="预览模式 (Ctrl+2)"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
          <span>预览</span>
        </button>
      </div>
    </div>
  );
};

export default ModeToggle; 