import React, { useRef, useEffect } from 'react';
import './SmartCardMenu.css';

const SmartCardMenu = ({ position, selectedText, onCreateCard, onClose }) => {
  console.log('🎯 SmartCardMenu 渲染开始');
  console.log('📍 position:', position);
  console.log('📝 selectedText:', selectedText);
  console.log('🔧 onCreateCard:', typeof onCreateCard);

  const menuRef = useRef(null);

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  // 卡片类型定义 - 与智能卡片系统保持一致
  const cardTypes = [
    {
      type: 'evaluate',
      analysisType: 'evaluate',
      icon: '📊',
      label: '评估分析',
      description: '对选中内容进行深度评估分析',
      color: 'blue'
    },
    {
      type: 'inspire',
      analysisType: 'inspire',
      icon: '💡',
      label: '灵感激发',
      description: '基于内容激发创新思路',
      color: 'yellow'
    },
    {
      type: 'knowledge',
      analysisType: 'knowledge',
      icon: '📚',
      label: '知识补充',
      description: '补充相关知识和参考资料',
      color: 'green'
    },
    {
      type: 'cases',
      analysisType: 'cases',
      icon: '🔍',
      label: '案例分析',
      description: '查找相关案例和最佳实践',
      color: 'purple'
    }
  ];

  // 处理卡片创建 - 按照正确流程：取消选中 → 关闭菜单 → 创建卡片
  const handleCardCreate = (cardType) => {
    // 步骤1：立即取消文本选中，防止重新触发菜单显示
    if (window.getSelection) {
      const selection = window.getSelection();
      if (selection.removeAllRanges) {
        selection.removeAllRanges();
      } else if (selection.empty) {
        selection.empty();
      }
    }

    // 步骤2：立即关闭菜单
    onClose();

    // 步骤3：延迟创建卡片，确保菜单已关闭
    setTimeout(() => {
      const cardData = {
        selectedText,
        analysisType: cardType.analysisType,
        type: cardType.type,
        title: cardType.label,
        icon: cardType.icon
      };

      try {
        onCreateCard(cardData);
      } catch (error) {
        console.error('❌ handleCardCreate 错误:', error);
      }
    }, 100); // 100ms延迟确保菜单关闭
  };

  // 获取颜色样式
  const getColorClasses = (color) => {
    const colorMap = {
      blue: 'text-blue-600 bg-blue-50 hover:bg-blue-100',
      green: 'text-green-600 bg-green-50 hover:bg-green-100',
      yellow: 'text-yellow-600 bg-yellow-50 hover:bg-yellow-100',
      purple: 'text-purple-600 bg-purple-50 hover:bg-purple-100'
    };
    return colorMap[color] || colorMap.blue;
  };

  const menuStyle = {
    left: Math.max(10, position.x - 160), // 居中显示，最小边距10px
    top: Math.max(10, position.y - 10),
    width: '320px', // 固定宽度
    maxWidth: 'calc(100vw - 20px)'
  };



  return (
    <div
      ref={menuRef}
      className="fixed z-50 bg-white rounded-lg shadow-lg border border-gray-200 smart-card-menu"
      style={menuStyle}
      onClick={(e) => {
        e.stopPropagation(); // 阻止事件冒泡
      }}
      onMouseUp={(e) => {
        e.stopPropagation(); // 阻止mouseUp事件冒泡，防止触发文本选中
      }}
    >
      {/* 菜单标题 */}
      <div className="px-4 py-3 border-b border-gray-100 bg-gray-50 rounded-t-lg">
        <div className="flex items-center justify-between">
          <h4 className="font-medium text-gray-900 text-sm">创建智能卡片</h4>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0 close-button"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <p className="text-xs text-gray-500 mt-1 truncate">
          已选中 {selectedText.length} 个字符
        </p>
      </div>

      {/* 卡片类型选项 */}
      <div className="py-2">
        {cardTypes.map((cardType) =>
            <button
              key={cardType.type}
              onClick={(e) => {
                // 立即阻止所有事件传播
                e.preventDefault();
                e.stopPropagation();

                // 处理卡片创建
                handleCardCreate(cardType);
              }}
              onMouseDown={(e) => {
                // 阻止mouseDown事件，防止触发文本选中
                e.preventDefault();
                e.stopPropagation();
              }}
              onMouseUp={(e) => {
                // 阻止mouseUp事件，防止触发文本选中
                e.preventDefault();
                e.stopPropagation();
              }}
              className={`w-full flex items-start px-4 py-3 text-left hover:bg-gray-50 transition-colors card-type-button ${getColorClasses(cardType.color)}`}
            >
              <span className="text-lg mr-3 mt-0.5 card-type-icon">{cardType.icon}</span>
              <div className="flex-1 min-w-0">
                <div className="font-medium text-gray-900 text-sm">
                  {cardType.label}
                </div>
                <div className="text-xs text-gray-500 mt-0.5">
                  {cardType.description}
                </div>
              </div>
              <svg className="w-4 h-4 text-gray-400 mt-1 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
        )}
      </div>

      {/* 选中文本预览 */}
      <div className="px-4 py-3 border-t border-gray-100 bg-gray-50 rounded-b-lg">
        <div className="text-xs text-gray-500 mb-2">选中内容预览：</div>
        <div className="text-xs text-gray-700 bg-white rounded p-2 border text-preview">
          {selectedText.length > 120 ? `${selectedText.substring(0, 120)}...` : selectedText}
        </div>
      </div>
    </div>
  );
};

export default SmartCardMenu;
