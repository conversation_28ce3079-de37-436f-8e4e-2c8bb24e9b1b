import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';

/**
 * 虚拟滚动列表组件
 * 用于高效渲染大量数据项，只渲染可见区域的项目
 */
const VirtualizedList = ({
  items = [],
  itemHeight = 50,
  containerHeight = 400,
  renderItem,
  overscan = 5,
  className = '',
  onScroll,
  ...props
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef(null);

  // 计算可见范围
  const visibleRange = useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );
    return { startIndex, endIndex };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);

  // 可见项目
  const visibleItems = useMemo(() => {
    const { startIndex, endIndex } = visibleRange;
    return items.slice(startIndex, endIndex + 1).map((item, index) => ({
      item,
      index: startIndex + index,
      key: item.id || startIndex + index
    }));
  }, [items, visibleRange]);

  // 总高度
  const totalHeight = items.length * itemHeight;

  // 偏移量
  const offsetY = visibleRange.startIndex * itemHeight;

  // 滚动处理
  const handleScroll = useCallback((e) => {
    const newScrollTop = e.target.scrollTop;
    setScrollTop(newScrollTop);
    
    if (onScroll) {
      onScroll(e);
    }
  }, [onScroll]);

  // 滚动到指定项目
  const scrollToItem = useCallback((index) => {
    if (containerRef.current) {
      const targetScrollTop = index * itemHeight;
      containerRef.current.scrollTop = targetScrollTop;
      setScrollTop(targetScrollTop);
    }
  }, [itemHeight]);

  // 滚动到指定位置
  const scrollToPosition = useCallback((position) => {
    if (containerRef.current) {
      containerRef.current.scrollTop = position;
      setScrollTop(position);
    }
  }, []);

  return (
    <div
      ref={containerRef}
      className={`virtual-list-container ${className}`}
      style={{
        height: containerHeight,
        overflow: 'auto',
        position: 'relative'
      }}
      onScroll={handleScroll}
      {...props}
    >
      {/* 总高度占位符 */}
      <div style={{ height: totalHeight, position: 'relative' }}>
        {/* 可见项目容器 */}
        <div
          style={{
            transform: `translateY(${offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }}
        >
          {visibleItems.map(({ item, index, key }) => (
            <div
              key={key}
              style={{
                height: itemHeight,
                overflow: 'hidden'
              }}
            >
              {renderItem(item, index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

/**
 * 虚拟化章节列表组件
 * 专门用于渲染大量章节数据
 */
export const VirtualizedSectionList = ({
  sections = [],
  onSectionClick,
  onSectionEdit,
  onSectionDelete,
  selectedSectionId,
  className = ''
}) => {
  const renderSectionItem = useCallback((section, index) => (
    <div
      className={`section-item p-3 border-b border-gray-200 hover:bg-gray-50 cursor-pointer ${
        selectedSectionId === section.id ? 'bg-blue-50 border-blue-200' : ''
      }`}
      onClick={() => onSectionClick && onSectionClick(section)}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            <span
              className="text-xs text-gray-500"
              style={{ marginLeft: `${section.level * 16}px` }}
            >
              {section.level === 0 ? '📄' : section.level === 1 ? '📋' : section.level === 2 ? '📝' : '📌'}
            </span>
            <h4 className="text-sm font-medium text-gray-900 truncate">
              {section.title || '未命名章节'}
            </h4>
          </div>
          {section.content && (
            <p className="text-xs text-gray-500 mt-1 truncate">
              {section.content.substring(0, 100)}...
            </p>
          )}
        </div>
        <div className="flex items-center space-x-1">
          {onSectionEdit && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onSectionEdit(section);
              }}
              className="p-1 text-gray-400 hover:text-blue-600"
              title="编辑章节"
            >
              ✏️
            </button>
          )}
          {onSectionDelete && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onSectionDelete(section);
              }}
              className="p-1 text-gray-400 hover:text-red-600"
              title="删除章节"
            >
              🗑️
            </button>
          )}
        </div>
      </div>
    </div>
  ), [onSectionClick, onSectionEdit, onSectionDelete, selectedSectionId]);

  return (
    <VirtualizedList
      items={sections}
      itemHeight={80}
      containerHeight={400}
      renderItem={renderSectionItem}
      className={className}
    />
  );
};

/**
 * 虚拟化文本行组件
 * 用于渲染大文档的文本内容
 */
export const VirtualizedTextLines = ({
  lines = [],
  lineHeight = 24,
  containerHeight = 400,
  onLineClick,
  highlightedLines = [],
  className = ''
}) => {
  const renderLineItem = useCallback((line, index) => (
    <div
      className={`text-line px-3 py-1 text-sm font-mono cursor-text ${
        highlightedLines.includes(index) ? 'bg-yellow-100' : ''
      }`}
      onClick={() => onLineClick && onLineClick(line, index)}
      style={{
        lineHeight: `${lineHeight}px`,
        whiteSpace: 'pre-wrap',
        wordBreak: 'break-word'
      }}
    >
      <span className="text-gray-400 mr-3 select-none">
        {(index + 1).toString().padStart(3, ' ')}
      </span>
      <span>{line}</span>
    </div>
  ), [lineHeight, onLineClick, highlightedLines]);

  return (
    <VirtualizedList
      items={lines}
      itemHeight={lineHeight}
      containerHeight={containerHeight}
      renderItem={renderLineItem}
      className={`virtualized-text-lines ${className}`}
    />
  );
};

/**
 * Hook: 虚拟滚动控制
 */
export const useVirtualScroll = (items, itemHeight, containerHeight) => {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef(null);

  const scrollToItem = useCallback((index) => {
    if (containerRef.current) {
      const targetScrollTop = index * itemHeight;
      containerRef.current.scrollTop = targetScrollTop;
    }
  }, [itemHeight]);

  const scrollToTop = useCallback(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = 0;
    }
  }, []);

  const scrollToBottom = useCallback(() => {
    if (containerRef.current) {
      const maxScrollTop = items.length * itemHeight - containerHeight;
      containerRef.current.scrollTop = Math.max(0, maxScrollTop);
    }
  }, [items.length, itemHeight, containerHeight]);

  return {
    containerRef,
    scrollTop,
    setScrollTop,
    scrollToItem,
    scrollToTop,
    scrollToBottom
  };
};

export default VirtualizedList;
