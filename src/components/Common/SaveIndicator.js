import React from 'react';

const SaveIndicator = ({ 
  isModified, 
  isSaving, 
  lastSaved, 
  saveError,
  onManualSave,
  className = '' 
}) => {
  // 格式化时间显示
  const formatTime = (date) => {
    if (!date) return '';
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit'
    });
  };

  // 获取状态文本和图标
  const getStatusInfo = () => {
    if (saveError) {
      return {
        text: '保存失败',
        icon: '⚠️',
        className: 'text-red-600 bg-red-50',
        showTime: false
      };
    }
    
    if (isSaving) {
      return {
        text: '保存中...',
        icon: '⏳',
        className: 'text-yellow-600 bg-yellow-50',
        showTime: false
      };
    }
    
    if (isModified) {
      return {
        text: '有未保存更改',
        icon: '⚫',
        className: 'text-orange-600 bg-orange-50',
        showTime: false
      };
    }
    
    if (lastSaved) {
      return {
        text: `已保存`,
        icon: '✅',
        className: 'text-green-600 bg-green-50',
        showTime: true,
        time: formatTime(lastSaved)
      };
    }
    
    return {
      text: '未保存',
      icon: '📝',
      className: 'text-gray-600 bg-gray-50',
      showTime: false
    };
  };

  const statusInfo = getStatusInfo();

  return (
    <div className={`save-indicator flex items-center gap-2 ${className}`}>
      <div className={`px-3 py-1.5 rounded-full text-sm font-medium flex items-center gap-2 ${statusInfo.className}`}>
        <span className="text-xs">{statusInfo.icon}</span>
        <span>{statusInfo.text}</span>
        {statusInfo.showTime && statusInfo.time && (
          <span className="text-xs opacity-75">
            {statusInfo.time}
          </span>
        )}
      </div>
      
      {/* 手动保存按钮 */}
      {(isModified || saveError) && onManualSave && (
        <button
          onClick={onManualSave}
          disabled={isSaving}
          className="px-3 py-1.5 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          title="立即保存"
        >
          💾 保存
        </button>
      )}
      
      {/* 错误重试提示 */}
      {saveError && (
        <div className="text-xs text-red-500 max-w-xs truncate" title={saveError}>
          {saveError}
        </div>
      )}
    </div>
  );
};

export default SaveIndicator; 