import React, { useState, useCallback } from 'react';

const OutlineNavigation = ({ 
  outline = [], 
  activeSection, 
  onItemClick 
}) => {
  const [collapsedSections, setCollapsedSections] = useState(new Set());

  // 切换折叠状态
  const toggleCollapse = useCallback((itemId, e) => {
    e.stopPropagation();
    setCollapsedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  }, []);

  // 构建层级树结构
  const buildTree = useCallback((items) => {
    const tree = [];
    const stack = [];

    items.forEach(item => {
      const node = { ...item, children: [] };

      // 找到正确的父节点
      while (stack.length > 0 && stack[stack.length - 1].level >= node.level) {
        stack.pop();
      }

      if (stack.length === 0) {
        tree.push(node);
      } else {
        stack[stack.length - 1].children.push(node);
      }

      stack.push(node);
    });

    return tree;
  }, []);

  // 渲染大纲项
  const renderOutlineItem = useCallback((item, depth = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isCollapsed = collapsedSections.has(item.id);
    const isActive = activeSection === item.id;

    return (
      <div key={item.id} className="outline-tree-node">
        <button
          onClick={onItemClick ? () => onItemClick(item) : undefined}
          data-outline-id={item.id}
          className={`w-full text-left p-2 rounded-lg transition-colors outline-item ${
            isActive ? 'active' : onItemClick ? 'hover:bg-slate-100' : 'cursor-default'
          } outline-level-${Math.min(item.level, 3)} ${
            !onItemClick ? 'opacity-60' : ''
          }`}
          disabled={!onItemClick}
        >
          <div className="flex items-center relative">
            {item.level > 1 && (
              <span className="mr-2 text-slate-400">•</span>
            )}
            <span className="truncate flex-1">{item.text}</span>
            {hasChildren && (
              <button
                onClick={(e) => toggleCollapse(item.id, e)}
                className={`outline-toggle ${isCollapsed ? 'collapsed' : ''}`}
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            )}
          </div>
        </button>
        
        {hasChildren && (
          <div className={`outline-children ${isCollapsed ? 'collapsed' : ''}`}>
            {item.children.map(child => renderOutlineItem(child, depth + 1))}
          </div>
        )}
      </div>
    );
  }, [collapsedSections, activeSection, onItemClick, toggleCollapse]);

  const tree = buildTree(outline);

  if (outline.length === 0) {
    return (
      <div className="text-center text-slate-500 py-8">
        <p className="text-sm">暂无大纲</p>
        <p className="text-xs mt-1">开始编辑文档时将自动生成</p>
      </div>
    );
  }

  return (
    <div className="space-y-1">
      {tree.map(item => renderOutlineItem(item))}
    </div>
  );
};

export default OutlineNavigation; 