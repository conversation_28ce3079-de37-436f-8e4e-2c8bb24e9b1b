import React, { useState, useEffect } from 'react';

const TableRenderingTestSimple = () => {
  const [testResults, setTestResults] = useState({});
  const [logs, setLogs] = useState([]);
  const [editorAvailable, setEditorAvailable] = useState(false);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { timestamp, message, type }]);
    console.log(`[测试工具 ${timestamp}] ${message}`);
  };

  const updateTestResult = (testName, status, details = '') => {
    setTestResults(prev => ({
      ...prev,
      [testName]: { status, details, timestamp: new Date().toLocaleTimeString() }
    }));
  };

  // 检查编辑器状态
  const checkEditorStatus = () => {
    addLog('开始检查编辑器状态...', 'info');

    try {
      // 检查所有可能的调试函数
      const debugFunctions = {
        debugEditor: typeof window.debugEditor,
        forceMarkdownReparse: typeof window.forceMarkdownReparse,
        forceTableRender: typeof window.forceTableRender,
        recreateEditor: typeof window.recreateEditor,
        testMarkdownRendering: typeof window.testMarkdownRendering,
        checkMilkdownTableSupport: typeof window.checkMilkdownTableSupport
      };

      addLog(`调试函数检查结果: ${JSON.stringify(debugFunctions)}`, 'info');

      if (typeof window.debugEditor === 'function') {
        const result = window.debugEditor();
        updateTestResult('editorStatus', 'success', JSON.stringify(result, null, 2));
        addLog('编辑器状态检查完成', 'success');
        return result;
      } else {
        // 提供详细的错误信息和解决方案
        const errorMsg = `找不到debugEditor函数。请确保：
1. 已访问包含编辑器的页面（如"需求分析"或"需求管理"）
2. 编辑器已完全加载
3. 等待几秒钟后重试

当前可用的全局函数: ${Object.keys(window).filter(key => key.includes('debug') || key.includes('Editor')).join(', ')}`;

        updateTestResult('editorStatus', 'warning', errorMsg);
        addLog('警告：debugEditor函数不可用，请先访问编辑器页面', 'warning');
        return null;
      }
    } catch (e) {
      updateTestResult('editorStatus', 'error', e.message);
      addLog(`编辑器状态检查失败：${e.message}`, 'error');
      return null;
    }
  };

  // 强制重新解析
  const forceReparse = () => {
    addLog('强制重新解析...', 'info');
    
    try {
      if (typeof window.forceMarkdownReparse === 'function') {
        window.forceMarkdownReparse();
        updateTestResult('forceReparse', 'success', '强制重新解析已执行');
        addLog('强制重新解析完成', 'success');
        return true;
      } else {
        updateTestResult('forceReparse', 'error', '找不到forceMarkdownReparse函数');
        addLog('错误：forceMarkdownReparse函数不可用', 'error');
        return false;
      }
    } catch (e) {
      updateTestResult('forceReparse', 'error', e.message);
      addLog(`强制重新解析失败：${e.message}`, 'error');
      return false;
    }
  };

  // 模拟粘贴表格
  const simulatePasteTable = () => {
    addLog('模拟粘贴表格...', 'info');
    
    const testTable = `| 姓名 | 年龄 | 城市 |
|------|------|------|
| 张三 | 25 | 北京 |
| 李四 | 30 | 上海 |`;

    try {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(testTable).then(() => {
          updateTestResult('simulatePaste', 'success', '测试表格已复制到剪贴板');
          addLog('测试表格已复制到剪贴板，请在编辑器中粘贴', 'success');
        }).catch(e => {
          updateTestResult('simulatePaste', 'warning', `复制失败：${e.message}`);
          addLog(`复制到剪贴板失败：${e.message}`, 'warning');
        });
      } else {
        updateTestResult('simulatePaste', 'warning', '剪贴板API不可用');
        addLog('剪贴板API不可用，请手动复制表格', 'warning');
      }
      
      return testTable;
    } catch (e) {
      updateTestResult('simulatePaste', 'error', e.message);
      addLog(`模拟粘贴失败：${e.message}`, 'error');
      return null;
    }
  };

  // 滚动到顶部
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // 滚动到底部
  const scrollToBottom = () => {
    window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
  };

  // 导航到编辑器页面
  const navigateToEditor = () => {
    addLog('导航到编辑器页面...', 'info');

    // 尝试通过URL导航
    const editorUrls = [
      '/requirement-analysis',
      '/requirement-management',
      '/detail-analysis'
    ];

    // 随机选择一个编辑器页面
    const randomUrl = editorUrls[Math.floor(Math.random() * editorUrls.length)];

    addLog(`正在跳转到: ${randomUrl}`, 'info');
    window.location.href = randomUrl;
  };

  // 检查编辑器是否可用（不触发重新渲染）
  const checkEditorAvailability = () => {
    const isAvailable = typeof window.debugEditor === 'function';
    setEditorAvailable(isAvailable);
    addLog(`编辑器可用性检查: ${isAvailable ? '可用' : '不可用'}`, isAvailable ? 'success' : 'warning');
    return isAvailable;
  };

  // 静默检查编辑器可用性（不记录日志）
  const isEditorAvailable = () => {
    return typeof window.debugEditor === 'function';
  };

  // 页面加载时自动检查
  useEffect(() => {
    const timer = setTimeout(() => {
      addLog('自动检查编辑器状态...', 'info');

      // 先检查可用性
      const available = checkEditorAvailability();
      if (!available) {
        addLog('编辑器不可用，请手动导航到编辑器页面', 'warning');
        updateTestResult('editorStatus', 'warning', '编辑器未加载，请先访问包含编辑器的页面');
      } else {
        checkEditorStatus();
      }
    }, 2000);

    return () => clearTimeout(timer);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 定期检查编辑器可用性
  useEffect(() => {
    const interval = setInterval(() => {
      const available = isEditorAvailable();
      if (available !== editorAvailable) {
        setEditorAvailable(available);
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [editorAvailable]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-50';
      case 'error': return 'text-red-600 bg-red-50';
      case 'warning': return 'text-yellow-600 bg-yellow-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getLogColor = (type) => {
    switch (type) {
      case 'success': return 'text-green-400';
      case 'error': return 'text-red-400';
      case 'warning': return 'text-yellow-400';
      default: return 'text-gray-300';
    }
  };

  return (
    <div className="h-full overflow-y-auto" style={{ scrollBehavior: 'smooth' }}>
      <div className="p-6 max-w-6xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            🔧 Milkdown表格渲染测试工具
          </h1>
          <p className="text-gray-600">
            这个工具用于诊断和测试Milkdown编辑器的表格渲染问题，支持上下滚动
          </p>
        </div>

        {/* 编辑器状态提示 */}
        {!editorAvailable && (
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="text-lg font-semibold text-yellow-800 mb-2">⚠️ 编辑器未加载</h3>
            <p className="text-yellow-700 mb-3">
              测试工具需要先加载编辑器才能正常工作。请选择以下操作之一：
            </p>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={navigateToEditor}
                className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
              >
                🚀 自动跳转到编辑器页面
              </button>
              <a
                href="/requirement-analysis"
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 inline-block"
              >
                📝 需求分析页面
              </a>
              <a
                href="/requirement-management"
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 inline-block"
              >
                📋 需求管理页面
              </a>
            </div>
          </div>
        )}

        {/* 编辑器可用提示 */}
        {editorAvailable && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h3 className="text-lg font-semibold text-green-800 mb-2">✅ 编辑器已加载</h3>
            <p className="text-green-700">
              编辑器调试功能已可用，您可以开始测试表格渲染功能。
            </p>
          </div>
        )}

        {/* 快速操作按钮 */}
        <div className="mb-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-lg font-semibold mb-3">快速操作</h3>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={checkEditorStatus}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              🔍 检查编辑器状态
            </button>
            <button
              onClick={() => {
                checkEditorAvailability();
                checkEditorStatus();
              }}
              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
            >
              🔄 重新检查
            </button>
            <button
              onClick={simulatePasteTable}
              className={`px-4 py-2 rounded ${
                editorAvailable
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : 'bg-gray-400 text-gray-200 cursor-not-allowed'
              }`}
              disabled={!editorAvailable}
            >
              📋 模拟粘贴表格
            </button>
            <button
              onClick={forceReparse}
              className={`px-4 py-2 rounded ${
                editorAvailable
                  ? 'bg-orange-600 text-white hover:bg-orange-700'
                  : 'bg-gray-400 text-gray-200 cursor-not-allowed'
              }`}
              disabled={!editorAvailable}
            >
              🔄 强制重新解析
            </button>
            <button
              onClick={scrollToBottom}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              ⬇️ 滚动到底部
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 测试结果 */}
          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="text-lg font-semibold mb-3">测试结果</h3>
            <div className="space-y-2 max-h-80 overflow-y-auto">
              {Object.entries(testResults).map(([testName, result]) => (
                <div key={testName} className={`p-3 rounded ${getStatusColor(result.status)}`}>
                  <div className="flex justify-between items-start">
                    <span className="font-medium">{testName}</span>
                    <span className="text-sm">{result.timestamp}</span>
                  </div>
                  {result.details && (
                    <pre className="text-sm mt-2 whitespace-pre-wrap max-h-32 overflow-y-auto">
                      {result.details}
                    </pre>
                  )}
                </div>
              ))}
              {Object.keys(testResults).length === 0 && (
                <p className="text-gray-500 text-center py-4">暂无测试结果</p>
              )}
            </div>
          </div>

          {/* 实时日志 */}
          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="text-lg font-semibold mb-3">实时日志</h3>
            <div className="bg-gray-900 text-gray-100 p-3 rounded h-80 overflow-y-auto font-mono text-sm">
              {logs.map((log, index) => (
                <div key={index} className="mb-1">
                  <span className="text-gray-400">[{log.timestamp}]</span>
                  <span className={getLogColor(log.type)}> {log.message}</span>
                </div>
              ))}
              {logs.length === 0 && (
                <p className="text-gray-500">等待日志输出...</p>
              )}
            </div>
          </div>
        </div>

        {/* 测试表格示例 */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-semibold mb-3">测试表格示例</h3>
          <p className="text-gray-600 mb-2">请复制以下内容到编辑器中测试：</p>
          <pre className="bg-white p-3 rounded border text-sm">
{`| 姓名 | 年龄 | 城市 |
|------|------|------|
| 张三 | 25 | 北京 |
| 李四 | 30 | 上海 |`}
          </pre>
        </div>

        {/* 使用说明 */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-lg font-semibold mb-3">📝 使用说明</h3>
          <div className="space-y-2 text-sm text-gray-700">
            <p><strong>1. 新功能测试（语雀风格表格转换）：</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>🎉 <strong>新增功能</strong>：粘贴表格时会弹出转换提示框</li>
              <li>复制下方的测试表格，在编辑器中粘贴</li>
              <li>应该会看到"是否转换为表格？"的提示框</li>
              <li>点击"转换为表格"将Markdown转换为可视化表格</li>
              <li>点击"保持原样"将保留原始Markdown格式</li>
            </ul>

            <p><strong>2. 基础测试：</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>点击"检查编辑器状态"验证编辑器是否正常</li>
              <li>点击"模拟粘贴表格"复制测试表格到剪贴板</li>
              <li>在编辑器中粘贴表格，观察是否正确渲染</li>
              <li>如果表格未渲染，点击"强制重新解析"</li>
            </ul>

            <p><strong>3. 滚动测试：</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>使用鼠标滚轮或触摸板滚动页面</li>
              <li>点击"滚动到底部"按钮测试自动滚动</li>
              <li>在页面底部点击"返回顶部"按钮</li>
            </ul>

            <p><strong>4. 控制台调试：</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>按F12打开开发者工具</li>
              <li>在控制台运行 <code className="bg-gray-200 px-1 rounded">window.debugEditor()</code></li>
              <li>运行 <code className="bg-gray-200 px-1 rounded">window.forceMarkdownReparse()</code></li>
            </ul>
          </div>
        </div>

        {/* 更多测试内容，确保页面有足够的滚动空间 */}
        <div className="mt-6 space-y-4">
          {[1, 2, 3, 4, 5].map(i => (
            <div key={i} className="p-4 bg-white rounded-lg shadow">
              <h4 className="font-semibold mb-2">测试区域 {i}</h4>
              <p className="text-gray-600">
                这是第 {i} 个测试区域，用于验证页面滚动功能是否正常工作。
                您可以使用鼠标滚轮、触摸板或键盘方向键来滚动页面。
                页面应该能够平滑滚动，没有任何卡顿或异常。
              </p>
              <div className="mt-2 p-2 bg-gray-100 rounded text-sm">
                <code>测试内容 {i}: 滚动功能正常 ✅</code>
              </div>
            </div>
          ))}
        </div>

        {/* 底部区域 */}
        <div className="mt-8 pb-8 text-center">
          <div className="p-6 bg-green-50 rounded-lg">
            <h3 className="text-lg font-semibold text-green-800 mb-2">
              🎉 恭喜！您已滚动到页面底部
            </h3>
            <p className="text-green-700 mb-4">
              如果您看到这条消息，说明页面滚动功能正常工作
            </p>
            <button
              onClick={scrollToTop}
              className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              ⬆️ 返回顶部
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TableRenderingTestSimple;
