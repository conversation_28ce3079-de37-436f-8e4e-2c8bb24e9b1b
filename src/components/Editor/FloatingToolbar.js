import React from 'react';
import './FloatingToolbar.css';

/**
 * 悬浮工具栏组件
 * 包含各种编辑工具，如表格插入、列表插入等
 */
const FloatingToolbar = ({
  onInsertTable,
  onInsertQuickTable,
  onInsertList,
  onInsertFormat,
  onClose,
  className = ''
}) => {
  const handleInsertTable = () => {
    onInsertTable && onInsertTable();
    onClose && onClose();
  };

  const handleInsertQuickTable = () => {
    onInsertQuickTable && onInsertQuickTable();
    onClose && onClose();
  };

  const handleInsertList = (ordered = false) => {
    onInsertList && onInsertList(ordered);
    onClose && onClose();
  };

  const handleFormat = (type) => {
    onInsertFormat && onInsertFormat(type);
    // 格式化操作后不关闭工具栏，方便连续操作
  };

  return (
    <div className={`floating-toolbar ${className}`}>
      {/* 插入工具区域 */}
      <div className="toolbar-section">
        <h3 className="toolbar-section-title">插入</h3>
        <div className="toolbar-items">
          <button
            className="toolbar-item"
            onClick={handleInsertQuickTable}
            title="快速插入3x3表格"
            aria-label="快速插入表格"
          >
            <span className="toolbar-item-icon">⚡</span>
            <span className="toolbar-item-text">快速表格</span>
          </button>

          <button
            className="toolbar-item"
            onClick={handleInsertTable}
            title="选择表格模板"
            aria-label="选择表格模板"
          >
            <span className="toolbar-item-icon">📊</span>
            <span className="toolbar-item-text">表格模板</span>
          </button>

          <button
            className="toolbar-item"
            onClick={() => handleInsertList(false)}
            title="插入无序列表"
            aria-label="插入无序列表"
          >
            <span className="toolbar-item-icon">•</span>
            <span className="toolbar-item-text">列表</span>
          </button>

          <button
            className="toolbar-item"
            onClick={() => handleInsertList(true)}
            title="插入有序列表"
            aria-label="插入有序列表"
          >
            <span className="toolbar-item-icon">1.</span>
            <span className="toolbar-item-text">编号</span>
          </button>
        </div>
      </div>
      
      {/* 格式工具区域 */}
      <div className="toolbar-section">
        <h3 className="toolbar-section-title">格式</h3>
        <div className="toolbar-items">
          <button
            className="toolbar-item"
            onClick={() => handleFormat('bold')}
            title="粗体"
            aria-label="粗体"
          >
            <span className="toolbar-item-icon"><strong>B</strong></span>
            <span className="toolbar-item-text">粗体</span>
          </button>

          <button
            className="toolbar-item"
            onClick={() => handleFormat('italic')}
            title="斜体"
            aria-label="斜体"
          >
            <span className="toolbar-item-icon"><em>I</em></span>
            <span className="toolbar-item-text">斜体</span>
          </button>

          <button
            className="toolbar-item"
            onClick={() => handleFormat('code')}
            title="代码"
            aria-label="代码"
          >
            <span className="toolbar-item-icon">&lt;/&gt;</span>
            <span className="toolbar-item-text">代码</span>
          </button>
        </div>
      </div>

      {/* 关闭按钮 */}
      <div className="toolbar-close">
        <button 
          className="toolbar-close-button"
          onClick={onClose}
          title="关闭工具栏"
          aria-label="关闭工具栏"
        >
          ✕
        </button>
      </div>
    </div>
  );
};

export default FloatingToolbar;
