/* 悬浮按钮样式 */
.floating-button {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  user-select: none;
  outline: none;
}

.floating-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.5);
  background: linear-gradient(135deg, #2563eb, #1e40af);
}

.floating-button:active {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.6);
}

.floating-button:focus {
  outline: 2px solid #93c5fd;
  outline-offset: 2px;
}

/* 打开状态的样式 */
.floating-button.open {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  transform: rotate(90deg);
}

.floating-button.open:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: rotate(90deg) scale(1.1);
}

/* 图标样式 */
.floating-button-icon {
  display: inline-block;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-button.open .floating-button-icon {
  transform: rotate(-90deg);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .floating-button {
    bottom: 16px;
    right: 16px;
    width: 48px;
    height: 48px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .floating-button {
    bottom: 12px;
    right: 12px;
    width: 44px;
    height: 44px;
    font-size: 14px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .floating-button {
    border: 2px solid #000;
    background: #0066cc;
  }
  
  .floating-button:hover {
    background: #0052a3;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .floating-button {
    transition: none;
  }
  
  .floating-button:hover {
    transform: none;
  }
  
  .floating-button-icon {
    transition: none;
  }
}
