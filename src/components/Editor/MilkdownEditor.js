import React, { useRef, useEffect } from 'react';
import { Editor, rootCtx, defaultValueCtx } from '@milkdown/core';
import { commonmark } from '@milkdown/preset-commonmark';
import { nord } from '@milkdown/theme-nord';
import { listener, listenerCtx } from '@milkdown/plugin-listener';
import { history } from '@milkdown/plugin-history';
import { cursor } from '@milkdown/plugin-cursor';
import debounce from 'lodash.debounce';
import './editor.css';

const MilkdownEditor = ({ 
  content = '', 
  onChange, 
  className = '',
  readOnly = false,
  placeholder = '在此输入您的PRD内容...'
}) => {
  const editorRef = useRef(null);
  const editorInstanceRef = useRef(null);
  const onChangeRef = useRef(onChange);

  // 保持onChange引用最新
  useEffect(() => {
    onChangeRef.current = onChange;
  }, [onChange]);

  // 防抖处理内容变化
  const debouncedOnChange = useRef(
    debounce((markdown) => {
      if (onChangeRef.current) {
        onChangeRef.current(markdown);
      }
    }, 300)
  ).current;

  useEffect(() => {
    const createEditor = async () => {
      if (!editorRef.current) return;

      // 清理之前的编辑器
      if (editorInstanceRef.current) {
        try {
          editorInstanceRef.current.destroy();
        } catch (error) {
          console.debug('编辑器销毁错误:', error);
        }
      }

      try {
        const editor = await Editor.make()
          .config((ctx) => {
            ctx.set(rootCtx, editorRef.current);
            ctx.set(defaultValueCtx, content || '');
            
            // 监听内容变化
            ctx.get(listenerCtx).markdownUpdated((ctx, markdown, prevMarkdown) => {
              if (markdown !== prevMarkdown) {
                debouncedOnChange(markdown);
              }
            });
          })
          .use(commonmark)
          .use(nord)
          .use(listener)
          .use(history)
          .use(cursor)
          .create();

        editorInstanceRef.current = editor;
      } catch (error) {
        console.error('Milkdown编辑器创建失败:', error);
        // 回退到简单的textarea
        if (editorRef.current) {
          editorRef.current.innerHTML = `
            <textarea 
              placeholder="${placeholder}"
              value="${content}"
              style="width: 100%; height: 100%; border: none; outline: none; padding: 24px; font-size: 16px; font-family: inherit; resize: none;"
            ></textarea>
          `;
          const textarea = editorRef.current.querySelector('textarea');
          if (textarea) {
            textarea.addEventListener('input', (e) => {
              debouncedOnChange(e.target.value);
            });
          }
        }
      }
    };

    createEditor();

    return () => {
      if (editorInstanceRef.current) {
        try {
          editorInstanceRef.current.destroy();
        } catch (error) {
          console.debug('编辑器清理错误:', error);
        }
      }
      debouncedOnChange.cancel();
    };
  }, [content, placeholder, debouncedOnChange]);

  return (
    <div className={`milkdown-container ${className}`}>
      <div ref={editorRef} style={{ height: '100%' }} />
    </div>
  );
};

export default MilkdownEditor; 