import React, { useState, useEffect, useRef } from 'react';

/**
 * 表格悬浮编辑按钮组件
 * 在表格右上角显示编辑按钮，点击后打开表格编辑器
 */
const TableHoverButton = ({ 
  tableElement, 
  onEdit, 
  className = '' 
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ top: 0, right: 0 });
  const buttonRef = useRef(null);

  useEffect(() => {
    if (!tableElement) return;

    const handleMouseEnter = () => {
      // 计算按钮位置
      const rect = tableElement.getBoundingClientRect();
      setPosition({
        top: rect.top + window.scrollY - 5,
        right: window.innerWidth - rect.right - 5
      });
      setIsVisible(true);
    };

    const handleMouseLeave = (e) => {
      // 检查鼠标是否移动到按钮上
      if (buttonRef.current && buttonRef.current.contains(e.relatedTarget)) {
        return;
      }
      setIsVisible(false);
    };

    const handleButtonMouseLeave = (e) => {
      // 检查鼠标是否移动到表格上
      if (tableElement.contains(e.relatedTarget)) {
        return;
      }
      setIsVisible(false);
    };

    // 添加事件监听器
    tableElement.addEventListener('mouseenter', handleMouseEnter);
    tableElement.addEventListener('mouseleave', handleMouseLeave);

    // 清理函数
    return () => {
      tableElement.removeEventListener('mouseenter', handleMouseEnter);
      tableElement.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [tableElement]);

  if (!isVisible) return null;

  return (
    <div
      ref={buttonRef}
      className={`table-hover-button ${className}`}
      style={{
        position: 'fixed',
        top: position.top,
        right: position.right,
        zIndex: 1000,
        pointerEvents: 'auto'
      }}
      onMouseLeave={(e) => {
        if (!tableElement.contains(e.relatedTarget)) {
          setIsVisible(false);
        }
      }}
    >
      <button
        onClick={onEdit}
        className="bg-blue-500 hover:bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-110"
        title="编辑表格"
      >
        <svg 
          className="w-4 h-4" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" 
          />
        </svg>
      </button>
    </div>
  );
};

export default TableHoverButton;
