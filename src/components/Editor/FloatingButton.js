import React from 'react';
import './FloatingButton.css';

/**
 * 悬浮按钮组件
 * 固定在页面右下角，用于触发悬浮工具栏
 */
const FloatingButton = ({ onClick, isOpen, className = '' }) => {
  return (
    <button
      className={`floating-button ${isOpen ? 'open' : ''} ${className}`}
      onClick={onClick}
      title={isOpen ? '关闭工具箱' : '打开编辑工具箱'}
      aria-label={isOpen ? '关闭工具箱' : '打开编辑工具箱'}
    >
      <span className="floating-button-icon">
        {isOpen ? '✕' : '🛠️'}
      </span>
    </button>
  );
};

export default FloatingButton;
