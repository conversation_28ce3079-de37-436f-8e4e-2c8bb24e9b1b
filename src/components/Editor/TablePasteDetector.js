import { TableConverter } from '../../utils/tableUtils';

/**
 * 表格粘贴检测器
 * 检测粘贴内容是否包含表格，并提供转换功能
 */
export class TablePasteDetector {
  constructor(options = {}) {
    this.options = {
      minRows: 2,           // 最少行数
      minCols: 2,           // 最少列数
      autoDetect: true,     // 自动检测
      showPreview: true,    // 显示预览
      ...options
    };
    
    this.onTableDetected = null;
    this.onConversionComplete = null;
  }

  /**
   * 检测文本是否包含表格
   */
  detectTable(text) {
    if (!text || typeof text !== 'string') {
      return null;
    }

    // 检查是否包含表格标记
    if (!text.includes('|') || !text.includes('---')) {
      return null;
    }

    try {
      // 尝试解析为表格
      const { headers, data } = TableConverter.fromMarkdown(text);
      
      // 验证表格有效性
      if (headers.length >= this.options.minCols && 
          data.length >= this.options.minRows) {
        
        return {
          isValid: true,
          headers,
          data,
          originalText: text,
          preview: this.generatePreview(headers, data)
        };
      }
    } catch (e) {
      console.warn('表格解析失败:', e);
    }

    return null;
  }

  /**
   * 生成表格预览HTML
   */
  generatePreview(headers, data) {
    try {
      return TableConverter.toHTML(data, headers, {
        className: 'preview-table',
        cellPadding: '4px',
        enableWordWrap: true
      });
    } catch (e) {
      console.warn('生成表格预览失败:', e);
      return null;
    }
  }

  /**
   * 处理粘贴事件
   */
  handlePaste(event, callback) {
    const clipboardData = event.clipboardData || window.clipboardData;
    if (!clipboardData) return false;

    const pastedText = clipboardData.getData('text/plain');
    if (!pastedText) return false;

    const tableInfo = this.detectTable(pastedText);
    if (!tableInfo) return false;

    // 阻止默认粘贴行为
    event.preventDefault();

    // 触发表格检测回调
    if (callback) {
      callback(tableInfo, event);
    }

    return true;
  }

  /**
   * 转换表格为HTML
   */
  convertToHTML(tableInfo, options = {}) {
    if (!tableInfo || !tableInfo.isValid) {
      throw new Error('无效的表格信息');
    }

    const htmlOptions = {
      className: 'milkdown-table table-auto border-collapse border border-gray-300 w-full',
      cellPadding: '8px',
      enableWordWrap: true,
      includeHeader: true,
      ...options
    };

    return TableConverter.toHTML(tableInfo.data, tableInfo.headers, htmlOptions);
  }

  /**
   * 获取表格的Markdown格式
   */
  getMarkdown(tableInfo) {
    if (!tableInfo || !tableInfo.isValid) {
      return tableInfo?.originalText || '';
    }

    return TableConverter.toMarkdown(tableInfo.data, tableInfo.headers);
  }

  /**
   * 设置表格检测回调
   */
  onDetected(callback) {
    this.onTableDetected = callback;
    return this;
  }

  /**
   * 设置转换完成回调
   */
  onConverted(callback) {
    this.onConversionComplete = callback;
    return this;
  }

  /**
   * 创建表格元素
   */
  createTableElement(tableInfo, options = {}) {
    const htmlTable = this.convertToHTML(tableInfo, options);
    
    // 创建容器
    const container = document.createElement('div');
    container.innerHTML = htmlTable;
    
    const tableElement = container.querySelector('table');
    if (tableElement) {
      // 添加额外的样式和属性
      tableElement.style.marginTop = '16px';
      tableElement.style.marginBottom = '16px';
      tableElement.setAttribute('data-table-type', 'converted');
      tableElement.setAttribute('data-original-markdown', tableInfo.originalText);
    }
    
    return tableElement;
  }

  /**
   * 检查文本是否可能是表格
   */
  static isLikelyTable(text) {
    if (!text || typeof text !== 'string') return false;
    
    const lines = text.trim().split('\n');
    if (lines.length < 2) return false;
    
    // 检查是否有多行包含管道符
    const pipeLines = lines.filter(line => line.includes('|'));
    if (pipeLines.length < 2) return false;
    
    // 检查是否有分隔符行
    const separatorLine = lines.find(line => 
      line.includes('---') && line.includes('|')
    );
    
    return !!separatorLine;
  }

  /**
   * 从剪贴板获取表格数据
   */
  static async getTableFromClipboard() {
    try {
      if (!navigator.clipboard) {
        throw new Error('剪贴板API不可用');
      }
      
      const text = await navigator.clipboard.readText();
      const detector = new TablePasteDetector();
      
      return detector.detectTable(text);
    } catch (e) {
      console.warn('从剪贴板获取表格失败:', e);
      return null;
    }
  }
}

export default TablePasteDetector;
