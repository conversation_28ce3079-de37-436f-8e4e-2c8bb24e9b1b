import React, { useEffect, useRef, useState } from 'react';
import { Editor, rootCtx, defaultValueCtx } from '@milkdown/core';
import { commonmark } from '@milkdown/preset-commonmark';
import { gfm } from '@milkdown/preset-gfm';
import { nord } from '@milkdown/theme-nord';
import { listener, listenerCtx } from '@milkdown/plugin-listener';
import { history } from '@milkdown/plugin-history';
import { cursor } from '@milkdown/plugin-cursor';
import { indent } from '@milkdown/plugin-indent';
import TableTemplateSelector from './TableTemplateSelector';
import TableInteractionManager from './TableInteractionManager';
import { useTableRenderEnhancer } from './TableRenderEnhancer';
import { TableConverter } from '../../utils/tableUtils';
import FloatingToolbox from './FloatingToolbox';
import TableConversionPrompt from './TableConversionPrompt';
import TablePasteDetector from './TablePasteDetector';

// 移除全局计数器，改用组件级别的状态管理

// 表格插件暂时禁用，使用自定义表格编辑器

// try {
//   const { upload } = require('@milkdown/plugin-upload');
//   uploadPlugin = upload;
// } catch (e) {
//   console.warn('Upload plugin not available:', e.message);
// }

// try {
//   const { menu } = require('@milkdown/plugin-menu');
//   menuPlugin = menu;
// } catch (e) {
//   console.warn('Menu plugin not available:', e.message);
// }

// try {
//   const { slash } = require('@milkdown/plugin-slash');
//   slashPlugin = slash;
// } catch (e) {
//   console.warn('Slash plugin not available:', e.message);
// }

// try {
//   const { tooltip } = require('@milkdown/plugin-tooltip');
//   tooltipPlugin = tooltip;
// } catch (e) {
//   console.warn('Tooltip plugin not available:', e.message);
// }

const EnhancedMilkdownEditor = ({
  content = '',
  onChange,
  onSave,
  placeholder = '开始编写内容...',
  readOnly = false,
  className = '',
  showToolbar = true,
  enableTable = true,
  enableUpload = true,
  enableMenu = true,
  enableSlash = true
}) => {
  const [editorReady, setEditorReady] = useState(false);
  const [error, setError] = useState(null);
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const [externalEditData, setExternalEditData] = useState(null);
  const [savedCursorPosition, setSavedCursorPosition] = useState(null);

  // 表格转换提示相关状态
  const [showTablePrompt, setShowTablePrompt] = useState(false);
  const [pendingTableData, setPendingTableData] = useState(null);
  const editorRef = useRef(null);
  const editorInstanceRef = useRef(null);
  const lastContentRef = useRef(content);
  const hasInitializedRef = useRef(false); // 标记是否已经成功初始化过
  const pasteDetectorRef = useRef(null);

  // 使用表格渲染增强器
  useTableRenderEnhancer(editorRef.current);

  // 初始化编辑器
  useEffect(() => {
    if (!editorRef.current) return;

    const initEditor = async () => {
      try {
        setError(null);

        // 如果已经成功初始化过且编辑器实例存在，直接跳过
        if (hasInitializedRef.current && editorInstanceRef.current) {
          console.log('⚠️ 编辑器已初始化且实例存在，跳过重复初始化');
          return;
        }

        // 如果标记为已初始化但实例不存在，重置标记
        if (hasInitializedRef.current && !editorInstanceRef.current) {
          console.log('🔄 编辑器标记为已初始化但实例不存在，重置标记');
          hasInitializedRef.current = false;
        }

        // 如果已经有编辑器实例，先清理
        if (editorInstanceRef.current) {
          console.log('🧹 清理现有编辑器实例');
          try {
            editorInstanceRef.current.destroy();
          } catch (e) {
            console.warn('清理现有编辑器失败:', e);
          }
          editorInstanceRef.current = null;
        }

        console.log(`🚀 开始初始化编辑器，内容长度:`, content?.length || 0);
        console.log(`🚀 初始化内容前100字符:`, content?.substring(0, 100) || '无内容');

        // 创建编辑器实例
        const editor = Editor.make()
          .config((ctx) => {
            ctx.set(rootCtx, editorRef.current);
            ctx.set(defaultValueCtx, content || '');

            // 设置监听器 - 防止循环更新
            ctx.get(listenerCtx).markdownUpdated((ctx, markdown, prevMarkdown) => {
              if (markdown !== prevMarkdown && onChange && markdown !== lastContentRef.current) {
                console.log('📝 编辑器内容变化，触发onChange，长度:', markdown.length);
                console.log('📝 变化内容前100字符:', markdown.substring(0, 100));

                // 检查是否包含表格语法
                if (markdown.includes('|') && markdown.includes('---')) {
                  console.log('📊 检测到表格语法，准备检查渲染');

                  // 延迟检查表格渲染
                  setTimeout(() => {
                    if (editorRef.current) {
                      const tables = editorRef.current.querySelectorAll('table');
                      console.log(`🔍 表格渲染检查：发现 ${tables.length} 个表格元素`);

                      if (tables.length === 0) {
                        console.warn('⚠️ 表格未渲染，尝试强制重新解析');
                        // 强制重新解析内容
                        forceMarkdownReparse();
                      }
                    }
                  }, 500);
                }

                lastContentRef.current = markdown;
                onChange(markdown);
              }
            });

            // 添加输入监听器，确保实时解析
            ctx.get(listenerCtx).updated((ctx, doc, prevDoc) => {
              console.log('📝 编辑器文档更新');
              // 检查是否需要强制重新解析
              const currentText = doc.textContent;
              if (currentText.includes('|') && currentText.includes('---')) {
                console.log('📊 文档更新中检测到表格语法');
                setTimeout(() => {
                  forceMarkdownReparse();
                }, 100);
              }
            });
          })
          .use([
            nord,
            commonmark,
            gfm,
            listener,
            history,
            cursor,
            indent
          ]);

        // 创建编辑器
        await editor.create();
        editorInstanceRef.current = editor;
        hasInitializedRef.current = true; // 标记为已初始化
        setEditorReady(true);

        console.log('✅ 编辑器初始化成功');

      } catch (e) {
        console.error('Failed to create editor:', e);
        setError(e.message);
        setEditorReady(false);
      }
    };

    initEditor();

    // 清理函数
    return () => {
      console.log('🧹 开始清理编辑器组件');

      if (editorInstanceRef.current) {
        try {
          editorInstanceRef.current.destroy();
          console.log('✅ 编辑器实例销毁成功');
        } catch (e) {
          console.warn('❌ 编辑器销毁失败:', e);
        }
        editorInstanceRef.current = null;
      }

      // 在StrictMode下，不重置初始化状态，避免重复初始化
      // hasInitializedRef 在整个组件生命周期中保持 true
      // 只有在真正的组件卸载时才会被重置（组件实例销毁时）
      console.log('🔄 编辑器清理完成，保持初始化状态以防止StrictMode重复初始化');
    };
  }, []);

  // 处理内容更新 - 防止循环更新
  useEffect(() => {
    if (editorInstanceRef.current && content !== undefined && content !== lastContentRef.current) {
      try {
        console.log('🔄 编辑器内容更新，长度:', content.length);
        console.log('🔄 更新内容前100字符:', content.substring(0, 100));
        console.log('🔄 上次内容长度:', lastContentRef.current?.length || 0);

        editorInstanceRef.current.action((ctx) => {
          ctx.set(defaultValueCtx, content);
        });
        lastContentRef.current = content;
        console.log('✅ 编辑器内容更新完成');
      } catch (e) {
        console.warn('Failed to update content:', e);
      }
    } else {
      console.log('🔄 跳过内容更新 - 编辑器未就绪或内容未变化');
    }
  }, [content]);

  // 快捷键和粘贴事件处理
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Ctrl+S 保存
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        if (onSave) {
          onSave();
        }
      }
    };

    // 初始化表格粘贴检测器
    if (!pasteDetectorRef.current) {
      pasteDetectorRef.current = new TablePasteDetector({
        minRows: 2,
        minCols: 2,
        showPreview: true
      });
    }

    // 粘贴事件处理 - 语雀风格的表格转换提示
    const handlePaste = (e) => {
      console.log('📋 检测到粘贴事件');

      // 使用表格检测器处理粘贴
      const isTableDetected = pasteDetectorRef.current.handlePaste(e, (tableInfo, event) => {
        console.log('📊 检测到表格内容:', tableInfo);

        // 设置提示状态（不需要位置，因为使用固定居中显示）
        setPendingTableData(tableInfo);
        setShowTablePrompt(true);

        console.log('✅ 表格转换提示已显示');
      });

      // 如果不是表格，使用原有的处理逻辑
      if (!isTableDetected) {
        console.log('📋 非表格内容，使用默认处理');
      }
    };

    if (editorReady && editorRef.current) {
      document.addEventListener('keydown', handleKeyDown);
      editorRef.current.addEventListener('paste', handlePaste);

      return () => {
        document.removeEventListener('keydown', handleKeyDown);
        if (editorRef.current) {
          editorRef.current.removeEventListener('paste', handlePaste);
        }
      };
    }
  }, [editorReady, onSave]);

  // 新的表格插入方案 - 使用与列表相同的简单方法
  const insertTableWithReload = (markdownTable) => {
    console.log('🔄 使用简化方案插入表格:', markdownTable);
    console.log('📊 当前状态检查:');
    console.log('- editorInstanceRef.current:', !!editorInstanceRef.current);
    console.log('- onChange:', !!onChange);
    console.log('- content length:', content?.length || 0);
    console.log('- savedCursorPosition:', savedCursorPosition);

    if (!onChange) {
      console.warn('⚠️ onChange不可用，无法插入表格');
      return;
    }

    try {
      const currentContent = content || '';
      let insertPosition = savedCursorPosition;

      // 确定插入位置
      if (insertPosition === null || insertPosition < 0 || insertPosition > currentContent.length) {
        insertPosition = currentContent.length;
        console.log('📍 使用文档末尾作为插入位置:', insertPosition);
      } else {
        console.log('📍 使用保存的光标位置:', insertPosition);
      }

      // 构建新内容
      const beforeCursor = currentContent.substring(0, insertPosition);
      const afterCursor = currentContent.substring(insertPosition);

      console.log('📍 光标前内容长度:', beforeCursor.length);
      console.log('📍 光标后内容长度:', afterCursor.length);

      // 确保表格前后有适当的空行
      let tableWithSpacing = markdownTable;
      if (beforeCursor && !beforeCursor.endsWith('\n\n')) {
        tableWithSpacing = (beforeCursor.endsWith('\n') ? '\n' : '\n\n') + tableWithSpacing;
      }
      if (afterCursor && !afterCursor.startsWith('\n')) {
        tableWithSpacing = tableWithSpacing + '\n\n';
      }

      const newContent = beforeCursor + tableWithSpacing + afterCursor;
      console.log('✅ 新内容构建完成，长度:', newContent.length);
      console.log('📋 插入的表格内容:', tableWithSpacing);

      // 直接使用onChange触发更新（与列表插入相同的方式）
      onChange(newContent);
      console.log('🔄 已触发onChange更新表格');

      // 验证表格渲染
      setTimeout(() => {
        if (editorRef.current) {
          const tableElements = editorRef.current.querySelectorAll('table');
          console.log(`🔍 表格渲染验证：发现 ${tableElements.length} 个表格元素`);

          if (tableElements.length > 0) {
            console.log('✅ 表格插入和渲染成功！');
            tableElements.forEach((table, index) => {
              const rows = table.querySelectorAll('tr');
              console.log(`📊 表格${index + 1}: ${rows.length}行`);
            });
          } else {
            console.warn('⚠️ 表格未渲染');
            console.log('🔍 编辑器DOM内容前200字符:', editorRef.current.innerHTML.substring(0, 200));
          }
        }

        // 清除保存的光标位置
        setSavedCursorPosition(null);
        console.log('🧹 已清除保存的光标位置');
      }, 500);

    } catch (e) {
      console.error('❌ 表格插入失败:', e);
      setSavedCursorPosition(null);
    }
  };

  // 快速插入简单表格
  const handleQuickTableInsert = () => {
    console.log('🔄 快速插入3x3表格');
    console.log('📊 编辑器状态检查:');
    console.log('- editorReady:', editorReady);
    console.log('- editorInstanceRef.current:', !!editorInstanceRef.current);
    console.log('- onChange:', !!onChange);
    console.log('- savedCursorPosition:', savedCursorPosition);

    // 创建一个简单的3x3表格
    const quickTable = `| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 内容1 | 内容2 | 内容3 |
| 内容4 | 内容5 | 内容6 |`;

    console.log('📋 准备插入的表格内容:', quickTable);

    // 使用新的插入方案
    try {
      insertTableWithReload(quickTable);
      console.log('✅ 快速表格插入调用完成');
    } catch (e) {
      console.error('❌ 快速表格插入失败:', e);
    }
  };

  // 表格相关处理函数
  const handleTemplateSelect = (template) => {
    // 通过TableInteractionManager打开表格编辑器
    const tableData = template.data || [['', ''], ['', '']];
    const tableHeaders = template.headers || ['列1', '列2'];

    setExternalEditData({
      headers: tableHeaders,
      data: tableData
    });
    setShowTemplateSelector(false);
  };

  // 插入表格到编辑器的通用函数 - 改进版本
  const insertTableToEditor = (markdownTable) => {
    console.log('🔄 开始插入表格:', markdownTable);
    console.log('📍 当前保存的光标位置:', savedCursorPosition);

    if (!editorInstanceRef.current) {
      console.warn('⚠️ 编辑器实例不可用，使用降级处理');
      fallbackInsertTable(markdownTable);
      return;
    }

    try {
      const currentContent = content || '';
      console.log('📄 当前文档内容长度:', currentContent.length);
      console.log('📄 当前文档前100字符:', currentContent.substring(0, 100));

      // 改进的插入位置计算
      let newContent;
      if (savedCursorPosition !== null && savedCursorPosition >= 0) {
        // 在保存的光标位置插入表格
        const beforeCursor = currentContent.substring(0, savedCursorPosition);
        const afterCursor = currentContent.substring(savedCursorPosition);

        console.log('📍 光标前内容长度:', beforeCursor.length);
        console.log('📍 光标后内容长度:', afterCursor.length);
        console.log('📍 光标前最后20字符:', beforeCursor.slice(-20));
        console.log('📍 光标后前20字符:', afterCursor.slice(0, 20));

        // 确保表格前后有适当的换行
        let tableWithSpacing = markdownTable;

        // 如果光标前面不是空行，添加换行
        if (beforeCursor && !beforeCursor.endsWith('\n')) {
          tableWithSpacing = '\n\n' + tableWithSpacing;
        } else if (beforeCursor && !beforeCursor.endsWith('\n\n')) {
          tableWithSpacing = '\n' + tableWithSpacing;
        }

        // 如果光标后面不是空行，添加换行
        if (afterCursor && !afterCursor.startsWith('\n')) {
          tableWithSpacing = tableWithSpacing + '\n\n';
        } else if (afterCursor && !afterCursor.startsWith('\n\n')) {
          tableWithSpacing = tableWithSpacing + '\n';
        }

        newContent = beforeCursor + tableWithSpacing + afterCursor;
        console.log('📍 在保存位置插入表格，位置:', savedCursorPosition);
        console.log('📍 插入的表格内容:', tableWithSpacing);
      } else {
        // 如果没有保存的位置，立即保存当前光标位置
        console.warn('⚠️ 没有保存的光标位置，立即保存当前位置');
        saveCursorPosition();

        // 使用文档末尾作为降级
        const tableWithSpacing = currentContent ? '\n\n' + markdownTable + '\n' : markdownTable + '\n';
        newContent = currentContent + tableWithSpacing;
        console.log('📍 插入到文档末尾');
      }

      console.log('✅ 表格内容准备完成，新文档长度:', newContent.length);

      // 通过onChange触发整个文档的重新解析和渲染
      if (onChange) {
        onChange(newContent);
        console.log('🔄 已触发onChange，开始重新渲染');
      } else {
        console.warn('⚠️ onChange不可用');
      }

      // 多阶段表格渲染检查和验证
      const checkTableRendering = (attempt = 1, maxAttempts = 5) => {
        setTimeout(() => {
          if (editorRef.current) {
            const tableElements = editorRef.current.querySelectorAll('table');
            console.log(`🔍 表格渲染检查 (第${attempt}次)，发现表格元素:`, tableElements.length);

            if (tableElements.length > 0) {
              console.log('✅ 表格渲染成功！');

              // 验证表格内容
              tableElements.forEach((table, index) => {
                const rows = table.querySelectorAll('tr');
                const cells = table.querySelectorAll('td, th');
                console.log(`📊 表格${index + 1}: ${rows.length}行, ${cells.length}个单元格`);
              });

              // 清除保存的光标位置
              setSavedCursorPosition(null);
              console.log('🧹 清除保存的光标位置');
            } else if (attempt < maxAttempts) {
              console.log(`⏳ 表格尚未渲染，${attempt + 1}秒后重试...`);
              checkTableRendering(attempt + 1, maxAttempts);
            } else {
              console.warn('⚠️ 表格渲染失败，已达到最大重试次数');

              // 检查文档中是否有表格Markdown
              const docText = editorRef.current.textContent || '';
              if (docText.includes('|')) {
                console.log('📝 文档中包含表格Markdown，可能需要手动刷新');
              }

              // 清除保存的光标位置
              setSavedCursorPosition(null);
            }
          }
        }, attempt * 200); // 递增延迟
      };

      // 开始检查
      checkTableRendering();

    } catch (e) {
      console.error('❌ 插入表格失败:', e);
      // 使用降级处理
      fallbackInsertTable(markdownTable);
    }
  };

  // 降级插入表格函数 - 改进版本
  const fallbackInsertTable = (markdownTable) => {
    console.log('🔄 使用降级方式插入表格');

    if (!onChange) {
      console.error('❌ 降级插入失败: onChange不可用');
      return;
    }

    try {
      const currentContent = content || '';
      let newContent;

      // 使用保存的光标位置或插入到末尾
      if (savedCursorPosition !== null && savedCursorPosition >= 0) {
        const beforeCursor = currentContent.substring(0, savedCursorPosition);
        const afterCursor = currentContent.substring(savedCursorPosition);
        const tableWithSpacing = '\n\n' + markdownTable + '\n\n';
        newContent = beforeCursor + tableWithSpacing + afterCursor;
        console.log('📍 降级模式：在保存位置插入表格，位置:', savedCursorPosition);
      } else {
        const tableWithSpacing = currentContent ? '\n\n' + markdownTable + '\n\n' : markdownTable + '\n';
        newContent = currentContent + tableWithSpacing;
        console.log('📍 降级模式：插入到文档末尾');
      }

      onChange(newContent);
      console.log('✅ 降级插入完成，新文档长度:', newContent.length);

      // 多次尝试强制渲染和检查
      const checkAndRender = (attempt = 1, maxAttempts = 4) => {
        setTimeout(() => {
          console.log(`🔄 降级模式强制渲染尝试 ${attempt}`);
          forceTableRender();

          if (editorRef.current) {
            const tableElements = editorRef.current.querySelectorAll('table');
            console.log(`🔍 降级模式检查 ${attempt}:`, tableElements.length, '个表格');

            if (tableElements.length === 0 && attempt < maxAttempts) {
              // 如果没有表格且未达到最大尝试次数，继续尝试
              checkAndRender(attempt + 1, maxAttempts);
            } else if (tableElements.length > 0) {
              console.log('✅ 降级模式表格渲染成功！');
              setSavedCursorPosition(null);
            }
          }
        }, attempt * 200);
      };

      checkAndRender();

      // 最终清理
      setTimeout(() => {
        setSavedCursorPosition(null);
        console.log('🧹 降级模式：清除保存的光标位置');
      }, 1000);

    } catch (e) {
      console.error('❌ 降级插入异常:', e);
      setSavedCursorPosition(null);
    }
  };

  // 保存当前光标位置 - 改进版本
  const saveCursorPosition = () => {
    console.log('🔄 开始保存光标位置...');

    if (!editorInstanceRef.current) {
      console.warn('⚠️ 编辑器实例不可用，无法保存光标位置');
      // 降级：使用文档末尾
      if (content) {
        setSavedCursorPosition(content.length);
        console.log('💾 降级保存光标位置到文档末尾，位置:', content.length);
      }
      return;
    }

    try {
      editorInstanceRef.current.action((ctx) => {
        console.log('🔄 正在获取编辑器视图...');
        const view = ctx.get('editorView');

        if (!view || !view.state) {
          console.warn('⚠️ 无法获取编辑器视图或状态');
          return;
        }

        const pos = view.state.selection.head;
        console.log('📍 ProseMirror光标位置:', pos);

        // 将ProseMirror位置转换为文本位置
        const doc = view.state.doc;
        let textPos = 0;
        let currentPos = 0;

        console.log('🔄 开始转换ProseMirror位置到文本位置...');

        doc.descendants((node, nodePos) => {
          if (currentPos >= pos) return false;

          if (node.isText) {
            const nodeEnd = nodePos + node.nodeSize;
            if (pos <= nodeEnd) {
              textPos += (pos - nodePos);
              console.log('📍 在文本节点中找到位置，textPos:', textPos);
              return false;
            } else {
              textPos += node.text.length;
            }
          } else if (node.isBlock && node.type.name !== 'doc') {
            textPos += 1; // 为块级元素添加换行符
          }

          currentPos = nodePos + node.nodeSize;
          return true;
        });

        setSavedCursorPosition(textPos);
        console.log('💾 保存光标位置成功 - ProseMirror位置:', pos, '文本位置:', textPos);

        // 验证保存的位置
        if (content) {
          const beforeCursor = content.substring(0, textPos);
          const afterCursor = content.substring(textPos);
          console.log('✅ 验证保存位置 - 前面字符数:', beforeCursor.length, '后面字符数:', afterCursor.length);
          console.log('✅ 光标前最后10字符:', beforeCursor.slice(-10));
          console.log('✅ 光标后前10字符:', afterCursor.slice(0, 10));
        }
      });
    } catch (e) {
      console.error('❌ 保存光标位置失败:', e);
      // 降级：保存当前内容长度作为插入位置
      if (content) {
        setSavedCursorPosition(content.length);
        console.log('💾 降级保存光标位置到文档末尾，位置:', content.length);
      }
    }
  };

  // 恢复光标位置
  const restoreCursorPosition = () => {
    if (editorInstanceRef.current && savedCursorPosition !== null) {
      try {
        editorInstanceRef.current.action((ctx) => {
          const view = ctx.get('editorView');
          if (view && view.state && view.dispatch) {
            const { state, dispatch } = view;
            const { tr } = state;

            // 确保位置在有效范围内
            const maxPos = state.doc.content.size;
            const safePos = Math.min(savedCursorPosition, maxPos);

            // 设置光标位置
            tr.setSelection(state.selection.constructor.create(tr.doc, safePos));
            dispatch(tr);

            console.log('🔄 恢复光标位置:', safePos);
          }
        });
      } catch (e) {
        console.warn('恢复光标位置失败:', e);
      }
    }
  };

  // 插入列表函数 - 使用与表格相同的插入机制
  const insertList = (ordered = false) => {
    console.log('🔄 开始插入列表，有序:', ordered);
    console.log('📍 当前保存的光标位置:', savedCursorPosition);

    if (!onChange) {
      console.warn('⚠️ onChange不可用，无法插入列表');
      return;
    }

    try {
      const currentContent = content || '';
      let insertPosition = savedCursorPosition;

      // 确定插入位置
      if (insertPosition === null || insertPosition < 0 || insertPosition > currentContent.length) {
        insertPosition = currentContent.length;
        console.log('📍 使用文档末尾作为插入位置:', insertPosition);
      }

      // 构建列表内容
      const listMarkdown = ordered ?
        '1. 列表项\n2. 列表项' :
        '- 列表项\n- 列表项';

      // 构建新内容
      const beforeCursor = currentContent.substring(0, insertPosition);
      const afterCursor = currentContent.substring(insertPosition);

      // 确保列表前后有适当的空行
      let listWithSpacing = listMarkdown;
      if (beforeCursor && !beforeCursor.endsWith('\n\n')) {
        listWithSpacing = (beforeCursor.endsWith('\n') ? '\n' : '\n\n') + listWithSpacing;
      }
      if (afterCursor && !afterCursor.startsWith('\n')) {
        listWithSpacing = listWithSpacing + '\n\n';
      }

      const newContent = beforeCursor + listWithSpacing + afterCursor;
      console.log('✅ 列表内容构建完成，长度:', newContent.length);

      // 触发内容更新
      onChange(newContent);
      console.log('🔄 已触发onChange更新列表');

      // 清除保存的光标位置
      setSavedCursorPosition(null);
      console.log('🧹 已清除保存的光标位置');

    } catch (e) {
      console.error('❌ 插入列表失败:', e);
      setSavedCursorPosition(null);
    }
  };

  // 强制Markdown重新解析函数
  const forceMarkdownReparse = () => {
    console.log('🔄 强制Markdown重新解析');

    if (!editorInstanceRef.current) {
      console.warn('⚠️ 编辑器实例不可用');
      return;
    }

    try {
      editorInstanceRef.current.action((ctx) => {
        const view = ctx.get('editorView');
        if (view && view.state) {
          const { state } = view;
          const currentContent = state.doc.textContent;

          console.log('🔄 当前编辑器内容长度:', currentContent.length);
          console.log('🔄 检查表格语法:', currentContent.includes('|') && currentContent.includes('---'));

          // 方法1: 重新设置整个文档内容
          console.log('🔄 方法1: 重新设置文档内容');
          ctx.set(defaultValueCtx, currentContent);

          // 方法2: 强制重新创建文档
          setTimeout(() => {
            console.log('🔄 方法2: 触发onChange重新解析');
            if (onChange) {
              onChange(currentContent);
            }
          }, 50);
        }
      });

      // 方法3: 触发编辑器重新渲染
      setTimeout(() => {
        console.log('🔄 方法3: 检查渲染结果');
        if (editorRef.current) {
          const tables = editorRef.current.querySelectorAll('table');
          console.log(`📊 重新解析后发现 ${tables.length} 个表格`);

          if (tables.length === 0) {
            console.warn('⚠️ 重新解析后仍无表格，尝试完全重建编辑器');
            recreateEditor();
          }
        }
      }, 200);

    } catch (e) {
      console.error('❌ 强制重新解析失败:', e);
    }
  };

  // 完全重建编辑器（最后的手段）
  const recreateEditor = async () => {
    console.log('🔄 完全重建编辑器');

    try {
      const currentContent = content || '';

      // 销毁当前编辑器
      if (editorInstanceRef.current) {
        editorInstanceRef.current.destroy();
        editorInstanceRef.current = null;
      }

      // 短暂延迟后重新创建
      setTimeout(async () => {
        try {
          const editor = Editor.make()
            .config((ctx) => {
              ctx.set(rootCtx, editorRef.current);
              ctx.set(defaultValueCtx, currentContent);

              // 重新设置监听器
              ctx.get(listenerCtx).markdownUpdated((ctx, markdown, prevMarkdown) => {
                if (markdown !== prevMarkdown && onChange && markdown !== lastContentRef.current) {
                  lastContentRef.current = markdown;
                  onChange(markdown);
                }
              });
            })
            .use([
              nord,
              commonmark,
              gfm,
              listener,
              history,
              cursor,
              indent
            ]);

          await editor.create();
          editorInstanceRef.current = editor;

          console.log('✅ 编辑器重建完成');

          // 验证表格渲染
          setTimeout(() => {
            if (editorRef.current) {
              const tables = editorRef.current.querySelectorAll('table');
              console.log(`📊 重建后发现 ${tables.length} 个表格`);
            }
          }, 500);

        } catch (e) {
          console.error('❌ 编辑器重建失败:', e);
        }
      }, 100);

    } catch (e) {
      console.error('❌ 编辑器重建过程失败:', e);
    }
  };

  // 强制触发表格渲染的辅助函数 - 改进版本
  const forceTableRender = () => {
    console.log('🔄 强制触发表格渲染');
    forceMarkdownReparse();
  };

  // 测试表格插入功能
  const testTableInsertion = () => {
    console.log('🧪 开始测试表格插入功能');

    // 保存当前光标位置
    saveCursorPosition();

    // 创建测试表格
    const testTable = `| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |`;

    // 延迟插入以模拟用户操作
    setTimeout(() => {
      console.log('🧪 插入测试表格');
      insertTableToEditor(testTable);
    }, 100);
  };

  // 简单的光标位置测试
  const testCursorPosition = () => {
    console.log('🧪 测试光标位置保存...');
    saveCursorPosition();

    setTimeout(() => {
      console.log('🧪 当前保存的光标位置:', savedCursorPosition);

      if (savedCursorPosition !== null) {
        const testTable = '| 测试 | 表格 |\n|------|------|\n| 数据1 | 数据2 |';
        console.log('🧪 插入测试表格到保存位置');
        insertTableToEditor(testTable);
      }
    }, 500);
  };

  // 直接测试表格插入（绕过工具箱）
  const directTestTableInsertion = () => {
    console.log('🧪 直接测试表格插入（绕过工具箱）');

    // 1. 保存光标位置
    saveCursorPosition();

    // 2. 等待一下再插入
    setTimeout(() => {
      const testTable = '| 直接测试 | 表格 |\n|----------|------|\n| 数据1 | 数据2 |';
      console.log('🧪 直接插入测试表格');
      insertTableToEditor(testTable);
    }, 500);
  };

  // 测试工具箱事件
  const testToolboxEvent = () => {
    console.log('🧪 测试工具箱事件');
    console.log('🔄 FloatingToolbox: 触发表格插入');
    saveCursorPosition(); // 保存光标位置
    setShowTemplateSelector(true);
  };

  // 检查Milkdown编辑器的表格渲染状态
  const checkMilkdownTableSupport = () => {
    console.log('🔍 检查Milkdown表格支持状态');

    if (!editorInstanceRef.current) {
      console.warn('⚠️ 编辑器实例不可用');
      return;
    }

    try {
      editorInstanceRef.current.action((ctx) => {
        const view = ctx.get('editorView');
        if (view && view.state) {
          const { state } = view;
          const { schema } = state;

          console.log('📋 编辑器Schema信息:');
          console.log('- 节点类型:', Object.keys(schema.nodes));
          console.log('- 标记类型:', Object.keys(schema.marks));

          // 检查是否有表格相关的节点
          const hasTable = 'table' in schema.nodes;
          const hasTableRow = 'table_row' in schema.nodes;
          const hasTableCell = 'table_cell' in schema.nodes;

          console.log('📊 表格支持检查:');
          console.log('- table节点:', hasTable);
          console.log('- table_row节点:', hasTableRow);
          console.log('- table_cell节点:', hasTableCell);

          if (hasTable && hasTableRow && hasTableCell) {
            console.log('✅ Milkdown表格支持正常');
          } else {
            console.warn('⚠️ Milkdown表格支持不完整');
          }
        }
      });
    } catch (e) {
      console.error('❌ 检查表格支持失败:', e);
    }
  };

  // 测试表格渲染的简单函数
  const testTableRender = () => {
    console.log('🧪 测试表格渲染');

    // 先检查表格支持
    checkMilkdownTableSupport();

    const testMarkdown = `这是一个测试表格：

| 姓名 | 年龄 | 城市 |
|------|------|------|
| 张三 | 25 | 北京 |
| 李四 | 30 | 上海 |
| 王五 | 28 | 广州 |

表格测试完成。`;

    if (onChange) {
      onChange(testMarkdown);
      console.log('✅ 测试内容已设置，等待渲染...');

      // 延迟检查渲染结果
      setTimeout(() => {
        if (editorRef.current) {
          const tables = editorRef.current.querySelectorAll('table');
          console.log(`🔍 渲染检查：发现 ${tables.length} 个表格元素`);

          if (tables.length > 0) {
            console.log('✅ 表格渲染成功！');
            tables.forEach((table, index) => {
              const rows = table.querySelectorAll('tr');
              console.log(`📊 表格 ${index + 1}: ${rows.length} 行`);
            });
          } else {
            console.warn('⚠️ 表格未渲染，检查DOM结构...');
            console.log('🔍 编辑器DOM内容:', editorRef.current.innerHTML.substring(0, 500));
            forceTableRender();
          }
        }
      }, 1000);
    }
  };

  // 在开发环境下暴露测试函数到全局
  if (process.env.NODE_ENV === 'development') {
    window.testTableInsertion = testTableInsertion;
    window.testCursorPosition = testCursorPosition;
    window.directTestTableInsertion = directTestTableInsertion;
    window.testToolboxEvent = testToolboxEvent;
    window.saveCursorPosition = saveCursorPosition;
    window.forceTableRender = forceTableRender;
    window.testQuickTableInsert = handleQuickTableInsert;
    window.testTableRender = testTableRender; // 新增表格渲染测试
    window.checkMilkdownTableSupport = checkMilkdownTableSupport; // 检查表格支持
    window.testQuickInsert = handleQuickTableInsert; // 测试快速插入
    window.insertTableWithReload = insertTableWithReload; // 测试重新加载插入
    // testMarkdownRendering 将在下面定义

    // 暴露状态查看
    window.getCurrentCursorPosition = () => savedCursorPosition;
    window.getCurrentContent = () => content;
    window.getEditorReady = () => editorReady;
    window.getReadOnly = () => readOnly;
    window.getShowTemplateSelector = () => showTemplateSelector;

    // 暴露组件状态和调试方法
    window.debugEditor = () => {
      console.log('📊 编辑器状态调试信息:');
      console.log('- editorReady:', editorReady);
      console.log('- readOnly:', readOnly);
      console.log('- savedCursorPosition:', savedCursorPosition);
      console.log('- showTemplateSelector:', showTemplateSelector);
      console.log('- content length:', content?.length || 0);
      console.log('- editorInstanceRef.current:', !!editorInstanceRef.current);

      if (editorRef.current) {
        const tables = editorRef.current.querySelectorAll('table');
        const codeBlocks = editorRef.current.querySelectorAll('pre');
        const paragraphs = editorRef.current.querySelectorAll('p');

        console.log('📊 DOM元素统计:');
        console.log('- 表格数量:', tables.length);
        console.log('- 代码块数量:', codeBlocks.length);
        console.log('- 段落数量:', paragraphs.length);

        // 检查是否有未渲染的表格语法
        const textContent = editorRef.current.textContent || '';
        const hasTableSyntax = textContent.includes('|') && textContent.includes('---');
        console.log('- 包含表格语法:', hasTableSyntax);

        if (hasTableSyntax && tables.length === 0) {
          console.warn('⚠️ 发现表格语法但没有表格元素，可能存在渲染问题');
          console.log('📋 文本内容前200字符:', textContent.substring(0, 200));
        }

        console.log('📋 DOM内容前200字符:', editorRef.current.innerHTML.substring(0, 200));
      }

      return {
        editorReady,
        readOnly,
        contentLength: content?.length || 0,
        tableCount: editorRef.current?.querySelectorAll('table').length || 0,
        hasTableSyntax: editorRef.current?.textContent?.includes('|') && editorRef.current?.textContent?.includes('---')
      };
    };

    // 暴露强制渲染方法
    window.forceTableRender = forceTableRender;
    window.forceMarkdownReparse = forceMarkdownReparse;
    window.recreateEditor = recreateEditor;

    // 检查Milkdown表格支持
    window.checkMilkdownTableSupport = () => {
      console.log('🔍 检查Milkdown表格支持');

      if (editorInstanceRef.current) {
        try {
          editorInstanceRef.current.action((ctx) => {
            console.log('📋 编辑器上下文可用');

            // 检查GFM插件是否正确加载
            try {
              const view = ctx.get('editorView');
              console.log('📋 EditorView可用:', !!view);

              if (view && view.state) {
                const { schema } = view.state;
                console.log('📋 Schema可用:', !!schema);
                console.log('📋 Schema节点:', Object.keys(schema.nodes));
                console.log('📋 是否支持table:', !!schema.nodes.table);
                console.log('📋 是否支持table_row:', !!schema.nodes.table_row);
                console.log('📋 是否支持table_cell:', !!schema.nodes.table_cell);

                return {
                  hasTable: !!schema.nodes.table,
                  hasTableRow: !!schema.nodes.table_row,
                  hasTableCell: !!schema.nodes.table_cell,
                  allNodes: Object.keys(schema.nodes)
                };
              }
            } catch (e) {
              console.error('❌ 检查EditorView失败:', e);
            }
          });
        } catch (e) {
          console.error('❌ 检查编辑器上下文失败:', e);
        }
      } else {
        console.warn('⚠️ 编辑器实例不可用');
      }
    };

    // 测试Markdown实时渲染功能
    window.testMarkdownRendering = () => {
      console.log('🧪 测试Markdown实时渲染功能');

      if (!editorInstanceRef.current) {
        console.warn('⚠️ 编辑器实例不可用');
        return;
      }

      // 检查编辑器模式
      try {
        editorInstanceRef.current.action((ctx) => {
          const view = ctx.get('editorView');
          if (view && view.state) {
            console.log('📊 编辑器状态信息:');
            console.log('- 编辑器类型:', view.constructor.name);
            console.log('- 文档类型:', view.state.doc.type.name);
            console.log('- 当前内容:', view.state.doc.textContent.substring(0, 100));

            // 检查是否是WYSIWYG模式
            const isWysiwyg = view.state.doc.type.name === 'doc';
            console.log('- WYSIWYG模式:', isWysiwyg);

            if (isWysiwyg) {
              console.log('✅ 编辑器处于WYSIWYG模式，应该支持实时渲染');
            } else {
              console.warn('⚠️ 编辑器可能不在WYSIWYG模式');
            }
          }
        });
      } catch (e) {
        console.error('❌ 检查编辑器模式失败:', e);
      }

      // 测试简单的Markdown渲染
      const testContent = `# 测试标题

这是一个**粗体**文本和*斜体*文本。

## 测试表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |

测试完成。`;

      console.log('🔄 设置测试内容...');
      if (onChange) {
        onChange(testContent);

        // 检查渲染结果
        setTimeout(() => {
          if (editorRef.current) {
            console.log('🔍 检查渲染结果:');

            const headings = editorRef.current.querySelectorAll('h1, h2, h3');
            const bold = editorRef.current.querySelectorAll('strong, b');
            const italic = editorRef.current.querySelectorAll('em, i');
            const tables = editorRef.current.querySelectorAll('table');

            console.log(`- 标题元素: ${headings.length}`);
            console.log(`- 粗体元素: ${bold.length}`);
            console.log(`- 斜体元素: ${italic.length}`);
            console.log(`- 表格元素: ${tables.length}`);

            if (headings.length > 0 && bold.length > 0 && tables.length > 0) {
              console.log('✅ Markdown实时渲染正常工作！');
            } else {
              console.warn('⚠️ Markdown渲染可能有问题');
              console.log('🔍 当前DOM结构:', editorRef.current.innerHTML.substring(0, 300));
            }
          }
        }, 1000);
      }
    };
  }

  // 插入格式化文本函数 - 移到组件主体中
  const insertFormat = (type) => {
    if (editorInstanceRef.current) {
      try {
        editorInstanceRef.current.action((ctx) => {
          const view = ctx.get('editorView');
          if (!view) return;

          const { state, dispatch } = view;
          const { selection } = state;
          const { from, to } = selection;

          let formatText = '';
          let wrapText = '';

          switch (type) {
            case 'bold':
              wrapText = '**';
              formatText = selection.empty ? '粗体文本' : '';
              break;
            case 'italic':
              wrapText = '*';
              formatText = selection.empty ? '斜体文本' : '';
              break;
            case 'code':
              wrapText = '`';
              formatText = selection.empty ? '代码' : '';
              break;
            default:
              return;
          }

          const tr = state.tr;

          if (selection.empty) {
            // 没有选中文本，插入格式化的示例文本
            const formattedText = wrapText + formatText + wrapText;
            tr.insertText(formattedText, from);
          } else {
            // 有选中文本，在选中文本前后添加格式化标记
            tr.insertText(wrapText, to);
            tr.insertText(wrapText, from);
          }

          dispatch(tr);
        });
      } catch (e) {
        console.warn('Failed to insert format:', e);
      }
    }
  };

  const handleTableCancel = () => {
    setShowTemplateSelector(false);
  };

  // 插入表格作为新表格（添加到文档末尾）
  const insertTableAsNew = (markdownTable) => {
    console.log('📋 保存为新表格:', markdownTable);

    try {
      const currentContent = content || '';
      console.log('📄 当前文档内容长度:', currentContent.length);

      // 在文档末尾添加新表格，间隔2行空位
      const newContent = currentContent + '\n\n\n' + markdownTable + '\n\n';

      console.log('📄 新文档内容长度:', newContent.length);
      console.log('📄 新增内容预览:', markdownTable.substring(0, 100));

      // 更新编辑器内容
      if (onChange) {
        onChange(newContent);
        console.log('✅ 新表格已添加到文档末尾');
      }

      // 延迟验证表格渲染
      setTimeout(() => {
        if (editorRef.current) {
          const tableElements = editorRef.current.querySelectorAll('table');
          console.log(`🔍 表格渲染验证：发现 ${tableElements.length} 个表格元素`);

          if (tableElements.length > 0) {
            console.log('✅ 新表格渲染成功！');
            // 滚动到最后一个表格
            const lastTable = tableElements[tableElements.length - 1];
            lastTable.scrollIntoView({ behavior: 'smooth', block: 'center' });
          } else {
            console.warn('⚠️ 新表格未渲染，尝试强制重新解析');
            forceMarkdownReparse();
          }
        }
      }, 500);

    } catch (e) {
      console.error('❌ 保存新表格失败:', e);
    }
  };

  // 处理外部表格编辑完成
  const handleExternalEditComplete = (result) => {
    if (result.action === 'save') {
      // 插入编辑后的表格
      const markdownTable = TableConverter.toMarkdown(result.data, result.headers);
      insertTableToEditor(markdownTable);
    } else if (result.action === 'save_as_new') {
      // 保存为新表格：添加到现有内容下方
      const markdownTable = TableConverter.toMarkdown(result.data, result.headers);
      insertTableAsNew(markdownTable);
    }
    // 清除外部编辑数据
    setExternalEditData(null);
  };

  // 处理表格转换确认
  const handleTableConversionConfirm = () => {
    if (!pendingTableData || !editorInstanceRef.current) {
      console.warn('⚠️ 无法转换表格：缺少数据或编辑器实例');
      setShowTablePrompt(false);
      setPendingTableData(null);
      return;
    }

    try {
      console.log('✅ 用户确认转换表格');

      // 创建表格元素
      const tableElement = pasteDetectorRef.current.createTableElement(pendingTableData, {
        className: 'milkdown-table table-auto border-collapse border border-gray-300 w-full'
      });

      if (tableElement && editorRef.current) {
        // 插入表格到编辑器
        const editorContainer = editorRef.current.querySelector('.milkdown');
        if (editorContainer) {
          editorContainer.appendChild(tableElement);

          // 更新编辑器内容
          const markdownTable = pasteDetectorRef.current.getMarkdown(pendingTableData);
          const currentContent = content || '';
          const newContent = currentContent + '\n\n' + markdownTable + '\n\n';

          if (onChange) {
            onChange(newContent);
          }

          console.log('✅ 表格转换完成');
        }
      }
    } catch (e) {
      console.error('❌ 表格转换失败:', e);
    } finally {
      // 清理状态
      setShowTablePrompt(false);
      setPendingTableData(null);
    }
  };

  // 处理表格转换取消
  const handleTableConversionCancel = () => {
    console.log('❌ 用户取消表格转换');

    if (pendingTableData && editorInstanceRef.current) {
      try {
        // 插入原始Markdown文本
        const originalText = pendingTableData.originalText;
        const currentContent = content || '';
        const newContent = currentContent + '\n\n' + originalText + '\n\n';

        if (onChange) {
          onChange(newContent);
        }

        console.log('📝 已插入原始Markdown文本');
      } catch (e) {
        console.error('❌ 插入原始文本失败:', e);
      }
    }

    // 清理状态
    setShowTablePrompt(false);
    setPendingTableData(null);
  };

  // 工具栏组件
  const Toolbar = () => {
    if (!showToolbar || !editorReady) return null;

    const insertTable = () => {
      setShowTemplateSelector(true);
    };

    const insertList = (ordered = false) => {
      if (editorInstanceRef.current) {
        try {
          editorInstanceRef.current.action((ctx) => {
            const view = ctx.get('editorView');
            const { state, dispatch } = view;
            const { tr } = state;
            const listMarkdown = ordered ? '\n\n1. 列表项\n2. 列表项\n\n' : '\n\n- 列表项\n- 列表项\n\n';

            // 在光标位置插入列表
            const pos = state.selection.head;
            tr.insertText(listMarkdown, pos);
            dispatch(tr);
          });
        } catch (e) {
          console.warn('Failed to insert list:', e);
          // 降级到简单文本追加
          if (onChange) {
            const listMarkdown = ordered ? '\n\n1. 列表项\n2. 列表项\n\n' : '\n\n- 列表项\n- 列表项\n\n';
            const newContent = content + listMarkdown;
            onChange(newContent);
          }
        }
      }
    };

    // 插入格式化文本
    const insertFormat = (type) => {
      if (editorInstanceRef.current) {
        try {
          editorInstanceRef.current.action((ctx) => {
            const view = ctx.get('editorView');
            if (!view) return;

            const { state, dispatch } = view;
            const { selection } = state;
            const { from, to } = selection;

            let formatText = '';
            let wrapText = '';

            switch (type) {
              case 'bold':
                wrapText = '**';
                formatText = selection.empty ? '粗体文本' : '';
                break;
              case 'italic':
                wrapText = '*';
                formatText = selection.empty ? '斜体文本' : '';
                break;
              case 'code':
                wrapText = '`';
                formatText = selection.empty ? '代码' : '';
                break;
              default:
                return;
            }

            const tr = state.tr;

            if (selection.empty) {
              // 没有选中文本，插入格式化的示例文本
              const formattedText = wrapText + formatText + wrapText;
              tr.insertText(formattedText, from);
            } else {
              // 有选中文本，在选中文本前后添加格式化标记
              tr.insertText(wrapText, to);
              tr.insertText(wrapText, from);
            }

            dispatch(tr);
          });
        } catch (e) {
          console.warn('Failed to insert format:', e);
        }
      }
    };

    return (
      <div className="enhanced-editor-toolbar flex items-center gap-2 p-2 border-b border-gray-200 bg-gray-50 sticky top-0 z-10 shadow-sm">
        <button
          onClick={() => insertList(false)}
          className="toolbar-btn px-2 py-1 text-sm border rounded hover:bg-gray-100"
          title="无序列表"
        >
          • 列表
        </button>
        <button
          onClick={() => insertList(true)}
          className="toolbar-btn px-2 py-1 text-sm border rounded hover:bg-gray-100"
          title="有序列表"
        >
          1. 列表
        </button>
        {enableTable && (
          <button
            onClick={insertTable}
            className="toolbar-btn px-2 py-1 text-sm border rounded hover:bg-gray-100"
            title="插入表格"
          >
            📊 表格
          </button>
        )}
        <div className="flex-1"></div>
        <span className="text-xs text-gray-500">
          {editorReady ? '编辑器已就绪' : '加载中...'}
        </span>
      </div>
    );
  };

  // 错误状态
  if (error) {
    return (
      <div className={`enhanced-milkdown-editor error ${className}`}>
        <div className="error-message p-4 bg-red-50 border border-red-200 rounded">
          <h3 className="text-red-800 font-medium">编辑器加载失败</h3>
          <p className="text-red-600 text-sm mt-1">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
          >
            重新加载
          </button>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`enhanced-milkdown-editor ${className}`}
      style={{
        width: '100%',
        height: '100%',
        minWidth: '0', // 允许收缩
        maxWidth: '100%',
        overflow: 'hidden',
        position: 'relative', // 为悬浮工具箱提供定位上下文
        boxSizing: 'border-box'
      }}
    >
      {/* 移除固定工具栏，改为悬浮工具箱 */}
      {/* <Toolbar /> */}

      <div
        ref={editorRef}
        className={`editor-container ${readOnly ? 'readonly' : ''}`}
        style={{
          height: '100%',
          minHeight: '400px', // 增加最小高度确保编辑体验
          width: '100%',
          minWidth: '0', // 允许收缩
          maxWidth: '100%',
          overflow: 'auto',
          padding: '24px',
          boxSizing: 'border-box'
        }}
      />

      {!editorReady && (
        <div className="loading-overlay absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
          <div className="text-gray-500">加载编辑器中...</div>
        </div>
      )}

      {/* 悬浮工具箱 - 只在编辑器准备好且不是只读模式时显示 */}
      {editorReady && !readOnly && (
        <FloatingToolbox
          editorRef={editorRef}
          editorInstance={editorInstanceRef.current}
          onInsertTable={() => {
            console.log('🔄 FloatingToolbox: 触发表格模板选择');
            saveCursorPosition(); // 保存光标位置
            setShowTemplateSelector(true);
          }}
          onInsertQuickTable={() => {
            console.log('🔄 FloatingToolbox: 触发快速表格插入');
            saveCursorPosition(); // 保存光标位置
            handleQuickTableInsert();
          }}
          onInsertList={(ordered) => {
            console.log('🔄 FloatingToolbox: 触发列表插入', { ordered });
            saveCursorPosition(); // 保存光标位置
            insertList(ordered);
          }}
          onInsertFormat={(type) => {
            console.log('🔄 FloatingToolbox: 触发格式化', { type });
            saveCursorPosition(); // 保存光标位置
            insertFormat(type);
          }}
          onToolboxOpen={() => {
            console.log('🔄 FloatingToolbox: 工具箱打开');
            saveCursorPosition(); // 工具箱打开时保存光标位置
          }}
          onToolboxClose={() => {
            console.log('🔄 FloatingToolbox: 工具箱关闭');
            // 工具箱关闭时可以选择是否清除保存的位置
          }}
        />
      )}

      {/* 表格交互管理器 */}
      {editorReady && editorRef.current && (
        <TableInteractionManager
          editorContainer={editorRef.current}
          onTableUpdate={(markdownTable) => {
            // 当表格更新时，通知父组件
            if (onChange) {
              // 这里可以根据需要更新整个文档内容
              console.log('Table updated:', markdownTable);
            }
          }}
          externalEditData={externalEditData}
          onExternalEditComplete={handleExternalEditComplete}
        />
      )}

      {/* 表格模板选择器 */}
      {showTemplateSelector && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl max-h-[80vh] overflow-hidden">
            <TableTemplateSelector
              onTemplateSelect={handleTemplateSelect}
              onClose={handleTableCancel}
            />
          </div>
        </div>
      )}

      {/* 表格转换提示 */}
      <TableConversionPrompt
        isVisible={showTablePrompt}
        onConfirm={handleTableConversionConfirm}
        onCancel={handleTableConversionCancel}
        tablePreview={pendingTableData?.preview}
      />

    </div>
  );
};

export default EnhancedMilkdownEditor;
