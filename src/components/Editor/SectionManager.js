import React, { useState } from 'react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

const SectionManager = ({
  sections = [],
  onAddSection,
  onDeleteSection,
  onMoveSection,
  onUpdateSection,
  onReorderSections
}) => {
  const [selectedSection, setSelectedSection] = useState(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingSection, setEditingSection] = useState(null);
  const [editingTitle, setEditingTitle] = useState('');

  // 获取选中章节的信息
  const getSelectedSectionInfo = () => {
    if (!selectedSection) return null;
    const index = sections.findIndex(s => s.id === selectedSection);
    return index !== -1 ? { section: sections[index], index } : null;
  };

  // 处理新增大章节
  const handleAddMainSection = () => {
    const selectedInfo = getSelectedSectionInfo();
    // 生成唯一ID，确保与现有章节不冲突
    const existingIds = sections.map(s => s.id);
    let newId = 1;
    while (existingIds.includes(`section-${newId}`)) {
      newId++;
    }

    const newSection = {
      id: `section-${newId}`,
      title: '新大章节',
      level: 0,
      content: ''
    };

    if (selectedInfo) {
      // 在选中章节后面新增
      const position = selectedInfo.index + 1;
      onAddSection && onAddSection(newSection, position);
    } else {
      // 在最末位新增
      onAddSection && onAddSection(newSection, 'end');
    }

    // 选中新创建的章节并进入编辑模式
    setTimeout(() => {
      setSelectedSection(newSection.id);
      startEditingTitle(newSection);
      scrollToSection(newSection.id);
    }, 100);
  };

  // 处理新增子章节
  const handleAddSubSection = () => {
    const selectedInfo = getSelectedSectionInfo();
    if (!selectedInfo) return; // 没有选中章节时不可用

    // 生成唯一ID，确保与现有章节不冲突
    const existingIds = sections.map(s => s.id);
    let newId = 1;
    while (existingIds.includes(`section-${newId}`)) {
      newId++;
    }

    const parentLevel = selectedInfo.section.level;
    const newSection = {
      id: `section-${newId}`,
      title: '新子章节',
      level: Math.min(parentLevel + 1, 3), // 最大层级为3
      content: ''
    };

    // 在选中章节下面新增子章节
    const position = selectedInfo.index + 1;
    onAddSection && onAddSection(newSection, position);

    // 选中新创建的章节并进入编辑模式
    setTimeout(() => {
      setSelectedSection(newSection.id);
      startEditingTitle(newSection);
      scrollToSection(newSection.id);
    }, 100);
  };

  // 滚动到指定章节
  const scrollToSection = (sectionId) => {
    const element = document.querySelector(`[data-section-id="${sectionId}"]`);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  };

  // 处理删除章节
  const handleDeleteSection = (sectionId, sectionTitle) => {
    if (window.confirm(`确定要删除章节"${sectionTitle}"吗？此操作不可撤销。`)) {
      if (onDeleteSection) {
        onDeleteSection(sectionId);
      }
    }
  };

  // 开始编辑章节标题
  const startEditingTitle = (section) => {
    setEditingSection(section.id);
    setEditingTitle(section.title);
  };

  // 保存编辑的标题
  const saveEditingTitle = () => {
    if (editingSection && editingTitle.trim() && onUpdateSection) {
      onUpdateSection(editingSection, 'title', editingTitle.trim());
    }
    setEditingSection(null);
    setEditingTitle('');
  };

  // 取消编辑
  const cancelEditingTitle = () => {
    setEditingSection(null);
    setEditingTitle('');
  };

  // 处理键盘事件
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      saveEditingTitle();
    } else if (e.key === 'Escape') {
      cancelEditingTitle();
    }
  };

  // 处理拖拽结束
  const handleDragEnd = (result) => {
    if (!result.destination) return;
    
    const fromIndex = result.source.index;
    const toIndex = result.destination.index;
    
    if (fromIndex !== toIndex && onReorderSections) {
      onReorderSections(fromIndex, toIndex);
    }
  };

  // 获取层级样式
  const getLevelStyle = (level) => {
    const baseStyle = 'section-item flex items-center justify-between p-2 mb-1 border rounded transition-colors cursor-pointer';
    const levelStyles = {
      0: 'bg-blue-50 border-blue-200 hover:bg-blue-100',
      1: 'bg-green-50 border-green-200 hover:bg-green-100 ml-3',
      2: 'bg-yellow-50 border-yellow-200 hover:bg-yellow-100 ml-6',
      3: 'bg-purple-50 border-purple-200 hover:bg-purple-100 ml-9'
    };
    return `${baseStyle} ${levelStyles[level] || levelStyles[0]}`;
  };

  // 获取层级标识
  const getLevelIcon = (level) => {
    const icons = ['📘', '📗', '📙', '📕'];
    return icons[level] || '📄';
  };

  return (
    <div className="section-manager h-full flex flex-col bg-white">
      {/* 工具栏 - 固定在顶部 */}
      <div className="section-toolbar flex-shrink-0 flex flex-col gap-2 p-4 pb-3 border-b bg-white">
        {/* 按钮行 */}
        <div className="flex gap-2 items-center">
          <button
            onClick={handleAddMainSection}
            className="btn btn-primary flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-sm"
          >
            📘 新增大章节
          </button>
          <button
            onClick={handleAddSubSection}
            disabled={!selectedSection}
            className={`btn btn-secondary flex items-center gap-2 px-3 py-2 rounded transition-colors text-sm ${
              selectedSection
                ? 'bg-green-600 text-white hover:bg-green-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
            title={selectedSection ? '在选中章节下新增子章节' : '请先选择一个章节'}
          >
            📗 新增子章节
          </button>
          <div className="flex-1"></div>
          <span className="text-sm text-gray-500 flex items-center">
            共 {sections.length} 个章节
          </span>
        </div>

        {/* 选中状态显示 */}
        {selectedSection && (
          <div className="flex items-center gap-2 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
            <span>已选中:</span>
            <span className="font-medium">
              {sections.find(s => s.id === selectedSection)?.title || '未知章节'}
            </span>
            <button
              onClick={() => setSelectedSection(null)}
              className="ml-1 text-blue-400 hover:text-blue-600"
              title="取消选中"
            >
              ×
            </button>
          </div>
        )}
      </div>

      {/* 章节列表 - 可滚动区域 */}
      <div className="flex-1 overflow-y-auto px-4 pb-4">
        {sections.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <div className="text-4xl mb-2">📝</div>
            <p>暂无章节，点击上方按钮添加第一个章节</p>
          </div>
        ) : (
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="sections">
              {(provided, snapshot) => (
                <div
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  className={`sections-list py-2 ${snapshot.isDraggingOver ? 'bg-gray-50 rounded' : ''}`}
                >
                {sections.map((section, index) => (
                  <Draggable
                    key={section.id}
                    draggableId={section.id}
                    index={index}
                  >
                    {(provided, snapshot) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        data-section-id={section.id}
                        className={`${getLevelStyle(section.level)} ${
                          snapshot.isDragging ? 'shadow-lg rotate-1' : 'hover:shadow-md'
                        } ${selectedSection === section.id ? 'ring-2 ring-blue-500 bg-blue-100' : ''}`}
                        onClick={() => setSelectedSection(section.id)}
                      >
                        {/* 拖拽手柄 */}
                        <div
                          {...provided.dragHandleProps}
                          className="drag-handle flex items-center gap-2 flex-1 cursor-move min-w-0"
                        >
                          <span className="text-gray-400 text-xs">⋮⋮</span>
                          <span className="text-sm">{getLevelIcon(section.level)}</span>

                          {/* 标题编辑区域 */}
                          {editingSection === section.id ? (
                            <input
                              type="text"
                              value={editingTitle}
                              onChange={(e) => setEditingTitle(e.target.value)}
                              onKeyDown={handleKeyPress}
                              onBlur={saveEditingTitle}
                              className="flex-1 px-2 py-1 text-sm border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                              autoFocus
                              onClick={(e) => e.stopPropagation()}
                            />
                          ) : (
                            <span
                              className="font-medium text-gray-800 text-sm truncate cursor-pointer hover:text-blue-600"
                              onDoubleClick={() => startEditingTitle(section)}
                              title="双击编辑标题"
                            >
                              {section.title || '未命名章节'}
                            </span>
                          )}

                          <span className="text-xs text-gray-500 ml-auto flex-shrink-0">
                            L{section.level + 1}
                          </span>
                        </div>

                        {/* 操作按钮 */}
                        <div className="section-actions flex gap-0.5">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              startEditingTitle(section);
                            }}
                            className="action-btn p-1 text-xs text-gray-500 hover:text-green-600 hover:bg-green-100 rounded transition-colors"
                            title="编辑标题"
                          >
                            ✏️
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onMoveSection && onMoveSection(section.id, 'up');
                            }}
                            className={`action-btn p-1 text-xs rounded transition-colors ${
                              index === 0
                                ? 'text-gray-300 cursor-not-allowed'
                                : 'text-gray-500 hover:text-blue-600 hover:bg-blue-100'
                            }`}
                            title="上移"
                            disabled={index === 0}
                          >
                            ↑
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onMoveSection && onMoveSection(section.id, 'down');
                            }}
                            className={`action-btn p-1 text-xs rounded transition-colors ${
                              index === sections.length - 1
                                ? 'text-gray-300 cursor-not-allowed'
                                : 'text-gray-500 hover:text-blue-600 hover:bg-blue-100'
                            }`}
                            title="下移"
                            disabled={index === sections.length - 1}
                          >
                            ↓
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteSection(section.id, section.title);
                            }}
                            className="action-btn p-1 text-xs text-gray-500 hover:text-red-600 hover:bg-red-100 rounded transition-colors"
                            title="删除"
                          >
                            ×
                          </button>
                        </div>
                      </div>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
        )}

        {/* 操作提示 */}
        {sections.length > 0 && (
          <div className="mt-4 pt-3 border-t text-xs text-gray-500">
            <p>💡 提示：拖拽章节可以重新排序，点击上移/下移按钮可以调整顺序</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SectionManager;
