import React, { useState, useCallback } from 'react';

/**
 * 编辑器降级组件
 * 当主编辑器失败时提供基础的文本编辑功能
 */
const EditorFallback = ({ 
  content = '', 
  onChange, 
  onSave,
  error,
  className = '',
  placeholder = '请输入内容...'
}) => {
  const [localContent, setLocalContent] = useState(content);
  const [isModified, setIsModified] = useState(false);

  const handleContentChange = useCallback((e) => {
    const newContent = e.target.value;
    setLocalContent(newContent);
    setIsModified(newContent !== content);
    
    if (onChange) {
      onChange(newContent);
    }
  }, [content, onChange]);

  const handleSave = useCallback(() => {
    if (onSave) {
      onSave(localContent);
      setIsModified(false);
    }
  }, [localContent, onSave]);

  const handleKeyDown = useCallback((e) => {
    // Ctrl+S 保存
    if (e.ctrlKey && e.key === 's') {
      e.preventDefault();
      handleSave();
    }
  }, [handleSave]);

  return (
    <div className={`editor-fallback ${className}`}>
      {/* 错误提示 */}
      {error && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">编辑器降级模式</h3>
              <p className="mt-1 text-sm text-yellow-700">
                高级编辑器暂时不可用，已切换到基础编辑模式。功能可能受限，但您仍可以编辑内容。
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 工具栏 */}
      <div className="border border-gray-300 rounded-t-md bg-gray-50 px-3 py-2 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600">基础编辑模式</span>
          {isModified && (
            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">
              未保存
            </span>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={handleSave}
            disabled={!isModified}
            className={`px-3 py-1 text-sm rounded ${
              isModified
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            保存 (Ctrl+S)
          </button>
        </div>
      </div>

      {/* 编辑区域 */}
      <textarea
        value={localContent}
        onChange={handleContentChange}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className="w-full h-96 p-4 border-l border-r border-b border-gray-300 rounded-b-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
        style={{
          minHeight: '400px',
          lineHeight: '1.5'
        }}
      />

      {/* 状态栏 */}
      <div className="bg-gray-100 px-3 py-1 text-xs text-gray-600 border-t border-gray-200 flex justify-between">
        <span>
          字符数: {localContent.length} | 行数: {localContent.split('\n').length}
        </span>
        <span>
          基础编辑模式 - 支持Markdown语法
        </span>
      </div>
    </div>
  );
};

/**
 * 编辑器错误边界包装器
 * 专门用于编辑器组件的错误处理
 */
export class EditorErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({ error });
    
    // 记录编辑器错误
    console.error('Editor Error:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      retryCount: this.state.retryCount
    });
  }

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      retryCount: prevState.retryCount + 1
    }));
  };

  render() {
    if (this.state.hasError) {
      const { content, onChange, onSave, fallbackProps = {} } = this.props;
      
      return (
        <div className="editor-error-boundary">
          <EditorFallback
            content={content}
            onChange={onChange}
            onSave={onSave}
            error={this.state.error}
            {...fallbackProps}
          />
          
          {/* 重试按钮 */}
          <div className="mt-4 text-center">
            <button
              onClick={this.handleRetry}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              重新加载编辑器
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * 安全的编辑器包装器
 * 自动处理编辑器加载失败的情况
 */
export const SafeEditor = ({ 
  EditorComponent, 
  fallbackProps = {},
  ...props 
}) => {
  return (
    <EditorErrorBoundary 
      content={props.content}
      onChange={props.onChange}
      onSave={props.onSave}
      fallbackProps={fallbackProps}
    >
      <EditorComponent {...props} />
    </EditorErrorBoundary>
  );
};

export default EditorFallback;
