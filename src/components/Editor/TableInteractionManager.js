import React, { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import TableHoverButton from './TableHoverButton';
import TableEditor from './TableEditor';
import { TableConverter } from '../../utils/tableUtils';

/**
 * 表格交互管理器
 * 管理表格的双击编辑和悬浮按钮功能
 */
const TableInteractionManager = ({
  editorContainer,
  onTableUpdate,
  className = '',
  // 新增：外部控制的表格编辑
  externalEditData = null,
  onExternalEditComplete = null
}) => {
  const [tables, setTables] = useState([]);
  const [editingTable, setEditingTable] = useState(null);
  const [tableData, setTableData] = useState([]);
  const [tableHeaders, setTableHeaders] = useState([]);
  const [showTableEditor, setShowTableEditor] = useState(false);
  const [isExternalEdit, setIsExternalEdit] = useState(false);
  const observerRef = useRef(null);

  // 处理外部编辑数据
  useEffect(() => {
    if (externalEditData) {
      setTableHeaders(externalEditData.headers || ['列1', '列2']);
      setTableData(externalEditData.data || [['', ''], ['', '']]);
      setIsExternalEdit(true);
      setEditingTable(null); // 外部编辑时不关联具体表格元素
      setShowTableEditor(true);
    }
  }, [externalEditData]);

  // 扫描编辑器中的表格
  const scanTables = () => {
    if (!editorContainer) return;

    const tableElements = editorContainer.querySelectorAll('table');
    const newTables = Array.from(tableElements).map((table, index) => ({
      id: `table-${index}`,
      element: table,
      index
    }));

    setTables(newTables);
  };

  // 从表格元素解析数据
  const parseTableData = (tableElement) => {
    const headers = [];
    const data = [];

    // 解析表头
    const headerRow = tableElement.querySelector('thead tr');
    if (headerRow) {
      const headerCells = headerRow.querySelectorAll('th');
      headerCells.forEach(cell => {
        headers.push(cell.textContent.trim());
      });
    } else {
      // 如果没有thead，尝试从第一行获取表头
      const firstRow = tableElement.querySelector('tr');
      if (firstRow) {
        const cells = firstRow.querySelectorAll('th, td');
        cells.forEach(cell => {
          headers.push(cell.textContent.trim());
        });
      }
    }

    // 解析数据行
    const bodyRows = tableElement.querySelectorAll('tbody tr');
    if (bodyRows.length > 0) {
      // 有tbody的情况
      bodyRows.forEach(row => {
        const rowData = [];
        const cells = row.querySelectorAll('td');
        cells.forEach(cell => {
          rowData.push(cell.textContent.trim());
        });
        if (rowData.length > 0) {
          data.push(rowData);
        }
      });
    } else {
      // 没有tbody的情况，从所有tr中解析（跳过表头行）
      const allRows = tableElement.querySelectorAll('tr');
      const startIndex = headerRow ? 1 : 0; // 如果有表头行，从第二行开始

      for (let i = startIndex; i < allRows.length; i++) {
        const row = allRows[i];
        const rowData = [];
        const cells = row.querySelectorAll('td, th');
        cells.forEach(cell => {
          rowData.push(cell.textContent.trim());
        });
        if (rowData.length > 0) {
          data.push(rowData);
        }
      }
    }

    // 确保数据完整性 - 如果没有数据行，创建一个空行
    if (data.length === 0 && headers.length > 0) {
      data.push(new Array(headers.length).fill(''));
    }

    // 确保所有行的列数一致
    const maxCols = Math.max(headers.length, ...data.map(row => row.length));

    // 补齐表头
    while (headers.length < maxCols) {
      headers.push(`列${headers.length + 1}`);
    }

    // 补齐数据行
    data.forEach(row => {
      while (row.length < maxCols) {
        row.push('');
      }
    });

    return { headers, data };
  };

  // 更新表格内容
  const updateTableContent = (tableElement, newHeaders, newData) => {
    // 生成新的表格HTML
    const markdownTable = TableConverter.toMarkdown(newData, newHeaders);
    const htmlTable = TableConverter.toHTML(newData, newHeaders, {
      className: 'table-auto border-collapse border border-gray-300',
      cellPadding: '8px'
    });

    // 创建临时容器来解析HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlTable;
    const newTableElement = tempDiv.querySelector('table');

    if (newTableElement && tableElement.parentNode) {
      // 复制原表格的属性
      newTableElement.className = tableElement.className;
      
      // 替换表格
      tableElement.parentNode.replaceChild(newTableElement, tableElement);
      
      // 通知父组件表格已更新
      if (onTableUpdate) {
        onTableUpdate(markdownTable);
      }
      
      // 重新扫描表格
      setTimeout(scanTables, 100);
    }
  };

  // 处理表格编辑
  const handleTableEdit = (table) => {
    // 如果正在进行外部编辑，忽略内部表格编辑请求
    if (isExternalEdit) {
      return;
    }

    const { headers, data } = parseTableData(table.element);

    console.log('Editing table:', {
      headers,
      data,
      headersLength: headers.length,
      dataLength: data.length,
      tableElement: table.element
    });

    setEditingTable(table);
    setTableHeaders([...headers]); // 创建副本避免引用问题
    setTableData(data.map(row => [...row])); // 创建深拷贝
    setIsExternalEdit(false);
    setShowTableEditor(true);
  };

  // 处理表格双击
  const handleTableDoubleClick = (e) => {
    const tableElement = e.target.closest('table');
    if (tableElement) {
      e.preventDefault();
      e.stopPropagation();
      
      const table = tables.find(t => t.element === tableElement);
      if (table) {
        handleTableEdit(table);
      }
    }
  };

  // 保存表格编辑
  const handleTableSave = () => {
    if (isExternalEdit) {
      // 外部编辑：通过回调返回编辑结果
      if (onExternalEditComplete) {
        onExternalEditComplete({
          headers: tableHeaders,
          data: tableData,
          action: 'save'
        });
      }
    } else if (editingTable) {
      // 内部编辑：直接更新表格元素
      updateTableContent(editingTable.element, tableHeaders, tableData);
    }

    setShowTableEditor(false);
    setEditingTable(null);
    setIsExternalEdit(false);
  };

  // 保存为新表格（添加到现有表格下方）
  const handleSaveAsNewTable = () => {
    if (onExternalEditComplete) {
      onExternalEditComplete({
        headers: tableHeaders,
        data: tableData,
        action: 'save_as_new'
      });
    }

    setShowTableEditor(false);
    setEditingTable(null);
    setIsExternalEdit(false);
  };

  // 取消表格编辑
  const handleTableCancel = () => {
    if (isExternalEdit && onExternalEditComplete) {
      onExternalEditComplete({
        headers: tableHeaders,
        data: tableData,
        action: 'cancel'
      });
    }

    setShowTableEditor(false);
    setEditingTable(null);
    setIsExternalEdit(false);
  };

  // 处理表格数据变化
  const handleTableDataChange = (newData, newHeaders) => {
    setTableData(newData);
    if (newHeaders) {
      setTableHeaders(newHeaders);
    }
  };

  // 监听DOM变化
  useEffect(() => {
    if (!editorContainer) return;

    // 初始扫描
    scanTables();

    // 创建MutationObserver监听DOM变化
    observerRef.current = new MutationObserver((mutations) => {
      let shouldRescan = false;
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // 检查是否有表格相关的变化
          const addedNodes = Array.from(mutation.addedNodes);
          const removedNodes = Array.from(mutation.removedNodes);
          
          const hasTableChanges = [...addedNodes, ...removedNodes].some(node => {
            return node.nodeType === Node.ELEMENT_NODE && 
                   (node.tagName === 'TABLE' || node.querySelector && node.querySelector('table'));
          });
          
          if (hasTableChanges) {
            shouldRescan = true;
          }
        }
      });
      
      if (shouldRescan) {
        setTimeout(scanTables, 100);
      }
    });

    observerRef.current.observe(editorContainer, {
      childList: true,
      subtree: true
    });

    // 添加双击事件监听器
    editorContainer.addEventListener('dblclick', handleTableDoubleClick);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
      editorContainer.removeEventListener('dblclick', handleTableDoubleClick);
    };
  }, [editorContainer]);

  return (
    <>
      {/* 为每个表格渲染悬浮按钮 */}
      {tables.map((table) => 
        createPortal(
          <TableHoverButton
            key={table.id}
            tableElement={table.element}
            onEdit={() => handleTableEdit(table)}
          />,
          document.body
        )
      )}

      {/* 表格编辑器弹窗 */}
      {showTableEditor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-6xl max-h-[80vh] overflow-hidden">
            <div className="p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold">
                {isExternalEdit ? '编辑新表格' : '编辑表格'}
              </h3>
            </div>
            <div className="p-4 overflow-auto" style={{ maxHeight: 'calc(80vh - 120px)' }}>
              <TableEditor
                initialData={tableData}
                headers={tableHeaders}
                onDataChange={handleTableDataChange}
                onHeaderChange={setTableHeaders}
                maxRows={50}
                maxCols={10}
              />
            </div>
            <div className="p-4 border-t border-gray-200 flex justify-between">
              <div className="flex space-x-2">
                {/* 保存为新表格按钮 - 只在编辑现有表格时显示 */}
                {!isExternalEdit && (
                  <button
                    onClick={handleSaveAsNewTable}
                    className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 flex items-center space-x-1"
                    title="将编辑的表格复制并添加到现有表格下方"
                  >
                    <span>📋</span>
                    <span>保存为新表格</span>
                  </button>
                )}
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={handleTableCancel}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  onClick={handleTableSave}
                  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  {isExternalEdit ? '插入表格' : '保存更改'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default TableInteractionManager;
