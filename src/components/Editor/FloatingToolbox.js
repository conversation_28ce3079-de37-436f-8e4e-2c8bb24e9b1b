import React, { useState, useEffect } from 'react';
import ReactDOM from 'react-dom';
import FloatingButton from './FloatingButton';
import FloatingToolbar from './FloatingToolbar';

/**
 * 悬浮工具箱主组件
 * 在编辑器容器内渲染，保持与编辑器的上下文关联
 */
const FloatingToolbox = ({
  editorRef,
  editorInstance,
  onInsertTable,
  onInsertQuickTable,
  onInsertList,
  onInsertFormat,
  onToolboxOpen,
  onToolboxClose,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // 点击外部关闭工具栏
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isOpen && !event.target.closest('.floating-toolbox-container')) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  // ESC键关闭工具栏
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
      return () => document.removeEventListener('keydown', handleEscKey);
    }
  }, [isOpen]);

  // 处理表格插入 - 保持编辑器焦点
  const handleInsertTable = () => {
    console.log('🔄 FloatingToolbox: 处理表格模板选择');

    // 保持编辑器焦点
    if (editorRef && editorRef.current) {
      editorRef.current.focus();
    }

    if (onInsertTable) {
      onInsertTable();
    }
  };

  // 处理快速表格插入 - 保持编辑器焦点
  const handleInsertQuickTable = () => {
    console.log('🔄 FloatingToolbox: 处理快速表格插入');

    // 保持编辑器焦点
    if (editorRef && editorRef.current) {
      editorRef.current.focus();
    }

    if (onInsertQuickTable) {
      onInsertQuickTable();
    }
  };

  // 处理列表插入 - 保持编辑器焦点
  const handleInsertList = (ordered = false) => {
    console.log('🔄 FloatingToolbox: 处理列表插入', { ordered });

    // 保持编辑器焦点
    if (editorRef && editorRef.current) {
      editorRef.current.focus();
    }

    if (onInsertList) {
      onInsertList(ordered);
    }
  };

  // 处理格式化 - 保持编辑器焦点
  const handleInsertFormat = (type) => {
    console.log('🔄 FloatingToolbox: 处理格式化', { type });

    // 保持编辑器焦点
    if (editorRef && editorRef.current) {
      editorRef.current.focus();
    }

    if (onInsertFormat) {
      onInsertFormat(type);
    }
  };

  // 切换工具栏显示状态
  const toggleToolbar = () => {
    const newIsOpen = !isOpen;
    setIsOpen(newIsOpen);

    // 通知父组件工具箱状态变化
    if (newIsOpen && onToolboxOpen) {
      // 工具箱打开时立即保存光标位置
      onToolboxOpen();
    } else if (!newIsOpen && onToolboxClose) {
      onToolboxClose();
    }
  };

  // 关闭工具栏
  const closeToolbar = () => {
    setIsOpen(false);
    if (onToolboxClose) {
      onToolboxClose();
    }
  };

  // 使用Portal渲染到document.body，确保全局可见
  return ReactDOM.createPortal(
    <div className={`floating-toolbox-container ${className}`}>
      <FloatingButton
        onClick={toggleToolbar}
        isOpen={isOpen}
      />
      {isOpen && (
        <FloatingToolbar
          onInsertTable={handleInsertTable}
          onInsertQuickTable={handleInsertQuickTable}
          onInsertList={handleInsertList}
          onInsertFormat={handleInsertFormat}
          onClose={closeToolbar}
        />
      )}
    </div>,
    document.body
  );
};

export default FloatingToolbox;
