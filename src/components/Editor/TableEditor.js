import React, { useState, useCallback, useRef, useEffect } from 'react';
import { debounce } from '../../utils/performanceUtils';

/**
 * 可视化表格编辑器组件
 * 提供类似Excel的表格编辑体验
 */
const TableEditor = ({
  initialData = [['', '']],
  headers = ['列1', '列2'],
  onDataChange,
  onHeaderChange,
  className = '',
  maxRows = 100,
  maxCols = 20,
  enableResize = true,
  enableSort = true,
  enableFilter = false,
  readOnly = false
}) => {
  const [data, setData] = useState(initialData);
  const [tableHeaders, setTableHeaders] = useState(headers);
  const [selectedCell, setSelectedCell] = useState(null);
  const [editingCell, setEditingCell] = useState(null);
  const [editValue, setEditValue] = useState('');
  const [dragStart, setDragStart] = useState(null);
  const [dragEnd, setDragEnd] = useState(null);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  
  const tableRef = useRef(null);
  const inputRef = useRef(null);

  // 当props变化时更新内部状态
  useEffect(() => {
    console.log('TableEditor props changed:', {
      initialData,
      headers,
      initialDataLength: initialData.length,
      headersLength: headers.length
    });

    setData(initialData);
    setTableHeaders(headers);
  }, [initialData, headers]);

  // 防抖的数据变化通知
  const debouncedOnDataChange = useCallback(
    debounce((newData, newHeaders) => {
      if (onDataChange) {
        onDataChange(newData, newHeaders);
      }
    }, 300),
    [onDataChange]
  );

  // 更新数据
  const updateData = useCallback((newData, newHeaders = tableHeaders) => {
    setData(newData);
    if (newHeaders !== tableHeaders) {
      setTableHeaders(newHeaders);
      if (onHeaderChange) {
        onHeaderChange(newHeaders);
      }
    }
    debouncedOnDataChange(newData, newHeaders);
  }, [tableHeaders, debouncedOnDataChange, onHeaderChange]);

  // 处理单元格点击
  const handleCellClick = useCallback((rowIndex, colIndex) => {
    if (readOnly) return;
    
    setSelectedCell({ row: rowIndex, col: colIndex });
    setEditingCell(null);
  }, [readOnly]);

  // 处理单元格双击
  const handleCellDoubleClick = useCallback((rowIndex, colIndex) => {
    if (readOnly) return;
    
    setEditingCell({ row: rowIndex, col: colIndex });
    setEditValue(data[rowIndex]?.[colIndex] || '');
    setSelectedCell({ row: rowIndex, col: colIndex });
  }, [data, readOnly]);

  // 处理编辑值变化
  const handleEditValueChange = useCallback((e) => {
    setEditValue(e.target.value);
  }, []);

  // 保存编辑
  const saveEdit = useCallback(() => {
    if (editingCell) {
      const newData = [...data];
      if (!newData[editingCell.row]) {
        newData[editingCell.row] = [];
      }
      newData[editingCell.row][editingCell.col] = editValue;
      updateData(newData);
      setEditingCell(null);
    }
  }, [editingCell, editValue, data, updateData]);

  // 取消编辑
  const cancelEdit = useCallback(() => {
    setEditingCell(null);
    setEditValue('');
  }, []);

  // 处理键盘事件
  const handleKeyDown = useCallback((e) => {
    if (editingCell) {
      if (e.key === 'Enter') {
        e.preventDefault();
        saveEdit();
      } else if (e.key === 'Escape') {
        e.preventDefault();
        cancelEdit();
      }
      return;
    }

    if (selectedCell) {
      const { row, col } = selectedCell;
      let newRow = row;
      let newCol = col;

      switch (e.key) {
        case 'ArrowUp':
          newRow = Math.max(0, row - 1);
          break;
        case 'ArrowDown':
          newRow = Math.min(data.length - 1, row + 1);
          break;
        case 'ArrowLeft':
          newCol = Math.max(0, col - 1);
          break;
        case 'ArrowRight':
          newCol = Math.min(tableHeaders.length - 1, col + 1);
          break;
        case 'Enter':
          if (!readOnly) {
            handleCellDoubleClick(row, col);
          }
          break;
        case 'Delete':
          if (!readOnly) {
            const newData = [...data];
            if (newData[row]) {
              newData[row][col] = '';
              updateData(newData);
            }
          }
          break;
        default:
          return;
      }

      if (newRow !== row || newCol !== col) {
        setSelectedCell({ row: newRow, col: newCol });
      }
      e.preventDefault();
    }
  }, [editingCell, selectedCell, data, tableHeaders.length, saveEdit, cancelEdit, handleCellDoubleClick, readOnly, updateData]);

  // 添加行
  const addRow = useCallback(() => {
    if (data.length >= maxRows) return;
    
    const newRow = new Array(tableHeaders.length).fill('');
    const newData = [...data, newRow];
    updateData(newData);
  }, [data, tableHeaders.length, maxRows, updateData]);

  // 删除行
  const deleteRow = useCallback((rowIndex) => {
    if (data.length <= 1) return;
    
    const newData = data.filter((_, index) => index !== rowIndex);
    updateData(newData);
    
    // 调整选中的单元格
    if (selectedCell && selectedCell.row === rowIndex) {
      setSelectedCell(null);
    } else if (selectedCell && selectedCell.row > rowIndex) {
      setSelectedCell({ ...selectedCell, row: selectedCell.row - 1 });
    }
  }, [data, updateData, selectedCell]);

  // 添加列
  const addColumn = useCallback(() => {
    if (tableHeaders.length >= maxCols) return;
    
    const newHeaders = [...tableHeaders, `列${tableHeaders.length + 1}`];
    const newData = data.map(row => [...row, '']);
    updateData(newData, newHeaders);
  }, [tableHeaders, data, maxCols, updateData]);

  // 删除列
  const deleteColumn = useCallback((colIndex) => {
    if (tableHeaders.length <= 1) return;
    
    const newHeaders = tableHeaders.filter((_, index) => index !== colIndex);
    const newData = data.map(row => row.filter((_, index) => index !== colIndex));
    updateData(newData, newHeaders);
    
    // 调整选中的单元格
    if (selectedCell && selectedCell.col === colIndex) {
      setSelectedCell(null);
    } else if (selectedCell && selectedCell.col > colIndex) {
      setSelectedCell({ ...selectedCell, col: selectedCell.col - 1 });
    }
  }, [tableHeaders, data, updateData, selectedCell]);

  // 排序
  const handleSort = useCallback((colIndex) => {
    if (!enableSort) return;
    
    let direction = 'asc';
    if (sortConfig.key === colIndex && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    
    const sortedData = [...data].sort((a, b) => {
      const aVal = a[colIndex] || '';
      const bVal = b[colIndex] || '';
      
      if (direction === 'asc') {
        return aVal.localeCompare(bVal);
      } else {
        return bVal.localeCompare(aVal);
      }
    });
    
    setSortConfig({ key: colIndex, direction });
    updateData(sortedData);
  }, [enableSort, sortConfig, data, updateData]);

  // 编辑表头
  const editHeader = useCallback((colIndex, newHeader) => {
    const newHeaders = [...tableHeaders];
    newHeaders[colIndex] = newHeader;
    setTableHeaders(newHeaders);
    if (onHeaderChange) {
      onHeaderChange(newHeaders);
    }
  }, [tableHeaders, onHeaderChange]);

  // 自动聚焦编辑输入框
  useEffect(() => {
    if (editingCell && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [editingCell]);

  // 键盘事件监听
  useEffect(() => {
    const handleGlobalKeyDown = (e) => {
      if (tableRef.current && tableRef.current.contains(document.activeElement)) {
        handleKeyDown(e);
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, [handleKeyDown]);

  return (
    <div className={`table-editor ${className}`} ref={tableRef} tabIndex={0}>
      {/* 工具栏 */}
      <div className="table-toolbar mb-4 flex items-center space-x-2">
        <button
          onClick={addRow}
          disabled={data.length >= maxRows || readOnly}
          className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          添加行
        </button>
        <button
          onClick={addColumn}
          disabled={tableHeaders.length >= maxCols || readOnly}
          className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          添加列
        </button>
        <span className="text-sm text-gray-500">
          {data.length} 行 × {tableHeaders.length} 列
        </span>
      </div>

      {/* 表格 */}
      <div className="table-container overflow-auto border border-gray-300 rounded">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50">
              <th className="w-8 p-2 border border-gray-300 text-center">#</th>
              {tableHeaders.map((header, colIndex) => (
                <th
                  key={colIndex}
                  className="p-2 border border-gray-300 min-w-24 relative group"
                >
                  <div className="flex items-center justify-between">
                    <input
                      type="text"
                      value={header}
                      onChange={(e) => editHeader(colIndex, e.target.value)}
                      disabled={readOnly}
                      className="bg-transparent border-none outline-none font-medium w-full"
                    />
                    <div className="flex items-center space-x-1">
                      {enableSort && (
                        <button
                          onClick={() => handleSort(colIndex)}
                          className="text-gray-400 hover:text-gray-600"
                          title="排序"
                        >
                          {sortConfig.key === colIndex ? (
                            sortConfig.direction === 'asc' ? '↑' : '↓'
                          ) : '↕'}
                        </button>
                      )}
                      {!readOnly && (
                        <button
                          onClick={() => deleteColumn(colIndex)}
                          disabled={tableHeaders.length <= 1}
                          className="text-red-400 hover:text-red-600 opacity-0 group-hover:opacity-100 disabled:opacity-0"
                          title="删除列"
                        >
                          ×
                        </button>
                      )}
                    </div>
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.map((row, rowIndex) => (
              <tr key={rowIndex} className="hover:bg-gray-50">
                <td className="p-2 border border-gray-300 text-center bg-gray-50 relative group">
                  <span>{rowIndex + 1}</span>
                  {!readOnly && (
                    <button
                      onClick={() => deleteRow(rowIndex)}
                      disabled={data.length <= 1}
                      className="absolute right-1 top-1/2 transform -translate-y-1/2 text-red-400 hover:text-red-600 opacity-0 group-hover:opacity-100 disabled:opacity-0"
                      title="删除行"
                    >
                      ×
                    </button>
                  )}
                </td>
                {tableHeaders.map((_, colIndex) => (
                  <td
                    key={colIndex}
                    className={`p-0 border border-gray-300 relative ${
                      selectedCell?.row === rowIndex && selectedCell?.col === colIndex
                        ? 'bg-blue-100 ring-2 ring-blue-500'
                        : ''
                    }`}
                    onClick={() => handleCellClick(rowIndex, colIndex)}
                    onDoubleClick={() => handleCellDoubleClick(rowIndex, colIndex)}
                  >
                    {editingCell?.row === rowIndex && editingCell?.col === colIndex ? (
                      <input
                        ref={inputRef}
                        type="text"
                        value={editValue}
                        onChange={handleEditValueChange}
                        onBlur={saveEdit}
                        className="w-full h-full p-2 border-none outline-none"
                      />
                    ) : (
                      <div className="p-2 min-h-8 cursor-cell">
                        {row[colIndex] || ''}
                      </div>
                    )}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 状态栏 */}
      <div className="table-status mt-2 text-sm text-gray-500 flex justify-between">
        <span>
          {selectedCell ? `选中: 行${selectedCell.row + 1}, 列${selectedCell.col + 1}` : ''}
        </span>
        <span>
          {editingCell ? '编辑模式 - Enter保存, Escape取消' : '双击编辑单元格'}
        </span>
      </div>
    </div>
  );
};

export default TableEditor;
