/* Enhanced Milkdown Editor Styles */
.enhanced-milkdown-editor {
  position: relative;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  overflow: hidden;
}

.enhanced-milkdown-editor.error {
  border-color: #ef4444;
}

/* 工具栏样式 */
.enhanced-editor-toolbar {
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.toolbar-btn {
  padding: 4px 8px;
  font-size: 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.toolbar-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.toolbar-btn:active {
  background: #e5e7eb;
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 编辑器容器样式 */
.editor-container {
  position: relative;
  min-height: 300px;
  padding: 16px;
  background: white;
}

.editor-container.readonly {
  background: #f9fafb;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* 错误状态 */
.error-message {
  padding: 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  margin: 16px;
}

.error-message h3 {
  color: #991b1b;
  font-weight: 500;
  margin: 0 0 4px 0;
}

.error-message p {
  color: #dc2626;
  font-size: 14px;
  margin: 4px 0 8px 0;
}

/* Milkdown 编辑器内容样式覆盖 */
.enhanced-milkdown-editor .milkdown {
  outline: none;
  border: none;
  background: transparent;
}

.enhanced-milkdown-editor .milkdown .editor {
  min-height: 250px;
  padding: 0;
  line-height: 1.6;
  font-size: 14px;
  color: #374151;
}

/* 标题样式 */
.enhanced-milkdown-editor .milkdown h1,
.enhanced-milkdown-editor .milkdown h2,
.enhanced-milkdown-editor .milkdown h3,
.enhanced-milkdown-editor .milkdown h4,
.enhanced-milkdown-editor .milkdown h5,
.enhanced-milkdown-editor .milkdown h6 {
  margin: 16px 0 8px 0;
  font-weight: 600;
  line-height: 1.4;
}

.enhanced-milkdown-editor .milkdown h1 { font-size: 24px; color: #111827; }
.enhanced-milkdown-editor .milkdown h2 { font-size: 20px; color: #1f2937; }
.enhanced-milkdown-editor .milkdown h3 { font-size: 18px; color: #374151; }
.enhanced-milkdown-editor .milkdown h4 { font-size: 16px; color: #4b5563; }
.enhanced-milkdown-editor .milkdown h5 { font-size: 14px; color: #6b7280; }
.enhanced-milkdown-editor .milkdown h6 { font-size: 12px; color: #9ca3af; }

/* 段落样式 */
.enhanced-milkdown-editor .milkdown p {
  margin: 8px 0;
  line-height: 1.6;
}

/* 列表样式 */
.enhanced-milkdown-editor .milkdown ul,
.enhanced-milkdown-editor .milkdown ol {
  margin: 8px 0;
  padding-left: 24px;
  list-style: none;
}

.enhanced-milkdown-editor .milkdown ul {
  list-style-type: disc;
  list-style-position: outside;
}

.enhanced-milkdown-editor .milkdown ol {
  list-style-type: decimal;
  list-style-position: outside;
}

.enhanced-milkdown-editor .milkdown li {
  margin: 4px 0;
  line-height: 1.5;
  display: list-item;
}

/* 确保嵌套列表正确显示 */
.enhanced-milkdown-editor .milkdown ul ul {
  list-style-type: circle;
  margin-left: 16px;
}

.enhanced-milkdown-editor .milkdown ul ul ul {
  list-style-type: square;
}

.enhanced-milkdown-editor .milkdown ol ol {
  list-style-type: lower-alpha;
  margin-left: 16px;
}

.enhanced-milkdown-editor .milkdown ol ol ol {
  list-style-type: lower-roman;
}

/* 表格样式 */
.enhanced-milkdown-editor .milkdown table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
  table-layout: auto; /* 允许自动调整列宽 */
}

.enhanced-milkdown-editor .milkdown th,
.enhanced-milkdown-editor .milkdown td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
  border-right: 1px solid #e5e7eb;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: anywhere;
  white-space: normal;
  vertical-align: top;
  min-width: 80px; /* 最小宽度确保可读性 */
  max-width: 250px; /* 最大宽度防止单元格过宽 */
}

.enhanced-milkdown-editor .milkdown th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.enhanced-milkdown-editor .milkdown td:last-child,
.enhanced-milkdown-editor .milkdown th:last-child {
  border-right: none;
}

.enhanced-milkdown-editor .milkdown tr:last-child td {
  border-bottom: none;
}

/* 代码样式 */
.enhanced-milkdown-editor .milkdown code {
  background: #f3f4f6;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #e11d48;
}

.enhanced-milkdown-editor .milkdown pre {
  background: #1f2937;
  color: #f9fafb;
  padding: 16px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 16px 0;
}

.enhanced-milkdown-editor .milkdown pre code {
  background: transparent;
  color: inherit;
  padding: 0;
}

/* 引用样式 */
.enhanced-milkdown-editor .milkdown blockquote {
  border-left: 4px solid #3b82f6;
  padding-left: 16px;
  margin: 16px 0;
  color: #6b7280;
  font-style: italic;
}

/* 链接样式 */
.enhanced-milkdown-editor .milkdown a {
  color: #3b82f6;
  text-decoration: underline;
}

.enhanced-milkdown-editor .milkdown a:hover {
  color: #1d4ed8;
}

/* 图片样式 */
.enhanced-milkdown-editor .milkdown img {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 8px 0;
}

/* 分割线样式 */
.enhanced-milkdown-editor .milkdown hr {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 24px 0;
}

/* 占位符样式 */
.enhanced-milkdown-editor .milkdown .placeholder {
  color: #9ca3af;
  font-style: italic;
}

/* 选中状态 */
.enhanced-milkdown-editor .milkdown ::selection {
  background: #dbeafe;
}

/* 焦点状态 - 移除双重边框效果 */
.enhanced-milkdown-editor:focus-within {
  border-color: #3b82f6;
  /* 移除box-shadow避免双重边框 */
}

/* 确保编辑器内部元素不会产生额外的focus样式 */
.enhanced-milkdown-editor .milkdown *:focus {
  outline: none;
  box-shadow: none;
}

.enhanced-milkdown-editor .editor-container:focus {
  outline: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enhanced-editor-toolbar {
    padding: 6px 8px;
    gap: 4px;
  }
  
  .toolbar-btn {
    padding: 3px 6px;
    font-size: 11px;
  }
  
  .editor-container {
    padding: 12px;
    min-height: 200px;
  }
  
  .enhanced-milkdown-editor .milkdown .editor {
    font-size: 13px;
  }
}
