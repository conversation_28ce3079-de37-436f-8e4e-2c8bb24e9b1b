import React, { useState, useCallback } from 'react';
import { templateCategories, getAllTemplates, getTemplatesByCategory } from '../../data/tableTemplates';

/**
 * 表格模板选择器组件
 * 提供表格模板的浏览和选择功能
 */
const TableTemplateSelector = ({
  onTemplateSelect,
  onClose,
  className = ''
}) => {
  const [selectedCategory, setSelectedCategory] = useState('requirements');
  const [searchTerm, setSearchTerm] = useState('');

  // 获取过滤后的模板
  const getFilteredTemplates = useCallback(() => {
    let templates = selectedCategory === 'all' 
      ? getAllTemplates() 
      : Object.entries(getTemplatesByCategory(selectedCategory)).map(([key, template]) => ({
          id: key,
          ...template
        }));

    if (searchTerm) {
      templates = templates.filter(template => 
        template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        template.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return templates;
  }, [selectedCategory, searchTerm]);

  const filteredTemplates = getFilteredTemplates();

  // 处理模板选择
  const handleTemplateSelect = useCallback((template) => {
    if (onTemplateSelect) {
      onTemplateSelect(template);
    }
  }, [onTemplateSelect]);

  // 处理分类切换
  const handleCategoryChange = useCallback((category) => {
    setSelectedCategory(category);
  }, []);

  // 处理搜索
  const handleSearchChange = useCallback((e) => {
    setSearchTerm(e.target.value);
  }, []);

  return (
    <div className={`table-template-selector ${className}`}>
      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">选择表格模板</h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* 搜索栏 */}
      <div className="p-4 border-b border-gray-200">
        <div className="relative">
          <input
            type="text"
            placeholder="搜索模板..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      <div className="flex h-96">
        {/* 分类侧边栏 */}
        <div className="w-48 border-r border-gray-200 bg-gray-50">
          <div className="p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3">模板分类</h4>
            <nav className="space-y-1">
              <button
                onClick={() => handleCategoryChange('all')}
                className={`w-full text-left px-3 py-2 text-sm rounded-md ${
                  selectedCategory === 'all'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                📁 全部模板
              </button>
              {Object.entries(templateCategories).map(([key, category]) => (
                <button
                  key={key}
                  onClick={() => handleCategoryChange(key)}
                  className={`w-full text-left px-3 py-2 text-sm rounded-md ${
                    selectedCategory === key
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  {category.icon} {category.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* 模板列表 */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-4">
            {filteredTemplates.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-gray-400 mb-2">
                  <svg className="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <p className="text-gray-500">没有找到匹配的模板</p>
              </div>
            ) : (
              <div className="grid gap-4">
                {filteredTemplates.map((template) => (
                  <div
                    key={template.id}
                    className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer"
                    onClick={() => handleTemplateSelect(template)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h5 className="text-sm font-medium text-gray-900 mb-1">
                          {template.name}
                        </h5>
                        <p className="text-xs text-gray-500 mb-3">
                          {template.description}
                        </p>
                        
                        {/* 预览表头 */}
                        <div className="bg-gray-50 rounded p-2">
                          <div className="text-xs text-gray-600 mb-1">表头预览:</div>
                          <div className="flex flex-wrap gap-1">
                            {template.headers.slice(0, 4).map((header, index) => (
                              <span
                                key={index}
                                className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded"
                              >
                                {header}
                              </span>
                            ))}
                            {template.headers.length > 4 && (
                              <span className="text-xs text-gray-500">
                                +{template.headers.length - 4} 更多
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <div className="ml-4 flex flex-col items-end">
                        <span className="text-xs text-gray-500 mb-2">
                          {template.headers.length} 列
                        </span>
                        <span className="text-xs text-gray-500">
                          {template.data.length} 行示例
                        </span>
                      </div>
                    </div>
                    
                    {/* 操作按钮 */}
                    <div className="mt-3 flex justify-end">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleTemplateSelect(template);
                        }}
                        className="px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors"
                      >
                        使用模板
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 底部操作 */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-500">
            找到 {filteredTemplates.length} 个模板
          </div>
          <div className="flex space-x-2">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
            >
              取消
            </button>
            <button
              onClick={() => handleTemplateSelect({ 
                id: 'empty',
                name: '空白表格',
                headers: ['列1', '列2', '列3'],
                data: [['', '', ''], ['', '', '']]
              })}
              className="px-4 py-2 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              创建空白表格
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * 表格模板预览组件
 */
export const TableTemplatePreview = ({ template, className = '' }) => {
  if (!template) {
    return null;
  }

  return (
    <div className={`table-template-preview ${className}`}>
      <div className="mb-4">
        <h4 className="text-lg font-medium text-gray-900">{template.name}</h4>
        <p className="text-sm text-gray-600">{template.description}</p>
      </div>
      
      <div className="border border-gray-200 rounded-lg overflow-hidden">
        <table className="w-full text-sm">
          <thead className="bg-gray-50">
            <tr>
              {template.headers.map((header, index) => (
                <th key={index} className="px-3 py-2 text-left font-medium text-gray-900 border-b border-gray-200">
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {template.data.slice(0, 3).map((row, rowIndex) => (
              <tr key={rowIndex} className="border-b border-gray-100">
                {row.map((cell, cellIndex) => (
                  <td key={cellIndex} className="px-3 py-2 text-gray-700">
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
        {template.data.length > 3 && (
          <div className="px-3 py-2 bg-gray-50 text-xs text-gray-500 text-center">
            ... 还有 {template.data.length - 3} 行数据
          </div>
        )}
      </div>
    </div>
  );
};

export default TableTemplateSelector;
