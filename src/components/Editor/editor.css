/* 简单编辑器样式 */
.simple-editor-container {
  height: 100%;
  background: white;
  overflow: hidden;
}

.simple-editor-textarea {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  padding: 24px 32px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Monaco', 'Men<PERSON>', 'Ubuntu Mono', monospace;
  font-size: 16px;
  line-height: 1.6;
  color: #374151;
  background: white;
  resize: none;
  tab-size: 2;
}

.simple-editor-textarea::placeholder {
  color: #9ca3af;
  font-style: italic;
}

.simple-editor-textarea:focus {
  outline: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .simple-editor-textarea {
    padding: 16px 20px;
    font-size: 15px;
  }
}

/* 滚动条样式 */
.simple-editor-textarea::-webkit-scrollbar {
  width: 8px;
}

.simple-editor-textarea::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.simple-editor-textarea::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.simple-editor-textarea::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Milkdown编辑器容器 */
.milkdown-container {
  height: 100%;
  background: white;
  overflow: hidden;
}

/* 编辑器主体样式 */
.milkdown-container .milkdown {
  height: 100%;
  padding: 24px 32px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #374151;
  outline: none;
  border: none;
  overflow-y: auto;
}

/* 覆盖默认主题颜色 */
.milkdown-container .milkdown .ProseMirror {
  outline: none;
}

/* 标题样式 */
.milkdown-container .milkdown h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 32px 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 3px solid #ff6b35;
  scroll-margin-top: 80px;
}

.milkdown-container .milkdown h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin: 24px 0 12px 0;
  scroll-margin-top: 80px;
}

.milkdown-container .milkdown h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #4b5563;
  margin: 20px 0 8px 0;
  scroll-margin-top: 80px;
}

/* 段落样式 */
.milkdown-container .milkdown p {
  margin: 12px 0;
  line-height: 1.7;
}

/* 强调样式 */
.milkdown-container .milkdown strong {
  font-weight: 700;
  color: #1f2937;
}

.milkdown-container .milkdown em {
  font-style: italic;
  color: #6b7280;
}

/* 引用块样式 */
.milkdown-container .milkdown blockquote {
  border-left: 4px solid #ff6b35;
  background: #fef3e2;
  padding: 16px 20px;
  margin: 20px 0;
  font-style: italic;
  border-radius: 0 6px 6px 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.milkdown-container .milkdown blockquote p {
  margin: 0;
  color: #92400e;
}

/* 列表样式 */
.milkdown-container .milkdown ul,
.milkdown-container .milkdown ol {
  margin: 16px 0;
  padding-left: 24px;
}

.milkdown-container .milkdown li {
  margin: 4px 0;
  line-height: 1.6;
}

.milkdown-container .milkdown ul li {
  list-style-type: disc;
}

.milkdown-container .milkdown ol li {
  list-style-type: decimal;
}

/* 表格样式 */
.milkdown-container .milkdown table {
  border-collapse: collapse;
  width: 100%;
  margin: 20px 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  table-layout: auto; /* 允许自动调整列宽 */
}

.milkdown-container .milkdown th,
.milkdown-container .milkdown td {
  border: 1px solid #e5e7eb;
  padding: 12px 16px;
  text-align: left;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: anywhere;
  white-space: normal;
  vertical-align: top;
  min-width: 80px;
  max-width: 250px;
}

.milkdown-container .milkdown th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.milkdown-container .milkdown tbody tr:nth-child(even) {
  background: #f9fafb;
}

.milkdown-container .milkdown tbody tr:hover {
  background: #f3f4f6;
}

/* 代码样式 */
.milkdown-container .milkdown code {
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
  color: #d97706;
  border: 1px solid #e5e7eb;
}

.milkdown-container .milkdown pre {
  background: #1f2937;
  color: #f9fafb;
  padding: 16px 20px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 20px 0;
  border: 1px solid #374151;
}

.milkdown-container .milkdown pre code {
  background: transparent;
  padding: 0;
  color: inherit;
  border: none;
}

/* 水平分割线 */
.milkdown-container .milkdown hr {
  border: none;
  border-top: 2px solid #e5e7eb;
  margin: 32px 0;
  border-radius: 1px;
}

/* 光标和选择样式 */
.milkdown-container .milkdown .ProseMirror-focused {
  outline: none;
}

.milkdown-container .milkdown ::selection {
  background: #fef3e2;
}

/* 占位符样式 */
.milkdown-container .milkdown .placeholder {
  color: #9ca3af;
  pointer-events: none;
  font-style: italic;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .milkdown-container .milkdown {
    padding: 16px 20px;
    font-size: 15px;
  }
  
  .milkdown-container .milkdown h1 {
    font-size: 1.75rem;
  }
  
  .milkdown-container .milkdown h2 {
    font-size: 1.375rem;
  }
  
  .milkdown-container .milkdown h3 {
    font-size: 1.125rem;
  }
}

/* 滚动条样式 */
.milkdown-container .milkdown::-webkit-scrollbar {
  width: 8px;
}

.milkdown-container .milkdown::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.milkdown-container .milkdown::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.milkdown-container .milkdown::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
} 