import React, { useState, useRef, useEffect } from 'react';
import debounce from 'lodash.debounce';
import './editor.css';

const SimpleEditor = ({ 
  content = '', 
  onChange, 
  className = '',
  placeholder = '在此输入您的PRD内容...'
}) => {
  const [value, setValue] = useState(content);
  const textareaRef = useRef(null);
  const onChangeRef = useRef(onChange);

  // 保持onChange引用最新
  useEffect(() => {
    onChangeRef.current = onChange;
  }, [onChange]);

  // 防抖处理内容变化
  const debouncedOnChange = useRef(
    debounce((newValue) => {
      if (onChangeRef.current) {
        onChangeRef.current(newValue);
      }
    }, 300)
  ).current;

  // 更新外部内容
  useEffect(() => {
    setValue(content);
  }, [content]);

  const handleChange = (e) => {
    const newValue = e.target.value;
    setValue(newValue);
    debouncedOnChange(newValue);
  };

  const handleKeyDown = (e) => {
    // Tab键插入缩进
    if (e.key === 'Tab') {
      e.preventDefault();
      const start = e.target.selectionStart;
      const end = e.target.selectionEnd;
      const newValue = value.substring(0, start) + '  ' + value.substring(end);
      setValue(newValue);
      debouncedOnChange(newValue);
      
      // 设置光标位置
      setTimeout(() => {
        e.target.selectionStart = e.target.selectionEnd = start + 2;
      }, 0);
    }
  };

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      debouncedOnChange.cancel();
    };
  }, [debouncedOnChange]);

  return (
    <div className={`simple-editor-container ${className}`}>
      <textarea
        ref={textareaRef}
        value={value}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className="simple-editor-textarea"
        spellCheck={false}
      />
    </div>
  );
};

export default SimpleEditor; 