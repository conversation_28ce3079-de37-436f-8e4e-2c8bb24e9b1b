import React, { useState, useEffect } from 'react';
import './TableConversionPrompt.css';

/**
 * 表格转换提示组件
 * 类似语雀编辑器的表格转换提示
 */
const TableConversionPrompt = ({
  isVisible,
  onConfirm,
  onCancel,
  tablePreview = null
}) => {
  const [showPreview, setShowPreview] = useState(false);

  useEffect(() => {
    if (isVisible) {
      // 延迟显示预览，让动画更流畅
      const timer = setTimeout(() => setShowPreview(true), 100);
      return () => clearTimeout(timer);
    } else {
      setShowPreview(false);
    }
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <div className="table-conversion-prompt">
      {/* 背景遮罩 */}
      <div className="prompt-backdrop" onClick={onCancel} />

      <div className="prompt-card">
        <div className="prompt-header">
          <span className="prompt-icon">📊</span>
          <span className="prompt-title">检测到表格内容</span>
        </div>

        <div className="prompt-content">
          <p className="prompt-message">
            是否将Markdown表格转换为可视化表格？
          </p>

          {/* 表格预览 */}
          {showPreview && tablePreview && (
            <div className="table-preview">
              <div className="preview-label">预览：</div>
              <div
                className="preview-content"
                dangerouslySetInnerHTML={{ __html: tablePreview }}
              />
            </div>
          )}
        </div>

        <div className="prompt-actions">
          <button
            className="btn-cancel"
            onClick={onCancel}
          >
            保持原样
          </button>
          <button
            className="btn-confirm"
            onClick={onConfirm}
          >
            转换为表格
          </button>
        </div>
      </div>
    </div>
  );
};

export default TableConversionPrompt;
