import React from 'react';
import { TableConverter } from '../../utils/tableUtils';

/**
 * 表格渲染增强器
 * 确保Markdown表格立即渲染为HTML表格样式
 */
export class TableRenderEnhancer {
  constructor(editorContainer) {
    this.editorContainer = editorContainer;
    this.observer = null;
    this.init();
  }

  init() {
    if (!this.editorContainer) return;

    // 初始扫描
    this.enhanceExistingTables();

    // 监听DOM变化
    this.observer = new MutationObserver((mutations) => {
      let shouldEnhance = false;
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // 检查是否有新的文本节点包含表格Markdown
          const addedNodes = Array.from(mutation.addedNodes);
          
          addedNodes.forEach(node => {
            if (node.nodeType === Node.TEXT_NODE) {
              const text = node.textContent;
              if (this.containsTableMarkdown(text)) {
                shouldEnhance = true;
              }
            } else if (node.nodeType === Node.ELEMENT_NODE) {
              // 检查元素内容
              const textContent = node.textContent;
              if (this.containsTableMarkdown(textContent)) {
                shouldEnhance = true;
              }
            }
          });
        }
      });
      
      if (shouldEnhance) {
        // 立即处理，然后延迟再次处理确保渲染
        this.enhanceExistingTables();
        setTimeout(() => this.enhanceExistingTables(), 50);
        setTimeout(() => this.enhanceExistingTables(), 200);
      }
    });

    this.observer.observe(this.editorContainer, {
      childList: true,
      subtree: true,
      characterData: true
    });
  }

  containsTableMarkdown(text) {
    // 检查文本是否包含Markdown表格语法
    return text.includes('|') && text.includes('---');
  }

  enhanceExistingTables() {
    if (!this.editorContainer) return;

    // 查找所有可能包含Markdown表格的文本节点
    const walker = document.createTreeWalker(
      this.editorContainer,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );

    const textNodes = [];
    let node;
    while (node = walker.nextNode()) {
      if (this.containsTableMarkdown(node.textContent)) {
        textNodes.push(node);
      }
    }

    // 处理找到的文本节点
    textNodes.forEach(textNode => {
      this.convertMarkdownTableToHTML(textNode);
    });
  }

  convertMarkdownTableToHTML(textNode) {
    const text = textNode.textContent;
    const lines = text.split('\n');
    
    let tableStart = -1;
    let tableEnd = -1;
    
    // 查找表格的开始和结束
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line.includes('|') && line.length > 0) {
        if (tableStart === -1) {
          tableStart = i;
        }
        tableEnd = i;
      } else if (tableStart !== -1 && line.length === 0) {
        // 空行可能是表格的一部分
        continue;
      } else if (tableStart !== -1) {
        // 非表格行，结束表格
        break;
      }
    }

    if (tableStart !== -1 && tableEnd !== -1 && tableEnd > tableStart) {
      const tableLines = lines.slice(tableStart, tableEnd + 1);
      const markdownTable = tableLines.join('\n');
      
      try {
        // 解析Markdown表格
        const { headers, data } = TableConverter.fromMarkdown(markdownTable);
        
        if (headers.length > 0) {
          // 生成HTML表格
          const htmlTable = TableConverter.toHTML(data, headers, {
            className: 'table-auto border-collapse border border-gray-300 w-full',
            cellPadding: '8px',
            enableWordWrap: true // 启用自动换行
          });
          
          // 创建表格元素
          const tableContainer = document.createElement('div');
          tableContainer.innerHTML = htmlTable;
          const tableElement = tableContainer.firstChild;
          
          // 添加样式
          if (tableElement) {
            tableElement.style.marginTop = '16px';
            tableElement.style.marginBottom = '16px';
            tableElement.style.borderCollapse = 'collapse';
            tableElement.style.width = '100%';
            
            // 替换文本节点
            const beforeText = lines.slice(0, tableStart).join('\n');
            const afterText = lines.slice(tableEnd + 1).join('\n');
            
            const parent = textNode.parentNode;
            if (parent) {
              // 创建前置文本节点
              if (beforeText.trim()) {
                const beforeNode = document.createTextNode(beforeText + '\n');
                parent.insertBefore(beforeNode, textNode);
              }
              
              // 插入表格
              parent.insertBefore(tableElement, textNode);
              
              // 创建后置文本节点
              if (afterText.trim()) {
                const afterNode = document.createTextNode('\n' + afterText);
                parent.insertBefore(afterNode, textNode);
              }
              
              // 移除原始文本节点
              parent.removeChild(textNode);
            }
          }
        }
      } catch (e) {
        console.warn('Failed to convert markdown table to HTML:', e);
      }
    }
  }

  destroy() {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
  }
}

/**
 * 表格渲染增强器React Hook
 */
export const useTableRenderEnhancer = (editorContainer) => {
  const enhancerRef = React.useRef(null);

  React.useEffect(() => {
    if (editorContainer && !enhancerRef.current) {
      enhancerRef.current = new TableRenderEnhancer(editorContainer);
    }

    return () => {
      if (enhancerRef.current) {
        enhancerRef.current.destroy();
        enhancerRef.current = null;
      }
    };
  }, [editorContainer]);

  return enhancerRef.current;
};
