/* 悬浮工具栏样式 */
.floating-toolbar {
  position: fixed;
  bottom: 90px;
  right: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(0, 0, 0, 0.08);
  padding: 16px;
  z-index: 999;
  min-width: 220px;
  max-width: 280px;
  animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 工具栏区域 */
.toolbar-section {
  margin-bottom: 16px;
}

.toolbar-section:last-of-type {
  margin-bottom: 0;
}

.toolbar-section-title {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0 0 8px 0;
  padding: 0;
}

/* 工具项容器 */
.toolbar-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 工具项样式 */
.toolbar-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #374151;
  text-align: left;
  width: 100%;
}

.toolbar-item:hover {
  background: #f3f4f6;
  color: #1f2937;
}

.toolbar-item:active {
  background: #e5e7eb;
  transform: scale(0.98);
}

.toolbar-item:focus {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
}

/* 工具项图标 */
.toolbar-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 16px;
  flex-shrink: 0;
}

/* 工具项文本 */
.toolbar-item-text {
  font-size: 14px;
  font-weight: 500;
  flex: 1;
}

/* 关闭按钮区域 */
.toolbar-close {
  border-top: 1px solid #e5e7eb;
  padding-top: 12px;
  margin-top: 12px;
  display: flex;
  justify-content: center;
}

.toolbar-close-button {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #6b7280;
  transition: all 0.2s ease;
}

.toolbar-close-button:hover {
  background: #e5e7eb;
  color: #374151;
}

.toolbar-close-button:active {
  background: #d1d5db;
  transform: scale(0.95);
}

.toolbar-close-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .floating-toolbar {
    bottom: 70px;
    right: 16px;
    left: 16px;
    min-width: auto;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .floating-toolbar {
    bottom: 60px;
    right: 12px;
    left: 12px;
    padding: 12px;
  }
  
  .toolbar-item {
    padding: 6px 8px;
    font-size: 13px;
  }
  
  .toolbar-item-icon {
    width: 18px;
    height: 18px;
    font-size: 14px;
  }
  
  .toolbar-item-text {
    font-size: 13px;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .floating-toolbar {
    background: #1f2937;
    border-color: #374151;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }
  
  .toolbar-section-title {
    color: #9ca3af;
  }
  
  .toolbar-item {
    color: #e5e7eb;
  }
  
  .toolbar-item:hover {
    background: #374151;
    color: #f9fafb;
  }
  
  .toolbar-item:active {
    background: #4b5563;
  }
  
  .toolbar-close {
    border-color: #374151;
  }
  
  .toolbar-close-button {
    background: #374151;
    color: #9ca3af;
  }
  
  .toolbar-close-button:hover {
    background: #4b5563;
    color: #e5e7eb;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .floating-toolbar {
    border: 2px solid #000;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }
  
  .toolbar-item {
    border: 1px solid transparent;
  }
  
  .toolbar-item:hover {
    border-color: #000;
    background: #f0f0f0;
  }
  
  .toolbar-item:focus {
    border-color: #0066cc;
    outline: 2px solid #0066cc;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .floating-toolbar {
    animation: none;
  }
  
  .toolbar-item {
    transition: none;
  }
  
  .toolbar-close-button {
    transition: none;
  }
}
