/* 表格转换提示组件样式 */
.table-conversion-prompt {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 999999 !important;
  pointer-events: auto !important;
}

.prompt-backdrop {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.4) !important;
  backdrop-filter: blur(2px) !important;
  z-index: 999998 !important;
}

.prompt-card {
  position: relative !important;
  background: white !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  min-width: 400px !important;
  max-width: 600px !important;
  max-height: 80vh !important;
  overflow: hidden !important;
  animation: promptSlideIn 0.2s ease-out !important;
  z-index: 999999 !important;
  pointer-events: auto !important;
}

@keyframes promptSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.prompt-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.prompt-icon {
  font-size: 20px;
  margin-right: 8px;
}

.prompt-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.prompt-content {
  padding: 20px;
}

.prompt-message {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.table-preview {
  margin-top: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;
}

.preview-label {
  padding: 8px 12px;
  background: #f8f9fa;
  font-size: 12px;
  color: #666;
  border-bottom: 1px solid #e8e8e8;
}

.preview-content {
  padding: 12px;
  max-height: 200px;
  overflow: auto;
}

.preview-content table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
}

.preview-content th,
.preview-content td {
  border: 1px solid #e8e8e8;
  padding: 6px 8px;
  text-align: left;
}

.preview-content th {
  background: #f8f9fa;
  font-weight: 600;
}

.prompt-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.btn-cancel,
.btn-confirm {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid;
}

.btn-cancel {
  background: white;
  color: #666;
  border-color: #d9d9d9;
}

.btn-cancel:hover {
  background: #f5f5f5;
  border-color: #b3b3b3;
}

.btn-confirm {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.btn-confirm:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

.btn-confirm:active {
  background: #096dd9;
  border-color: #096dd9;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .prompt-card {
    min-width: 320px;
    margin: 20px;
  }
  
  .prompt-actions {
    flex-direction: column;
  }
  
  .btn-cancel,
  .btn-confirm {
    width: 100%;
  }
}
