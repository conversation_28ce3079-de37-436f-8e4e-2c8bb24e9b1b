# PRD评审确认页面实施方案

## 概述

本方案专门针对PRD智能评审流程中的"人工确认"页面进行详细设计和实施指导。该页面是整个评审流程的核心环节，负责展示智能分析结果并允许用户进行人工确认和修改。

## 页面功能概述

### 核心功能
1. **三栏布局设计**：文档大纲 + 文档内容 + 问题详情
2. **文档大纲导航**：显示文档结构，支持快速跳转
3. **问题标记显示**：在文档内容中高亮显示发现的问题
4. **问题详情查看**：右侧面板显示问题的详细信息
5. **建议采纳/拒绝**：支持对每个问题进行确认操作
6. **评审进度跟踪**：实时显示评审完成进度
7. **批量操作支持**：支持批量处理相似问题

### 页面布局结构
```
┌─────────────────────────────────────────────────────────────┐
│                        页面标题栏                            │
├─────────────┬─────────────────────────┬─────────────────────┤
│             │                         │                     │
│   文档大纲   │       文档内容区域        │     问题详情面板     │
│   导航区域   │     (带问题标记)         │                     │
│             │                         │                     │
│   - 章节1    │  # 1. 项目概述          │   问题类型: 错误     │
│     - 问题2  │  [问题标记] 内容...     │   问题标题: ...     │
│   - 章节2    │                         │   问题描述: ...     │
│     - 问题1  │  # 2. 用户需求          │   修改建议: ...     │
│             │  [问题标记] 内容...     │                     │
│             │                         │   [采纳] [拒绝]     │
│             │                         │                     │
├─────────────┴─────────────────────────┴─────────────────────┤
│                      底部操作栏                              │
│              评审进度: 3/10  [保存草稿] [查看评审结果]        │
└─────────────────────────────────────────────────────────────┘
```

## 技术实现方案

### 文件结构
```
src/components/PRDEvaluation/
├── PRDReviewTab.js                    # 主页面组件
└── components/
    ├── DocumentOutline.js             # 文档大纲组件
    ├── DocumentContent.js             # 文档内容组件
    ├── IssueDetailPanel.js            # 问题详情面板
    ├── IssueMarker.js                 # 问题标记组件
    ├── ReviewProgress.js              # 评审进度组件
    └── BatchOperations.js             # 批量操作组件
```

### 数据模型
```javascript
// 文档章节数据模型
const DocumentSectionSchema = {
  id: '',
  title: '',
  content: '',
  level: 1,
  issues: []
}

// 问题标记数据模型
const IssueMarkerSchema = {
  id: '',
  issueId: '',
  startOffset: 0,
  endOffset: 0,
  type: 'error', // error | warning | suggestion
  highlighted: false
}

// 评审状态数据模型
const ReviewStateSchema = {
  selectedIssue: null,
  reviewedIssues: new Set(),
  acceptedSuggestions: new Set(),
  rejectedSuggestions: new Set(),
  currentSection: '',
  outlineExpanded: true
}
```

## 详细实现

### 1. 主页面组件 (PRDReviewTab.js)

```javascript
import React, { useState, useEffect, useCallback } from 'react'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { List, ChevronUp, ChevronDown } from 'lucide-react'
import DocumentOutline from './components/DocumentOutline'
import DocumentContent from './components/DocumentContent'
import IssueDetailPanel from './components/IssueDetailPanel'
import ReviewProgress from './components/ReviewProgress'
import { usePRDReview } from '../../hooks/usePRDReview'

const PRDReviewTab = ({ document, analysisResults, onReviewComplete }) => {
  const {
    outlineExpanded,
    setOutlineExpanded,
    selectedIssue,
    setSelectedIssue,
    reviewedIssues,
    acceptedSuggestions,
    documentSections,
    handleIssueReview,
    handleCompleteReview,
    scrollToSection,
    initializeDocumentSections
  } = usePRDReview(document, analysisResults, onReviewComplete)

  useEffect(() => {
    if (document && analysisResults) {
      initializeDocumentSections()
    }
  }, [document, analysisResults, initializeDocumentSections])

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setOutlineExpanded(!outlineExpanded)}
          >
            <List className="w-4 h-4 mr-2" />
            {outlineExpanded ? "隐藏大纲" : "显示大纲"}
            {outlineExpanded ? <ChevronUp className="w-4 h-4 ml-2" /> : <ChevronDown className="w-4 h-4 ml-2" />}
          </Button>
          <h2 className="text-xl font-semibold text-gray-900">
            {document?.title || "智能驾驶系统PRD"}
          </h2>
        </div>
        <Badge variant="secondary">v1.2.0 汽车行业</Badge>
      </div>

      {/* 三栏布局主体 */}
      <div className="flex gap-6 h-[700px]">
        {/* 左栏：文档大纲导航 */}
        {outlineExpanded && (
          <DocumentOutline
            documentSections={documentSections}
            selectedIssue={selectedIssue}
            onSectionClick={scrollToSection}
            onIssueClick={setSelectedIssue}
          />
        )}

        {/* 中栏：文档内容 */}
        <DocumentContent
          documentSections={documentSections}
          selectedIssue={selectedIssue}
          reviewedIssues={reviewedIssues}
          acceptedSuggestions={acceptedSuggestions}
          onIssueClick={setSelectedIssue}
        />

        {/* 右栏：问题详情面板 */}
        <IssueDetailPanel
          selectedIssue={selectedIssue}
          reviewedIssues={reviewedIssues}
          acceptedSuggestions={acceptedSuggestions}
          onIssueReview={handleIssueReview}
          onClose={() => setSelectedIssue(null)}
        />
      </div>

      {/* 底部操作栏 */}
      <ReviewProgress
        reviewedIssues={reviewedIssues}
        totalIssues={analysisResults?.totalIssues || 0}
        onSaveDraft={() => console.log('保存草稿')}
        onCompleteReview={handleCompleteReview}
      />
    </div>
  )
}

export default PRDReviewTab
```

### 2. 自定义Hook (usePRDReview.js)

```javascript
import { useState, useCallback } from 'react'

export const usePRDReview = (document, analysisResults, onReviewComplete) => {
  const [outlineExpanded, setOutlineExpanded] = useState(true)
  const [selectedIssue, setSelectedIssue] = useState(null)
  const [reviewedIssues, setReviewedIssues] = useState(new Set())
  const [acceptedSuggestions, setAcceptedSuggestions] = useState(new Set())
  const [documentSections, setDocumentSections] = useState([])

  // 初始化文档章节
  const initializeDocumentSections = useCallback(() => {
    const sections = [
      {
        id: "section-1",
        title: "1. 项目概述",
        level: 1,
        content: "智能驾驶系统PRD文档，旨在定义自动驾驶功能的需求规格。本文档将详细描述系统的功能需求、性能指标、用户界面设计以及技术实现方案。",
        issues: analysisResults?.issues.filter(issue => issue.section === "section-1") || []
      },
      {
        id: "section-2", 
        title: "2. 用户需求",
        level: 1,
        content: "用户场景描述过于简略，缺少极端情况和边缘案例的考虑。需要补充详细的用户画像、使用场景和需求优先级。",
        issues: analysisResults?.issues.filter(issue => issue.section === "section-2") || []
      },
      {
        id: "section-3",
        title: "3. 产品功能", 
        level: 1,
        content: "本功能模块主要实现对PRD文档的智能评审，包括但不限于以下几点：自动检测文档结构完整性、识别需求描述的模糊性、验证功能逻辑的一致性。",
        issues: analysisResults?.issues.filter(issue => issue.section === "section-3") || []
      },
      {
        id: "section-4",
        title: "4. 非功能需求",
        level: 1,
        content: "系统性能要求、安全性要求、可用性要求等。系统应支持并发用户数不少于1000，响应时间不超过2秒，可用性达到99.9%。",
        issues: analysisResults?.issues.filter(issue => issue.section === "section-4") || []
      },
      {
        id: "section-5",
        title: "5. 技术架构",
        level: 1,
        content: "系统架构设计、技术选型、部署方案等。采用微服务架构，前端使用React，后端使用Node.js，数据库使用MongoDB。",
        issues: analysisResults?.issues.filter(issue => issue.section === "section-5") || []
      }
    ]
    setDocumentSections(sections)
  }, [analysisResults])

  // 处理问题确认
  const handleIssueReview = useCallback((issueId, accepted) => {
    setReviewedIssues(prev => new Set([...prev, issueId]))
    if (accepted) {
      setAcceptedSuggestions(prev => new Set([...prev, issueId]))
    } else {
      setAcceptedSuggestions(prev => {
        const newSet = new Set(prev)
        newSet.delete(issueId)
        return newSet
      })
    }
  }, [])

  // 完成评审
  const handleCompleteReview = useCallback(() => {
    const reviewResults = {
      documentId: document.id,
      reviewTimestamp: new Date().toISOString(),
      totalIssues: analysisResults?.totalIssues || 0,
      reviewedIssues: reviewedIssues.size,
      acceptedSuggestions: acceptedSuggestions.size,
      rejectedSuggestions: reviewedIssues.size - acceptedSuggestions.size,
      reviewedIssueIds: Array.from(reviewedIssues),
      acceptedSuggestionIds: Array.from(acceptedSuggestions),
      overallApproval: reviewedIssues.size > 0 ? (acceptedSuggestions.size / reviewedIssues.size) * 100 : 0
    }
    onReviewComplete(reviewResults)
  }, [document, analysisResults, reviewedIssues, acceptedSuggestions, onReviewComplete])

  // 滚动到指定章节
  const scrollToSection = useCallback((sectionId) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }, [])

  return {
    outlineExpanded,
    setOutlineExpanded,
    selectedIssue,
    setSelectedIssue,
    reviewedIssues,
    acceptedSuggestions,
    documentSections,
    handleIssueReview,
    handleCompleteReview,
    scrollToSection,
    initializeDocumentSections
  }
}
```

### 3. 文档大纲组件 (DocumentOutline.js)

```javascript
import React from 'react'
import { Badge } from '../../ui/badge'
import { ScrollArea } from '../../ui/scroll-area'

const DocumentOutline = ({
  documentSections,
  selectedIssue,
  onSectionClick,
  onIssueClick
}) => {
  return (
    <div className="w-64 bg-white border border-gray-200 rounded-lg p-4">
      <h3 className="font-medium text-gray-900 mb-4">文档大纲</h3>
      <div className="space-y-1">
        <div className="text-xs text-gray-500 px-2 py-1">
          {documentSections.length > 0 ? `${documentSections.length} 个章节` : '暂无章节'}
        </div>
      </div>

      <ScrollArea className="h-[600px] mt-4">
        <div className="space-y-2">
          {documentSections.map((section) => (
            <div key={section.id} className="space-y-1">
              <div
                className="p-2 rounded cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => onSectionClick(section.id)}
              >
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-900">
                    {section.title}
                  </span>
                  {section.issues.length > 0 && (
                    <Badge variant="destructive" className="text-xs">
                      {section.issues.length}
                    </Badge>
                  )}
                </div>
              </div>

              {/* 章节问题列表 */}
              {section.issues.length > 0 && (
                <div className="ml-4 space-y-1">
                  {section.issues.map((issue) => (
                    <div
                      key={issue.id}
                      className={`text-xs p-2 rounded cursor-pointer transition-colors ${
                        selectedIssue?.id === issue.id
                          ? 'bg-orange-100 border border-orange-300'
                          : issue.type === 'error'
                            ? 'bg-red-50 text-red-700 hover:bg-red-100'
                            : issue.type === 'warning'
                              ? 'bg-yellow-50 text-yellow-700 hover:bg-yellow-100'
                              : 'bg-blue-50 text-blue-700 hover:bg-blue-100'
                      }`}
                      onClick={() => onIssueClick(issue)}
                    >
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${
                          issue.type === 'error' ? 'bg-red-500' :
                          issue.type === 'warning' ? 'bg-yellow-500' :
                          'bg-blue-500'
                        }`} />
                        <span className="truncate">{issue.title}</span>
                      </div>
                      <div className="mt-1 text-xs text-gray-500">
                        置信度: {issue.confidence}%
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  )
}

export default DocumentOutline
```

### 4. 文档内容组件 (DocumentContent.js)

```javascript
import React from 'react'
import { ScrollArea } from '../../ui/scroll-area'
import IssueMarker from './IssueMarker'

const DocumentContent = ({
  documentSections,
  selectedIssue,
  reviewedIssues,
  acceptedSuggestions,
  onIssueClick
}) => {
  return (
    <div className="flex-1 bg-white border border-gray-200 rounded-lg overflow-hidden">
      <ScrollArea className="h-full">
        <div className="p-6 space-y-6">
          {documentSections.map((section) => (
            <div key={section.id} id={section.id} className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                {section.title}
              </h3>

              <div className="prose prose-gray max-w-none">
                <p className="text-gray-700 leading-relaxed">{section.content}</p>
              </div>

              {/* 章节问题标记 */}
              {section.issues.map((issue) => (
                <IssueMarker
                  key={issue.id}
                  issue={issue}
                  isSelected={selectedIssue?.id === issue.id}
                  isReviewed={reviewedIssues.has(issue.id)}
                  isAccepted={acceptedSuggestions.has(issue.id)}
                  onClick={() => onIssueClick(issue)}
                />
              ))}
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  )
}

export default DocumentContent
```

### 5. 问题标记组件 (IssueMarker.js)

```javascript
import React from 'react'
import { Badge } from '../../ui/badge'

const IssueMarker = ({
  issue,
  isSelected,
  isReviewed,
  isAccepted,
  onClick
}) => {
  const getIssueTypeColor = (type) => {
    switch (type) {
      case 'error':
        return 'border-red-500 bg-red-50 hover:bg-red-100'
      case 'warning':
        return 'border-yellow-500 bg-yellow-50 hover:bg-yellow-100'
      case 'suggestion':
        return 'border-blue-500 bg-blue-50 hover:bg-blue-100'
      default:
        return 'border-gray-500 bg-gray-50 hover:bg-gray-100'
    }
  }

  const getIssueTypeText = (type) => {
    switch (type) {
      case 'error': return '错误'
      case 'warning': return '警告'
      case 'suggestion': return '建议'
      default: return '未知'
    }
  }

  const getIssueTypeBadgeColor = (type) => {
    switch (type) {
      case 'error':
        return 'bg-red-100 text-red-800'
      case 'warning':
        return 'bg-yellow-100 text-yellow-800'
      case 'suggestion':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div
      className={`border-l-4 p-4 rounded-r-lg cursor-pointer transition-all duration-200 ${
        getIssueTypeColor(issue.type)
      } ${isSelected ? 'ring-2 ring-orange-300 shadow-md' : ''} ${
        isReviewed ? 'opacity-60' : ''
      }`}
      onClick={onClick}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <Badge className={getIssueTypeBadgeColor(issue.type)}>
              {getIssueTypeText(issue.type)}
            </Badge>
            <span className="text-sm text-gray-500">
              置信度: {issue.confidence}%
            </span>
            <span className="text-sm text-gray-500">
              规则: {issue.rule}
            </span>
          </div>

          <h4 className="font-medium text-gray-900 mb-1">{issue.title}</h4>
          <p className="text-sm text-gray-600 mb-2">{issue.description}</p>

          <div className="bg-blue-50 border border-blue-200 rounded p-2 mb-2">
            <p className="text-sm text-blue-700">
              <span className="font-medium">修改建议：</span>
              {issue.suggestion}
            </p>
          </div>

          {issue.originalContent && (
            <div className="text-xs text-gray-500">
              <span className="font-medium">原文内容：</span>
              "{issue.originalContent}"
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2 ml-4">
          {isReviewed ? (
            <Badge className={
              isAccepted
                ? 'bg-green-100 text-green-800'
                : 'bg-gray-100 text-gray-800'
            }>
              {isAccepted ? '已采纳' : '已拒绝'}
            </Badge>
          ) : (
            <Badge variant="outline">待确认</Badge>
          )}
        </div>
      </div>
    </div>
  )
}

export default IssueMarker
```

### 6. 问题详情面板 (IssueDetailPanel.js)

```javascript
import React from 'react'
import { Button } from '../../ui/button'
import { Badge } from '../../ui/badge'
import { Progress } from '../../ui/progress'
import { X, CheckCircle, FileText } from 'lucide-react'

const IssueDetailPanel = ({
  selectedIssue,
  reviewedIssues,
  acceptedSuggestions,
  onIssueReview,
  onClose
}) => {
  if (!selectedIssue) {
    return (
      <div className="w-80 bg-white border border-gray-200 rounded-lg p-4">
        <div className="text-center text-gray-500 mt-8">
          <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p>点击左侧问题查看详情</p>
          <p className="text-xs mt-2">选择文档中的问题标记来查看详细信息和处理建议</p>
        </div>
      </div>
    )
  }

  const isReviewed = reviewedIssues.has(selectedIssue.id)
  const isAccepted = acceptedSuggestions.has(selectedIssue.id)

  const getIssueTypeText = (type) => {
    switch (type) {
      case 'error': return '错误'
      case 'warning': return '警告'
      case 'suggestion': return '建议'
      default: return '未知'
    }
  }

  const getIssueTypeBadgeColor = (type) => {
    switch (type) {
      case 'error': return 'bg-red-100 text-red-800'
      case 'warning': return 'bg-yellow-100 text-yellow-800'
      case 'suggestion': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityText = (priority) => {
    switch (priority) {
      case 'high': return '高优先级'
      case 'medium': return '中优先级'
      case 'low': return '低优先级'
      default: return '未知优先级'
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="w-80 bg-white border border-gray-200 rounded-lg p-4">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-gray-900">问题详情</h3>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        <div className="space-y-4">
          {/* 问题基本信息 */}
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-700">问题类型</label>
              <div className="mt-1">
                <Badge className={getIssueTypeBadgeColor(selectedIssue.type)}>
                  {getIssueTypeText(selectedIssue.type)}
                </Badge>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700">优先级</label>
              <div className="mt-1">
                <Badge className={getPriorityColor(selectedIssue.priority)}>
                  {getPriorityText(selectedIssue.priority)}
                </Badge>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700">问题标题</label>
              <p className="text-sm text-gray-900 mt-1 p-2 bg-gray-50 rounded">
                {selectedIssue.title}
              </p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700">问题描述</label>
              <p className="text-sm text-gray-600 mt-1 p-2 bg-gray-50 rounded">
                {selectedIssue.description}
              </p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700">修改建议</label>
              <div className="mt-1 p-3 bg-blue-50 border border-blue-200 rounded">
                <p className="text-sm text-blue-700">{selectedIssue.suggestion}</p>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700">相关规则</label>
              <p className="text-sm text-gray-600 mt-1 p-2 bg-gray-50 rounded">
                {selectedIssue.rule} (ID: {selectedIssue.ruleId})
              </p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700">AI置信度</label>
              <div className="flex items-center space-x-2 mt-1">
                <Progress value={selectedIssue.confidence} className="flex-1 h-2" />
                <span className="text-sm text-gray-600 font-medium">
                  {selectedIssue.confidence}%
                </span>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {selectedIssue.confidence >= 90 ? '高置信度' :
                 selectedIssue.confidence >= 70 ? '中等置信度' : '低置信度'}
              </p>
            </div>

            {/* 原文内容 */}
            {selectedIssue.originalContent && (
              <div>
                <label className="text-sm font-medium text-gray-700">原文内容</label>
                <div className="mt-1 p-2 bg-gray-50 border rounded text-sm text-gray-600">
                  "{selectedIssue.originalContent}"
                </div>
              </div>
            )}

            {/* 时间戳 */}
            {selectedIssue.timestamp && (
              <div>
                <label className="text-sm font-medium text-gray-700">发现时间</label>
                <p className="text-sm text-gray-600 mt-1">
                  {new Date(selectedIssue.timestamp).toLocaleString('zh-CN')}
                </p>
              </div>
            )}
          </div>

          {/* 操作按钮 */}
          {!isReviewed ? (
            <div className="space-y-2 pt-4 border-t border-gray-200">
              <Button
                className="w-full bg-green-600 hover:bg-green-700 text-white"
                onClick={() => onIssueReview(selectedIssue.id, true)}
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                采纳建议
              </Button>
              <Button
                variant="outline"
                className="w-full border-red-300 text-red-600 hover:bg-red-50"
                onClick={() => onIssueReview(selectedIssue.id, false)}
              >
                <X className="w-4 h-4 mr-2" />
                拒绝建议
              </Button>
            </div>
          ) : (
            <div className="pt-4 border-t border-gray-200">
              <div className="text-center">
                <Badge className={
                  isAccepted
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'
                }>
                  {isAccepted ? '✓ 已采纳此建议' : '✗ 已拒绝此建议'}
                </Badge>
                <p className="text-xs text-gray-500 mt-2">
                  {isAccepted ? '此建议将被应用到最终文档中' : '此建议已被标记为不适用'}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default IssueDetailPanel
```

### 7. 评审进度组件 (ReviewProgress.js)

```javascript
import React from 'react'
import { Button } from '../../ui/button'
import { Progress } from '../../ui/progress'
import { Save, Eye } from 'lucide-react'

const ReviewProgress = ({
  reviewedIssues,
  totalIssues,
  onSaveDraft,
  onCompleteReview
}) => {
  const progressPercentage = totalIssues > 0 ? (reviewedIssues.size / totalIssues) * 100 : 0
  const isCompleteDisabled = reviewedIssues.size === 0

  return (
    <div className="flex items-center justify-between bg-white border border-gray-200 rounded-lg p-4">
      <div className="flex items-center space-x-4">
        <div className="text-sm text-gray-600">
          评审进度: {reviewedIssues.size} / {totalIssues}
        </div>
        <div className="flex items-center space-x-2">
          <Progress
            value={progressPercentage}
            className="w-32 h-2"
          />
          <span className="text-sm text-gray-500">
            {Math.round(progressPercentage)}%
          </span>
        </div>
        <div className="text-xs text-gray-500">
          {reviewedIssues.size === totalIssues
            ? '✓ 所有问题已处理完成'
            : `还有 ${totalIssues - reviewedIssues.size} 个问题待处理`}
        </div>
      </div>

      <div className="flex space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onSaveDraft}
        >
          <Save className="w-4 h-4 mr-2" />
          保存草稿
        </Button>
        <Button
          className="bg-orange-500 hover:bg-orange-600"
          size="sm"
          onClick={onCompleteReview}
          disabled={isCompleteDisabled}
        >
          <Eye className="w-4 h-4 mr-2" />
          查看评审结果
        </Button>
      </div>
    </div>
  )
}

export default ReviewProgress
```

## 测试数据

### 模拟分析结果数据

```javascript
// src/data/mockAnalysisResults.js
export const mockAnalysisResults = {
  documentId: "doc-001",
  analysisTimestamp: "2024-01-15T10:30:00Z",
  overallScore: 78,
  totalIssues: 8,
  errorCount: 2,
  warningCount: 3,
  suggestionCount: 3,
  issues: [
    {
      id: "issue-001",
      type: "error",
      title: "缺少关键功能描述",
      description: "产品功能模块缺少详细的功能描述和用户场景，无法准确评估开发工作量和技术难度。",
      section: "section-3",
      sectionTitle: "3. 产品功能",
      suggestion: "建议补充详细的功能描述，包括用户操作流程、系统响应行为、异常处理机制和预期结果。每个功能点应包含输入、处理、输出的完整描述。",
      priority: "high",
      confidence: 92,
      rule: "功能完整性检查",
      ruleId: "rule-001",
      originalContent: "本功能模块主要实现对PRD文档的智能评审",
      timestamp: "2024-01-15T10:25:00Z"
    },
    {
      id: "issue-002",
      type: "error",
      title: "性能指标缺失",
      description: "非功能需求章节缺少具体的性能指标定义，如响应时间、吞吐量、并发用户数等关键指标。",
      section: "section-4",
      sectionTitle: "4. 非功能需求",
      suggestion: "建议添加具体的性能指标，包括：响应时间不超过2秒、支持并发用户数1000+、系统可用性99.9%、数据处理吞吐量等量化指标。",
      priority: "high",
      confidence: 88,
      rule: "性能指标检查",
      ruleId: "rule-004",
      originalContent: "系统性能要求、安全性要求、可用性要求等",
      timestamp: "2024-01-15T10:26:00Z"
    },
    {
      id: "issue-003",
      type: "warning",
      title: "用户场景描述不够具体",
      description: "用户需求章节的场景描述过于简略，缺少具体的用户画像、使用环境和操作步骤。",
      section: "section-2",
      sectionTitle: "2. 用户需求",
      suggestion: "建议增加详细的用户画像描述，包括用户角色、技能水平、使用环境、典型操作流程，以及异常情况的处理方式。",
      priority: "medium",
      confidence: 78,
      rule: "场景完整性检查",
      ruleId: "rule-002",
      originalContent: "用户场景描述过于简略，缺少极端情况和边缘案例的考虑",
      timestamp: "2024-01-15T10:27:00Z"
    },
    {
      id: "issue-004",
      type: "warning",
      title: "技术架构描述模糊",
      description: "技术架构章节缺少具体的技术选型理由和架构设计细节。",
      section: "section-5",
      sectionTitle: "5. 技术架构",
      suggestion: "建议补充技术选型的具体理由、架构图、数据流图、部署架构等详细信息，以便开发团队理解和实施。",
      priority: "medium",
      confidence: 82,
      rule: "技术架构检查",
      ruleId: "rule-005",
      originalContent: "系统架构设计、技术选型、部署方案等",
      timestamp: "2024-01-15T10:28:00Z"
    },
    {
      id: "issue-005",
      type: "warning",
      title: "项目概述缺少背景信息",
      description: "项目概述章节缺少项目背景、目标用户、市场分析等重要信息。",
      section: "section-1",
      sectionTitle: "1. 项目概述",
      suggestion: "建议补充项目背景、业务目标、目标用户群体、市场需求分析、竞品分析等信息，为后续需求提供充分的上下文。",
      priority: "medium",
      confidence: 75,
      rule: "项目背景检查",
      ruleId: "rule-006",
      originalContent: "智能驾驶系统PRD文档，旨在定义自动驾驶功能的需求规格",
      timestamp: "2024-01-15T10:29:00Z"
    },
    {
      id: "issue-006",
      type: "suggestion",
      title: "建议增加验收标准",
      description: "各功能模块缺少明确的验收标准和测试用例。",
      section: "section-3",
      sectionTitle: "3. 产品功能",
      suggestion: "建议为每个功能点添加明确的验收标准，包括功能测试用例、性能测试标准、用户体验评估标准等。",
      priority: "low",
      confidence: 65,
      rule: "验收标准检查",
      ruleId: "rule-007",
      originalContent: "包括但不限于以下几点：自动检测文档结构完整性",
      timestamp: "2024-01-15T10:30:00Z"
    },
    {
      id: "issue-007",
      type: "suggestion",
      title: "建议添加风险评估",
      description: "文档缺少项目风险评估和应对策略。",
      section: "section-1",
      sectionTitle: "1. 项目概述",
      suggestion: "建议添加项目风险评估章节，包括技术风险、进度风险、资源风险等，并提供相应的风险应对策略。",
      priority: "low",
      confidence: 58,
      rule: "风险评估检查",
      ruleId: "rule-008",
      originalContent: "本文档将详细描述系统的功能需求、性能指标",
      timestamp: "2024-01-15T10:31:00Z"
    },
    {
      id: "issue-008",
      type: "suggestion",
      title: "建议完善数据模型",
      description: "技术架构章节缺少详细的数据模型设计。",
      section: "section-5",
      sectionTitle: "5. 技术架构",
      suggestion: "建议补充详细的数据模型设计，包括实体关系图、数据字典、数据流图等，以便开发团队理解数据结构。",
      priority: "low",
      confidence: 70,
      rule: "数据模型检查",
      ruleId: "rule-009",
      originalContent: "数据库使用MongoDB",
      timestamp: "2024-01-15T10:32:00Z"
    }
  ],
  sectionScores: {
    "section-1": 75,
    "section-2": 72,
    "section-3": 68,
    "section-4": 65,
    "section-5": 80
  },
  completenessScore: 75,
  clarityScore: 80,
  consistencyScore: 82,
  feasibilityScore: 78
}

// 模拟文档数据
export const mockDocument = {
  id: "doc-001",
  title: "智能驾驶系统PRD",
  uploadDate: "2024-01-15",
  status: "analyzed",
  progress: 100,
  issues: 8,
  fixed: 0,
  fileSize: "2.5 MB",
  fileType: "application/pdf",
  content: "智能驾驶系统产品需求文档..."
}
```

## 样式配置

### CSS样式 (可选，如果不使用Tailwind)

```css
/* src/components/PRDEvaluation/PRDReviewTab.css */

.prd-review-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: 100%;
}

.prd-review-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding: 1rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
}

.prd-review-layout {
  display: flex;
  gap: 1.5rem;
  height: 700px;
  flex: 1;
}

.document-outline {
  width: 256px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-y: auto;
}

.document-content {
  flex: 1;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.issue-detail-panel {
  width: 320px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
}

.issue-marker {
  border-left: 4px solid;
  padding: 1rem;
  border-radius: 0 0.5rem 0.5rem 0;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 1rem;
}

.issue-marker.error {
  border-left-color: #ef4444;
  background-color: #fef2f2;
}

.issue-marker.warning {
  border-left-color: #f59e0b;
  background-color: #fffbeb;
}

.issue-marker.suggestion {
  border-left-color: #3b82f6;
  background-color: #eff6ff;
}

.issue-marker.selected {
  box-shadow: 0 0 0 2px #fb923c;
}

.issue-marker.reviewed {
  opacity: 0.6;
}

.review-progress {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.progress-actions {
  display: flex;
  gap: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .prd-review-layout {
    flex-direction: column;
    height: auto;
  }

  .document-outline {
    width: 100%;
    height: 200px;
  }

  .issue-detail-panel {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .prd-review-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .review-progress {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}
```

## 实施步骤

### 第一阶段：基础组件开发 (2天)

#### Day 1: 核心Hook和数据结构
1. **创建usePRDReview Hook**
   - 实现状态管理逻辑
   - 添加问题确认处理函数
   - 实现文档章节初始化

2. **准备测试数据**
   - 创建mockAnalysisResults.js
   - 准备完整的测试用例数据
   - 验证数据结构完整性

#### Day 2: 基础UI组件
1. **实现DocumentOutline组件**
   - 文档大纲树形结构
   - 问题数量标记
   - 点击跳转功能

2. **实现IssueMarker组件**
   - 问题标记样式
   - 不同类型问题的视觉区分
   - 选中和已处理状态显示

### 第二阶段：核心功能开发 (3天)

#### Day 3: 文档内容区域
1. **实现DocumentContent组件**
   - 文档内容渲染
   - 问题标记集成
   - 滚动和定位功能

2. **完善IssueMarker交互**
   - 点击选中效果
   - 状态变化动画
   - 响应式布局适配

#### Day 4: 问题详情面板
1. **实现IssueDetailPanel组件**
   - 详细信息展示
   - 操作按钮功能
   - 状态反馈显示

2. **添加交互逻辑**
   - 采纳/拒绝操作
   - 状态同步更新
   - 用户反馈提示

#### Day 5: 进度跟踪和主页面集成
1. **实现ReviewProgress组件**
   - 进度条显示
   - 操作按钮状态管理
   - 完成度计算

2. **集成主页面PRDReviewTab**
   - 三栏布局实现
   - 组件间数据传递
   - 响应式设计适配

### 第三阶段：功能完善和测试 (2天)

#### Day 6: 功能完善
1. **添加高级功能**
   - 键盘快捷键支持
   - 批量操作功能
   - 搜索和筛选

2. **性能优化**
   - 虚拟滚动（如果需要）
   - 组件懒加载
   - 状态更新优化

#### Day 7: 测试和调优
1. **功能测试**
   - 单元测试编写
   - 集成测试验证
   - 用户体验测试

2. **样式和交互优化**
   - 动画效果调整
   - 响应式布局完善
   - 无障碍访问支持

## 验收标准

### 功能完整性验收

#### ✅ 基础功能
- [ ] 页面正确显示三栏布局（大纲+内容+详情）
- [ ] 文档大纲正确显示章节结构和问题数量
- [ ] 文档内容正确渲染并显示问题标记
- [ ] 问题详情面板正确显示选中问题的详细信息
- [ ] 评审进度正确计算和显示

#### ✅ 交互功能
- [ ] 点击大纲章节能正确跳转到对应内容
- [ ] 点击问题标记能在详情面板显示问题信息
- [ ] 采纳/拒绝按钮功能正常，状态正确更新
- [ ] 大纲显示/隐藏切换功能正常
- [ ] 保存草稿和完成评审按钮状态正确

#### ✅ 数据处理
- [ ] 正确解析analysisResults数据
- [ ] 问题状态变化正确同步到所有相关组件
- [ ] 评审结果数据格式正确
- [ ] 进度计算准确无误

#### ✅ 视觉效果
- [ ] 不同类型问题有明确的视觉区分
- [ ] 选中状态有明显的视觉反馈
- [ ] 已处理问题有适当的视觉标识
- [ ] 响应式布局在不同屏幕尺寸下正常显示

### 性能验收

#### ✅ 响应性能
- [ ] 页面初始加载时间 < 2秒
- [ ] 问题切换响应时间 < 200ms
- [ ] 大纲展开/收起动画流畅
- [ ] 滚动性能良好，无卡顿

#### ✅ 内存使用
- [ ] 长时间使用无明显内存泄漏
- [ ] 组件卸载时正确清理事件监听器
- [ ] 大量问题数据处理不影响性能

### 兼容性验收

#### ✅ 浏览器兼容
- [ ] Chrome 90+ 正常运行
- [ ] Firefox 88+ 正常运行
- [ ] Safari 14+ 正常运行
- [ ] Edge 90+ 正常运行

#### ✅ 设备兼容
- [ ] 桌面端 (1920x1080) 显示正常
- [ ] 笔记本 (1366x768) 显示正常
- [ ] 平板横屏 (1024x768) 显示正常
- [ ] 平板竖屏 (768x1024) 显示正常

### 用户体验验收

#### ✅ 易用性
- [ ] 界面布局清晰，信息层次分明
- [ ] 操作流程符合用户习惯
- [ ] 重要操作有明确的确认机制
- [ ] 错误状态有友好的提示信息

#### ✅ 无障碍访问
- [ ] 支持键盘导航
- [ ] 重要元素有适当的ARIA标签
- [ ] 颜色对比度符合WCAG标准
- [ ] 支持屏幕阅读器

## 测试用例

### 基础功能测试

```javascript
// 测试用例示例
describe('PRD评审确认页面', () => {
  test('应该正确显示文档大纲', () => {
    // 测试大纲渲染
  })

  test('应该正确处理问题选中', () => {
    // 测试问题选择逻辑
  })

  test('应该正确更新评审状态', () => {
    // 测试状态更新
  })

  test('应该正确计算评审进度', () => {
    // 测试进度计算
  })
})
```

### 集成测试

```javascript
describe('评审流程集成测试', () => {
  test('完整评审流程', async () => {
    // 1. 加载页面
    // 2. 选择问题
    // 3. 查看详情
    // 4. 做出决定
    // 5. 完成评审
    // 6. 验证结果
  })
})
```

## 部署清单

### 文件创建清单
- [ ] `src/components/PRDEvaluation/PRDReviewTab.js`
- [ ] `src/components/PRDEvaluation/components/DocumentOutline.js`
- [ ] `src/components/PRDEvaluation/components/DocumentContent.js`
- [ ] `src/components/PRDEvaluation/components/IssueDetailPanel.js`
- [ ] `src/components/PRDEvaluation/components/IssueMarker.js`
- [ ] `src/components/PRDEvaluation/components/ReviewProgress.js`
- [ ] `src/hooks/usePRDReview.js`
- [ ] `src/data/mockAnalysisResults.js`
- [ ] `src/components/PRDEvaluation/PRDReviewTab.css` (可选)

### 依赖检查清单
- [ ] React 18+ 已安装
- [ ] UI组件库已配置
- [ ] 图标库已安装
- [ ] 样式系统已配置

### 集成检查清单
- [ ] 主页面正确引入PRDReviewTab组件
- [ ] 数据传递接口正确对接
- [ ] 路由配置正确
- [ ] 样式不冲突

## 总结

本实施方案专门针对PRD智能评审流程中的"人工确认"页面，提供了完整的1:1还原实现方案。通过7天的分阶段开发，可以完全复刻原页面的所有功能和交互效果。

### 核心特点
1. **完整的三栏布局**：文档大纲、内容区域、问题详情
2. **丰富的交互功能**：问题选择、状态管理、进度跟踪
3. **详细的测试数据**：8个不同类型的模拟问题
4. **完善的验收标准**：功能、性能、兼容性全覆盖
5. **分阶段实施**：7天完成，每天有明确的交付目标

通过这个方案，可以在现有项目中快速实现高质量的PRD评审确认页面，确保与原设计100%一致。
