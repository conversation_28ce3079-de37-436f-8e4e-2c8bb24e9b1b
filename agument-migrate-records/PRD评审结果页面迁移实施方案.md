# PRD评审结果页面迁移实施方案

## 📋 项目概览

### 源项目分析
- **源文件**: `test_data/assessment-report-v2.6.html` (单页面HTML实现)
- **技术栈**: 原生HTML + TailwindCSS + Vanilla JavaScript
- **功能特性**: 完整的PRD评审报告展示系统，包含评分概览、章节详情、建议管理等

### 目标项目环境
- **项目路径**: `/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/req-ai-edit-agent`
- **技术栈**: React 18 + TailwindCSS + Node.js Express
- **目标位置**: `src/components/PRDEvaluation/PRDResultsTab.js`
- **集成点**: PRD智能评估流程的结果展示阶段

## 🎯 迁移目标与范围

### 核心功能迁移清单
- ✅ **报告摘要与核心发现** - 评分展示、改进任务列表
- ✅ **章节评审详情** - 可展开的问题详情卡片
- ✅ **改进建议管理** - 建议确认/拒绝状态管理
- ✅ **维度评分分析** - 五维度评分可视化
- ✅ **交互功能** - Tab切换、详情展开、状态切换
- ✅ **响应式设计** - 适配不同屏幕尺寸

### 保持不变的功能
- 现有PRD评估流程的其他Tab页面
- 主框架导航和布局
- 数据流和状态管理机制

## 🏗️ 技术架构设计

### 数据结构分析

#### HTML源数据结构
```javascript
const reportData = {
  problems: {
    "P001": {
      severity: "严重",
      title: "产品架构/系统架构图完全缺失",
      location: "2.2 产品架构/系统架构",
      dimension: "完整性",
      details: "...",
      impact: "...",
      solution: {
        idea: "...",
        specific: ["...", "..."],
        verification: "..."
      },
      expectedState: "...",
      originalExcerpt: "..."
    }
  },
  chapters: [
    { name: "封面", problems: [] },
    { name: "编制/变更日志", problems: ["P009"] }
  ]
}
```

#### 目标数据适配器设计
```javascript
// src/services/reportDataAdapter.js
export const adaptAnalysisResultsToReport = (analysisResults, reviewResults) => {
  return {
    documentInfo: {
      title: analysisResults.documentTitle,
      version: analysisResults.version,
      overallScore: calculateOverallScore(analysisResults),
      reviewResult: determineReviewResult(analysisResults)
    },
    problems: transformProblemsData(analysisResults.issues),
    chapters: transformChaptersData(analysisResults.sections),
    dimensionScores: calculateDimensionScores(analysisResults),
    statistics: generateStatistics(analysisResults, reviewResults)
  }
}
```

### 组件架构设计

#### 主组件结构
```
PRDResultsTab (主容器)
├── ReportHeader (报告头部)
├── StickyOverview (粘性概览卡片)
├── TabNavigation (Tab导航)
├── TabContent (Tab内容容器)
│   ├── HighlightsTab (评审总结)
│   ├── ChapterDetailsTab (章节详情)
│   ├── SuggestionsTab (建议转行动)
│   └── ReviewContextTab (评审上下文)
├── ActionButtons (全局操作按钮)
└── Modals (各种弹窗)
    ├── ExportModal
    ├── ShareModal
    └── OptimizeModal
```

#### 子组件详细设计
```
src/components/PRDEvaluation/ReportComponents/
├── ReportHeader.js           # 报告标题和基本信息
├── StickyOverview.js         # 粘性概览卡片(评分+问题分布)
├── TabNavigation.js          # Tab导航栏
├── HighlightsTab.js          # 评审总结Tab
├── ChapterDetailsTab.js      # 章节详情Tab
├── SuggestionsTab.js         # 建议转行动Tab
├── ReviewContextTab.js       # 评审上下文Tab
├── ProblemDetailCard.js      # 问题详情卡片
├── DimensionScoreCard.js     # 维度评分卡片
├── SeverityBadge.js          # 严重程度标签
├── AdoptionToggle.js         # 采纳状态切换
├── ExportButton.js           # 导出按钮
└── ActionButtons.js          # 全局操作按钮组
```

## 📊 数据流设计

### 数据转换流程
```
现有数据 (analysisResults + reviewResults)
    ↓
数据适配器 (reportDataAdapter.js)
    ↓
标准化报告数据 (reportData)
    ↓
React组件渲染 (PRDResultsTab)
    ↓
用户交互更新 (状态管理)
```

### 状态管理策略
```javascript
// 在PRDResultsTab中管理的状态
const [reportData, setReportData] = useState(null)
const [activeTab, setActiveTab] = useState('highlights')
const [expandedProblems, setExpandedProblems] = useState(new Set())
const [adoptionStatus, setAdoptionStatus] = useState({})
const [showDetailedScores, setShowDetailedScores] = useState(false)
```

## 🎨 样式系统迁移

### CSS类映射策略
- 保持原有TailwindCSS类名
- 迁移自定义CSS到组件级样式
- 确保响应式设计完整性

### 关键样式组件
```css
/* 核心样式类 */
.section-title { /* 章节标题样式 */ }
.card { /* 卡片容器样式 */ }
.severity-red-tag { /* 严重程度标签 */ }
.detail-card { /* 详情卡片样式 */ }
.problem-distribution-bar { /* 问题分布条 */ }
.sticky-header { /* 粘性头部 */ }
```

## 🔄 交互功能实现

### 核心交互列表
1. **Tab切换** - 四个主要Tab页面间的切换
2. **详情展开** - 章节问题详情的展开/收起
3. **状态切换** - 建议采纳/拒绝状态管理
4. **粘性滚动** - 概览卡片的粘性滚动效果
5. **导出功能** - PDF/Word/图片导出
6. **分享功能** - 生成分享链接

### 事件处理设计
```javascript
// 主要事件处理函数
const handleTabSwitch = (tabId) => { /* Tab切换逻辑 */ }
const handleProblemToggle = (problemId) => { /* 问题详情切换 */ }
const handleAdoptionChange = (problemId, status) => { /* 采纳状态变更 */ }
const handleExport = (format) => { /* 导出功能 */ }
const handleShare = (userIds) => { /* 分享功能 */ }
```

## 📅 实施计划

### Phase 1: 数据适配器开发 (1天)
- 分析现有数据结构
- 设计数据转换逻辑
- 实现reportDataAdapter.js

### Phase 2: 核心组件开发 (2天)
- 实现主要子组件
- 完成基础布局和样式
- 实现Tab切换功能

### Phase 3: 交互功能实现 (2天)
- 实现详情展开/收起
- 实现状态管理
- 实现粘性滚动效果

### Phase 4: 高级功能开发 (1天)
- 实现导出功能
- 实现分享功能
- 完善响应式设计

### Phase 5: 集成与测试 (1天)
- 替换现有PRDResultsTab
- 集成测试
- 功能验证

## 🔧 技术实施细节

### 关键文件修改清单
1. **完全重写**: `src/components/PRDEvaluation/PRDResultsTab.js`
2. **新增文件**: `src/services/reportDataAdapter.js`
3. **新增目录**: `src/components/PRDEvaluation/ReportComponents/`
4. **扩展Hook**: `src/hooks/usePRDEvaluation.js` (添加报告相关状态)

### 依赖管理
- 无需新增外部依赖
- 充分利用现有TailwindCSS
- 使用React内置Hooks进行状态管理

## ✅ 验收标准

### 功能完整性
- [ ] 所有Tab页面正常显示和切换
- [ ] 问题详情卡片展开/收起正常
- [ ] 采纳状态切换功能正常
- [ ] 粘性滚动效果正常
- [ ] 导出和分享功能正常

### 视觉还原度
- [ ] 整体布局与HTML版本一致
- [ ] 颜色和字体样式一致
- [ ] 响应式效果一致
- [ ] 动画效果流畅

### 集成兼容性
- [ ] 与现有PRD评估流程无缝集成
- [ ] 数据传递正确无误
- [ ] 不影响其他Tab页面功能
- [ ] 性能表现良好

## 🚀 下一步行动

1. **立即开始**: 数据结构分析和适配器设计
2. **并行开发**: 核心组件架构搭建
3. **迭代完善**: 交互功能和样式细节
4. **全面测试**: 功能完整性和集成效果验证

---

*本方案确保了HTML评审报告页面的完整功能迁移，同时保持与现有React项目架构的良好集成。*
