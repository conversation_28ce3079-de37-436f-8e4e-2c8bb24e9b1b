# PRD智能评审完整实施方案

## 概述

本方案将完整实现PRD智能评审功能，包含5个核心页面的完整流程：文档上传 → 智能分析 → 评审确认 → 评审结果 → 自检优化。每个步骤都可以独立实施，确保在另一个项目中能够完全复刻当前功能。

---

## 第1步：主菜单导航与页面框架

### 1.1 导航按钮实现

**目标文件：** 主页面导航组件

**实现内容：**
```tsx
// 在左侧导航菜单中添加PRD智能评审入口
<button
  onClick={() => setCurrentPage("prd-review")}
  className={`w-full flex items-center px-3 py-2 text-sm rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 ${
    currentPage === "prd-review" ? "bg-orange-100 text-orange-700" : "text-gray-700 hover:bg-gray-100"
  }`}
>
  <CheckCircle className="w-4 h-4 mr-3" />
  <span>PRD智能评审</span>
</button>
```

### 1.2 页面框架结构

**目标文件：** `components/prd-review-page.tsx`

**页面结构：**
```javascript
// src/components/PRDEvaluation/index.js
import React, { useState } from 'react'
import { Button } from '../ui/button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { ChevronLeft } from 'lucide-react'
import PRDUploadTab from './PRDUploadTab'
import PRDAnalysisTab from './PRDAnalysisTab'
import PRDReviewTab from './PRDReviewTab'
import PRDResultsTab from './PRDResultsTab'
import PRDOptimizationTab from './PRDOptimizationTab'
import { usePRDEvaluation } from '../../hooks/usePRDEvaluation'

const PRDEvaluationPage = ({ onBack }) => {
  const {
    activeTab,
    setActiveTab,
    hasStartedAnalysis,
    hasCompletedAnalysis,
    hasStartedReview,
    hasCompletedReview,
    currentDocument,
    analysisResults,
    reviewResults
  } = usePRDEvaluation()

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">PRD智能评审</h1>
          <p className="text-gray-600 mt-1">智能化PRD文档评审与优化平台</p>
        </div>
        {onBack && (
          <Button variant="outline" onClick={onBack}>
            <ChevronLeft className="w-4 h-4 mr-2" />
            返回
          </Button>
        )}
      </div>

      {/* 进度指示器 */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {[1, 2, 3, 4, 5].map((step, index) => (
              <React.Fragment key={step}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  activeTab === ['upload', 'analysis', 'review', 'results', 'optimization'][index]
                    ? "bg-orange-500 text-white"
                    : [hasStartedAnalysis, hasCompletedAnalysis, hasStartedReview, hasCompletedReview, false][index]
                      ? "bg-green-500 text-white"
                      : "bg-gray-200 text-gray-500"
                }`}>
                  {step}
                </div>
                {index < 4 && (
                  <div className={`w-16 h-1 ${
                    [hasStartedAnalysis, hasCompletedAnalysis, hasStartedReview, hasCompletedReview][index]
                      ? "bg-green-500"
                      : "bg-gray-200"
                  }`}></div>
                )}
              </React.Fragment>
            ))}
          </div>
          <div className="text-sm text-gray-600">
            {currentDocument ? `当前文档: ${currentDocument.title}` : "请上传PRD文档"}
          </div>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="upload">文档上传</TabsTrigger>
          <TabsTrigger value="analysis" disabled={!hasStartedAnalysis}>智能分析</TabsTrigger>
          <TabsTrigger value="review" disabled={!hasCompletedAnalysis}>评审确认</TabsTrigger>
          <TabsTrigger value="results" disabled={!hasStartedReview}>评审结果</TabsTrigger>
          <TabsTrigger value="optimization" disabled={!hasCompletedReview}>自检优化</TabsTrigger>
        </TabsList>

        <TabsContent value="upload">
          <PRDUploadTab />
        </TabsContent>

        <TabsContent value="analysis">
          {currentDocument && <PRDAnalysisTab document={currentDocument} />}
        </TabsContent>

        <TabsContent value="review">
          {currentDocument && analysisResults && (
            <PRDReviewTab
              document={currentDocument}
              analysisResults={analysisResults}
            />
          )}
        </TabsContent>

        <TabsContent value="results">
          {currentDocument && analysisResults && reviewResults && (
            <PRDResultsTab
              document={currentDocument}
              analysisResults={analysisResults}
              reviewResults={reviewResults}
            />
          )}
        </TabsContent>

        <TabsContent value="optimization">
          {currentDocument && reviewResults && analysisResults && (
            <PRDOptimizationTab
              document={currentDocument}
              reviewResults={reviewResults}
              analysisResults={analysisResults}
            />
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default PRDEvaluationPage
```

### 1.3 自定义Hooks实现

**目标文件：** `src/hooks/usePRDEvaluation.js`

```javascript
// src/hooks/usePRDEvaluation.js
import { useState, useCallback } from 'react'
import { PRDReviewStateSchema } from '../types/prdEvaluation'

export const usePRDEvaluation = () => {
  // 页面状态管理
  const [activeTab, setActiveTab] = useState("upload")
  const [hasStartedAnalysis, setHasStartedAnalysis] = useState(false)
  const [hasCompletedAnalysis, setHasCompletedAnalysis] = useState(false)
  const [hasStartedReview, setHasStartedReview] = useState(false)
  const [hasCompletedReview, setHasCompletedReview] = useState(false)

  // 数据状态管理
  const [uploadedDocuments, setUploadedDocuments] = useState([])
  const [currentDocument, setCurrentDocument] = useState(null)
  const [analysisResults, setAnalysisResults] = useState(null)
  const [reviewResults, setReviewResults] = useState(null)
  const [optimizationData, setOptimizationData] = useState(null)

  // 流程控制函数
  const handleStartAnalysis = useCallback((document) => {
    setCurrentDocument(document)
    setHasStartedAnalysis(true)
    setActiveTab("analysis")

    // 更新文档状态
    setUploadedDocuments(prev =>
      prev.map(doc =>
        doc.id === document.id
          ? { ...doc, status: "analyzing", progress: 0 }
          : doc
      )
    )
  }, [])

  const handleAnalysisComplete = useCallback((results) => {
    setAnalysisResults(results)
    setHasCompletedAnalysis(true)

    // 更新文档状态
    if (currentDocument) {
      setUploadedDocuments(prev =>
        prev.map(doc =>
          doc.id === currentDocument.id
            ? {
                ...doc,
                status: "analyzed",
                progress: 100,
                issues: results.totalIssues
              }
            : doc
        )
      )
    }
  }, [currentDocument])

  const handleEnterReview = useCallback(() => {
    setHasStartedReview(true)
    setActiveTab("review")

    // 更新文档状态
    if (currentDocument) {
      setUploadedDocuments(prev =>
        prev.map(doc =>
          doc.id === currentDocument.id
            ? { ...doc, status: "reviewing" }
            : doc
        )
      )
    }
  }, [currentDocument])

  const handleReviewComplete = useCallback((results) => {
    setReviewResults(results)
    setHasCompletedReview(true)
    setActiveTab("results")

    // 更新文档状态
    if (currentDocument) {
      setUploadedDocuments(prev =>
        prev.map(doc =>
          doc.id === currentDocument.id
            ? {
                ...doc,
                status: "completed",
                progress: 100,
                fixed: results.acceptedSuggestions
              }
            : doc
        )
      )
    }
  }, [currentDocument])

  const handleStartOptimization = useCallback(() => {
    setActiveTab("optimization")
  }, [])

  return {
    // 状态
    activeTab,
    hasStartedAnalysis,
    hasCompletedAnalysis,
    hasStartedReview,
    hasCompletedReview,
    uploadedDocuments,
    currentDocument,
    analysisResults,
    reviewResults,
    optimizationData,

    // 操作函数
    setActiveTab,
    setUploadedDocuments,
    handleStartAnalysis,
    handleAnalysisComplete,
    handleEnterReview,
    handleReviewComplete,
    handleStartOptimization
  }
}
```

**数据模型：**
```javascript
// src/types/prdEvaluation.js

/**
 * PRD文档数据模型
 */
export const PRDDocumentSchema = {
  id: '',
  title: '',
  uploadDate: '',
  status: 'uploaded', // "uploaded" | "analyzing" | "analyzed" | "reviewing" | "completed"
  progress: 0,
  issues: 0,
  fixed: 0,
  fileSize: '',
  fileType: '',
  content: ''
}

/**
 * PRD评审状态数据模型
 */
export const PRDReviewStateSchema = {
  currentDocument: null,
  analysisResults: null,
  reviewIssues: [],
  reviewResults: null,
  optimizationData: null
}

/**
 * 评审问题数据模型
 */
export const ReviewIssueSchema = {
  id: '',
  type: 'error', // "error" | "warning" | "suggestion"
  title: '',
  description: '',
  section: '',
  sectionTitle: '',
  suggestion: '',
  accepted: false,
  originalContent: '',
  modifiedContent: '',
  rule: '',
  ruleId: '',
  confidence: 0,
  aiRecommendation: '',
  humanFeedback: '',
  timestamp: '',
  priority: 'medium' // "high" | "medium" | "low"
}
```

---

## 第2步：文档上传页面实现

### 2.1 上传页面组件

**目标文件：** `src/components/PRDEvaluation/PRDUploadTab.js`

**核心功能：**
1. 文件拖拽上传
2. 文件格式验证（PDF、Word、Markdown）
3. 上传进度显示
4. 已上传文档列表
5. 文档预览功能

**实现内容：**
```javascript
// src/components/PRDEvaluation/PRDUploadTab.js
import React, { useState } from 'react'
import { Button } from '../ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table'
import { Upload, Eye, Download } from 'lucide-react'
import { usePRDEvaluation } from '../../hooks/usePRDEvaluation'
import { usePRDUpload } from '../../hooks/usePRDUpload'
import DocumentUploader from './components/DocumentUploader'

const PRDUploadTab = () => {
  const { uploadedDocuments, handleStartAnalysis } = usePRDEvaluation()
  const {
    isDragging,
    uploadProgress,
    isUploading,
    handleFileUpload,
    validateFileType,
    formatFileSize,
    getStatusVariant,
    getStatusText
  } = usePRDUpload()

  return (
    <div className="space-y-6">
      {/* 文件上传区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Upload className="w-5 h-5" />
            <span>文档上传</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <DocumentUploader
            isDragging={isDragging}
            setIsDragging={setIsDragging}
            isUploading={isUploading}
            uploadProgress={uploadProgress}
            onFileUpload={handleFileUpload}
          />
        </CardContent>
      </Card>

      {/* 已上传文档列表 */}
      <Card>
        <CardHeader>
          <CardTitle>已上传文档</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>文档名称</TableHead>
                  <TableHead>上传日期</TableHead>
                  <TableHead>文件大小</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>进度</TableHead>
                  <TableHead>问题数</TableHead>
                  <TableHead>已修复</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {uploadedDocuments.map((doc) => (
                  <TableRow key={doc.id}>
                    <TableCell className="font-medium">{doc.title}</TableCell>
                    <TableCell>{doc.uploadDate}</TableCell>
                    <TableCell>{doc.fileSize}</TableCell>
                    <TableCell>
                      <Badge variant={getStatusVariant(doc.status)}>
                        {getStatusText(doc.status)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="w-24">
                        <Progress value={doc.progress} className="h-2" />
                      </div>
                    </TableCell>
                    <TableCell>{doc.issues}</TableCell>
                    <TableCell>{doc.fixed}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        {doc.status === "uploaded" && (
                          <Button
                            size="sm"
                            onClick={() => handleStartAnalysis(doc)}
                            className="bg-orange-500 hover:bg-orange-600"
                          >
                            开始分析
                          </Button>
                        )}
                        <Button variant="ghost" size="sm">
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default PRDUploadTab

  // 文件上传处理
  const handleFileUpload = async (files: FileList) => {
    setIsUploading(true)
    setUploadProgress(0)

    for (const file of Array.from(files)) {
      // 验证文件格式
      if (!validateFileType(file)) {
        toast.error(`不支持的文件格式: ${file.name}`)
        continue
      }

      // 模拟上传进度
      for (let i = 0; i <= 100; i += 10) {
        setUploadProgress(i)
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      // 创建文档记录
      const newDocument: PRDDocument = {
        id: Date.now().toString(),
        title: file.name.replace(/\.[^/.]+$/, ""),
        uploadDate: new Date().toLocaleDateString(),
        status: "uploaded",
        progress: 0,
        issues: 0,
        fixed: 0,
        fileSize: formatFileSize(file.size),
        fileType: file.type,
        content: await readFileContent(file)
      }

      setUploadedDocuments(prev => [...prev, newDocument])
    }

    setIsUploading(false)
    setUploadProgress(0)
  }

  // 开始分析处理
  const handleStartAnalysis = (documentId: string) => {
    const document = uploadedDocuments.find(doc => doc.id === documentId)
    if (document) {
      onStartAnalysis(document)
    }
  }

  return (
    <div className="space-y-6">
      {/* 文件上传区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Upload className="w-5 h-5" />
            <span>文档上传</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div 
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              isDragging ? 'border-orange-400 bg-orange-50' : 'border-gray-300'
            }`}
            onDragOver={(e) => {
              e.preventDefault()
              setIsDragging(true)
            }}
            onDragLeave={() => setIsDragging(false)}
            onDrop={(e) => {
              e.preventDefault()
              setIsDragging(false)
              handleFileUpload(e.dataTransfer.files)
            }}
          >
            <Upload className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <p className="text-lg font-medium text-gray-900 mb-2">上传PRD文档</p>
            <p className="text-sm text-gray-500 mb-4">支持PDF、Word、Markdown格式，最大50MB</p>
            
            {isUploading ? (
              <div className="space-y-2">
                <Progress value={uploadProgress} className="w-64 mx-auto" />
                <p className="text-sm text-gray-600">上传中... {uploadProgress}%</p>
              </div>
            ) : (
              <div className="flex justify-center space-x-3">
                <Button 
                  className="bg-orange-500 hover:bg-orange-600"
                  onClick={() => document.getElementById('file-input')?.click()}
                >
                  选择文件
                </Button>
                <Button variant="outline">
                  拖拽文件到此处
                </Button>
              </div>
            )}
            
            <input
              id="file-input"
              type="file"
              multiple
              accept=".pdf,.doc,.docx,.md,.txt"
              className="hidden"
              onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
            />
          </div>
        </CardContent>
      </Card>

      {/* 已上传文档列表 */}
      <Card>
        <CardHeader>
          <CardTitle>已上传文档</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>文档名称</TableHead>
                  <TableHead>上传日期</TableHead>
                  <TableHead>文件大小</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>进度</TableHead>
                  <TableHead>问题数</TableHead>
                  <TableHead>已修复</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {uploadedDocuments.map((doc) => (
                  <TableRow key={doc.id}>
                    <TableCell className="font-medium">{doc.title}</TableCell>
                    <TableCell>{doc.uploadDate}</TableCell>
                    <TableCell>{doc.fileSize}</TableCell>
                    <TableCell>
                      <Badge variant={getStatusVariant(doc.status)}>
                        {getStatusText(doc.status)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="w-24">
                        <Progress value={doc.progress} className="h-2" />
                      </div>
                    </TableCell>
                    <TableCell>{doc.issues}</TableCell>
                    <TableCell>{doc.fixed}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        {doc.status === "uploaded" && (
                          <Button
                            size="sm"
                            onClick={() => handleStartAnalysis(doc.id)}
                            className="bg-orange-500 hover:bg-orange-600"
                          >
                            开始分析
                          </Button>
                        )}
                        <Button variant="ghost" size="sm">
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
```

### 2.2 文档上传Hook

**目标文件：** `src/hooks/usePRDUpload.js`

```javascript
// src/hooks/usePRDUpload.js
import { useState, useCallback } from 'react'
import { PRDDocumentSchema } from '../types/prdEvaluation'
import { usePRDEvaluation } from './usePRDEvaluation'

export const usePRDUpload = () => {
  const { setUploadedDocuments } = usePRDEvaluation()
  const [isDragging, setIsDragging] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isUploading, setIsUploading] = useState(false)

  // 文件类型验证
  const validateFileType = useCallback((file) => {
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/markdown',
      'text/plain'
    ]
    return allowedTypes.includes(file.type)
  }, [])

  // 文件大小格式化
  const formatFileSize = useCallback((bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }, [])

  // 读取文件内容
  const readFileContent = useCallback(async (file) => {
    return new Promise((resolve) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result || '')
      reader.readAsText(file)
    })
  }, [])

  // 文件上传处理
  const handleFileUpload = useCallback(async (files) => {
    setIsUploading(true)
    setUploadProgress(0)

    for (const file of Array.from(files)) {
      // 验证文件格式
      if (!validateFileType(file)) {
        console.error(`不支持的文件格式: ${file.name}`)
        continue
      }

      // 模拟上传进度
      for (let i = 0; i <= 100; i += 10) {
        setUploadProgress(i)
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      // 创建文档记录
      const newDocument = {
        ...PRDDocumentSchema,
        id: Date.now().toString(),
        title: file.name.replace(/\.[^/.]+$/, ""),
        uploadDate: new Date().toLocaleDateString(),
        status: "uploaded",
        progress: 0,
        issues: 0,
        fixed: 0,
        fileSize: formatFileSize(file.size),
        fileType: file.type,
        content: await readFileContent(file)
      }

      setUploadedDocuments(prev => [...prev, newDocument])
    }

    setIsUploading(false)
    setUploadProgress(0)
  }, [validateFileType, formatFileSize, readFileContent, setUploadedDocuments])

  // 状态样式映射
  const getStatusVariant = useCallback((status) => {
    switch (status) {
      case "completed": return "default"
      case "reviewing": return "secondary"
      case "analyzing": return "secondary"
      case "analyzed": return "outline"
      default: return "outline"
    }
  }, [])

  const getStatusText = useCallback((status) => {
    switch (status) {
      case "uploaded": return "已上传"
      case "analyzing": return "分析中"
      case "analyzed": return "已分析"
      case "reviewing": return "评审中"
      case "completed": return "已完成"
      default: return "未知"
    }
  }, [])

  return {
    isDragging,
    setIsDragging,
    uploadProgress,
    isUploading,
    handleFileUpload,
    validateFileType,
    formatFileSize,
    getStatusVariant,
    getStatusText
  }
}
```

### 2.3 文档上传器组件

**目标文件：** `src/components/PRDEvaluation/components/DocumentUploader.js`

```javascript
// src/components/PRDEvaluation/components/DocumentUploader.js
import React from 'react'
import { Button } from '../../ui/button'
import { Progress } from '../../ui/progress'
import { Upload } from 'lucide-react'

const DocumentUploader = ({
  isDragging,
  setIsDragging,
  isUploading,
  uploadProgress,
  onFileUpload
}) => {
  return (
    <div
      className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
        isDragging ? 'border-orange-400 bg-orange-50' : 'border-gray-300'
      }`}
      onDragOver={(e) => {
        e.preventDefault()
        setIsDragging(true)
      }}
      onDragLeave={() => setIsDragging(false)}
      onDrop={(e) => {
        e.preventDefault()
        setIsDragging(false)
        onFileUpload(e.dataTransfer.files)
      }}
    >
      <Upload className="w-12 h-12 mx-auto text-gray-400 mb-4" />
      <p className="text-lg font-medium text-gray-900 mb-2">上传PRD文档</p>
      <p className="text-sm text-gray-500 mb-4">支持PDF、Word、Markdown格式，最大50MB</p>

      {isUploading ? (
        <div className="space-y-2">
          <Progress value={uploadProgress} className="w-64 mx-auto" />
          <p className="text-sm text-gray-600">上传中... {uploadProgress}%</p>
        </div>
      ) : (
        <div className="flex justify-center space-x-3">
          <Button
            className="bg-orange-500 hover:bg-orange-600"
            onClick={() => document.getElementById('file-input')?.click()}
          >
            选择文件
          </Button>
          <Button variant="outline">
            拖拽文件到此处
          </Button>
        </div>
      )}

      <input
        id="file-input"
        type="file"
        multiple
        accept=".pdf,.doc,.docx,.md,.txt"
        className="hidden"
        onChange={(e) => e.target.files && onFileUpload(e.target.files)}
      />
    </div>
  )
}

export default DocumentUploader
```

### 2.4 API服务实现

**目标文件：** `src/services/prdEvaluationAPI.js`

```javascript
// src/services/prdEvaluationAPI.js

/**
 * PRD评估API服务
 */
class PRDEvaluationAPI {
  constructor() {
    this.baseURL = process.env.REACT_APP_API_BASE_URL || '/api'
  }

  /**
   * 上传PRD文档
   */
  async uploadDocument(file, onProgress) {
    const formData = new FormData()
    formData.append('file', file)

    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest()

      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable && onProgress) {
          const progress = (e.loaded / e.total) * 100
          onProgress(progress)
        }
      })

      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          resolve(JSON.parse(xhr.responseText))
        } else {
          reject(new Error(`Upload failed: ${xhr.statusText}`))
        }
      })

      xhr.addEventListener('error', () => {
        reject(new Error('Upload failed'))
      })

      xhr.open('POST', `${this.baseURL}/documents/upload`)
      xhr.send(formData)
    })
  }

  /**
   * 开始文档分析
   */
  async startAnalysis(documentId) {
    const response = await fetch(`${this.baseURL}/documents/${documentId}/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })

    if (!response.ok) {
      throw new Error(`Analysis failed: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * 获取分析进度
   */
  async getAnalysisProgress(documentId) {
    const response = await fetch(`${this.baseURL}/documents/${documentId}/analysis/progress`)

    if (!response.ok) {
      throw new Error(`Failed to get progress: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * 获取分析结果
   */
  async getAnalysisResults(documentId) {
    const response = await fetch(`${this.baseURL}/documents/${documentId}/analysis/results`)

    if (!response.ok) {
      throw new Error(`Failed to get results: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * 提交评审结果
   */
  async submitReviewResults(documentId, reviewData) {
    const response = await fetch(`${this.baseURL}/documents/${documentId}/review`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(reviewData)
    })

    if (!response.ok) {
      throw new Error(`Review submission failed: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * 生成优化建议
   */
  async generateOptimizationSuggestions(documentId, reviewResults) {
    const response = await fetch(`${this.baseURL}/documents/${documentId}/optimize`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(reviewResults)
    })

    if (!response.ok) {
      throw new Error(`Optimization failed: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * 导出评审报告
   */
  async exportReport(documentId, format = 'pdf') {
    const response = await fetch(`${this.baseURL}/documents/${documentId}/export?format=${format}`)

    if (!response.ok) {
      throw new Error(`Export failed: ${response.statusText}`)
    }

    return response.blob()
  }
}

export default new PRDEvaluationAPI()
```

### 2.5 工具函数实现

**目标文件：** `src/utils/prdEvaluationHelpers.js`

```javascript
// src/utils/prdEvaluationHelpers.js

/**
 * 文件验证工具
 */
export const fileValidation = {
  /**
   * 验证文件类型
   */
  validateFileType: (file) => {
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/markdown',
      'text/plain'
    ]
    return allowedTypes.includes(file.type)
  },

  /**
   * 验证文件大小
   */
  validateFileSize: (file, maxSizeMB = 50) => {
    const maxSizeBytes = maxSizeMB * 1024 * 1024
    return file.size <= maxSizeBytes
  },

  /**
   * 格式化文件大小
   */
  formatFileSize: (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}

/**
 * 状态管理工具
 */
export const statusHelpers = {
  /**
   * 获取状态显示文本
   */
  getStatusText: (status) => {
    const statusMap = {
      'uploaded': '已上传',
      'analyzing': '分析中',
      'analyzed': '已分析',
      'reviewing': '评审中',
      'completed': '已完成'
    }
    return statusMap[status] || '未知'
  },

  /**
   * 获取状态样式变体
   */
  getStatusVariant: (status) => {
    const variantMap = {
      'completed': 'default',
      'reviewing': 'secondary',
      'analyzing': 'secondary',
      'analyzed': 'outline',
      'uploaded': 'outline'
    }
    return variantMap[status] || 'outline'
  },

  /**
   * 获取优先级颜色
   */
  getPriorityColor: (priority) => {
    const colorMap = {
      'high': 'bg-red-100 text-red-800',
      'medium': 'bg-yellow-100 text-yellow-800',
      'low': 'bg-blue-100 text-blue-800'
    }
    return colorMap[priority] || 'bg-gray-100 text-gray-800'
  }
}

/**
 * 数据处理工具
 */
export const dataHelpers = {
  /**
   * 生成唯一ID
   */
  generateId: () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  },

  /**
   * 深拷贝对象
   */
  deepClone: (obj) => {
    return JSON.parse(JSON.stringify(obj))
  },

  /**
   * 计算评审统计
   */
  calculateReviewStats: (issues, reviewResults) => {
    const acceptedIssues = issues.filter(issue =>
      reviewResults.acceptedSuggestionIds.includes(issue.id)
    )
    const rejectedIssues = issues.filter(issue =>
      reviewResults.reviewedIssueIds.includes(issue.id) &&
      !reviewResults.acceptedSuggestionIds.includes(issue.id)
    )

    return {
      totalIssues: issues.length,
      acceptedCount: acceptedIssues.length,
      rejectedCount: rejectedIssues.length,
      acceptanceRate: Math.round((acceptedIssues.length / issues.length) * 100),
      criticalIssues: acceptedIssues.filter(issue => issue.type === 'error').length,
      acceptedByType: {
        error: acceptedIssues.filter(issue => issue.type === 'error').length,
        warning: acceptedIssues.filter(issue => issue.type === 'warning').length,
        suggestion: acceptedIssues.filter(issue => issue.type === 'suggestion').length
      }
    }
  }
}

/**
 * 时间格式化工具
 */
export const timeHelpers = {
  /**
   * 格式化时间戳
   */
  formatTimestamp: (timestamp) => {
    return new Date(timestamp).toLocaleString('zh-CN')
  },

  /**
   * 获取相对时间
   */
  getRelativeTime: (timestamp) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diff = now - time

    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)

    if (minutes < 1) return '刚刚'
    if (minutes < 60) return `${minutes}分钟前`
    if (hours < 24) return `${hours}小时前`
    return `${days}天前`
  }
}

/**
 * 导出工具
 */
export const exportHelpers = {
  /**
   * 下载文件
   */
  downloadFile: (blob, filename) => {
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
  },

  /**
   * 导出JSON数据
   */
  exportToJSON: (data, filename) => {
    const jsonStr = JSON.stringify(data, null, 2)
    const blob = new Blob([jsonStr], { type: 'application/json' })
    exportHelpers.downloadFile(blob, filename)
  }
}
```
```

---

## 第3步：智能分析页面实现

### 3.1 分析页面组件

**目标文件：** `components/prd-analysis-tab.tsx`

**核心功能：**
1. 分析进度显示
2. 实时分析状态更新
3. 分析结果预览
4. 问题统计展示

**实现内容：**
```tsx
export default function PRDAnalysisTab({
  document,
  onAnalysisComplete
}: PRDAnalysisTabProps) {
  const [analysisProgress, setAnalysisProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState("")
  const [analysisResults, setAnalysisResults] = useState<AnalysisResult | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)

  // 分析步骤定义
  const analysisSteps = [
    { id: 1, name: "文档解析", description: "解析文档结构和内容", duration: 2000 },
    { id: 2, name: "内容分析", description: "分析需求完整性和逻辑性", duration: 3000 },
    { id: 3, name: "规则匹配", description: "应用评审规则进行问题检测", duration: 4000 },
    { id: 4, name: "问题分类", description: "对发现的问题进行分类和优先级排序", duration: 2000 },
    { id: 5, name: "建议生成", description: "生成修改建议和优化方案", duration: 3000 },
    { id: 6, name: "报告生成", description: "生成分析报告和评审结果", duration: 1000 }
  ]

  // 开始分析
  useEffect(() => {
    if (document && !isAnalyzing) {
      startAnalysis()
    }
  }, [document])

  const startAnalysis = async () => {
    setIsAnalyzing(true)
    setAnalysisProgress(0)

    let totalProgress = 0
    const stepProgress = 100 / analysisSteps.length

    for (const step of analysisSteps) {
      setCurrentStep(step.name)

      // 模拟分析过程
      const stepDuration = step.duration
      const updateInterval = stepDuration / 20

      for (let i = 0; i < 20; i++) {
        await new Promise(resolve => setTimeout(resolve, updateInterval))
        const currentStepProgress = (i + 1) / 20 * stepProgress
        setAnalysisProgress(totalProgress + currentStepProgress)
      }

      totalProgress += stepProgress
    }

    // 生成分析结果
    const results = generateAnalysisResults(document)
    setAnalysisResults(results)
    setIsAnalyzing(false)
    setCurrentStep("分析完成")

    // 通知父组件分析完成
    onAnalysisComplete(results)
  }

  // 生成模拟分析结果
  const generateAnalysisResults = (doc: PRDDocument): AnalysisResult => {
    const issues: ReviewIssue[] = [
      {
        id: "issue-1",
        type: "error",
        title: "缺少关键功能描述",
        description: "产品功能模块缺少详细的功能描述和用户场景",
        section: "section-3",
        sectionTitle: "3. 产品功能",
        suggestion: "建议补充详细的功能描述，包括用户操作流程和预期结果",
        priority: "high",
        confidence: 92,
        rule: "功能完整性检查",
        ruleId: "rule-001"
      },
      {
        id: "issue-2",
        type: "warning",
        title: "用户场景描述不够具体",
        description: "用户场景描述过于简略，缺少极端情况和边缘案例的考虑",
        section: "section-2",
        sectionTitle: "2. 用户需求",
        suggestion: "建议增加更多具体的用户场景，包括异常情况的处理",
        priority: "medium",
        confidence: 78,
        rule: "场景完整性检查",
        ruleId: "rule-002"
      },
      {
        id: "issue-3",
        type: "suggestion",
        title: "建议增加性能指标",
        description: "缺少明确的性能指标和质量标准",
        section: "section-4",
        sectionTitle: "4. 非功能需求",
        suggestion: "建议添加具体的性能指标，如响应时间、并发用户数等",
        priority: "low",
        confidence: 65,
        rule: "质量标准检查",
        ruleId: "rule-003"
      }
    ]

    return {
      documentId: doc.id,
      analysisTimestamp: new Date().toISOString(),
      overallScore: 78,
      totalIssues: issues.length,
      errorCount: issues.filter(i => i.type === "error").length,
      warningCount: issues.filter(i => i.type === "warning").length,
      suggestionCount: issues.filter(i => i.type === "suggestion").length,
      issues,
      sectionScores: {
        "section-1": 85,
        "section-2": 72,
        "section-3": 68,
        "section-4": 80,
        "section-5": 90
      },
      completenessScore: 75,
      clarityScore: 80,
      consistencyScore: 82,
      feasibilityScore: 78
    }
  }

  return (
    <div className="space-y-6">
      {/* 分析进度 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Brain className="w-5 h-5" />
            <span>智能分析进行中</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">分析进度</span>
            <span className="text-sm text-gray-500">{Math.round(analysisProgress)}%</span>
          </div>
          <Progress value={analysisProgress} className="h-3" />
          <div className="flex items-center space-x-2">
            {isAnalyzing ? (
              <div className="w-4 h-4 border-2 border-orange-500 border-t-transparent rounded-full animate-spin" />
            ) : (
              <CheckCircle className="w-4 h-4 text-green-500" />
            )}
            <span className="text-sm text-gray-600">{currentStep}</span>
          </div>
        </CardContent>
      </Card>

      {/* 分析步骤 */}
      <Card>
        <CardHeader>
          <CardTitle>分析步骤</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analysisSteps.map((step, index) => {
              const isCompleted = analysisProgress >= (index + 1) * (100 / analysisSteps.length)
              const isCurrent = currentStep === step.name && isAnalyzing

              return (
                <div key={step.id} className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    isCompleted ? 'bg-green-500 text-white' :
                    isCurrent ? 'bg-orange-500 text-white' :
                    'bg-gray-200 text-gray-500'
                  }`}>
                    {isCompleted ? (
                      <CheckCircle className="w-4 h-4" />
                    ) : isCurrent ? (
                      <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <span className="text-sm font-medium">{step.id}</span>
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{step.name}</div>
                    <div className="text-sm text-gray-500">{step.description}</div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* 分析结果预览 */}
      {analysisResults && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>分析结果预览</span>
              <Badge className="bg-green-100 text-green-800">
                分析完成
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 总体评分 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {analysisResults.overallScore}
                </div>
                <div className="text-sm text-gray-600">总体评分</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {analysisResults.errorCount}
                </div>
                <div className="text-sm text-gray-600">错误</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {analysisResults.warningCount}
                </div>
                <div className="text-sm text-gray-600">警告</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {analysisResults.suggestionCount}
                </div>
                <div className="text-sm text-gray-600">建议</div>
              </div>
            </div>

            {/* 主要问题 */}
            <div>
              <h4 className="font-medium mb-2">主要问题</h4>
              <div className="space-y-2">
                {analysisResults.issues.slice(0, 3).map((issue) => (
                  <div key={issue.id} className="flex items-center space-x-2 p-2 bg-gray-50 rounded">
                    <div className={`w-2 h-2 rounded-full ${
                      issue.type === 'error' ? 'bg-red-500' :
                      issue.type === 'warning' ? 'bg-yellow-500' :
                      'bg-blue-500'
                    }`} />
                    <span className="text-sm">{issue.title}</span>
                    <Badge variant="outline" className="text-xs">
                      {issue.sectionTitle}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-end space-x-2">
              <Button variant="outline">
                查看详细报告
              </Button>
              <Button
                className="bg-orange-500 hover:bg-orange-600"
                onClick={() => onAnalysisComplete(analysisResults)}
              >
                进入人工确认
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
```

### 3.2 数据模型

```tsx
interface AnalysisResult {
  documentId: string
  analysisTimestamp: string
  overallScore: number
  totalIssues: number
  errorCount: number
  warningCount: number
  suggestionCount: number
  issues: ReviewIssue[]
  sectionScores: { [sectionId: string]: number }
  completenessScore: number
  clarityScore: number
  consistencyScore: number
  feasibilityScore: number
}
```

---

## 第4步：评审确认页面实现

### 4.1 评审确认页面组件

**目标文件：** `components/prd-review-tab.tsx`

**核心功能：**
1. 文档大纲导航
2. 问题标记和查看
3. 修改建议确认
4. 问题状态管理

**实现内容：**
```tsx
export default function PRDReviewTab({
  document,
  analysisResults,
  onReviewComplete
}: PRDReviewTabProps) {
  const [outlineExpanded, setOutlineExpanded] = useState(true)
  const [selectedIssue, setSelectedIssue] = useState<ReviewIssue | null>(null)
  const [reviewedIssues, setReviewedIssues] = useState<Set<string>>(new Set())
  const [acceptedSuggestions, setAcceptedSuggestions] = useState<Set<string>>(new Set())
  const [documentSections, setDocumentSections] = useState<DocumentSection[]>([])

  useEffect(() => {
    if (document && analysisResults) {
      initializeDocumentSections()
    }
  }, [document, analysisResults])

  // 初始化文档章节
  const initializeDocumentSections = () => {
    const sections: DocumentSection[] = [
      {
        id: "section-1",
        title: "1. 项目概述",
        content: "智能驾驶系统PRD文档，旨在定义自动驾驶功能的需求规格...",
        issues: analysisResults?.issues.filter(issue => issue.section === "section-1") || []
      },
      {
        id: "section-2",
        title: "2. 用户需求",
        content: "用户场景描述过于简略，缺少极端情况和边缘案例的考虑...",
        issues: analysisResults?.issues.filter(issue => issue.section === "section-2") || []
      },
      {
        id: "section-3",
        title: "3. 产品功能",
        content: "本功能模块主要实现对PRD文档的智能评审，包括但不限于以下几点...",
        issues: analysisResults?.issues.filter(issue => issue.section === "section-3") || []
      },
      {
        id: "section-4",
        title: "4. 非功能需求",
        content: "系统性能要求、安全性要求、可用性要求等...",
        issues: analysisResults?.issues.filter(issue => issue.section === "section-4") || []
      },
      {
        id: "section-5",
        title: "5. 技术架构",
        content: "系统架构设计、技术选型、部署方案等...",
        issues: analysisResults?.issues.filter(issue => issue.section === "section-5") || []
      }
    ]
    setDocumentSections(sections)
  }

  // 处理问题确认
  const handleIssueReview = (issueId: string, accepted: boolean) => {
    setReviewedIssues(prev => new Set([...prev, issueId]))
    if (accepted) {
      setAcceptedSuggestions(prev => new Set([...prev, issueId]))
    }
  }

  // 完成评审
  const handleCompleteReview = () => {
    const reviewResults: ReviewResults = {
      documentId: document.id,
      reviewTimestamp: new Date().toISOString(),
      totalIssues: analysisResults?.totalIssues || 0,
      reviewedIssues: reviewedIssues.size,
      acceptedSuggestions: acceptedSuggestions.size,
      rejectedSuggestions: reviewedIssues.size - acceptedSuggestions.size,
      reviewedIssueIds: Array.from(reviewedIssues),
      acceptedSuggestionIds: Array.from(acceptedSuggestions),
      overallApproval: (acceptedSuggestions.size / reviewedIssues.size) * 100
    }
    onReviewComplete(reviewResults)
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setOutlineExpanded(!outlineExpanded)}
          >
            <List className="w-4 h-4 mr-2" />
            {outlineExpanded ? "隐藏大纲" : "显示大纲"}
          </Button>
          <h2 className="text-xl font-semibold text-gray-900">
            {document?.title || "智能驾驶系统PRD"}
          </h2>
        </div>
        <Badge variant="secondary">v1.2.0 汽车行业</Badge>
      </div>

      <div className="flex gap-6 h-[700px]">
        {/* 文档大纲 - 左侧 */}
        {outlineExpanded && (
          <div className="w-64 bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-4">文档大纲</h3>
            <div className="space-y-2">
              {documentSections.map((section) => (
                <div
                  key={section.id}
                  className="p-2 rounded cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={() => scrollToSection(section.id)}
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{section.title}</span>
                    {section.issues.length > 0 && (
                      <Badge variant="destructive" className="text-xs">
                        {section.issues.length}
                      </Badge>
                    )}
                  </div>
                  {section.issues.length > 0 && (
                    <div className="mt-1 space-y-1">
                      {section.issues.map((issue) => (
                        <div
                          key={issue.id}
                          className={`text-xs p-1 rounded ${
                            issue.type === 'error' ? 'bg-red-100 text-red-700' :
                            issue.type === 'warning' ? 'bg-yellow-100 text-yellow-700' :
                            'bg-blue-100 text-blue-700'
                          }`}
                        >
                          {issue.title}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 文档内容 - 中间 */}
        <div className="flex-1 bg-white border border-gray-200 rounded-lg overflow-hidden">
          <ScrollArea className="h-full">
            <div className="p-6 space-y-6">
              {documentSections.map((section) => (
                <div key={section.id} id={section.id} className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                    {section.title}
                  </h3>
                  <div className="prose prose-gray max-w-none">
                    <p className="text-gray-700 leading-relaxed">{section.content}</p>
                  </div>

                  {/* 章节问题标记 */}
                  {section.issues.map((issue) => (
                    <div
                      key={issue.id}
                      className={`border-l-4 p-4 rounded-r-lg cursor-pointer transition-colors ${
                        issue.type === 'error' ? 'border-red-500 bg-red-50 hover:bg-red-100' :
                        issue.type === 'warning' ? 'border-yellow-500 bg-yellow-50 hover:bg-yellow-100' :
                        'border-blue-500 bg-blue-50 hover:bg-blue-100'
                      } ${reviewedIssues.has(issue.id) ? 'opacity-60' : ''}`}
                      onClick={() => setSelectedIssue(issue)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <Badge className={
                              issue.type === 'error' ? 'bg-red-100 text-red-800' :
                              issue.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-blue-100 text-blue-800'
                            }>
                              {issue.type === 'error' ? '错误' : issue.type === 'warning' ? '警告' : '建议'}
                            </Badge>
                            <span className="text-sm text-gray-500">置信度: {issue.confidence}%</span>
                          </div>
                          <h4 className="font-medium text-gray-900 mb-1">{issue.title}</h4>
                          <p className="text-sm text-gray-600 mb-2">{issue.description}</p>
                          <p className="text-sm text-blue-600">{issue.suggestion}</p>
                        </div>
                        <div className="flex items-center space-x-2 ml-4">
                          {reviewedIssues.has(issue.id) ? (
                            <Badge className={acceptedSuggestions.has(issue.id) ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                              {acceptedSuggestions.has(issue.id) ? '已采纳' : '已拒绝'}
                            </Badge>
                          ) : (
                            <Badge variant="outline">待确认</Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>

        {/* 问题详情 - 右侧 */}
        <div className="w-80 bg-white border border-gray-200 rounded-lg p-4">
          {selectedIssue ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-gray-900">问题详情</h3>
                <Button variant="ghost" size="sm" onClick={() => setSelectedIssue(null)}>
                  <X className="w-4 h-4" />
                </Button>
              </div>

              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-700">问题类型</label>
                  <Badge className={`ml-2 ${
                    selectedIssue.type === 'error' ? 'bg-red-100 text-red-800' :
                    selectedIssue.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-blue-100 text-blue-800'
                  }`}>
                    {selectedIssue.type === 'error' ? '错误' : selectedIssue.type === 'warning' ? '警告' : '建议'}
                  </Badge>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">问题标题</label>
                  <p className="text-sm text-gray-900 mt-1">{selectedIssue.title}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">问题描述</label>
                  <p className="text-sm text-gray-600 mt-1">{selectedIssue.description}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">修改建议</label>
                  <p className="text-sm text-blue-600 mt-1">{selectedIssue.suggestion}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">相关规则</label>
                  <p className="text-sm text-gray-600 mt-1">{selectedIssue.rule}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">置信度</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Progress value={selectedIssue.confidence} className="flex-1 h-2" />
                    <span className="text-sm text-gray-600">{selectedIssue.confidence}%</span>
                  </div>
                </div>
              </div>

              {!reviewedIssues.has(selectedIssue.id) && (
                <div className="space-y-2 pt-4 border-t border-gray-200">
                  <Button
                    className="w-full bg-green-600 hover:bg-green-700"
                    onClick={() => handleIssueReview(selectedIssue.id, true)}
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    采纳建议
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => handleIssueReview(selectedIssue.id, false)}
                  >
                    <X className="w-4 h-4 mr-2" />
                    拒绝建议
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center text-gray-500 mt-8">
              <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>点击左侧问题查看详情</p>
            </div>
          )}
        </div>
      </div>

      {/* 底部操作栏 */}
      <div className="flex items-center justify-between bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-600">
            评审进度: {reviewedIssues.size} / {analysisResults?.totalIssues || 0}
          </div>
          <Progress
            value={(reviewedIssues.size / (analysisResults?.totalIssues || 1)) * 100}
            className="w-32 h-2"
          />
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            保存草稿
          </Button>
          <Button
            className="bg-orange-500 hover:bg-orange-600"
            onClick={handleCompleteReview}
            disabled={reviewedIssues.size === 0}
          >
            查看评审结果
          </Button>
        </div>
      </div>
    </div>
  )
}
```

### 4.2 数据模型

```tsx
interface DocumentSection {
  id: string
  title: string
  content: string
  issues: ReviewIssue[]
}

interface ReviewResults {
  documentId: string
  reviewTimestamp: string
  totalIssues: number
  reviewedIssues: number
  acceptedSuggestions: number
  rejectedSuggestions: number
  reviewedIssueIds: string[]
  acceptedSuggestionIds: string[]
  overallApproval: number
}
```

---

## 第5步：评审结果页面实现

### 5.1 评审结果页面组件

**目标文件：** `components/prd-results-tab.tsx`

**核心功能：**
1. 评审结果总览
2. 问题统计分析
3. 修改建议汇总
4. 结果导出功能

**实现内容：**
```tsx
export default function PRDResultsTab({
  document,
  analysisResults,
  reviewResults,
  onStartOptimization
}: PRDResultsTabProps) {
  const [showDetailedReport, setShowDetailedReport] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string>("all")

  // 计算统计数据
  const statistics = useMemo(() => {
    if (!analysisResults || !reviewResults) return null

    const acceptedIssues = analysisResults.issues.filter(issue =>
      reviewResults.acceptedSuggestionIds.includes(issue.id)
    )
    const rejectedIssues = analysisResults.issues.filter(issue =>
      reviewResults.reviewedIssueIds.includes(issue.id) &&
      !reviewResults.acceptedSuggestionIds.includes(issue.id)
    )

    return {
      totalScore: analysisResults.overallScore,
      improvementPotential: Math.round(acceptedIssues.length * 3.5),
      acceptanceRate: Math.round((reviewResults.acceptedSuggestions / reviewResults.totalIssues) * 100),
      criticalIssues: acceptedIssues.filter(issue => issue.type === 'error').length,
      acceptedByType: {
        error: acceptedIssues.filter(issue => issue.type === 'error').length,
        warning: acceptedIssues.filter(issue => issue.type === 'warning').length,
        suggestion: acceptedIssues.filter(issue => issue.type === 'suggestion').length
      },
      rejectedByType: {
        error: rejectedIssues.filter(issue => issue.type === 'error').length,
        warning: rejectedIssues.filter(issue => issue.type === 'warning').length,
        suggestion: rejectedIssues.filter(issue => issue.type === 'suggestion').length
      }
    }
  }, [analysisResults, reviewResults])

  // 生成改进建议
  const generateImprovementSuggestions = () => {
    if (!analysisResults || !reviewResults) return []

    const acceptedIssues = analysisResults.issues.filter(issue =>
      reviewResults.acceptedSuggestionIds.includes(issue.id)
    )

    return acceptedIssues.map(issue => ({
      id: issue.id,
      section: issue.sectionTitle,
      issue: issue.title,
      suggestion: issue.suggestion,
      priority: issue.priority,
      impact: issue.type === 'error' ? 'high' : issue.type === 'warning' ? 'medium' : 'low'
    }))
  }

  const improvementSuggestions = generateImprovementSuggestions()

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">
          评审结果总结 - {document?.title || "智能驾驶系统PRD"}
        </h2>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            导出结果
          </Button>
          <Button variant="outline" size="sm">
            <FileText className="w-4 h-4 mr-2" />
            生成报告
          </Button>
          <Button variant="outline" size="sm">
            <Share className="w-4 h-4 mr-2" />
            分享结果
          </Button>
        </div>
      </div>

      {/* 总体评分卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-orange-600 mb-2">
              {statistics?.totalScore || 0}
            </div>
            <div className="text-sm text-orange-700">文档质量评分</div>
            <div className="text-xs text-orange-600 mt-1">
              {statistics?.totalScore >= 80 ? '优秀' :
               statistics?.totalScore >= 60 ? '良好' : '需改进'}
            </div>
          </CardContent>
        </Card>

        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">
              {statistics?.acceptanceRate || 0}%
            </div>
            <div className="text-sm text-green-700">建议采纳率</div>
            <div className="text-xs text-green-600 mt-1">
              {reviewResults?.acceptedSuggestions || 0}/{reviewResults?.totalIssues || 0} 条建议
            </div>
          </CardContent>
        </Card>

        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">
              +{statistics?.improvementPotential || 0}
            </div>
            <div className="text-sm text-blue-700">预期改进分数</div>
            <div className="text-xs text-blue-600 mt-1">
              采纳建议后预期提升
            </div>
          </CardContent>
        </Card>

        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-red-600 mb-2">
              {statistics?.criticalIssues || 0}
            </div>
            <div className="text-sm text-red-700">关键问题</div>
            <div className="text-xs text-red-600 mt-1">
              需要优先处理
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 问题分析图表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>问题类型分布</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded"></div>
                  <span className="text-sm">错误</span>
                </div>
                <div className="flex space-x-4 text-sm">
                  <span className="text-green-600">已采纳: {statistics?.acceptedByType.error || 0}</span>
                  <span className="text-gray-600">已拒绝: {statistics?.rejectedByType.error || 0}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded"></div>
                  <span className="text-sm">警告</span>
                </div>
                <div className="flex space-x-4 text-sm">
                  <span className="text-green-600">已采纳: {statistics?.acceptedByType.warning || 0}</span>
                  <span className="text-gray-600">已拒绝: {statistics?.rejectedByType.warning || 0}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded"></div>
                  <span className="text-sm">建议</span>
                </div>
                <div className="flex space-x-4 text-sm">
                  <span className="text-green-600">已采纳: {statistics?.acceptedByType.suggestion || 0}</span>
                  <span className="text-gray-600">已拒绝: {statistics?.rejectedByType.suggestion || 0}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>章节质量评分</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(analysisResults?.sectionScores || {}).map(([sectionId, score]) => (
                <div key={sectionId} className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>章节 {sectionId.replace('section-', '')}</span>
                    <span className="font-medium">{score}分</span>
                  </div>
                  <Progress value={score} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 改进建议列表 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>采纳的改进建议</CardTitle>
            <div className="flex space-x-2">
              <Button
                variant={selectedCategory === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory("all")}
              >
                全部
              </Button>
              <Button
                variant={selectedCategory === "high" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory("high")}
              >
                高优先级
              </Button>
              <Button
                variant={selectedCategory === "medium" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory("medium")}
              >
                中优先级
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {improvementSuggestions
              .filter(suggestion => selectedCategory === "all" || suggestion.priority === selectedCategory)
              .map((suggestion, index) => (
                <div key={suggestion.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <Badge variant="outline">{suggestion.section}</Badge>
                        <Badge className={
                          suggestion.priority === 'high' ? 'bg-red-100 text-red-800' :
                          suggestion.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-blue-100 text-blue-800'
                        }>
                          {suggestion.priority === 'high' ? '高优先级' :
                           suggestion.priority === 'medium' ? '中优先级' : '低优先级'}
                        </Badge>
                      </div>
                      <h4 className="font-medium text-gray-900 mb-1">{suggestion.issue}</h4>
                      <p className="text-sm text-gray-600">{suggestion.suggestion}</p>
                    </div>
                    <div className="ml-4">
                      <Badge className="bg-green-100 text-green-800">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        已采纳
                      </Badge>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>

      {/* 底部操作 */}
      <div className="flex items-center justify-between bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => setShowDetailedReport(!showDetailedReport)}>
            <FileText className="w-4 h-4 mr-2" />
            {showDetailedReport ? '隐藏' : '查看'}详细报告
          </Button>
          <Button variant="outline">
            <MessageSquare className="w-4 h-4 mr-2" />
            生成智能建议
          </Button>
        </div>
        <Button
          className="bg-orange-500 hover:bg-orange-600"
          onClick={onStartOptimization}
        >
          <Settings className="w-4 h-4 mr-2" />
          PRD自检优化
        </Button>
      </div>
    </div>
  )
}
```

---

## 第6步：自检优化页面实现

### 6.1 自检优化页面组件

**目标文件：** `components/prd-optimization-tab.tsx`

**核心功能：**
1. PRD自检概览
2. 规则优化建议
3. 智能建议卡片
4. 优化效果预测

**实现内容：**
```tsx
export default function PRDOptimizationTab({
  document,
  reviewResults,
  analysisResults
}: PRDOptimizationTabProps) {
  const [optimizationData, setOptimizationData] = useState<OptimizationData | null>(null)
  const [isRunningOptimization, setIsRunningOptimization] = useState(false)
  const [optimizationProgress, setOptimizationProgress] = useState(0)
  const [selectedOptimization, setSelectedOptimization] = useState<string>("all")
  const [showRuleOptimization, setShowRuleOptimization] = useState(false)

  useEffect(() => {
    if (reviewResults && analysisResults) {
      generateOptimizationData()
    }
  }, [reviewResults, analysisResults])

  // 生成优化数据
  const generateOptimizationData = async () => {
    setIsRunningOptimization(true)
    setOptimizationProgress(0)

    // 模拟优化分析过程
    const steps = [
      "分析评审结果...",
      "计算采纳率指标...",
      "生成规则优化建议...",
      "预测改进效果...",
      "生成优化报告..."
    ]

    for (let i = 0; i < steps.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 800))
      setOptimizationProgress(((i + 1) / steps.length) * 100)
    }

    // 生成优化数据
    const data: OptimizationData = {
      documentId: document.id,
      optimizationTimestamp: new Date().toISOString(),
      humanAdoptionRate: Math.round((reviewResults.acceptedSuggestions / reviewResults.totalIssues) * 100),
      aiAccuracyRate: 87,
      suggestionModificationRate: 23,
      ruleOptimizationSuggestions: generateRuleOptimizations(),
      intelligentSuggestions: generateIntelligentSuggestions(),
      performanceMetrics: {
        beforeOptimization: analysisResults.overallScore,
        afterOptimization: analysisResults.overallScore + 12,
        improvementPotential: 15,
        confidenceLevel: 89
      }
    }

    setOptimizationData(data)
    setIsRunningOptimization(false)
  }

  // 生成规则优化建议
  const generateRuleOptimizations = (): RuleOptimizationSuggestion[] => {
    return [
      {
        id: "rule-opt-1",
        ruleId: "rule-001",
        ruleName: "功能完整性检查",
        currentVersion: "v1.2",
        suggestedChanges: "增加对用户故事格式的检查，提高功能描述的标准化程度",
        reason: "当前规则对功能描述的检查过于宽泛，导致一些不规范的描述被遗漏",
        expectedImprovement: "预计可提高功能完整性检查准确率15%",
        priority: "high",
        status: "pending",
        testResults: []
      },
      {
        id: "rule-opt-2",
        ruleId: "rule-002",
        ruleName: "场景完整性检查",
        currentVersion: "v1.1",
        suggestedChanges: "优化异常场景识别算法，增加边缘案例检测",
        reason: "用户反馈显示该规则对复杂场景的识别能力不足",
        expectedImprovement: "预计可提高场景覆盖率20%",
        priority: "medium",
        status: "pending",
        testResults: []
      },
      {
        id: "rule-opt-3",
        ruleId: "rule-003",
        ruleName: "质量标准检查",
        currentVersion: "v1.0",
        suggestedChanges: "更新性能指标阈值，适应当前行业标准",
        reason: "当前阈值设置过于保守，导致误报率较高",
        expectedImprovement: "预计可降低误报率30%",
        priority: "low",
        status: "pending",
        testResults: []
      }
    ]
  }

  // 生成智能建议
  const generateIntelligentSuggestions = (): IntelligentSuggestionCard[] => {
    return [
      {
        id: "suggestion-1",
        type: "template",
        title: "推荐使用标准PRD模板",
        content: "基于评审结果，建议采用汽车行业标准PRD模板，可提高文档规范性",
        priority: "high",
        confidence: 92,
        estimatedImprovement: 18,
        status: "pending",
        category: "structure",
        actionable: true,
        relatedSections: ["section-1", "section-3"]
      },
      {
        id: "suggestion-2",
        type: "content",
        title: "补充用户画像描述",
        content: "建议在用户需求章节增加详细的用户画像和使用场景描述",
        priority: "medium",
        confidence: 85,
        estimatedImprovement: 12,
        status: "pending",
        category: "content",
        actionable: true,
        relatedSections: ["section-2"]
      },
      {
        id: "suggestion-3",
        type: "quality",
        title: "增加验收标准",
        content: "为每个功能点添加明确的验收标准和测试用例",
        priority: "medium",
        confidence: 78,
        estimatedImprovement: 15,
        status: "pending",
        category: "quality",
        actionable: true,
        relatedSections: ["section-3", "section-4"]
      }
    ]
  }

  // 运行规则优化
  const handleRunRuleOptimization = async () => {
    setShowRuleOptimization(true)
    // 这里可以集成之前实现的规则库自检组件
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">
          自检优化 - {document?.title || "智能驾驶系统PRD"}
        </h2>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            导出优化建议
          </Button>
          <Button variant="outline" size="sm">
            <FileText className="w-4 h-4 mr-2" />
            生成新版本
          </Button>
        </div>
      </div>

      {/* 优化进度 */}
      {isRunningOptimization && (
        <Card>
          <CardContent className="p-6">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">自检优化进度</span>
                <span className="text-sm text-gray-500">{Math.floor(optimizationProgress)}%</span>
              </div>
              <Progress value={optimizationProgress} className="h-2" />
              <p className="text-sm text-gray-600">正在分析评审结果并生成优化建议...</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* PRD自检概览 */}
      {optimizationData && (
        <>
          <div className="grid grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {optimizationData.humanAdoptionRate}%
                </div>
                <div className="text-sm text-gray-600">人工采纳率</div>
                <div className="text-xs text-gray-500 mt-1">
                  {reviewResults?.acceptedSuggestions}/{reviewResults?.totalIssues} 建议被采纳
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {optimizationData.aiAccuracyRate}%
                </div>
                <div className="text-sm text-gray-600">AI准确率</div>
                <div className="text-xs text-gray-500 mt-1">
                  基于历史数据评估
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-orange-600 mb-2">
                  {optimizationData.suggestionModificationRate}%
                </div>
                <div className="text-sm text-gray-600">建议修改率</div>
                <div className="text-xs text-gray-500 mt-1">
                  需要优化的建议比例
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">
                  +{optimizationData.performanceMetrics.improvementPotential}
                </div>
                <div className="text-sm text-gray-600">改进潜力</div>
                <div className="text-xs text-gray-500 mt-1">
                  预期质量提升分数
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 优化建议选项卡 */}
          <Tabs value={selectedOptimization} onValueChange={setSelectedOptimization}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="all">全部建议</TabsTrigger>
              <TabsTrigger value="rules">规则优化</TabsTrigger>
              <TabsTrigger value="intelligent">智能建议</TabsTrigger>
            </TabsList>

            {/* 全部建议 */}
            <TabsContent value="all" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* 规则优化概览 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>规则优化建议</span>
                      <Badge variant="outline">
                        {optimizationData.ruleOptimizationSuggestions.length} 条
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {optimizationData.ruleOptimizationSuggestions.slice(0, 3).map((rule) => (
                      <div key={rule.id} className="border border-gray-200 rounded p-3">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-sm">{rule.ruleName}</span>
                          <Badge className={
                            rule.priority === 'high' ? 'bg-red-100 text-red-800' :
                            rule.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-blue-100 text-blue-800'
                          }>
                            {rule.priority === 'high' ? '高' : rule.priority === 'medium' ? '中' : '低'}
                          </Badge>
                        </div>
                        <p className="text-xs text-gray-600">{rule.reason}</p>
                      </div>
                    ))}
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={handleRunRuleOptimization}
                    >
                      查看全部规则优化
                    </Button>
                  </CardContent>
                </Card>

                {/* 智能建议概览 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>智能建议</span>
                      <Badge variant="outline">
                        {optimizationData.intelligentSuggestions.length} 条
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {optimizationData.intelligentSuggestions.map((suggestion) => (
                      <div key={suggestion.id} className="border border-gray-200 rounded p-3">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-sm">{suggestion.title}</span>
                          <Badge className={
                            suggestion.type === 'template' ? 'bg-purple-100 text-purple-800' :
                            suggestion.type === 'content' ? 'bg-blue-100 text-blue-800' :
                            'bg-green-100 text-green-800'
                          }>
                            {suggestion.type === 'template' ? '模板' :
                             suggestion.type === 'content' ? '内容' : '质量'}
                          </Badge>
                        </div>
                        <p className="text-xs text-gray-600 mb-2">{suggestion.content}</p>
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-gray-500">置信度: {suggestion.confidence}%</span>
                          <span className="text-green-600">+{suggestion.estimatedImprovement}分</span>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </div>

              {/* 性能预测 */}
              <Card>
                <CardHeader>
                  <CardTitle>优化效果预测</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-600 mb-1">
                        {optimizationData.performanceMetrics.beforeOptimization}
                      </div>
                      <div className="text-sm text-gray-500">优化前评分</div>
                    </div>
                    <div className="flex items-center justify-center">
                      <ArrowRight className="w-8 h-8 text-orange-500" />
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600 mb-1">
                        {optimizationData.performanceMetrics.afterOptimization}
                      </div>
                      <div className="text-sm text-gray-500">预期优化后评分</div>
                    </div>
                  </div>
                  <div className="mt-4 text-center">
                    <Badge className="bg-green-100 text-green-800">
                      预期提升 +{optimizationData.performanceMetrics.improvementPotential} 分
                      (置信度: {optimizationData.performanceMetrics.confidenceLevel}%)
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  )
}
```

### 6.2 数据模型

```tsx
interface OptimizationData {
  documentId: string
  optimizationTimestamp: string
  humanAdoptionRate: number
  aiAccuracyRate: number
  suggestionModificationRate: number
  ruleOptimizationSuggestions: RuleOptimizationSuggestion[]
  intelligentSuggestions: IntelligentSuggestionCard[]
  performanceMetrics: {
    beforeOptimization: number
    afterOptimization: number
    improvementPotential: number
    confidenceLevel: number
  }
}

interface IntelligentSuggestionCard {
  id: string
  type: "template" | "content" | "quality"
  title: string
  content: string
  priority: "high" | "medium" | "low"
  confidence: number
  estimatedImprovement: number
  status: "pending" | "applied" | "rejected"
  category: string
  actionable: boolean
  relatedSections: string[]
}
```

---

## 第7步：流程串联与完整集成

### 7.1 主页面集成

**目标文件：** `components/prd-review-page.tsx` (完整版本)

```tsx
"use client"

import React, { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ChevronLeft } from "lucide-react"
import PRDUploadTab from "./prd-upload-tab"
import PRDAnalysisTab from "./prd-analysis-tab"
import PRDReviewTab from "./prd-review-tab"
import PRDResultsTab from "./prd-results-tab"
import PRDOptimizationTab from "./prd-optimization-tab"

interface PRDReviewPageProps {
  onBack?: () => void
}

export default function PRDReviewPage({ onBack }: PRDReviewPageProps) {
  // 页面状态管理
  const [activeTab, setActiveTab] = useState("upload")
  const [hasStartedAnalysis, setHasStartedAnalysis] = useState(false)
  const [hasCompletedAnalysis, setHasCompletedAnalysis] = useState(false)
  const [hasStartedReview, setHasStartedReview] = useState(false)
  const [hasCompletedReview, setHasCompletedReview] = useState(false)

  // 数据状态管理
  const [uploadedDocuments, setUploadedDocuments] = useState<PRDDocument[]>([])
  const [currentDocument, setCurrentDocument] = useState<PRDDocument | null>(null)
  const [analysisResults, setAnalysisResults] = useState<AnalysisResult | null>(null)
  const [reviewResults, setReviewResults] = useState<ReviewResults | null>(null)
  const [optimizationData, setOptimizationData] = useState<OptimizationData | null>(null)

  // 流程控制函数
  const handleStartAnalysis = (document: PRDDocument) => {
    setCurrentDocument(document)
    setHasStartedAnalysis(true)
    setActiveTab("analysis")

    // 更新文档状态
    setUploadedDocuments(prev =>
      prev.map(doc =>
        doc.id === document.id
          ? { ...doc, status: "analyzing", progress: 0 }
          : doc
      )
    )
  }

  const handleAnalysisComplete = (results: AnalysisResult) => {
    setAnalysisResults(results)
    setHasCompletedAnalysis(true)

    // 更新文档状态
    if (currentDocument) {
      setUploadedDocuments(prev =>
        prev.map(doc =>
          doc.id === currentDocument.id
            ? {
                ...doc,
                status: "analyzed",
                progress: 100,
                issues: results.totalIssues
              }
            : doc
        )
      )
    }
  }

  const handleEnterReview = () => {
    setHasStartedReview(true)
    setActiveTab("review")

    // 更新文档状态
    if (currentDocument) {
      setUploadedDocuments(prev =>
        prev.map(doc =>
          doc.id === currentDocument.id
            ? { ...doc, status: "reviewing" }
            : doc
        )
      )
    }
  }

  const handleReviewComplete = (results: ReviewResults) => {
    setReviewResults(results)
    setHasCompletedReview(true)
    setActiveTab("results")

    // 更新文档状态
    if (currentDocument) {
      setUploadedDocuments(prev =>
        prev.map(doc =>
          doc.id === currentDocument.id
            ? {
                ...doc,
                status: "completed",
                progress: 100,
                fixed: results.acceptedSuggestions
              }
            : doc
        )
      )
    }
  }

  const handleStartOptimization = () => {
    setActiveTab("optimization")
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">PRD智能评审</h1>
          <p className="text-gray-600 mt-1">智能化PRD文档评审与优化平台</p>
        </div>
        {onBack && (
          <Button variant="outline" onClick={onBack}>
            <ChevronLeft className="w-4 h-4 mr-2" />
            返回
          </Button>
        )}
      </div>

      {/* 进度指示器 */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              activeTab === "upload" ? "bg-orange-500 text-white" :
              hasStartedAnalysis ? "bg-green-500 text-white" : "bg-gray-200 text-gray-500"
            }`}>
              1
            </div>
            <div className={`w-16 h-1 ${hasStartedAnalysis ? "bg-green-500" : "bg-gray-200"}`}></div>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              activeTab === "analysis" ? "bg-orange-500 text-white" :
              hasCompletedAnalysis ? "bg-green-500 text-white" : "bg-gray-200 text-gray-500"
            }`}>
              2
            </div>
            <div className={`w-16 h-1 ${hasCompletedAnalysis ? "bg-green-500" : "bg-gray-200"}`}></div>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              activeTab === "review" ? "bg-orange-500 text-white" :
              hasStartedReview ? "bg-green-500 text-white" : "bg-gray-200 text-gray-500"
            }`}>
              3
            </div>
            <div className={`w-16 h-1 ${hasStartedReview ? "bg-green-500" : "bg-gray-200"}`}></div>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              activeTab === "results" ? "bg-orange-500 text-white" :
              hasCompletedReview ? "bg-green-500 text-white" : "bg-gray-200 text-gray-500"
            }`}>
              4
            </div>
            <div className={`w-16 h-1 ${hasCompletedReview ? "bg-green-500" : "bg-gray-200"}`}></div>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              activeTab === "optimization" ? "bg-orange-500 text-white" : "bg-gray-200 text-gray-500"
            }`}>
              5
            </div>
          </div>
          <div className="text-sm text-gray-600">
            {currentDocument ? `当前文档: ${currentDocument.title}` : "请上传PRD文档"}
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="upload">文档上传</TabsTrigger>
          <TabsTrigger value="analysis" disabled={!hasStartedAnalysis}>智能分析</TabsTrigger>
          <TabsTrigger value="review" disabled={!hasCompletedAnalysis}>评审确认</TabsTrigger>
          <TabsTrigger value="results" disabled={!hasStartedReview}>评审结果</TabsTrigger>
          <TabsTrigger value="optimization" disabled={!hasCompletedReview}>自检优化</TabsTrigger>
        </TabsList>

        {/* 各个Tab页面内容 */}
        <TabsContent value="upload">
          <PRDUploadTab
            onStartAnalysis={handleStartAnalysis}
            uploadedDocuments={uploadedDocuments}
            setUploadedDocuments={setUploadedDocuments}
          />
        </TabsContent>

        <TabsContent value="analysis">
          {currentDocument && (
            <PRDAnalysisTab
              document={currentDocument}
              onAnalysisComplete={handleAnalysisComplete}
              onEnterReview={handleEnterReview}
            />
          )}
        </TabsContent>

        <TabsContent value="review">
          {currentDocument && analysisResults && (
            <PRDReviewTab
              document={currentDocument}
              analysisResults={analysisResults}
              onReviewComplete={handleReviewComplete}
            />
          )}
        </TabsContent>

        <TabsContent value="results">
          {currentDocument && analysisResults && reviewResults && (
            <PRDResultsTab
              document={currentDocument}
              analysisResults={analysisResults}
              reviewResults={reviewResults}
              onStartOptimization={handleStartOptimization}
            />
          )}
        </TabsContent>

        <TabsContent value="optimization">
          {currentDocument && reviewResults && analysisResults && (
            <PRDOptimizationTab
              document={currentDocument}
              reviewResults={reviewResults}
              analysisResults={analysisResults}
            />
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
```

### 7.2 主应用集成

**在主页面 `app/page.tsx` 中集成：**

```tsx
// 在导入部分添加
import PRDReviewPage from "@/components/prd-review-page"

// 在页面导航状态中添加
const [currentPage, setCurrentPage] = useState("main") // 添加 "prd-review"

// 在 renderPageContent 函数中添加
case "prd-review":
  return (
    <PRDReviewPage
      onBack={() => setCurrentPage("main")}
    />
  )

// 在侧边栏导航中添加
<button
  onClick={() => setCurrentPage("prd-review")}
  className={`w-full flex items-center px-3 py-2 text-sm rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 ${
    currentPage === "prd-review" ? "bg-orange-100 text-orange-700" : "text-gray-700 hover:bg-gray-100"
  }`}
>
  <FileText className="w-4 h-4 mr-3" />
  <span>PRD智能评审</span>
</button>
```

---

## 完整数据模型总结

### 核心数据模型

```tsx
// PRD文档模型
interface PRDDocument {
  id: string
  title: string
  uploadDate: string
  status: "uploaded" | "analyzing" | "analyzed" | "reviewing" | "completed"
  progress: number
  issues: number
  fixed: number
  fileSize: string
  fileType: string
  content?: string
}

// 评审问题模型
interface ReviewIssue {
  id: string
  type: "error" | "warning" | "suggestion"
  title: string
  description: string
  section: string
  sectionTitle: string
  suggestion: string
  accepted?: boolean
  originalContent?: string
  modifiedContent?: string
  rule?: string
  ruleId?: string
  confidence?: number
  aiRecommendation?: string
  humanFeedback?: string
  timestamp?: string
  priority?: "high" | "medium" | "low"
}

// 分析结果模型
interface AnalysisResult {
  documentId: string
  analysisTimestamp: string
  overallScore: number
  totalIssues: number
  errorCount: number
  warningCount: number
  suggestionCount: number
  issues: ReviewIssue[]
  sectionScores: { [sectionId: string]: number }
  completenessScore: number
  clarityScore: number
  consistencyScore: number
  feasibilityScore: number
}

// 文档章节模型
interface DocumentSection {
  id: string
  title: string
  content: string
  issues: ReviewIssue[]
}

// 评审结果模型
interface ReviewResults {
  documentId: string
  reviewTimestamp: string
  totalIssues: number
  reviewedIssues: number
  acceptedSuggestions: number
  rejectedSuggestions: number
  reviewedIssueIds: string[]
  acceptedSuggestionIds: string[]
  overallApproval: number
}

// 优化数据模型
interface OptimizationData {
  documentId: string
  optimizationTimestamp: string
  humanAdoptionRate: number
  aiAccuracyRate: number
  suggestionModificationRate: number
  ruleOptimizationSuggestions: RuleOptimizationSuggestion[]
  intelligentSuggestions: IntelligentSuggestionCard[]
  performanceMetrics: {
    beforeOptimization: number
    afterOptimization: number
    improvementPotential: number
    confidenceLevel: number
  }
}

// 规则优化建议模型
interface RuleOptimizationSuggestion {
  id: string
  ruleId: string
  ruleName: string
  currentVersion: string
  suggestedChanges: string
  reason: string
  expectedImprovement: string
  priority: "high" | "medium" | "low"
  status: "pending" | "testing" | "approved" | "rejected"
  testResults?: RuleTestResult[]
}

// 智能建议卡片模型
interface IntelligentSuggestionCard {
  id: string
  type: "template" | "content" | "quality"
  title: string
  content: string
  priority: "high" | "medium" | "low"
  confidence: number
  estimatedImprovement: number
  status: "pending" | "applied" | "rejected"
  category: string
  actionable: boolean
  relatedSections: string[]
}

// 规则测试结果模型
interface RuleTestResult {
  id: string
  ruleId: string
  testCaseId: string
  beforeScore: number
  afterScore: number
  issuesFound: number
  issuesResolved: number
  accuracy: number
  testTimestamp: string
}
```

---

## 目标项目结构

```
src/
├── components/
│   ├── PRDEvaluation/                    # 新增PRD评估模块
│   │   ├── index.js                      # 主入口组件
│   │   ├── PRDUploadTab.js              # 文档上传页面
│   │   ├── PRDAnalysisTab.js            # 智能分析页面
│   │   ├── PRDReviewTab.js              # 评审确认页面
│   │   ├── PRDResultsTab.js             # 评审结果页面
│   │   ├── PRDOptimizationTab.js        # 自检优化页面
│   │   └── components/                   # 子组件目录
│   │       ├── RuleSuggestionCards.js   # 规则建议卡片
│   │       ├── RuleTestDialog.js        # 规则测试对话框
│   │       ├── RuleLibrarySelfCheck.js  # 规则库自检
│   │       ├── DocumentUploader.js      # 文档上传器
│   │       ├── AnalysisProgress.js      # 分析进度显示
│   │       ├── ReviewIssueList.js       # 评审问题列表
│   │       ├── ScoreCharts.js           # 评分图表
│   │       └── OptimizationCards.js     # 优化建议卡片
│   ├── Layout/
│   └── ...
├── hooks/
│   ├── usePRDEvaluation.js              # PRD评估状态管理
│   ├── usePRDUpload.js                  # 文档上传逻辑
│   ├── usePRDAnalysis.js                # 智能分析逻辑
│   └── ...
├── services/
│   ├── prdEvaluationAPI.js              # PRD评估API服务
│   └── ...
├── utils/
│   ├── prdEvaluationHelpers.js          # 评估工具函数
│   └── ...
└── types/
    └── prdEvaluation.js                 # 数据模型定义
```

---

## 实施步骤总结

### 步骤1：主菜单导航与页面框架
- 在主页面导航中添加PRD智能评审入口
- 创建PRD智能评审主页面组件，包含5个Tab页面
- 实现页面框架和进度指示器

### 步骤2：文档上传页面
- 实现文件拖拽上传功能
- 添加文件格式验证和上传进度显示
- 创建已上传文档列表
- 实现"开始分析"功能，触发页面跳转

### 步骤3：智能分析页面
- 实现分析进度显示和步骤指示
- 模拟分析过程，生成分析结果
- 显示分析结果预览
- 添加"进入人工确认"按钮，触发页面跳转

### 步骤4：评审确认页面
- 实现文档大纲导航
- 显示文档内容和问题标记
- 添加问题详情查看功能
- 实现建议采纳/拒绝功能
- 添加"查看评审结果"按钮，触发页面跳转

### 步骤5：评审结果页面
- 显示评审总体评分和统计数据
- 实现问题类型分布图表
- 显示章节质量评分
- 创建采纳的改进建议列表
- 添加"PRD自检优化"按钮，触发页面跳转

### 步骤6：自检优化页面
- 显示PRD自检概览数据
- 实现规则优化建议卡片
- 添加智能建议卡片
- 显示优化效果预测
- 集成规则库自检功能

### 步骤7：流程串联与完整集成
- 在主页面组件中实现状态管理
- 添加流程控制函数，处理页面跳转
- 实现数据传递和状态更新
- 在主应用中集成PRD智能评审页面

---

## 集成指南

### 前置条件

1. **技术栈要求**
   - React 18+ 框架
   - JavaScript ES6+ 支持
   - CSS Modules 或 Styled Components 或 Tailwind CSS
   - UI组件库 (Ant Design / Material-UI / 自定义组件)
   - lucide-react 或其他图标库

2. **依赖安装**
```bash
# 核心依赖
npm install react react-dom

# 图标库
npm install lucide-react

# 如果使用Ant Design
npm install antd

# 如果使用Material-UI
npm install @mui/material @emotion/react @emotion/styled

# 如果使用Tailwind CSS
npm install -D tailwindcss postcss autoprefixer
```

### 集成步骤

#### 1. 创建组件文件结构
按照目标项目结构创建以下组件文件：
- `src/components/PRDEvaluation/index.js` (主入口组件)
- `src/components/PRDEvaluation/PRDUploadTab.js`
- `src/components/PRDEvaluation/PRDAnalysisTab.js`
- `src/components/PRDEvaluation/PRDReviewTab.js`
- `src/components/PRDEvaluation/PRDResultsTab.js`
- `src/components/PRDEvaluation/PRDOptimizationTab.js`

#### 2. 创建子组件
在 `src/components/PRDEvaluation/components/` 目录下创建以下子组件：
- `RuleSuggestionCards.js`
- `RuleTestDialog.js`
- `RuleLibrarySelfCheck.js`
- `DocumentUploader.js`
- `AnalysisProgress.js`
- `ReviewIssueList.js`
- `ScoreCharts.js`
- `OptimizationCards.js`

#### 3. 添加数据模型
在目标项目中创建 `src/types/prdEvaluation.js` 文件，包含所有数据模型定义。

#### 4. 创建自定义Hooks
在 `src/hooks/` 目录下创建以下Hooks：
- `usePRDEvaluation.js` - PRD评估状态管理
- `usePRDUpload.js` - 文档上传逻辑
- `usePRDAnalysis.js` - 智能分析逻辑

#### 5. 创建API服务
在 `src/services/` 目录下创建：
- `prdEvaluationAPI.js` - PRD评估API服务

#### 6. 创建工具函数
在 `src/utils/` 目录下创建：
- `prdEvaluationHelpers.js` - 评估工具函数

#### 7. 集成UI组件
确保目标项目中有以下UI组件：
- Button, Card, Badge, Tabs, Progress
- Dialog, ScrollArea, Separator
- Table, Input, Label, Textarea

#### 8. 添加导航入口
在主页面导航中添加PRD智能评审入口，实现页面切换逻辑。

#### 9. 样式集成
确保CSS样式配置正确，包含所需的样式类。

#### 10. 功能测试
测试整个PRD智能评审流程，确保各个页面之间的跳转和数据传递正常。

### 注意事项

1. **组件依赖**
   - 检查所有组件的导入路径
   - 确保UI组件库版本兼容

2. **状态管理**
   - 根据项目的状态管理方式调整代码
   - 如使用Redux等，需要相应修改

3. **API集成**
   - 当前使用模拟数据，实际使用时需要集成真实API
   - 替换模拟函数为实际API调用

4. **样式兼容性**
   - 确保样式与项目设计系统兼容
   - 可能需要调整颜色、字体等

5. **性能优化**
   - 对于大型文档，考虑实现虚拟滚动
   - 使用React.memo或useMemo优化渲染性能

---

## 页面功能详解

### 1. 文档上传页面
**核心功能：**
- 文件拖拽上传区域
- 文件格式验证（PDF、Word、Markdown）
- 上传进度显示
- 已上传文档列表，显示文档状态和进度
- "开始分析"按钮，触发智能分析流程

**数据流：**
- 输入：用户上传的文档文件
- 输出：PRDDocument对象，传递给智能分析页面

### 2. 智能分析页面
**核心功能：**
- 分析进度显示
- 分析步骤指示器
- 实时分析状态更新
- 分析结果预览，包括总体评分和问题统计
- "进入人工确认"按钮，触发评审确认流程

**数据流：**
- 输入：PRDDocument对象
- 处理：分析文档内容，生成问题列表
- 输出：AnalysisResult对象，传递给评审确认页面

### 3. 评审确认页面
**核心功能：**
- 文档大纲导航，显示章节和问题数量
- 文档内容显示，带有问题标记
- 问题详情查看，显示问题类型、描述和建议
- 建议采纳/拒绝功能
- 评审进度显示
- "查看评审结果"按钮，触发评审结果流程

**数据流：**
- 输入：PRDDocument和AnalysisResult对象
- 处理：用户确认问题和建议
- 输出：ReviewResults对象，传递给评审结果页面

### 4. 评审结果页面
**核心功能：**
- 评审总体评分和统计数据
- 问题类型分布图表
- 章节质量评分
- 采纳的改进建议列表，可按优先级筛选
- 详细评审报告
- "PRD自检优化"按钮，触发自检优化流程

**数据流：**
- 输入：PRDDocument、AnalysisResult和ReviewResults对象
- 处理：生成评审结果统计和报告
- 输出：触发自检优化流程

### 5. 自检优化页面
**核心功能：**
- PRD自检概览，显示采纳率和AI准确率
- 规则优化建议，包括规则修改和测试功能
- 智能建议卡片，提供模板、内容和质量建议
- 优化效果预测，显示预期改进分数
- 规则库自检集成，提供完整的规则优化功能

**数据流：**
- 输入：PRDDocument、AnalysisResult和ReviewResults对象
- 处理：生成优化建议和规则改进方案
- 输出：OptimizationData对象，包含规则优化建议和智能建议

---

## JavaScript项目实施计划时间表

| 阶段 | 任务 | 文件创建 | 预计时间 | 验收标准 |
|------|------|----------|----------|----------|
| 1 | 项目结构搭建 | 创建目录结构和基础文件 | 0.5天 | 目录结构完整，基础配置正确 |
| 2 | 数据模型定义 | `src/types/prdEvaluation.js` | 0.5天 | 数据模型定义完整 |
| 3 | 自定义Hooks开发 | `src/hooks/usePRDEvaluation.js`<br>`src/hooks/usePRDUpload.js`<br>`src/hooks/usePRDAnalysis.js` | 1天 | Hooks功能正常，状态管理正确 |
| 4 | API服务开发 | `src/services/prdEvaluationAPI.js` | 1天 | API接口完整，错误处理正确 |
| 5 | 工具函数开发 | `src/utils/prdEvaluationHelpers.js` | 0.5天 | 工具函数功能正常 |
| 6 | 主页面组件 | `src/components/PRDEvaluation/index.js` | 1天 | 主页面布局正确，Tab切换正常 |
| 7 | 文档上传功能 | `src/components/PRDEvaluation/PRDUploadTab.js`<br>`src/components/PRDEvaluation/components/DocumentUploader.js` | 2天 | 文件上传功能完整，列表显示正常 |
| 8 | 智能分析功能 | `src/components/PRDEvaluation/PRDAnalysisTab.js`<br>`src/components/PRDEvaluation/components/AnalysisProgress.js` | 2天 | 分析进度显示，结果生成正常 |
| 9 | 评审确认功能 | `src/components/PRDEvaluation/PRDReviewTab.js`<br>`src/components/PRDEvaluation/components/ReviewIssueList.js` | 3天 | 大纲导航，问题确认功能完整 |
| 10 | 评审结果功能 | `src/components/PRDEvaluation/PRDResultsTab.js`<br>`src/components/PRDEvaluation/components/ScoreCharts.js` | 2天 | 结果统计显示，图表渲染正常 |
| 11 | 自检优化功能 | `src/components/PRDEvaluation/PRDOptimizationTab.js`<br>`src/components/PRDEvaluation/components/OptimizationCards.js`<br>`src/components/PRDEvaluation/components/RuleSuggestionCards.js`<br>`src/components/PRDEvaluation/components/RuleTestDialog.js`<br>`src/components/PRDEvaluation/components/RuleLibrarySelfCheck.js` | 4天 | 优化建议显示，规则集成完整 |
| 12 | 流程串联集成 | 整合所有组件，完善数据流 | 2天 | 整个流程跳转正常，数据传递正确 |
| 13 | 测试与调优 | 功能测试，性能优化，bug修复 | 3天 | 功能测试通过，性能优化完成 |
| | **总计** | **所有组件文件** | **22天** | **完整功能交付** |

### 验收标准

1. **功能完整性**
   - 所有5个页面功能正常
   - 页面间跳转流畅
   - 数据传递准确

2. **用户体验**
   - 界面美观，交互友好
   - 响应速度快
   - 错误处理完善

3. **代码质量**
   - 代码结构清晰
   - 组件复用性好
   - 类型定义完整

4. **兼容性**
   - 支持主流浏览器
   - 响应式设计
   - 移动端适配

---

## 总结

本实施方案提供了完整的PRD智能评审功能实现指南，包含：

1. **5个核心页面**：文档上传、智能分析、评审确认、评审结果、自检优化
2. **完整的数据模型**：涵盖所有业务场景的数据结构
3. **详细的实施步骤**：每个步骤都可以独立实施和验证
4. **集成指南**：确保在另一个项目中能够顺利集成
5. **实施计划**：18天的详细时间安排和验收标准

通过这个方案，您可以在另一个项目中完全复刻当前的PRD智能评审功能，实现从文档上传到自检优化的完整流程，为用户提供专业的PRD文档评审服务。

---

## 完整文件清单

### 核心组件文件 (必须创建)
```
src/components/PRDEvaluation/
├── index.js                      # 主入口组件 ✓
├── PRDUploadTab.js              # 文档上传页面 ✓
├── PRDAnalysisTab.js            # 智能分析页面
├── PRDReviewTab.js              # 评审确认页面
├── PRDResultsTab.js             # 评审结果页面
├── PRDOptimizationTab.js        # 自检优化页面
└── components/                   # 子组件目录
    ├── DocumentUploader.js      # 文档上传器 ✓
    ├── AnalysisProgress.js      # 分析进度显示
    ├── ReviewIssueList.js       # 评审问题列表
    ├── ScoreCharts.js           # 评分图表
    ├── OptimizationCards.js     # 优化建议卡片
    ├── RuleSuggestionCards.js   # 规则建议卡片
    ├── RuleTestDialog.js        # 规则测试对话框
    └── RuleLibrarySelfCheck.js  # 规则库自检
```

### 状态管理文件 (必须创建)
```
src/hooks/
├── usePRDEvaluation.js          # PRD评估状态管理 ✓
├── usePRDUpload.js              # 文档上传逻辑 ✓
├── usePRDAnalysis.js            # 智能分析逻辑
├── usePRDReview.js              # 评审确认逻辑
├── usePRDResults.js             # 评审结果逻辑
└── usePRDOptimization.js        # 自检优化逻辑
```

### 服务层文件 (必须创建)
```
src/services/
└── prdEvaluationAPI.js          # PRD评估API服务 ✓
```

### 工具函数文件 (必须创建)
```
src/utils/
└── prdEvaluationHelpers.js      # 评估工具函数 ✓
```

### 数据模型文件 (必须创建)
```
src/types/
└── prdEvaluation.js             # 数据模型定义 ✓
```

### UI组件依赖 (需要确保存在)
```
src/components/ui/
├── button.js                    # 按钮组件
├── card.js                      # 卡片组件
├── badge.js                     # 标签组件
├── tabs.js                      # 选项卡组件
├── progress.js                  # 进度条组件
├── table.js                     # 表格组件
├── dialog.js                    # 对话框组件
├── input.js                     # 输入框组件
├── textarea.js                  # 文本域组件
├── select.js                    # 选择器组件
├── checkbox.js                  # 复选框组件
├── radio.js                     # 单选框组件
├── switch.js                    # 开关组件
├── slider.js                    # 滑块组件
├── tooltip.js                   # 提示组件
├── popover.js                   # 弹出框组件
├── dropdown.js                  # 下拉菜单组件
├── separator.js                 # 分隔符组件
└── scroll-area.js               # 滚动区域组件
```

---

## 快速部署指南

### 第一阶段：基础搭建 (1-2天)
1. **创建目录结构**
   ```bash
   mkdir -p src/components/PRDEvaluation/components
   mkdir -p src/hooks
   mkdir -p src/services
   mkdir -p src/utils
   mkdir -p src/types
   ```

2. **创建基础文件**
   - 复制数据模型定义到 `src/types/prdEvaluation.js`
   - 复制工具函数到 `src/utils/prdEvaluationHelpers.js`
   - 复制API服务到 `src/services/prdEvaluationAPI.js`

3. **创建核心Hooks**
   - 实现 `usePRDEvaluation.js` 状态管理
   - 实现 `usePRDUpload.js` 上传逻辑

### 第二阶段：核心功能 (3-5天)
1. **实现主页面组件**
   - 创建 `src/components/PRDEvaluation/index.js`
   - 实现Tab切换和进度指示器

2. **实现文档上传功能**
   - 创建 `PRDUploadTab.js` 和 `DocumentUploader.js`
   - 集成文件上传和列表显示

3. **实现智能分析功能**
   - 创建 `PRDAnalysisTab.js` 和 `AnalysisProgress.js`
   - 实现分析进度和结果显示

### 第三阶段：高级功能 (6-10天)
1. **实现评审确认功能**
   - 创建 `PRDReviewTab.js` 和 `ReviewIssueList.js`
   - 实现问题确认和大纲导航

2. **实现评审结果功能**
   - 创建 `PRDResultsTab.js` 和 `ScoreCharts.js`
   - 实现结果统计和图表显示

3. **实现自检优化功能**
   - 创建 `PRDOptimizationTab.js` 和相关子组件
   - 实现规则优化和智能建议

### 第四阶段：集成测试 (2-3天)
1. **流程集成**
   - 完善组件间数据传递
   - 实现完整的用户流程

2. **功能测试**
   - 单元测试
   - 集成测试
   - 用户体验测试

3. **性能优化**
   - 代码分割
   - 懒加载
   - 缓存优化

---

## 部署检查清单

### ✅ 开发环境检查
- [ ] Node.js 版本 >= 16
- [ ] React 版本 >= 18
- [ ] 必要依赖已安装
- [ ] 开发服务器正常启动

### ✅ 功能完整性检查
- [ ] 文档上传功能正常
- [ ] 智能分析流程完整
- [ ] 评审确认功能正常
- [ ] 评审结果显示正确
- [ ] 自检优化功能完整

### ✅ 用户体验检查
- [ ] 页面加载速度正常
- [ ] 交互响应及时
- [ ] 错误处理完善
- [ ] 移动端适配良好

### ✅ 代码质量检查
- [ ] 代码结构清晰
- [ ] 组件复用性好
- [ ] 错误边界处理
- [ ] 性能优化到位

---

## 技术支持

如果在实施过程中遇到问题，可以参考以下资源：

1. **React官方文档**: https://react.dev/
2. **JavaScript MDN文档**: https://developer.mozilla.org/
3. **CSS样式指南**: 根据项目使用的样式方案查阅相应文档
4. **UI组件库文档**: 根据项目使用的组件库查阅相应文档

通过这个详细的实施方案，您可以在JavaScript项目中完整实现PRD智能评审功能，为用户提供专业的文档评审服务。
```
