# 评审中心Tab 1:1完整实现方案

## 项目概述

本文档专门针对PRD评审中心的第一个Tab"评审中心"进行1:1完整实现，将Team Evolve Requirements Assistant项目中的评审中心功能完整迁移到req-ai-edit-agent项目中。

## 目标项目信息

- **源项目**: Team Evolve Requirements Assistant (Next.js 15 + React 18 + TypeScript)
- **目标项目**: req-ai-edit-agent (React 18 + JavaScript + Tailwind CSS)
- **项目路径**: `/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/req-ai-edit-agent`
- **替换目标**: `src/components/PRDEvaluation/PRDUploadTab.js`
- **实现级别**: 像素级1:1还原，保持现有接口兼容性

## 1. 功能结构分析

### 1.1 页面布局结构
```
评审中心Tab
├── 页面标题区域
│   ├── 标题 + 图标 (MessageSquare + "评审中心")
│   └── 时间筛选下拉框 (Calendar + Select)
├── 统计面板区域 (4个统计卡片)
│   ├── 待评审文档 (橙色图标)
│   ├── 评审通过率 (绿色图标)
│   ├── 问题采纳率 (蓝色图标)
│   └── 评估规则数 (紫色图标)
└── 评审列表区域
    ├── 卡片标题 + 操作按钮
    ├── 空状态显示 (无文档时)
    └── 数据表格 (有文档时)
        ├── 表头 (10列)
        ├── 主文档行
        └── 版本历史行 (可展开)
```

### 1.2 核心组件清单
- **ReviewCenterTab** - 主容器组件
- **PageHeader** - 页面标题和筛选器
- **StatsPanel** - 统计面板
- **ReviewList** - 评审列表
- **DocumentRow** - 文档行组件
- **VersionRow** - 版本历史行组件
- **EmptyState** - 空状态组件

## 2. 目标项目适配分析

### 2.1 现有接口兼容性
```javascript
// 目标项目现有接口 (PRDUploadTab.js)
const PRDUploadTab = ({
  uploadedDocuments,      // 已上传文档列表
  setUploadedDocuments,   // 设置文档列表函数
  handleStartAnalysis     // 开始分析处理函数
}) => {
  // 需要保持此接口不变
}

// 现有文档数据结构
const existingDocumentStructure = {
  id: string,
  title: string,
  uploadDate: string,
  fileSize: string,
  status: 'uploaded' | 'analyzing' | 'analyzed' | 'completed',
  progress: number,
  issues: number,
  fixed: number
}
```

### 2.2 新数据结构定义 (兼容现有结构)
```javascript
// 扩展的评审文档类型 (向后兼容)
const ReviewDocument = {
  // 保持现有字段
  id: '',
  title: '',           // 对应源项目的 name
  uploadDate: '',      // 对应源项目的 uploadTime
  fileSize: '',        // 对应源项目的 size
  status: '',          // 保持现有状态值
  progress: 0,
  issues: 0,
  fixed: 0,

  // 新增字段 (可选)
  version: '',
  fileName: '',
  productManager: '',
  architect: '',
  testManager: '',
  source: 'manual',    // 'manual' | 'ai' | 'self-review'
  submitter: '',
  hasNewVersion: false,
  isCurrentVersion: true,
  versions: []
}

// 评审统计类型
const ReviewStats = {
  pendingReviews: 0,
  passRate: 0,
  issueAdoptionRate: 0,
  ruleCount: 0
}

// 时间筛选类型
const TimeFilter = '1week' | '1month' | '3months' | '6months' | '1year'
```

### 2.3 状态管理 (React Hooks)
```javascript
// 主要状态变量 (使用JavaScript)
const [reviewDocuments, setReviewDocuments] = useState([])
const [reviewStats, setReviewStats] = useState({
  pendingReviews: 5,
  passRate: 85,
  issueAdoptionRate: 72,
  ruleCount: 24
})
const [timeFilter, setTimeFilter] = useState('1month')
const [expandedReviewDocuments, setExpandedReviewDocuments] = useState(new Set())
```

## 3. UI组件详细实现 (适配目标项目)

### 3.1 页面标题区域
```javascript
// src/components/PRDEvaluation/components/PageHeader.js
import React from 'react';

const PageHeader = ({ timeFilter, onTimeFilterChange }) => {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-2">
        {/* 使用简单的SVG图标或文字图标替代 */}
        <div className="w-6 h-6 text-orange-500">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M21 6h-2l-1-2H6L5 6H3a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1h18a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1z"/>
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-gray-900">评审中心</h2>
      </div>
      <div className="flex items-center space-x-2">
        <div className="w-4 h-4 text-gray-500">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
          </svg>
        </div>
        <select
          value={timeFilter}
          onChange={(e) => onTimeFilterChange(e.target.value)}
          className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
        >
          <option value="1week">最近一周</option>
          <option value="1month">最近一个月</option>
          <option value="3months">最近三个月</option>
          <option value="6months">最近半年</option>
          <option value="1year">最近一年</option>
        </select>
      </div>
    </div>
  );
};

export default PageHeader;
```

### 3.2 统计面板组件
```javascript
// src/components/PRDEvaluation/components/StatsPanel.js
import React from 'react';

const StatsPanel = ({ stats }) => {
  const statItems = [
    {
      label: "待评审文档",
      value: stats.pendingReviews,
      bgColor: "bg-orange-100",
      iconColor: "text-orange-600",
      icon: (
        <svg viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
        </svg>
      )
    },
    {
      label: "评审通过率",
      value: `${stats.passRate}%`,
      bgColor: "bg-green-100",
      iconColor: "text-green-600",
      icon: (
        <svg viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
          <path d="M16,6L18.29,8.29L13.41,13.17L9.41,9.17L2,16.59L3.41,18L9.41,12L13.41,16L19.71,9.71L22,12V6H16Z"/>
        </svg>
      )
    },
    {
      label: "问题采纳率",
      value: `${stats.issueAdoptionRate}%`,
      bgColor: "bg-blue-100",
      iconColor: "text-blue-600",
      icon: (
        <svg viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
          <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
        </svg>
      )
    },
    {
      label: "评估规则数",
      value: stats.ruleCount,
      bgColor: "bg-purple-100",
      iconColor: "text-purple-600",
      icon: (
        <svg viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
          <path d="M22,21H2V3H4V19H6V10H10V19H12V6H16V19H18V14H22V21Z"/>
        </svg>
      )
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statItems.map((item, index) => (
        <div key={index} className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{item.label}</p>
                <p className="text-2xl font-bold text-gray-900">{item.value}</p>
              </div>
              <div className={`w-8 h-8 ${item.bgColor} rounded-full flex items-center justify-center`}>
                <div className={item.iconColor}>
                  {item.icon}
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default StatsPanel;
```

### 3.3 评审列表组件
```typescript
// components/review-center/ReviewList.tsx
interface ReviewListProps {
  documents: ReviewDocument[]
  expandedDocuments: Set<string>
  onToggleExpansion: (id: string) => void
  onStartReview: (documentId: string) => void
  onContinueReview: (documentId: string) => void
  onViewResults: (documentId: string) => void
  onRetryReview: (documentId: string) => void
  onUploadDocument: () => void
  onImportFromEditor: () => void
}

export function ReviewList({
  documents,
  expandedDocuments,
  onToggleExpansion,
  onStartReview,
  onContinueReview,
  onViewResults,
  onRetryReview,
  onUploadDocument,
  onImportFromEditor
}: ReviewListProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>评审列表</span>
          <div className="flex space-x-2">
            <Button 
              className="bg-orange-500 hover:bg-orange-600" 
              size="sm" 
              onClick={onUploadDocument}
            >
              <Upload className="w-4 h-4 mr-2" />
              上传文档
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onImportFromEditor}
            >
              <FileText className="w-4 h-4 mr-2" />
              从AI PRD编辑器导入
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {documents.length === 0 ? (
          <EmptyState />
        ) : (
          <DocumentTable 
            documents={documents}
            expandedDocuments={expandedDocuments}
            onToggleExpansion={onToggleExpansion}
            onStartReview={onStartReview}
            onContinueReview={onContinueReview}
            onViewResults={onViewResults}
            onRetryReview={onRetryReview}
          />
        )}
      </CardContent>
    </Card>
  )
}
```

### 3.4 空状态组件
```typescript
// components/review-center/EmptyState.tsx
export function EmptyState() {
  return (
    <div className="text-center py-8">
      <MessageSquare className="w-12 h-12 mx-auto text-gray-400 mb-4" />
      <p className="text-gray-500">暂无需要评审的文档</p>
    </div>
  )
}
```

## 4. 表格组件详细实现

### 4.1 文档表格组件
```typescript
// components/review-center/DocumentTable.tsx
interface DocumentTableProps {
  documents: ReviewDocument[]
  expandedDocuments: Set<string>
  onToggleExpansion: (id: string) => void
  onStartReview: (documentId: string) => void
  onContinueReview: (documentId: string) => void
  onViewResults: (documentId: string) => void
  onRetryReview: (documentId: string) => void
}

export function DocumentTable({
  documents,
  expandedDocuments,
  onToggleExpansion,
  onStartReview,
  onContinueReview,
  onViewResults,
  onRetryReview
}: DocumentTableProps) {
  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-1/4">文档名称</TableHead>
            <TableHead className="w-20">来源</TableHead>
            <TableHead className="w-24">状态</TableHead>
            <TableHead className="w-20">问题数</TableHead>
            <TableHead className="w-24">修复问题数</TableHead>
            <TableHead className="w-24">产品经理</TableHead>
            <TableHead className="w-24">架构师</TableHead>
            <TableHead className="w-24">测试经理</TableHead>
            <TableHead className="w-24">提交人</TableHead>
            <TableHead className="w-28">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {documents.map((document) => (
            <DocumentRowGroup
              key={document.id}
              document={document}
              isExpanded={expandedDocuments.has(document.id)}
              onToggleExpansion={onToggleExpansion}
              onStartReview={onStartReview}
              onContinueReview={onContinueReview}
              onViewResults={onViewResults}
              onRetryReview={onRetryReview}
            />
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
```

### 4.2 文档行组件组
```typescript
// components/review-center/DocumentRowGroup.tsx
interface DocumentRowGroupProps {
  document: ReviewDocument
  isExpanded: boolean
  onToggleExpansion: (id: string) => void
  onStartReview: (documentId: string) => void
  onContinueReview: (documentId: string) => void
  onViewResults: (documentId: string) => void
  onRetryReview: (documentId: string) => void
}

export function DocumentRowGroup({
  document,
  isExpanded,
  onToggleExpansion,
  onStartReview,
  onContinueReview,
  onViewResults,
  onRetryReview
}: DocumentRowGroupProps) {
  const hasMultipleVersions = document.versions && document.versions.length > 0

  return (
    <React.Fragment>
      {/* 主文档行 */}
      <TableRow>
        <TableCell className="font-medium">
          <div className="flex items-center space-x-2">
            {hasMultipleVersions && (
              <button
                onClick={() => onToggleExpansion(document.id)}
                className="cursor-pointer hover:text-blue-600"
              >
                <ChevronDown className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
              </button>
            )}
            <span>{document.name}</span>
            <Badge variant="outline" className="text-xs">
              {document.version}
            </Badge>
            {hasMultipleVersions && (
              <Badge variant="secondary" className="text-xs">
                {document.versions.length + 1} 版本
              </Badge>
            )}
          </div>
        </TableCell>
        <TableCell>
          <SourceIcon source={document.source} />
        </TableCell>
        <TableCell>
          <StatusBadge status={document.status} />
        </TableCell>
        <TableCell className="text-sm">{document.issues || 0}</TableCell>
        <TableCell className="text-sm">
          {document.fixed !== undefined ? (
            <span className="text-green-600 font-medium">{document.fixed}</span>
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </TableCell>
        <TableCell className="text-sm">{document.productManager}</TableCell>
        <TableCell className="text-sm">{document.architect}</TableCell>
        <TableCell className="text-sm">{document.testManager}</TableCell>
        <TableCell className="text-sm">{document.submitter}</TableCell>
        <TableCell>
          <ActionButtons
            document={document}
            onStartReview={onStartReview}
            onContinueReview={onContinueReview}
            onViewResults={onViewResults}
            onRetryReview={onRetryReview}
          />
        </TableCell>
      </TableRow>

      {/* 版本历史行 */}
      {isExpanded && hasMultipleVersions && document.versions.map((version) => (
        <TableRow key={version.id} className="bg-gray-50">
          <TableCell className="pl-8">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <span>└─ {version.fileName}</span>
              <Badge variant="outline" className="text-xs">
                {version.version}
              </Badge>
            </div>
          </TableCell>
          <TableCell>
            <SourceIcon source={version.source} />
          </TableCell>
          <TableCell>
            <StatusBadge status={version.status} />
          </TableCell>
          <TableCell className="text-sm">{version.issues || 0}</TableCell>
          <TableCell className="text-sm">
            {version.fixed !== undefined ? (
              <span className="text-green-600 font-medium">{version.fixed}</span>
            ) : (
              <span className="text-gray-400">-</span>
            )}
          </TableCell>
          <TableCell className="text-sm">{version.productManager}</TableCell>
          <TableCell className="text-sm">{version.architect}</TableCell>
          <TableCell className="text-sm">{version.testManager}</TableCell>
          <TableCell className="text-sm">{version.submitter}</TableCell>
          <TableCell>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onViewResults(version.id)}
              className="text-blue-600"
            >
              查看
            </Button>
          </TableCell>
        </TableRow>
      ))}
    </React.Fragment>
  )
}
```

### 4.3 来源图标组件
```typescript
// components/review-center/SourceIcon.tsx
interface SourceIconProps {
  source: 'manual' | 'ai' | 'self-review'
}

export function SourceIcon({ source }: SourceIconProps) {
  const sourceConfig = {
    ai: {
      icon: Bot,
      color: "text-blue-500",
      bgColor: "text-blue-600",
      label: "AI"
    },
    manual: {
      icon: Upload,
      color: "text-green-500",
      bgColor: "text-green-600",
      label: "上传"
    },
    'self-review': {
      icon: Search,
      color: "text-purple-500",
      bgColor: "text-purple-600",
      label: "自评"
    }
  }

  const config = sourceConfig[source]
  const IconComponent = config.icon

  return (
    <div className="flex items-center space-x-1">
      <IconComponent className={`w-4 h-4 ${config.color}`} />
      <span className={`text-xs ${config.bgColor}`}>{config.label}</span>
    </div>
  )
}
```

### 4.4 状态徽章组件
```typescript
// components/review-center/StatusBadge.tsx
interface StatusBadgeProps {
  status: 'uploaded' | 'analyzing' | 'reviewing' | 'completed' | 'needs-revision'
}

export function StatusBadge({ status }: StatusBadgeProps) {
  const statusConfig = {
    completed: {
      variant: "default" as const,
      label: "已通过"
    },
    reviewing: {
      variant: "secondary" as const,
      label: "评审中"
    },
    analyzing: {
      variant: "secondary" as const,
      label: "分析中"
    },
    'needs-revision': {
      variant: "destructive" as const,
      label: "需修改"
    },
    uploaded: {
      variant: "outline" as const,
      label: "待评审"
    }
  }

  const config = statusConfig[status]

  return (
    <Badge variant={config.variant}>
      {config.label}
    </Badge>
  )
}
```

### 4.5 操作按钮组件
```typescript
// components/review-center/ActionButtons.tsx
interface ActionButtonsProps {
  document: ReviewDocument
  onStartReview: (documentId: string) => void
  onContinueReview: (documentId: string) => void
  onViewResults: (documentId: string) => void
  onRetryReview: (documentId: string) => void
}

export function ActionButtons({
  document,
  onStartReview,
  onContinueReview,
  onViewResults,
  onRetryReview
}: ActionButtonsProps) {
  const buttonConfig = {
    uploaded: {
      label: "开始评审",
      onClick: () => onStartReview(document.id),
      className: "text-blue-600"
    },
    reviewing: {
      label: "继续评审",
      onClick: () => onContinueReview(document.id),
      className: "text-orange-600"
    },
    completed: {
      label: "查看结果",
      onClick: () => onViewResults(document.id),
      className: "text-green-600"
    },
    'needs-revision': {
      label: "重新评审",
      onClick: () => onRetryReview(document.id),
      className: "text-red-600"
    }
  }

  const config = buttonConfig[document.status]

  if (!config) return null

  return (
    <div className="flex space-x-1">
      <Button
        variant="ghost"
        size="sm"
        onClick={config.onClick}
        className={config.className}
      >
        {config.label}
      </Button>
    </div>
  )
}
```

## 5. 主容器组件实现

### 5.1 评审中心Tab主组件
```typescript
// components/review-center/ReviewCenterTab.tsx
export function ReviewCenterTab() {
  // 状态管理
  const [reviewDocuments, setReviewDocuments] = useState<ReviewDocument[]>([])
  const [reviewStats, setReviewStats] = useState<ReviewStats>({
    pendingReviews: 5,
    passRate: 85,
    issueAdoptionRate: 72,
    ruleCount: 24
  })
  const [timeFilter, setTimeFilter] = useState<TimeFilter>('1month')
  const [expandedReviewDocuments, setExpandedReviewDocuments] = useState<Set<string>>(new Set())

  // 初始化数据
  useEffect(() => {
    initializeReviewData()
  }, [])

  // 初始化评审数据
  const initializeReviewData = () => {
    setReviewDocuments([
      {
        id: "review-doc1",
        name: "智能驾驶系统PRD",
        version: "v1.3.0",
        fileName: "智能驾驶系统PRD_v1.3.0.pdf",
        size: "2.5 MB",
        uploadTime: "2025-01-10 16:45",
        status: "reviewing",
        issues: 12,
        fixed: 8,
        productManager: "张三",
        architect: "李四",
        testManager: "王五",
        source: "ai",
        submitter: "张三",
        hasNewVersion: true,
        isCurrentVersion: true,
        versions: [
          {
            id: "review-doc1-v1",
            name: "智能驾驶系统PRD",
            version: "v1.0.0",
            fileName: "智能驾驶系统PRD_v1.0.0.pdf",
            size: "2.1 MB",
            uploadTime: "2024-12-15 14:30",
            status: "completed",
            issues: 18,
            fixed: 15,
            productManager: "张三",
            architect: "李四",
            testManager: "王五",
            source: "ai",
            submitter: "张三"
          }
        ]
      },
      {
        id: "review-doc2",
        name: "用户体验优化方案",
        version: "v2.1.0",
        fileName: "用户体验优化方案_v2.1.0.pdf",
        size: "1.8 MB",
        uploadTime: "2025-01-08 14:20",
        status: "completed",
        issues: 8,
        fixed: 8,
        productManager: "李明",
        architect: "王强",
        testManager: "赵敏",
        source: "manual",
        submitter: "李明"
      },
      {
        id: "review-doc3",
        name: "API接口设计文档",
        version: "v1.0.0",
        fileName: "API接口设计文档_v1.0.0.pdf",
        size: "3.2 MB",
        uploadTime: "2025-01-05 09:30",
        status: "needs-revision",
        issues: 15,
        fixed: 3,
        productManager: "陈华",
        architect: "刘洋",
        testManager: "孙丽",
        source: "self-review",
        submitter: "陈华"
      }
    ])
  }

  // 事件处理函数
  const handleToggleExpansion = (documentId: string) => {
    setExpandedReviewDocuments(prev => {
      const newSet = new Set(prev)
      if (newSet.has(documentId)) {
        newSet.delete(documentId)
      } else {
        newSet.add(documentId)
      }
      return newSet
    })
  }

  const handleStartReview = (documentId: string) => {
    // 跳转到智能分析Tab
    console.log('开始评审:', documentId)
  }

  const handleContinueReview = (documentId: string) => {
    // 跳转到评审确认Tab
    console.log('继续评审:', documentId)
  }

  const handleViewResults = (documentId: string) => {
    // 跳转到评估报告Tab
    console.log('查看结果:', documentId)
  }

  const handleRetryReview = (documentId: string) => {
    // 重新开始评审
    console.log('重新评审:', documentId)
  }

  const handleUploadDocument = () => {
    // 触发文件上传
    console.log('上传文档')
  }

  const handleImportFromEditor = () => {
    // 从AI PRD编辑器导入
    window.open('/ai-prd-editor', '_blank')
  }

  return (
    <div className="space-y-6">
      <PageHeader
        timeFilter={timeFilter}
        onTimeFilterChange={setTimeFilter}
      />

      <StatsPanel stats={reviewStats} />

      <ReviewList
        documents={reviewDocuments}
        expandedDocuments={expandedReviewDocuments}
        onToggleExpansion={handleToggleExpansion}
        onStartReview={handleStartReview}
        onContinueReview={handleContinueReview}
        onViewResults={handleViewResults}
        onRetryReview={handleRetryReview}
        onUploadDocument={handleUploadDocument}
        onImportFromEditor={handleImportFromEditor}
      />
    </div>
  )
}
```

## 6. 样式配置与依赖

### 6.1 目标项目现有依赖 (无需额外安装)
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-scripts": "5.0.1",
    "tailwindcss": "^3.3.0",
    "postcss": "^8.4.24",
    "autoprefixer": "^10.4.14",
    "marked": "^15.0.12",
    "dompurify": "^3.2.6",
    "lodash.debounce": "^4.0.8"
  }
}
```

### 6.2 图标实现方案 (使用内联SVG)
```javascript
// src/components/PRDEvaluation/components/Icons.js
export const Icons = {
  MessageSquare: (props) => (
    <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path d="M21 6h-2l-1-2H6L5 6H3a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1h18a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1z"/>
    </svg>
  ),
  Calendar: (props) => (
    <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
    </svg>
  ),
  FileText: (props) => (
    <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
    </svg>
  ),
  Upload: (props) => (
    <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
    </svg>
  ),
  ChevronDown: (props) => (
    <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"/>
    </svg>
  ),
  Bot: (props) => (
    <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path d="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21V20A2,2 0 0,1 19,22H5A2,2 0 0,1 3,20V19H2A1,1 0 0,1 1,18V15A1,1 0 0,1 2,14H3A7,7 0 0,1 10,7H11V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2M7.5,13A2.5,2.5 0 0,0 5,15.5A2.5,2.5 0 0,0 7.5,18A2.5,2.5 0 0,0 10,15.5A2.5,2.5 0 0,0 7.5,13M16.5,13A2.5,2.5 0 0,0 14,15.5A2.5,2.5 0 0,0 16.5,18A2.5,2.5 0 0,0 19,15.5A2.5,2.5 0 0,0 16.5,13Z"/>
    </svg>
  ),
  Search: (props) => (
    <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"/>
    </svg>
  )
};
```

### 6.3 UI组件实现 (无需外部依赖)
```javascript
// src/components/PRDEvaluation/components/UIComponents.js

// 按钮组件
export const Button = ({
  children,
  onClick,
  className = '',
  variant = 'default',
  size = 'default',
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';

  const variants = {
    default: 'bg-orange-500 text-white hover:bg-orange-600 focus:ring-orange-500',
    outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-orange-500',
    ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-orange-500'
  };

  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    default: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  return (
    <button
      className={`${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`}
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  );
};

// 徽章组件
export const Badge = ({ children, variant = 'default', className = '' }) => {
  const variants = {
    default: 'bg-orange-100 text-orange-800',
    secondary: 'bg-gray-100 text-gray-800',
    outline: 'border border-gray-300 text-gray-700',
    destructive: 'bg-red-100 text-red-800'
  };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variants[variant]} ${className}`}>
      {children}
    </span>
  );
};

// 表格组件
export const Table = ({ children, className = '' }) => (
  <div className="relative w-full overflow-auto">
    <table className={`w-full caption-bottom text-sm ${className}`}>
      {children}
    </table>
  </div>
);

export const TableHeader = ({ children }) => (
  <thead className="[&_tr]:border-b">{children}</thead>
);

export const TableBody = ({ children }) => (
  <tbody className="[&_tr:last-child]:border-0">{children}</tbody>
);

export const TableRow = ({ children, className = '' }) => (
  <tr className={`border-b transition-colors hover:bg-gray-50 ${className}`}>
    {children}
  </tr>
);

export const TableHead = ({ children, className = '' }) => (
  <th className={`h-12 px-4 text-left align-middle font-medium text-gray-500 ${className}`}>
    {children}
  </th>
);

export const TableCell = ({ children, className = '' }) => (
  <td className={`p-4 align-middle ${className}`}>
    {children}
  </td>
);
```

## 7. 目标项目文件结构组织

### 7.1 目标项目文件结构 (适配现有结构)
```
src/
├── components/
│   └── PRDEvaluation/
│       ├── PRDUploadTab.js              # 主文件 (需要替换)
│       └── components/
│           ├── PageHeader.js            # 页面标题组件
│           ├── StatsPanel.js            # 统计面板组件
│           ├── ReviewList.js            # 评审列表组件
│           ├── DocumentTable.js         # 文档表格组件
│           ├── DocumentRowGroup.js      # 文档行组件组
│           ├── SourceIcon.js            # 来源图标组件
│           ├── StatusBadge.js           # 状态徽章组件
│           ├── ActionButtons.js         # 操作按钮组件
│           ├── EmptyState.js            # 空状态组件
│           ├── Icons.js                 # 图标组件
│           └── UIComponents.js          # UI基础组件
├── hooks/
│   └── usePRDUpload.js                  # 现有Hook (可能需要扩展)
├── types/
│   └── prdEvaluation.js                 # 现有类型定义 (需要扩展)
└── utils/
    └── dataTransform.js                 # 数据转换工具 (新增)
```

### 7.2 主文件实现 (PRDUploadTab.js)
```javascript
// src/components/PRDEvaluation/PRDUploadTab.js
import React, { useState, useEffect } from 'react';
import PageHeader from './components/PageHeader';
import StatsPanel from './components/StatsPanel';
import ReviewList from './components/ReviewList';
import { transformToReviewDocument, transformFromReviewDocument } from '../../utils/dataTransform';

const PRDUploadTab = ({
  uploadedDocuments,      // 保持现有接口
  setUploadedDocuments,   // 保持现有接口
  handleStartAnalysis     // 保持现有接口
}) => {
  // 新增状态 (内部使用)
  const [reviewStats, setReviewStats] = useState({
    pendingReviews: 5,
    passRate: 85,
    issueAdoptionRate: 72,
    ruleCount: 24
  });
  const [timeFilter, setTimeFilter] = useState('1month');
  const [expandedReviewDocuments, setExpandedReviewDocuments] = useState(new Set());

  // 转换现有数据格式为新格式
  const reviewDocuments = uploadedDocuments.map(transformToReviewDocument);

  // 事件处理函数
  const handleToggleExpansion = (documentId) => {
    setExpandedReviewDocuments(prev => {
      const newSet = new Set(prev);
      if (newSet.has(documentId)) {
        newSet.delete(documentId);
      } else {
        newSet.add(documentId);
      }
      return newSet;
    });
  };

  const handleStartReview = (documentId) => {
    // 调用现有的开始分析函数
    handleStartAnalysis(documentId);
  };

  const handleContinueReview = (documentId) => {
    // 跳转到评审确认Tab的逻辑
    console.log('继续评审:', documentId);
  };

  const handleViewResults = (documentId) => {
    // 跳转到评估报告Tab的逻辑
    console.log('查看结果:', documentId);
  };

  const handleRetryReview = (documentId) => {
    // 重新开始评审的逻辑
    console.log('重新评审:', documentId);
  };

  const handleUploadDocument = () => {
    // 触发文件上传的逻辑 (可能需要调用现有的上传函数)
    console.log('上传文档');
  };

  const handleImportFromEditor = () => {
    // 从AI PRD编辑器导入的逻辑
    console.log('从编辑器导入');
  };

  return (
    <div className="space-y-6">
      <PageHeader
        timeFilter={timeFilter}
        onTimeFilterChange={setTimeFilter}
      />

      <StatsPanel stats={reviewStats} />

      <ReviewList
        documents={reviewDocuments}
        expandedDocuments={expandedReviewDocuments}
        onToggleExpansion={handleToggleExpansion}
        onStartReview={handleStartReview}
        onContinueReview={handleContinueReview}
        onViewResults={handleViewResults}
        onRetryReview={handleRetryReview}
        onUploadDocument={handleUploadDocument}
        onImportFromEditor={handleImportFromEditor}
      />
    </div>
  );
};

export default PRDUploadTab;
```

### 7.3 数据转换工具
```javascript
// src/utils/dataTransform.js

// 将现有文档格式转换为新的评审文档格式
export const transformToReviewDocument = (existingDoc) => {
  return {
    // 保持现有字段
    id: existingDoc.id,
    title: existingDoc.title,
    uploadDate: existingDoc.uploadDate,
    fileSize: existingDoc.fileSize,
    status: existingDoc.status,
    progress: existingDoc.progress,
    issues: existingDoc.issues,
    fixed: existingDoc.fixed,

    // 新增字段 (使用默认值或从现有数据推导)
    name: existingDoc.title,
    version: 'v1.0.0',
    fileName: `${existingDoc.title}.pdf`,
    size: existingDoc.fileSize,
    uploadTime: existingDoc.uploadDate,
    productManager: '产品经理',
    architect: '架构师',
    testManager: '测试经理',
    source: 'manual',
    submitter: '提交人',
    hasNewVersion: false,
    isCurrentVersion: true,
    versions: []
  };
};

// 将新的评审文档格式转换回现有格式 (如果需要)
export const transformFromReviewDocument = (reviewDoc) => {
  return {
    id: reviewDoc.id,
    title: reviewDoc.title,
    uploadDate: reviewDoc.uploadDate,
    fileSize: reviewDoc.fileSize,
    status: reviewDoc.status,
    progress: reviewDoc.progress,
    issues: reviewDoc.issues,
    fixed: reviewDoc.fixed
  };
};
```

## 8. 实施步骤 (适配目标项目)

### 8.1 第一阶段：环境准备和备份 (0.5天)
- [ ] 备份现有 `PRDUploadTab.js` 文件
- [ ] 创建新的组件文件夹结构 `src/components/PRDEvaluation/components/`
- [ ] 创建数据转换工具文件 `src/utils/dataTransform.js`
- [ ] 确认现有Tailwind CSS配置正常

### 8.2 第二阶段：基础UI组件开发 (1天)
- [ ] 创建 `UIComponents.js` (Button, Badge, Table等)
- [ ] 创建 `Icons.js` (所有需要的SVG图标)
- [ ] 开发 `PageHeader.js` 组件
- [ ] 开发 `StatsPanel.js` 组件
- [ ] 开发 `EmptyState.js` 组件

### 8.3 第三阶段：表格组件开发 (1.5天)
- [ ] 开发 `DocumentTable.js` 组件
- [ ] 开发 `DocumentRowGroup.js` 组件
- [ ] 开发 `SourceIcon.js` 组件
- [ ] 开发 `StatusBadge.js` 组件
- [ ] 开发 `ActionButtons.js` 组件
- [ ] 开发 `ReviewList.js` 组件

### 8.4 第四阶段：主组件集成 (1天)
- [ ] 实现数据转换函数 `dataTransform.js`
- [ ] 重写 `PRDUploadTab.js` 主组件
- [ ] 保持现有接口兼容性
- [ ] 集成所有子组件
- [ ] 实现状态管理和事件处理

### 8.5 第五阶段：测试与优化 (1天)
- [ ] 功能测试 (确保所有按钮和交互正常)
- [ ] 接口兼容性测试 (确保与其他Tab正常交互)
- [ ] 样式一致性检查 (与源项目对比)
- [ ] 响应式布局测试
- [ ] 性能优化和错误处理

### 8.6 第六阶段：部署与验证 (0.5天)
- [ ] 在目标项目中替换原有文件
- [ ] 启动项目验证功能正常
- [ ] 与其他Tab的集成测试
- [ ] 最终样式和交互调整

## 9. 验收标准

### 9.1 功能验收
- [ ] 页面标题和时间筛选器正常工作
- [ ] 统计面板数据正确显示
- [ ] 评审列表表格完整展示
- [ ] 文档行展开/收起功能正常
- [ ] 所有操作按钮响应正确
- [ ] 空状态正确显示

### 9.2 UI验收
- [ ] 与源项目像素级一致
- [ ] 颜色、字体、间距完全匹配
- [ ] 图标样式和尺寸一致
- [ ] 响应式布局正确

### 9.3 交互验收
- [ ] 鼠标悬停效果正确
- [ ] 点击响应及时
- [ ] 动画效果流畅
- [ ] 键盘导航支持

## 10. 使用示例和集成指南

### 10.1 在目标项目中的使用方式
```javascript
// src/components/PRDEvaluation/index.js (现有Tab管理文件)
import PRDUploadTab from './PRDUploadTab';

// 在Tab配置中使用 (保持现有方式)
const tabs = [
  {
    id: 'upload',
    label: '评审中心',
    component: PRDUploadTab
  },
  // 其他Tab...
];
```

### 10.2 与现有Hook的集成
```javascript
// 在PRDUploadTab.js中使用现有Hook
import { usePRDUpload } from '../../hooks/usePRDUpload';

const PRDUploadTab = ({ uploadedDocuments, setUploadedDocuments, handleStartAnalysis }) => {
  // 使用现有Hook
  const {
    isDragging,
    setIsDragging,
    uploadProgress,
    isUploading,
    handleFileUpload,
    getStatusText
  } = usePRDUpload();

  // 新增的评审中心功能...

  return (
    // 组件实现
  );
};
```

### 10.3 样式主题适配
```javascript
// 确保使用目标项目的橙色主题
const themeClasses = {
  primary: 'bg-orange-500 hover:bg-orange-600 text-white',
  primaryOutline: 'border-orange-500 text-orange-500 hover:bg-orange-50',
  focus: 'focus:ring-orange-500 focus:border-orange-500'
};
```

## 11. 迁移检查清单

### 11.1 文件创建检查清单
- [ ] `src/components/PRDEvaluation/components/PageHeader.js`
- [ ] `src/components/PRDEvaluation/components/StatsPanel.js`
- [ ] `src/components/PRDEvaluation/components/ReviewList.js`
- [ ] `src/components/PRDEvaluation/components/DocumentTable.js`
- [ ] `src/components/PRDEvaluation/components/DocumentRowGroup.js`
- [ ] `src/components/PRDEvaluation/components/SourceIcon.js`
- [ ] `src/components/PRDEvaluation/components/StatusBadge.js`
- [ ] `src/components/PRDEvaluation/components/ActionButtons.js`
- [ ] `src/components/PRDEvaluation/components/EmptyState.js`
- [ ] `src/components/PRDEvaluation/components/Icons.js`
- [ ] `src/components/PRDEvaluation/components/UIComponents.js`
- [ ] `src/utils/dataTransform.js`
- [ ] 备份原有 `src/components/PRDEvaluation/PRDUploadTab.js`

### 11.2 功能验证检查清单
- [ ] 页面标题和时间筛选器显示正常
- [ ] 统计面板4个卡片数据显示正确
- [ ] 评审列表表格完整展示
- [ ] 文档行展开/收起功能正常
- [ ] 所有操作按钮点击响应正确
- [ ] 空状态正确显示
- [ ] 与现有Tab切换正常
- [ ] 现有接口 `uploadedDocuments` 数据正常传递
- [ ] `handleStartAnalysis` 函数调用正常

### 11.3 样式验证检查清单
- [ ] 与源项目像素级一致
- [ ] 橙色主题色彩正确应用
- [ ] 字体大小和行高一致
- [ ] 间距和布局完全匹配
- [ ] 图标样式和尺寸一致
- [ ] 响应式布局在不同屏幕尺寸下正常
- [ ] 鼠标悬停效果正确
- [ ] 动画过渡效果流畅

## 12. 故障排除指南

### 12.1 常见问题及解决方案

#### 问题1: 样式不生效
```bash
# 确保Tailwind CSS正确编译
npm run build:css
# 或重启开发服务器
npm start
```

#### 问题2: 组件导入错误
```javascript
// 确保使用相对路径导入
import PageHeader from './components/PageHeader';
// 而不是绝对路径
```

#### 问题3: 数据格式不匹配
```javascript
// 检查数据转换函数
console.log('原始数据:', uploadedDocuments);
console.log('转换后数据:', reviewDocuments);
```

#### 问题4: 事件处理不响应
```javascript
// 确保事件处理函数正确绑定
const handleClick = useCallback((id) => {
  console.log('点击事件:', id);
}, []);
```

### 12.2 调试技巧
- 使用 `console.log` 检查数据流
- 使用浏览器开发者工具检查样式
- 逐个组件测试，确保每个组件独立工作正常
- 对比源项目和目标项目的DOM结构

---

**实施负责人**: Jason
**目标项目路径**: `/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/req-ai-edit-agent`
**预计完成时间**: 5.5个工作日
**技术栈**: React 18 + JavaScript + Tailwind CSS
**最后更新**: 2025-07-16
**文档版本**: v2.0 (目标项目适配版)
