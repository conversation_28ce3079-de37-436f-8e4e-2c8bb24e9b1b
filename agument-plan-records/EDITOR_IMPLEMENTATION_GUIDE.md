# PRD AI编辑器 - 编辑器模块完整实现方案

## 概述

本文档总结了PRD AI编辑器项目中编辑器模块的完整实现方案，包含功能架构、技术方案和部署配置，可作为复用模板用于其他Markdown编辑器项目。

## 1. 核心功能架构

### 双编辑器模式

- **主编辑器：Milkdown富文本编辑器**
  - 支持Markdown格式，具备完整格式化功能
  - 基于@milkdown/core架构，插件化设计
  - 提供WYSIWYG编辑体验

- **备用编辑器：SimpleEditor纯文本编辑器**
  - 原生textarea实现，确保可用性
  - 智能Tab键缩进支持
  - 错误降级机制的安全保障

- **智能切换机制**
  - 检测Milkdown加载失败时自动降级到SimpleEditor
  - 用户友好的错误提示
  - 无缝切换体验

### 内容状态管理

- **useContentState Hook**
  - 统一管理内容、大纲、修改状态、元数据
  - 初始化内容加载和持久化
  - 状态同步和更新机制

- **实时大纲生成**
  - 自动解析H1-H3标题生成导航结构
  - DOM元素匹配和ID生成
  - 平滑滚动和高亮效果

- **修改状态追踪**
  - 实时检测内容变化
  - 触发保存提示和状态更新
  - 防抖机制避免频繁更新

### 持久化存储

- **自动保存机制**
  - useAutoSave Hook，3秒防抖延迟
  - localStorage本地存储
  - 保存状态和错误处理

- **手动保存功能**
  - Ctrl+S快捷键支持
  - 立即保存和状态反馈
  - 保存成功/失败提示

- **元数据统计**
  - 字数、字符数、行数统计
  - 最后保存时间记录
  - 文档健康度指标

## 2. 技术实现方案

### 编辑器组件设计

#### MilkdownEditor: 富文本编辑器

```javascript
import React, { useRef, useEffect } from 'react';
import { Editor, rootCtx, defaultValueCtx } from '@milkdown/core';
import { commonmark } from '@milkdown/preset-commonmark';
import { nord } from '@milkdown/theme-nord';
import { listener, listenerCtx } from '@milkdown/plugin-listener';
import { history } from '@milkdown/plugin-history';
import { cursor } from '@milkdown/plugin-cursor';

const MilkdownEditor = ({ content, onChange, className, placeholder }) => {
  // 核心特性：
  // - 基于@milkdown/core架构
  // - 集成commonmark、nord主题、history、cursor插件
  // - 300ms防抖内容变更监听
  // - 错误边界处理，失败时降级到textarea
};
```

#### SimpleEditor: 简化编辑器

```javascript
import React, { useState, useRef, useEffect } from 'react';
import debounce from 'lodash.debounce';

const SimpleEditor = ({ content, onChange, className, placeholder }) => {
  // 核心特性：
  // - 原生textarea实现
  // - Tab键智能缩进支持（2空格）
  // - 防抖内容同步（300ms）
  // - 完整键盘交互支持
};
```

### Hook架构设计

#### useContentState: 内容状态管理

```javascript
const useContentState = (initialContent) => {
  return {
    content,           // 当前内容
    outline,           // 大纲结构
    isModified,        // 修改状态
    lastSaved,         // 最后保存时间
    metadata,          // 文档元数据
    updateContent,     // 更新内容函数
    saveContentManually, // 手动保存函数
    resetContent       // 重置内容函数
  };
};
```

#### useAutoSave: 自动保存机制

```javascript
const useAutoSave = (content, isModified, enabled = true, delay = 3000) => {
  return {
    isSaving,          // 保存中状态
    lastAutoSaved,     // 最后自动保存时间
    saveError,         // 保存错误信息
    clearError         // 清除错误函数
  };
};
```

#### useOutlineSync: 大纲导航同步

```javascript
const useOutlineSync = () => {
  return {
    activeSection,     // 当前活跃节点
    handleOutlineClick // 大纲点击处理函数
  };
};
```

### 样式主题化

#### 编辑器容器样式

```css
/* Milkdown编辑器容器 */
.milkdown-container {
  height: 100%;
  background: white;
  overflow: hidden;
}

.milkdown-container .milkdown {
  height: 100%;
  padding: 24px 32px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #374151;
}

/* 简单编辑器容器 */
.simple-editor-container {
  height: 100%;
  background: white;
  overflow: hidden;
}

.simple-editor-textarea {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  padding: 24px 32px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 16px;
  line-height: 1.6;
  color: #374151;
  tab-size: 2;
}
```

#### 主题适配

- **Nord主题集成**：使用@milkdown/theme-nord提供现代化外观
- **橙色品牌色系**：标题下划线、引用块边框使用#ff6b35
- **响应式设计**：移动端字体大小和间距调整
- **滚动条美化**：Webkit滚动条样式自定义

## 3. 集成接口设计

### 组件Props接口

```typescript
interface EditorProps {
  content: string;                    // 编辑器内容
  onChange: (newContent: string) => void; // 内容变更回调
  className?: string;                 // 样式类名
  placeholder?: string;               // 占位符文本
  readOnly?: boolean;                 // 只读模式(仅Milkdown)
}
```

### 存储工具接口

```javascript
// 存储操作
export const saveContent = async (content) => Promise<metadata>
export const loadContent = () => string | null
export const getMetadata = () => object | null
export const exportContent = (content, filename) => boolean
export const clearStorage = () => boolean

// 大纲解析
export const generateOutline = (content) => Array<OutlineItem>
export const findHeadingInDOM = (outline) => Array<OutlineItem>
export const scrollToHeading = (headingId) => void
```

## 4. 错误处理和降级

### 错误边界处理

```javascript
// 全局错误监听
useEffect(() => {
  const handleError = (error) => {
    console.error('编辑器错误:', error);
    if (error.message && error.message.includes('setEditorFactory')) {
      setEditorError(true); // 触发降级到SimpleEditor
    }
  };

  window.addEventListener('error', handleError);
  return () => window.removeEventListener('error', handleError);
}, []);

// 条件渲染
const renderEditor = () => {
  if (editorError) {
    return <SimpleEditor {...props} />;
  }
  return <MilkdownEditor {...props} onError={handleEditorError} />;
};
```

### 性能优化策略

- **防抖处理**：300ms延迟处理内容变更，避免频繁操作
- **懒加载**：大纲DOM检查延迟200ms执行，等待渲染完成
- **内存清理**：组件卸载时清理定时器和事件监听器
- **异步操作**：使用setTimeout进行非阻塞DOM操作

## 5. 部署配置要点

### 依赖包管理

```json
{
  "dependencies": {
    "@milkdown/core": "^7.13.1",
    "@milkdown/preset-commonmark": "^7.13.1",
    "@milkdown/theme-nord": "^7.13.1",
    "@milkdown/plugin-listener": "^7.13.1",
    "@milkdown/plugin-history": "^7.13.1",
    "@milkdown/plugin-cursor": "^7.13.1",
    "@milkdown/react": "^7.13.1",
    "lodash.debounce": "^4.0.8"
  }
}
```

### 快捷键支持

```javascript
// 全局快捷键处理
useEffect(() => {
  const handleKeyDown = (e) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 's':
          e.preventDefault();
          saveContentManually();    // 手动保存
          break;
        case '1':
          e.preventDefault();
          setMode('edit');          // 切换到编辑模式
          break;
        case '2':
          e.preventDefault();
          setMode('preview');       // 切换到预览模式
          break;
      }
    }
  };

  window.addEventListener('keydown', handleKeyDown);
  return () => window.removeEventListener('keydown', handleKeyDown);
}, [saveContentManually]);
```

### Tab键智能缩进（SimpleEditor）

```javascript
const handleKeyDown = (e) => {
  if (e.key === 'Tab') {
    e.preventDefault();
    const start = e.target.selectionStart;
    const end = e.target.selectionEnd;
    const newValue = value.substring(0, start) + '  ' + value.substring(end);
    setValue(newValue);
    debouncedOnChange(newValue);
    
    // 设置光标位置
    setTimeout(() => {
      e.target.selectionStart = e.target.selectionEnd = start + 2;
    }, 0);
  }
};
```

## 6. 使用示例

### 基础集成

```javascript
import React from 'react';
import MilkdownEditor from './components/Editor/MilkdownEditor';
import SimpleEditor from './components/Editor/SimpleEditor';
import useContentState from './hooks/useContentState';
import useAutoSave from './hooks/useAutoSave';

function App() {
  const {
    content,
    updateContent,
    saveContentManually
  } = useContentState('# 默认内容');

  const { isSaving } = useAutoSave(content, true);

  return (
    <div className="editor-container">
      <MilkdownEditor
        content={content}
        onChange={updateContent}
        className="h-full"
      />
      {isSaving && <div>保存中...</div>}
    </div>
  );
}
```

### 完整功能集成

```javascript
// 参考src/App.js中的完整实现
// 包含：模式切换、大纲导航、AI交互、状态管理等
```

## 7. 特色功能

### 智能大纲导航

- 自动解析Markdown标题生成大纲
- 点击大纲项平滑滚动到对应位置
- 高亮当前活跃节点
- 支持H1-H3标题层级

### 自动保存机制

- 3秒防抖延迟，避免频繁保存
- 错误处理和重试机制
- 保存状态实时反馈
- 手动保存快捷键支持

### 错误降级策略

- Milkdown加载失败自动切换到SimpleEditor
- 保持编辑功能完整可用
- 用户友好的状态提示
- 无缝切换体验

## 总结

这套编辑器实现方案提供了完整的Markdown编辑功能，包含：

- ✅ **双模式支持**：富文本 + 纯文本编辑器
- ✅ **自动保存**：防抖机制 + 本地存储
- ✅ **大纲导航**：智能解析 + 平滑滚动
- ✅ **错误处理**：自动降级 + 状态提示
- ✅ **性能优化**：防抖 + 懒加载 + 内存清理
- ✅ **企业级特性**：快捷键 + 导出 + 元数据统计

该方案具有高度的可复用性和扩展性，可直接应用于其他Markdown编辑器项目中，提供企业级的编辑体验。 