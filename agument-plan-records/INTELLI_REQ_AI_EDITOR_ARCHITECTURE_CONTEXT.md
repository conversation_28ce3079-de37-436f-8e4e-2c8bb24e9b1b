# Intelli-req AI Editor 完整架构与功能上下文文档

## 📋 项目概览

**项目名称**: Intelli-req AI Editor  
**版本**: v2.0  
**架构模式**: 前后端分离 + 多Agent协同  
**技术栈**: React + Express + Node.js + Milkdown  
**部署状态**: 开发环境运行中  
- 前端: http://localhost:3004  
- 后端: http://localhost:8000  

## 🏗️ 整体架构设计

### 三层架构模式

```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 (React SPA)                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 编辑器组件  │ │ 预览组件    │ │ AI卡片系统  │ │ 导航组件│ │
│  │ Milkdown   │ │ Markdown    │ │ 智能分析    │ │ 大纲树  │ │
│  │ SimpleEdit │ │ Renderer    │ │ 多轮对话    │ │ 定位    │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    服务层 (API Gateway)                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ HTTP Client │ │ Agent Client│ │ Mock Service│ │ WebSocket│ │
│  │ 请求管理    │ │ 服务发现    │ │ 开发模拟    │ │ 实时通信│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    后端层 (Express Server)                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ API Routes  │ │ Agent编排器 │ │ 任务队列    │ │ 中间件  │ │
│  │ RESTful API │ │ 智能调度    │ │ 优先级管理  │ │ 安全认证│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 核心功能特性

### 1. 智能编辑器系统

#### 双编辑器架构
- **主编辑器**: Milkdown (基于ProseMirror)
  - 富文本编辑体验
  - Markdown语法支持
  - 实时预览同步
  - 插件扩展能力

- **备用编辑器**: SimpleEditor (Textarea)
  - 容错机制保障
  - 纯文本编辑
  - 自动降级切换
  - 基础功能保证

#### 编辑器特性
- 自动保存机制 (useAutoSave)
- 内容状态管理 (useContentState)
- 大纲同步 (useOutlineSync)
- 快捷键支持 (Ctrl+S保存, Ctrl+1/2切换模式)

### 2. AI Agent协同系统

#### Agent类型定义
```javascript
const AGENT_TYPES = {
  ANALYSIS: 'analysis-agent',      // 需求分析Agent
  KNOWLEDGE: 'knowledge-agent',    // 知识补充Agent  
  CASES: 'cases-agent',           // 案例参考Agent
  INSPIRATION: 'inspiration-agent' // 灵感激发Agent
}
```

#### 分析功能类型
- **对话分析** (dialogue): 💬 橙色主题，互动式分析
- **灵感激发** (inspire): 💡 黄色主题，创意思维
- **评估分析** (evaluate): 📊 蓝色主题，专业评估
- **知识补充** (knowledge): 📚 绿色主题，标准规范
- **案例分析** (cases): 🔍 紫色主题，最佳实践

#### Agent协同模式
- **并行执行**: 多Agent同时处理，提高效率
- **串行执行**: 依赖关系处理，结果传递
- **智能调度**: 根据任务类型自动选择执行模式

### 3. AI卡片管理系统

#### 卡片生命周期
```
创建 → 加载中 → 分析完成 → 用户操作 → 历史记录
  ↓      ↓        ↓         ↓         ↓
pending → loading → completed → adopted/ignored → archived
```

#### 卡片功能特性
- **智能分类**: 5种分析类型，颜色主题区分
- **交互操作**: 采纳、忽略、删除、定位原文
- **多轮对话**: 对话类型支持连续交互
- **版本控制**: 内容刷新类型支持历史版本
- **状态管理**: 已读/未读状态，自动收起展开

#### 卡片区域分离
- **智能卡片区域**: 新建+待处理卡片
- **历史记录区域**: 已采纳+已忽略卡片
- **Agent状态区域**: 可用Agent监控

### 4. 文档导航系统

#### 大纲导航特性
- **自动解析**: Markdown标题层级识别
- **精确定位**: offsetTop计算，准确滚动
- **双向同步**: 大纲选中↔内容区域高亮
- **折叠展开**: 层级结构可视化管理

#### 定位搜索算法
```javascript
// 多层级搜索策略
1. 精确匹配: text.indexOf(searchText)
2. 模糊匹配: 去除多余空格后匹配
3. 关键词匹配: 提取关键词分别搜索
4. 分行匹配: 按行分割后逐行匹配
5. 中文关键词: 正则提取中文词汇匹配
```

## 🔧 技术实现细节

### 前端核心Hooks

#### useAICards - AI卡片状态管理
```javascript
const {
  cards,                    // 卡片列表
  isLoading,               // 加载状态
  availableAgents,         // 可用Agent列表
  handleAIInteraction,     // AI交互处理
  executeMultiAgentAnalysis, // 多Agent协同
  adoptCard,               // 采纳卡片
  ignoreCard,              // 忽略卡片
  deleteCard,              // 删除卡片
  markAsRead,              // 标记已读
  getStats                 // 统计信息
} = useAICards();
```

#### useContentState - 内容状态管理
```javascript
const {
  content,                 // 文档内容
  outline,                 // 大纲结构
  isModified,             // 修改状态
  lastSaved,              // 最后保存时间
  metadata,               // 文档元数据
  updateContent,          // 更新内容
  saveContentManually,    // 手动保存
  resetContent            // 重置内容
} = useContentState(defaultContent);
```

### 后端核心服务

#### AgentOrchestrator - Agent编排器
```javascript
class AgentOrchestrator {
  // Agent注册管理
  async registerAgent(agentConfig)
  async unregisterAgent(agentId)
  
  // 任务调度
  async createTask(taskConfig)
  async executeTask(task)
  async cancelTask(taskId)
  
  // 健康监控
  startHealthCheck()
  performHealthCheck()
  
  // 协同执行
  async executeCollaborativeTask(task)
  async executeSingleAgentTask(task)
}
```

## 📊 Mock数据支撑机制

### MockAgentService实现
- **开发环境**: 完全使用Mock数据，无需真实Agent
- **响应模拟**: 根据Agent类型生成不同风格的分析结果
- **延迟模拟**: 1-3秒随机延迟，模拟真实网络环境
- **错误模拟**: 10%概率失败，测试错误处理机制

### Mock响应示例
```javascript
// 分析Agent响应
{
  type: 'analysis',
  title: '需求评估分析', 
  content: '## 需求完整性评估\n...',
  confidence: 0.89,
  tags: ['需求分析', '评估', '改进建议']
}

// 知识Agent响应  
{
  type: 'knowledge',
  title: '行业知识补充',
  content: '## 相关标准与规范\n...',
  confidence: 0.91,
  tags: ['知识库', '标准规范', '最佳实践']
}
```

## 🎨 UI/UX设计特色

### 三栏式布局
- **左侧栏**: 文档控制+大纲导航 (w-72)
- **中间区**: 编辑器+预览切换 (flex-1)  
- **右侧栏**: AI助手+卡片管理 (w-96)

### 视觉设计系统
- **主色调**: Orange-500 (#f97316)
- **状态色彩**: 
  - 成功: Green-600
  - 警告: Yellow-500  
  - 错误: Red-500
  - 信息: Blue-500
- **字体**: font-sans, antialiased
- **圆角**: rounded-lg (8px)
- **阴影**: shadow-sm, shadow-md

### 交互体验
- **平滑动画**: transition-all duration-200
- **加载状态**: animate-pulse, skeleton效果
- **反馈机制**: hover状态, 点击反馈
- **响应式**: 移动端适配考虑

## 🔄 数据流架构

### 状态管理流程
```
用户操作 → Hook处理 → Service调用 → API请求 → Agent处理 → 结果返回 → 状态更新 → UI重渲染
```

### 关键数据流
1. **内容编辑流**: Editor → updateContent → useContentState → 自动保存
2. **AI分析流**: 选择文本 → handleAIInteraction → Agent调用 → 卡片创建
3. **导航同步流**: 大纲点击 → handleOutlineClick → 内容定位 → 状态同步

## 🚀 扩展性设计

### Agent插件化
- 标准化Agent接口
- 动态注册机制
- 能力声明系统
- 负载均衡支持

### 功能模块化
- 组件化设计
- Hook复用
- 服务分层
- 配置驱动

### 未来扩展方向
- 更多Agent类型
- 实时协作编辑
- 版本控制系统
- 插件市场
- 云端同步

## 🔧 关键技术实现

### 前端组件架构

#### 编辑器组件 (src/components/Editor/)
```javascript
// MilkdownEditor.js - 主编辑器
- 基于 @milkdown/react 构建
- 支持 CommonMark + GFM 语法
- 实时内容同步
- 错误边界处理

// SimpleEditor.js - 备用编辑器
- 纯 textarea 实现
- 容错机制保障
- 基础编辑功能
- 自动高度调整
```

#### 预览组件 (src/components/Preview/)
```javascript
// PreviewRenderer.js - 预览渲染器
- marked.js Markdown解析
- DOMPurify XSS防护
- AI交互层集成
- 文本选择检测

// AIInteractionLayer.js - AI交互层
- 文本选择监听
- 5种AI功能触发
- 浮动工具栏
- 多Agent协同入口
```

#### 通用组件 (src/components/Common/)
```javascript
// OutlineNavigation.js - 大纲导航
- 递归渲染树结构
- 点击定位功能
- 活跃状态同步
- 折叠展开控制

// SaveIndicator.js - 保存状态指示器
- 实时保存状态显示
- 错误提示处理
- 手动保存触发
- 最后保存时间

// ModeToggle.js - 模式切换
- 编辑/预览模式
- 快捷键支持
- 状态持久化
```

### 后端API设计

#### 路由结构 (backend-server/src/routes/)
```javascript
// agents.js - Agent管理API
GET    /api/agents/discovery     // 获取可用Agent列表
GET    /api/agents/:id/health    // Agent健康检查
POST   /api/agents/collaborate   // 多Agent协同执行
GET    /api/agents/stats         // Agent统计信息

// analysis.js - 分析服务API
POST   /api/analysis             // 执行单次分析
GET    /api/analysis/history     // 获取分析历史
GET    /api/analysis/:id         // 获取分析详情
POST   /api/analysis/:id/rerun   // 重新运行分析

// tasks.js - 任务管理API
POST   /api/tasks                // 创建任务
GET    /api/tasks/:id            // 获取任务状态
DELETE /api/tasks/:id            // 取消任务
POST   /api/tasks/batch          // 批量操作
```

#### 中间件系统 (backend-server/src/middleware/)
```javascript
// validation.js - 请求验证
- Joi schema验证
- 参数类型检查
- 必填字段验证
- 数据格式规范

// errorHandler.js - 错误处理
- 统一错误响应格式
- 错误日志记录
- 开发/生产环境区分
- 堆栈信息控制
```

### 状态管理架构

#### Hook设计模式
```javascript
// useContentState - 内容状态管理
- 内容变更检测
- 大纲自动解析
- 元数据计算
- 本地存储同步

// useAutoSave - 自动保存
- 防抖延迟保存
- 保存状态追踪
- 错误重试机制
- 保存历史记录

// useOutlineSync - 大纲同步
- 滚动位置监听
- 活跃章节计算
- 双向同步控制
- 平滑滚动动画

// useWebSocket - 实时通信
- WebSocket连接管理
- 心跳检测机制
- 断线重连逻辑
- 消息队列处理
```

## 📱 响应式设计

### 断点系统
```css
/* Tailwind CSS 断点 */
sm: 640px   // 小屏设备
md: 768px   // 平板设备
lg: 1024px  // 笔记本
xl: 1280px  // 桌面显示器
2xl: 1536px // 大屏显示器
```

### 布局适配策略
- **桌面端**: 三栏布局，完整功能展示
- **平板端**: 可折叠侧边栏，主要功能保留
- **手机端**: 单栏布局，底部导航切换

## 🔒 安全性设计

### 前端安全
- **XSS防护**: DOMPurify内容净化
- **CSRF防护**: Token验证机制
- **输入验证**: 客户端参数检查
- **内容安全**: CSP策略配置

### 后端安全
- **CORS配置**: 跨域请求控制
- **Rate Limiting**: 请求频率限制
- **Helmet.js**: 安全头部设置
- **输入验证**: Joi schema严格验证

## 🚀 性能优化

### 前端优化
- **代码分割**: React.lazy动态导入
- **虚拟滚动**: 大量卡片列表优化
- **防抖节流**: 用户输入事件优化
- **缓存策略**: API响应缓存

### 后端优化
- **连接池**: 数据库连接复用
- **任务队列**: 异步任务处理
- **负载均衡**: Agent请求分发
- **缓存层**: Redis缓存热点数据

## 🧪 测试策略

### 前端测试
- **单元测试**: Jest + React Testing Library
- **组件测试**: 用户交互模拟
- **Hook测试**: 自定义Hook验证
- **E2E测试**: Cypress端到端测试

### 后端测试
- **API测试**: Supertest HTTP测试
- **单元测试**: Jest服务层测试
- **集成测试**: Agent协同测试
- **压力测试**: 并发性能测试

## 📊 监控与日志

### 前端监控
- **错误监控**: 全局错误捕获
- **性能监控**: Web Vitals指标
- **用户行为**: 操作路径追踪
- **API监控**: 请求成功率统计

### 后端监控
- **Winston日志**: 结构化日志记录
- **健康检查**: Agent状态监控
- **性能指标**: 响应时间统计
- **错误追踪**: 异常堆栈记录

## 📋 项目配置与部署

### 环境配置

#### 前端环境变量
```bash
# .env.development
REACT_APP_API_BASE_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000
REACT_APP_ENV=development
REACT_APP_LOG_LEVEL=debug

# .env.production
REACT_APP_API_BASE_URL=https://api.intelli-req.com
REACT_APP_WS_URL=wss://api.intelli-req.com
REACT_APP_ENV=production
REACT_APP_LOG_LEVEL=error
```

#### 后端环境配置
```bash
# backend-server/.env
NODE_ENV=development
PORT=8000
LOG_LEVEL=debug

# Agent服务端点
ANALYSIS_AGENT_URL=http://localhost:8001
KNOWLEDGE_AGENT_URL=http://localhost:8002
CASES_AGENT_URL=http://localhost:8003
INSPIRATION_AGENT_URL=http://localhost:8004

# 安全配置
JWT_SECRET=your-secret-key
CORS_ORIGIN=http://localhost:3004
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
```

### 启动命令

#### 开发环境启动
```bash
# 前端启动
npm start                    # 启动在 http://localhost:3004

# 后端启动
cd backend-server
npm run dev                  # 启动在 http://localhost:8000

# 同时启动前后端
npm run dev:all             # 并行启动前后端服务
```

#### 生产环境部署
```bash
# 前端构建
npm run build               # 生成 build/ 目录

# 后端启动
cd backend-server
npm start                   # 生产模式启动
```

### 默认内容配置

#### PRD模板内容
系统内置了完整的PRD模板，包含：
- **产品概述**: 项目背景、用户画像
- **核心功能**: 瞬时停车、长租车位、功能优先级
- **非功能性需求**: 性能指标、安全要求
- **风险与对策**: 市场风险、技术风险分析

#### Agent能力映射
```javascript
const CAPABILITIES = {
  'dialogue-agent': ['conversation', 'discussion', 'q&a'],
  'inspiration-agent': ['creative', 'brainstorm', 'innovative'],
  'analysis-agent': ['evaluate', 'inspect', 'validate'],
  'knowledge-agent': ['supplement', 'reference', 'standards'],
  'cases-agent': ['examples', 'patterns', 'best-practices']
}
```

## 🔄 开发工作流

### 代码组织结构
```
src/
├── components/           # React组件
│   ├── Common/          # 通用组件
│   ├── Editor/          # 编辑器组件
│   └── Preview/         # 预览组件
├── hooks/               # 自定义Hooks
├── services/            # 服务层
│   ├── api/            # API配置
│   └── agents/         # Agent客户端
├── constants/           # 常量定义
└── utils/              # 工具函数

backend-server/
├── src/
│   ├── controllers/     # 控制器
│   ├── middleware/      # 中间件
│   ├── routes/         # 路由定义
│   ├── services/       # 业务服务
│   └── utils/          # 工具函数
└── logs/               # 日志文件
```

### Git工作流
```bash
# 功能开发分支
git checkout -b feature/ai-cards-enhancement
git commit -m "feat: 增强AI卡片交互功能"
git push origin feature/ai-cards-enhancement

# 发布分支
git checkout -b release/v2.1.0
git commit -m "release: v2.1.0 版本发布"
git tag v2.1.0
```

### 代码质量保证
- **ESLint**: 代码风格检查
- **Prettier**: 代码格式化
- **Husky**: Git hooks管理
- **Jest**: 单元测试覆盖
- **TypeScript**: 类型安全检查(计划中)

## 🎯 重构与优化方向

### 短期优化 (1-2周)
1. **TypeScript迁移**: 增强类型安全
2. **组件优化**: 减少不必要的重渲染
3. **API缓存**: 实现智能缓存策略
4. **错误边界**: 完善错误处理机制

### 中期规划 (1-2月)
1. **实时协作**: WebSocket实时编辑
2. **插件系统**: Agent插件化架构
3. **云端同步**: 文档云端存储
4. **移动端适配**: 响应式设计优化

### 长期愿景 (3-6月)
1. **AI增强**: 更智能的内容建议
2. **团队协作**: 多人实时编辑
3. **版本控制**: Git-like版本管理
4. **企业集成**: SSO、权限管理

## 📊 项目成熟度评估

### 功能完成度
| 模块 | 完成度 | 状态 | 备注 |
|------|--------|------|------|
| 编辑器系统 | 95% | ✅ 生产就绪 | 双编辑器+容错机制 |
| AI卡片系统 | 90% | ✅ 核心完成 | 5种类型+生命周期管理 |
| Agent协同 | 85% | ✅ 基础完成 | Mock+真实Agent支持 |
| 大纲导航 | 95% | ✅ 优化完成 | 精确定位+同步高亮 |
| 预览渲染 | 90% | ✅ 功能完整 | Markdown+AI交互层 |
| 文档管理 | 70% | 🔄 基础完成 | 保存+导出功能 |
| 用户体验 | 85% | ✅ 体验良好 | 响应式+交互优化 |

### 技术债务
1. **TypeScript迁移**: 当前为JavaScript，需要类型安全
2. **测试覆盖**: 单元测试覆盖率需要提升
3. **性能优化**: 大文档渲染性能待优化
4. **错误处理**: 边界情况处理需要完善

### 生产就绪度
- **开发环境**: ✅ 完全就绪
- **测试环境**: 🔄 基础就绪，需要完善测试
- **生产环境**: ⚠️ 需要安全加固和性能优化

## 🎯 核心竞争优势

### 技术优势
1. **双编辑器架构**: 保证系统稳定性和用户体验
2. **多Agent协同**: 提供多维度智能分析能力
3. **实时交互**: 流畅的AI交互体验
4. **模块化设计**: 高度可扩展的架构

### 用户体验优势
1. **智能卡片**: 直观的AI分析结果展示
2. **精确定位**: 一键定位到原文位置
3. **多轮对话**: 深度AI交互能力
4. **历史管理**: 完整的操作历史追踪

### 架构优势
1. **前后端分离**: 清晰的职责分离
2. **微服务架构**: Agent服务独立部署
3. **容错设计**: 多层次的容错机制
4. **扩展性**: 支持新Agent类型快速接入

## 🚀 未来发展路线图

### Phase 1: 稳定性提升 (1个月)
- TypeScript完全迁移
- 测试覆盖率达到80%+
- 性能优化和内存泄漏修复
- 生产环境部署准备

### Phase 2: 功能增强 (2-3个月)
- 实时协作编辑
- 更多AI Agent类型
- 高级文档管理功能
- 移动端适配

### Phase 3: 企业级特性 (3-6个月)
- 用户权限管理
- 团队协作功能
- 企业级安全特性
- 私有化部署支持

### Phase 4: 生态建设 (6个月+)
- Agent插件市场
- 第三方集成API
- 开发者工具链
- 社区生态建设

---

## 📝 总结

Intelli-req AI Editor是一个技术先进、架构清晰的智能文档编辑系统。通过前后端分离的架构设计，结合多Agent协同的AI能力，为用户提供了强大的智能文档编辑体验。

**核心亮点**:
- 🎯 **双编辑器容错机制**确保系统稳定性
- 🤖 **5种AI分析类型**提供全方位智能支持
- 🔄 **多Agent协同**实现复杂任务的智能分解
- 📍 **精确定位系统**提供卓越的用户体验
- 🎨 **现代化UI设计**符合用户使用习惯

**技术特色**:
- React + Express 现代化技术栈
- Mock数据完美支撑前端开发
- 模块化、可扩展的架构设计
- 完善的错误处理和容错机制

该系统已具备生产环境部署的基础条件，通过持续的优化和功能增强，将成为智能文档编辑领域的标杆产品。

---

**文档版本**: v2.0
**更新时间**: 2025-07-08
**维护者**: AI Requirements Editor Team
