/**
 * 验证修复效果的综合测试
 * 测试工具箱可见性、表格渲染等核心问题
 */

const fs = require('fs').promises;
const path = require('path');

class VerificationFixTest {
  constructor() {
    this.testResults = [];
    this.projectRoot = path.resolve(__dirname, '..');
    this.testSuite = 'Verification Fix Test';
  }

  log(message) {
    console.log(`[${this.testSuite}] ${message}`);
  }

  async runAllTests() {
    this.log('🚀 开始验证修复效果测试');
    
    try {
      await this.testToolboxVisibility();
      await this.testTableRenderingMechanism();
      await this.testForceRenderFunction();
      await this.testPortalImplementation();
      await this.testTableRenderEnhancer();
      
      this.generateReport();
    } catch (error) {
      this.log(`❌ 测试执行失败: ${error.message}`);
      throw error;
    }
  }

  async testToolboxVisibility() {
    this.log('👁️ 测试工具箱可见性修复');
    
    try {
      // 检查FloatingButton是否使用fixed定位
      const buttonCssPath = path.join(this.projectRoot, 'src/components/Editor/FloatingButton.css');
      const buttonCssContent = await fs.readFile(buttonCssPath, 'utf8');
      
      const usesFixedPosition = buttonCssContent.includes('position: fixed');
      
      if (usesFixedPosition) {
        this.addTestResult('工具箱按钮定位', true, '使用fixed定位，确保全局可见');
      } else {
        this.addTestResult('工具箱按钮定位', false, '未使用fixed定位，可能导致可见性问题');
      }

      // 检查FloatingToolbar是否使用fixed定位
      const toolbarCssPath = path.join(this.projectRoot, 'src/components/Editor/FloatingToolbar.css');
      const toolbarCssContent = await fs.readFile(toolbarCssPath, 'utf8');
      
      const toolbarUsesFixed = toolbarCssContent.includes('position: fixed');
      
      if (toolbarUsesFixed) {
        this.addTestResult('工具栏定位', true, '使用fixed定位，确保全局可见');
      } else {
        this.addTestResult('工具栏定位', false, '未使用fixed定位，可能导致可见性问题');
      }

      // 检查是否恢复Portal渲染
      const toolboxPath = path.join(this.projectRoot, 'src/components/Editor/FloatingToolbox.js');
      const toolboxContent = await fs.readFile(toolboxPath, 'utf8');
      
      const usesPortal = toolboxContent.includes('ReactDOM.createPortal');
      const rendersToBody = toolboxContent.includes('document.body');
      
      if (usesPortal && rendersToBody) {
        this.addTestResult('Portal渲染', true, '使用Portal渲染到document.body，确保全局可见');
      } else {
        this.addTestResult('Portal渲染', false, 'Portal渲染配置不正确');
      }
      
    } catch (error) {
      this.addTestResult('工具箱可见性', false, `测试失败: ${error.message}`);
    }
  }

  async testTableRenderingMechanism() {
    this.log('🎨 测试表格渲染机制');
    
    try {
      // 检查是否有forceTableRender函数
      const editorPath = path.join(this.projectRoot, 'src/components/Editor/EnhancedMilkdownEditor.js');
      const editorContent = await fs.readFile(editorPath, 'utf8');
      
      const hasForceRender = editorContent.includes('forceTableRender');
      
      if (hasForceRender) {
        this.addTestResult('强制渲染函数', true, '存在forceTableRender函数');
      } else {
        this.addTestResult('强制渲染函数', false, '缺少forceTableRender函数');
      }

      // 检查是否在插入后调用强制渲染
      const callsForceRender = editorContent.includes('forceTableRender()');
      
      if (callsForceRender) {
        this.addTestResult('强制渲染调用', true, '在表格插入后调用强制渲染');
      } else {
        this.addTestResult('强制渲染调用', false, '未在插入后调用强制渲染');
      }

      // 检查是否有立即内容更新
      const hasImmediateUpdate = editorContent.includes('立即触发内容变化事件');
      
      if (hasImmediateUpdate) {
        this.addTestResult('立即内容更新', true, '实现了立即内容更新机制');
      } else {
        this.addTestResult('立即内容更新', false, '缺少立即内容更新机制');
      }
      
    } catch (error) {
      this.addTestResult('表格渲染机制', false, `测试失败: ${error.message}`);
    }
  }

  async testForceRenderFunction() {
    this.log('⚡ 测试强制渲染函数实现');
    
    try {
      const editorPath = path.join(this.projectRoot, 'src/components/Editor/EnhancedMilkdownEditor.js');
      const editorContent = await fs.readFile(editorPath, 'utf8');
      
      // 检查forceTableRender函数的实现
      const forceRenderMatch = editorContent.match(/const forceTableRender = \(\) => \{([\s\S]*?)\};/);
      
      if (forceRenderMatch) {
        const functionContent = forceRenderMatch[1];
        
        // 检查是否包含多种渲染方法
        const hasDOM = functionContent.includes('Event(\'input\'');
        const hasMilkdown = functionContent.includes('forceRender');
        const hasOnChange = functionContent.includes('onChange(content)');
        
        if (hasDOM && hasMilkdown && hasOnChange) {
          this.addTestResult('强制渲染实现', true, '包含DOM事件、Milkdown强制渲染和内容变化三种方法');
        } else {
          this.addTestResult('强制渲染实现', false, '强制渲染方法不完整');
        }
      } else {
        this.addTestResult('强制渲染实现', false, '未找到forceTableRender函数实现');
      }
      
    } catch (error) {
      this.addTestResult('强制渲染函数', false, `测试失败: ${error.message}`);
    }
  }

  async testPortalImplementation() {
    this.log('🌐 测试Portal实现');
    
    try {
      const toolboxPath = path.join(this.projectRoot, 'src/components/Editor/FloatingToolbox.js');
      const toolboxContent = await fs.readFile(toolboxPath, 'utf8');
      
      // 检查ReactDOM导入
      const hasReactDOMImport = toolboxContent.includes('import ReactDOM from \'react-dom\'');
      
      if (hasReactDOMImport) {
        this.addTestResult('ReactDOM导入', true, '正确导入ReactDOM');
      } else {
        this.addTestResult('ReactDOM导入', false, '缺少ReactDOM导入');
      }

      // 检查Portal使用
      const portalMatch = toolboxContent.match(/ReactDOM\.createPortal\(([\s\S]*?),\s*document\.body\s*\)/);
      
      if (portalMatch) {
        this.addTestResult('Portal使用', true, '正确使用Portal渲染到document.body');
      } else {
        this.addTestResult('Portal使用', false, 'Portal使用不正确');
      }
      
    } catch (error) {
      this.addTestResult('Portal实现', false, `测试失败: ${error.message}`);
    }
  }

  async testTableRenderEnhancer() {
    this.log('🔧 测试表格渲染增强器');
    
    try {
      const enhancerPath = path.join(this.projectRoot, 'src/components/Editor/TableRenderEnhancer.js');
      const enhancerContent = await fs.readFile(enhancerPath, 'utf8');
      
      // 检查是否有多次渲染调用
      const hasMultipleRender = enhancerContent.includes('this.enhanceExistingTables()') &&
                               enhancerContent.includes('setTimeout(() => this.enhanceExistingTables(), 50)') &&
                               enhancerContent.includes('setTimeout(() => this.enhanceExistingTables(), 200)');
      
      if (hasMultipleRender) {
        this.addTestResult('多次渲染调用', true, '实现了立即和延迟的多次渲染调用');
      } else {
        this.addTestResult('多次渲染调用', false, '缺少多次渲染调用机制');
      }
      
    } catch (error) {
      this.addTestResult('表格渲染增强器', false, `测试失败: ${error.message}`);
    }
  }

  addTestResult(testName, passed, message) {
    this.testResults.push({
      name: testName,
      passed,
      message
    });
    
    const status = passed ? '✅' : '❌';
    this.log(`${status} ${testName}: ${message}`);
  }

  generateReport() {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    
    this.log('\n📊 验证修复测试结果汇总:');
    this.log(`总测试数: ${totalTests}`);
    this.log(`通过测试: ${passedTests}`);
    this.log(`失败测试: ${failedTests}`);
    this.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    this.log('\n📋 详细结果:');
    this.testResults.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      this.log(`${status} ${result.name}: ${result.message}`);
    });

    // 生成修复状态报告
    this.generateFixStatus();
    
    return {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      successRate: (passedTests / totalTests) * 100,
      results: this.testResults
    };
  }

  generateFixStatus() {
    this.log('\n🎯 修复状态报告:');
    
    const passedTests = this.testResults.filter(r => r.passed);
    const failedTests = this.testResults.filter(r => !r.passed);
    
    if (failedTests.length === 0) {
      this.log('🎉 所有修复都已完成！');
      this.log('✅ 工具箱可见性问题已解决');
      this.log('✅ 表格渲染机制已优化');
      this.log('✅ 强制渲染功能已实现');
      this.log('✅ Portal渲染已恢复');
    } else {
      this.log('⚠️ 仍有部分问题需要解决:');
      failedTests.forEach(test => {
        this.log(`❌ ${test.name}: ${test.message}`);
      });
    }
  }
}

// 运行测试
if (require.main === module) {
  const test = new VerificationFixTest();
  test.runAllTests().catch(console.error);
}

module.exports = VerificationFixTest;
