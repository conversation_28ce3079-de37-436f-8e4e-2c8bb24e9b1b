# 🧰 悬浮工具箱设计方案

## 🎯 设计目标

将编辑器的固定工具栏改为悬浮工具箱，实现：
1. **悬浮按钮** - 固定在页面右下角的悬浮按钮
2. **悬浮工具栏** - 点击按钮后弹出的悬浮工具栏
3. **全局可用** - 无论滚动到页面哪个位置都能访问
4. **不影响现有功能** - 保持所有现有功能和布局

## 🏗️ 技术架构

### 组件结构
```
FloatingToolbox (新组件)
├── FloatingButton (悬浮按钮)
└── FloatingToolbar (悬浮工具栏)
    ├── TableTool (表格工具)
    ├── ListTool (列表工具)
    ├── FormatTool (格式工具)
    └── OtherTools (其他工具)
```

### 实现方案

#### 方案A: Portal + Fixed定位 (推荐)
```javascript
// 使用React Portal实现完全脱离文档流的悬浮工具箱
const FloatingToolbox = ({ editorRef, onInsertTable, onInsertList }) => {
  const [isOpen, setIsOpen] = useState(false);
  
  return ReactDOM.createPortal(
    <div className="floating-toolbox-container">
      <FloatingButton onClick={() => setIsOpen(!isOpen)} />
      {isOpen && (
        <FloatingToolbar 
          onInsertTable={onInsertTable}
          onInsertList={onInsertList}
          onClose={() => setIsOpen(false)}
        />
      )}
    </div>,
    document.body
  );
};
```

**优势**:
- 完全脱离文档流，不受滚动影响
- 可以覆盖任何元素
- 位置控制灵活
- 不影响现有布局

#### 方案B: 编辑器内绝对定位
```javascript
// 在编辑器容器内使用绝对定位
const EditorWithFloatingToolbox = () => {
  return (
    <div className="editor-container relative">
      <Editor />
      <div className="absolute bottom-4 right-4 z-50">
        <FloatingToolbox />
      </div>
    </div>
  );
};
```

**劣势**:
- 受编辑器容器滚动影响
- 可能被其他元素遮挡

### 选择方案A - Portal + Fixed定位

## 🎨 UI设计

### 悬浮按钮设计
```css
.floating-button {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  border: none;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
}

.floating-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.5);
}
```

### 悬浮工具栏设计
```css
.floating-toolbar {
  position: fixed;
  bottom: 90px;
  right: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  padding: 12px;
  z-index: 999;
  min-width: 200px;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
```

## 🔧 实现细节

### 1. FloatingButton组件
```javascript
const FloatingButton = ({ onClick, isOpen }) => {
  return (
    <button
      className={`floating-button ${isOpen ? 'open' : ''}`}
      onClick={onClick}
      title="编辑工具"
    >
      {isOpen ? '✕' : '🛠️'}
    </button>
  );
};
```

### 2. FloatingToolbar组件
```javascript
const FloatingToolbar = ({ 
  onInsertTable, 
  onInsertList, 
  onClose,
  editorRef 
}) => {
  const handleInsertTable = () => {
    onInsertTable();
    onClose();
  };

  const handleInsertList = (ordered = false) => {
    onInsertList(ordered);
    onClose();
  };

  return (
    <div className="floating-toolbar">
      <div className="toolbar-section">
        <h3 className="toolbar-section-title">插入</h3>
        <button 
          className="toolbar-item"
          onClick={handleInsertTable}
          title="插入表格"
        >
          📊 表格
        </button>
        <button 
          className="toolbar-item"
          onClick={() => handleInsertList(false)}
          title="无序列表"
        >
          • 列表
        </button>
        <button 
          className="toolbar-item"
          onClick={() => handleInsertList(true)}
          title="有序列表"
        >
          1. 列表
        </button>
      </div>
      
      <div className="toolbar-section">
        <h3 className="toolbar-section-title">格式</h3>
        <button className="toolbar-item" title="粗体">
          <strong>B</strong>
        </button>
        <button className="toolbar-item" title="斜体">
          <em>I</em>
        </button>
      </div>
    </div>
  );
};
```

### 3. FloatingToolbox主组件
```javascript
const FloatingToolbox = ({ 
  editorRef, 
  editorInstance,
  onInsertTable, 
  onInsertList 
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isOpen && !event.target.closest('.floating-toolbox-container')) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen]);

  // ESC键关闭
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscKey);
    return () => document.removeEventListener('keydown', handleEscKey);
  }, [isOpen]);

  return ReactDOM.createPortal(
    <div className="floating-toolbox-container">
      <FloatingButton 
        onClick={() => setIsOpen(!isOpen)} 
        isOpen={isOpen}
      />
      {isOpen && (
        <FloatingToolbar
          onInsertTable={onInsertTable}
          onInsertList={onInsertList}
          onClose={() => setIsOpen(false)}
          editorRef={editorRef}
        />
      )}
    </div>,
    document.body
  );
};
```

## 🔗 集成到编辑器

### 修改EnhancedMilkdownEditor
```javascript
// 在EnhancedMilkdownEditor中集成FloatingToolbox
const EnhancedMilkdownEditor = ({ ... }) => {
  // ... 现有代码 ...

  return (
    <div className="enhanced-milkdown-editor">
      {/* 现有的编辑器内容 */}
      <div ref={editorRef} className="milkdown-editor-container">
        {/* 编辑器内容 */}
      </div>

      {/* 移除原有的固定工具栏 */}
      {/* <Toolbar /> */}

      {/* 新增悬浮工具箱 */}
      {editorReady && (
        <FloatingToolbox
          editorRef={editorRef}
          editorInstance={editorInstanceRef.current}
          onInsertTable={() => setShowTemplateSelector(true)}
          onInsertList={insertList}
        />
      )}

      {/* 其他现有组件保持不变 */}
      {/* TableInteractionManager, TableTemplateSelector等 */}
    </div>
  );
};
```

## 📱 响应式设计

### 移动端适配
```css
@media (max-width: 768px) {
  .floating-button {
    bottom: 16px;
    right: 16px;
    width: 48px;
    height: 48px;
  }

  .floating-toolbar {
    bottom: 70px;
    right: 16px;
    left: 16px;
    min-width: auto;
  }
}
```

## 🧪 测试计划

### 功能测试
1. ✅ 悬浮按钮在任何滚动位置都可见
2. ✅ 点击按钮正确显示/隐藏工具栏
3. ✅ 工具栏中的所有工具都能正常工作
4. ✅ 点击外部或ESC键能关闭工具栏

### 兼容性测试
1. ✅ 不影响现有编辑器功能
2. ✅ 不影响表格插入和编辑
3. ✅ 不影响其他组件的布局
4. ✅ 在不同屏幕尺寸下正常工作

## 🚀 实施步骤

### 第一步: 创建基础组件 (30分钟)
1. 创建FloatingButton组件
2. 创建FloatingToolbar组件
3. 创建FloatingToolbox主组件

### 第二步: 集成到编辑器 (20分钟)
1. 在EnhancedMilkdownEditor中集成FloatingToolbox
2. 移除原有的固定工具栏
3. 确保所有功能正常工作

### 第三步: 样式和交互优化 (15分钟)
1. 完善CSS样式
2. 添加动画效果
3. 优化用户体验

### 第四步: 测试验证 (15分钟)
1. 测试悬浮工具箱功能
2. 验证表格插入功能
3. 确保不影响现有功能

---

**设计时间**: 2025-01-20  
**设计者**: 开发团队  
**实施优先级**: 高  
**预计完成时间**: 80分钟
