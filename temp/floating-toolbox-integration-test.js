/**
 * 悬浮工具箱集成测试
 * 测试光标位置保存、表格插入位置、表格渲染等核心功能
 */

const fs = require('fs').promises;
const path = require('path');

class FloatingToolboxIntegrationTest {
  constructor() {
    this.testResults = [];
    this.projectRoot = path.resolve(__dirname, '..');
    this.testSuite = 'Floating Toolbox Integration Test';
  }

  log(message) {
    console.log(`[${this.testSuite}] ${message}`);
  }

  async runAllTests() {
    this.log('🚀 开始悬浮工具箱集成测试');
    
    try {
      await this.testToolboxPositioning();
      await this.testCursorPositionSaving();
      await this.testTableInsertionPosition();
      await this.testTableRendering();
      await this.testEditorIntegration();
      
      this.generateReport();
    } catch (error) {
      this.log(`❌ 测试执行失败: ${error.message}`);
      throw error;
    }
  }

  async testToolboxPositioning() {
    this.log('📍 测试工具箱定位');
    
    try {
      // 检查FloatingToolbox是否使用Portal渲染到document.body
      const toolboxPath = path.join(this.projectRoot, 'src/components/Editor/FloatingToolbox.js');
      const toolboxContent = await fs.readFile(toolboxPath, 'utf8');
      
      const usesPortal = toolboxContent.includes('ReactDOM.createPortal');
      const rendersToBody = toolboxContent.includes('document.body');
      
      if (usesPortal && rendersToBody) {
        this.addTestResult('工具箱定位', false, '工具箱渲染到document.body，应该渲染到编辑器容器内');
      } else {
        this.addTestResult('工具箱定位', true, '工具箱正确定位在编辑器容器内');
      }
    } catch (error) {
      this.addTestResult('工具箱定位', false, `测试失败: ${error.message}`);
    }
  }

  async testCursorPositionSaving() {
    this.log('💾 测试光标位置保存');
    
    try {
      // 检查EnhancedMilkdownEditor是否有光标位置保存机制
      const editorPath = path.join(this.projectRoot, 'src/components/Editor/EnhancedMilkdownEditor.js');
      const editorContent = await fs.readFile(editorPath, 'utf8');
      
      const hasCursorSaving = editorContent.includes('savedCursorPosition') || 
                             editorContent.includes('saveCursor') ||
                             editorContent.includes('cursorPosition');
      
      if (hasCursorSaving) {
        this.addTestResult('光标位置保存', true, '编辑器具有光标位置保存机制');
      } else {
        this.addTestResult('光标位置保存', false, '编辑器缺少光标位置保存机制');
      }
    } catch (error) {
      this.addTestResult('光标位置保存', false, `测试失败: ${error.message}`);
    }
  }

  async testTableInsertionPosition() {
    this.log('📊 测试表格插入位置');
    
    try {
      // 检查insertTableToEditor函数是否使用保存的光标位置
      const editorPath = path.join(this.projectRoot, 'src/components/Editor/EnhancedMilkdownEditor.js');
      const editorContent = await fs.readFile(editorPath, 'utf8');
      
      const insertTableFunction = editorContent.match(/const insertTableToEditor = \([\s\S]*?\};/);
      
      if (insertTableFunction) {
        const functionContent = insertTableFunction[0];
        const usesSavedPosition = functionContent.includes('savedCursorPosition') ||
                                 functionContent.includes('savedPosition');
        
        if (usesSavedPosition) {
          this.addTestResult('表格插入位置', true, '表格插入使用保存的光标位置');
        } else {
          this.addTestResult('表格插入位置', false, '表格插入未使用保存的光标位置');
        }
      } else {
        this.addTestResult('表格插入位置', false, '未找到insertTableToEditor函数');
      }
    } catch (error) {
      this.addTestResult('表格插入位置', false, `测试失败: ${error.message}`);
    }
  }

  async testTableRendering() {
    this.log('🎨 测试表格渲染');
    
    try {
      // 检查编辑器是否正确配置了表格支持
      const editorPath = path.join(this.projectRoot, 'src/components/Editor/EnhancedMilkdownEditor.js');
      const editorContent = await fs.readFile(editorPath, 'utf8');
      
      const hasGfmSupport = editorContent.includes('gfm');
      const hasTableRenderEnhancer = editorContent.includes('useTableRenderEnhancer');
      
      if (hasGfmSupport && hasTableRenderEnhancer) {
        this.addTestResult('表格渲染配置', true, '编辑器正确配置了表格支持');
      } else {
        this.addTestResult('表格渲染配置', false, '编辑器表格支持配置不完整');
      }

      // 检查表格渲染增强器是否处理新插入的表格
      const enhancerPath = path.join(this.projectRoot, 'src/components/Editor/TableRenderEnhancer.js');
      const enhancerContent = await fs.readFile(enhancerPath, 'utf8');
      
      const hasMutationObserver = enhancerContent.includes('MutationObserver');
      
      if (hasMutationObserver) {
        this.addTestResult('表格渲染监听', true, '表格渲染增强器监听DOM变化');
      } else {
        this.addTestResult('表格渲染监听', false, '表格渲染增强器未监听DOM变化');
      }
    } catch (error) {
      this.addTestResult('表格渲染', false, `测试失败: ${error.message}`);
    }
  }

  async testEditorIntegration() {
    this.log('🔗 测试编辑器集成');
    
    try {
      // 检查FloatingToolbox是否正确集成到编辑器
      const editorPath = path.join(this.projectRoot, 'src/components/Editor/EnhancedMilkdownEditor.js');
      const editorContent = await fs.readFile(editorPath, 'utf8');
      
      const hasFloatingToolbox = editorContent.includes('FloatingToolbox');
      const hasToolboxProps = editorContent.includes('onInsertTable') &&
                             editorContent.includes('onInsertList') &&
                             editorContent.includes('onInsertFormat');
      
      if (hasFloatingToolbox && hasToolboxProps) {
        this.addTestResult('编辑器集成', true, 'FloatingToolbox正确集成到编辑器');
      } else {
        this.addTestResult('编辑器集成', false, 'FloatingToolbox集成不完整');
      }
    } catch (error) {
      this.addTestResult('编辑器集成', false, `测试失败: ${error.message}`);
    }
  }

  addTestResult(testName, passed, message) {
    this.testResults.push({
      name: testName,
      passed,
      message
    });
    
    const status = passed ? '✅' : '❌';
    this.log(`${status} ${testName}: ${message}`);
  }

  generateReport() {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    
    this.log('\n📊 测试结果汇总:');
    this.log(`总测试数: ${totalTests}`);
    this.log(`通过测试: ${passedTests}`);
    this.log(`失败测试: ${failedTests}`);
    this.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    this.log('\n📋 详细结果:');
    this.testResults.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      this.log(`${status} ${result.name}: ${result.message}`);
    });

    // 生成修复建议
    this.generateFixSuggestions();
    
    return {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      successRate: (passedTests / totalTests) * 100,
      results: this.testResults
    };
  }

  generateFixSuggestions() {
    this.log('\n🔧 修复建议:');
    
    const failedTests = this.testResults.filter(r => !r.passed);
    
    if (failedTests.length === 0) {
      this.log('🎉 所有测试通过，无需修复！');
      return;
    }

    failedTests.forEach(test => {
      switch (test.name) {
        case '工具箱定位':
          this.log('1. 修改FloatingToolbox.js，移除Portal，改为在编辑器容器内渲染');
          break;
        case '光标位置保存':
          this.log('2. 在EnhancedMilkdownEditor.js中添加光标位置保存机制');
          break;
        case '表格插入位置':
          this.log('3. 修改insertTableToEditor函数，使用保存的光标位置');
          break;
        case '表格渲染配置':
          this.log('4. 检查并完善编辑器表格支持配置');
          break;
        case '表格渲染监听':
          this.log('5. 在TableRenderEnhancer中添加DOM变化监听');
          break;
        case '编辑器集成':
          this.log('6. 完善FloatingToolbox与编辑器的集成');
          break;
      }
    });
  }
}

// 运行测试
if (require.main === module) {
  const test = new FloatingToolboxIntegrationTest();
  test.runAllTests().catch(console.error);
}

module.exports = FloatingToolboxIntegrationTest;
