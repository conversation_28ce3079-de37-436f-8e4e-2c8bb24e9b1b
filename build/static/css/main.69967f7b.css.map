{"version": 3, "file": "static/css/main.69967f7b.css", "mappings": "AAAA,mDAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,gHAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,QAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CAEd,wCAAmB,CAAnB,2BAAmB,CAAnB,6BAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,uBAAmB,CAAnB,gBAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,cAAmB,CAAnB,gBAAmB,CAAnB,qBAAmB,CAAnB,YAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,qCAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,sBAAmB,CAAnB,iBAAmB,CAAnB,yBAAmB,CAAnB,iBAAmB,CAAnB,uBAAmB,CAAnB,gCAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,2BAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,uBAAmB,CAAnB,2BAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,oBAAmB,CAAnB,kCAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CAAnB,gCAAmB,CAAnB,oBAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,aAAmB,CAAnB,kBAAmB,CAAnB,sBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,mBAAmB,CAAnB,sBAAmB,CAAnB,yBAAmB,CAAnB,+BAAmB,CAAnB,qBAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,2BAAmB,CAAnB,8BAAmB,CAAnB,YAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,sBAAmB,CAAnB,6BAAmB,CAAnB,mCAAmB,CAAnB,yCAAmB,CAAnB,wCAAmB,CAAnB,kOAAmB,CAAnB,sCAAmB,CAAnB,8BAAmB,CAAnB,mNAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,0NAAmB,CAAnB,kCAAmB,CAAnB,iBAAmB,CAAnB,wMAAmB,CAAnB,0CAAmB,EAAnB,+CAAmB,CAAnB,wBAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,sCAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qCAAmB,CAAnB,gBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,uCAAmB,CAAnB,+BAAmB,CAAnB,0DAAmB,CAAnB,0DAAmB,CAAnB,0DAAmB,CAAnB,+BAAmB,CAAnB,yBAAmB,CAAnB,mCAAmB,CAAnB,+BAAmB,CAAnB,gCAAmB,CAAnB,oCAAmB,CAAnB,qCAAmB,CAAnB,sCAAmB,CAAnB,8CAAmB,CAAnB,4CAAmB,CAAnB,qBAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,oHAAmB,CAAnB,oEAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,4BAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,yBAAmB,CAAnB,sBAAmB,CAAnB,+CAAmB,CAAnB,yCAAmB,CAAnB,qCAAmB,CAAnB,6BAAmB,CAAnB,kCAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,gCAAmB,CAAnB,8EAAmB,CAAnB,kFAAmB,CAAnB,8CAAmB,CAAnB,yDAAmB,CAAnB,0CAAmB,CAAnB,4CAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,iCAAmB,CAAnB,mCAAmB,CAAnB,yCAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,uCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,kCAAmB,CAAnB,8BAAmB,CAAnB,gCAAmB,CAAnB,sCAAmB,CAAnB,kCAAmB,CAAnB,8BAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,sDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,uCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,uCAAmB,CAAnB,oBAAmB,CAAnB,sDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,qCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,qCAAmB,CAAnB,oBAAmB,CAAnB,sDAAmB,CAAnB,uCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,uCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,mCAAmB,CAAnB,iBAAmB,CAAnB,wDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,sDAAmB,CAAnB,+CAAmB,CAAnB,wCAAmB,CAAnB,yBAAmB,CAAnB,4DAAmB,CAAnB,uCAAmB,CAAnB,yBAAmB,CAAnB,6DAAmB,CAAnB,wCAAmB,CAAnB,yBAAmB,CAAnB,6DAAmB,CAAnB,wCAAmB,CAAnB,yBAAmB,CAAnB,6DAAmB,CAAnB,0CAAmB,CAAnB,yBAAmB,CAAnB,4DAAmB,CAAnB,uCAAmB,CAAnB,yBAAmB,CAAnB,2DAAmB,CAAnB,0CAAmB,CAAnB,yBAAmB,CAAnB,2DAAmB,CAAnB,4CAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,kDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,4BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,wCAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,mCAAmB,CAAnB,qFAAmB,CAAnB,2EAAmB,CAAnB,qDAAmB,CAAnB,iEAAmB,CAAnB,8DAAmB,CAAnB,cAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,6BAAmB,CAAnB,qBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,6BAAmB,CAAnB,qBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,mDAAmB,CAAnB,8CAAmB,CAAnB,mDAAmB,CAAnB,2CAAmB,CAAnB,4CAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,CAAnB,0CAAmB,CAAnB,sBAAmB,CAAnB,0BAAmB,CAAnB,2BAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,uBAAmB,CAAnB,uBAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,uBAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,6BAAmB,CAAnB,mCAAmB,CAAnB,8GAAmB,CAAnB,2HAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,mBAAmB,CAAnB,2BAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,kBAAmB,CAAnB,2BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,mCAAmB,CAAnB,yBAAmB,CAAnB,2BAAmB,CAAnB,kCAAmB,CAAnB,sCAAmB,CAAnB,oCAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,+CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,+CAAmB,CAAnB,iCAAmB,CAAnB,oBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,kEAAmB,CAAnB,4FAAmB,CAAnB,qEAAmB,CAAnB,kGAAmB,CAAnB,qDAAmB,CAAnB,4DAAmB,CAAnB,0EAAmB,CAAnB,iGAAmB,CAAnB,qEAAmB,CAAnB,kGAAmB,CAAnB,wEAAmB,CAAnB,+FAAmB,CAAnB,4CAAmB,CAAnB,sDAAmB,CAAnB,qEAAmB,CAAnB,kGAAmB,CAAnB,2EAAmB,CAAnB,kGAAmB,CAAnB,qCAAmB,CAAnB,kBAAmB,CAAnB,4BAAmB,CAAnB,kHAAmB,CAAnB,wGAAmB,CAAnB,uFAAmB,CAAnB,wFAAmB,CAAnB,kHAAmB,CAAnB,wGAAmB,CAAnB,kCAAmB,CAAnB,yDAAmB,CAAnB,kCAAmB,CAAnB,wDAAmB,CAAnB,oCAAmB,CAAnB,yDAAmB,CAAnB,wLAAmB,CAAnB,0LAAmB,CAAnB,6IAAmB,CAAnB,qKAAmB,CAAnB,kDAAmB,CAAnB,gEAAmB,CAAnB,kDAAmB,CAAnB,qIAAmB,CAAnB,kDAAmB,CAAnB,wEAAmB,CAAnB,kDAAmB,CAAnB,4EAAmB,CAAnB,kDAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,+DAAmB,CAAnB,2DAAmB,CAGnB,MACI,iBAAkB,CAClB,iBAAkB,CAClB,iBAAkB,CAClB,mBACJ,CAGA,gBACI,uBACJ,CAGA,iBACI,GAAK,wBAA+B,CACpC,IAAM,wBAAkC,CAAlC,iCAAoC,CAC1C,GAAO,wBAA+B,CAC1C,CAEA,iBACI,kCACJ,CAGA,iCACI,YACJ,CAEA,cACI,uBAAwB,CACxB,oBACJ,CAGA,iBACI,cACJ,CAGA,UAII,aAAqB,CAArB,oBAAqB,CAFrB,cAAe,CACf,eAAgB,CAFhB,sBAAuB,CAIvB,oCACJ,CAEA,UAEI,gBAIJ,CAEA,oBAJI,aAAqB,CAArB,oBAAqB,CADrB,eAAgB,CAFhB,sBAAuB,CAIvB,oCASJ,CANA,UAEI,iBAIJ,CAGA,cACI,iBAGJ,CAEA,qBAOI,gBAAuB,CAEvB,yBAA0B,CAJ1B,QAAS,CAJT,UAAW,CAEX,MAAO,CADP,iBAAkB,CAElB,KAAM,CAIN,8BAAgC,CAFhC,SAIJ,CAMA,uDACI,kBAA0B,CAA1B,yBACJ,CAGA,iBAGI,aAAqB,CAArB,oBAAqB,CAFrB,cAAe,CACf,eAAgB,CAGhB,oBAAsB,CADtB,iBAEJ,CAEA,iBAGI,aAAc,CAFd,iBAAmB,CACnB,eAAgB,CAGhB,qBAAuB,CADvB,oBAEJ,CAEA,iBAEI,aAAc,CADd,gBAAkB,CAGlB,qBAAuB,CADvB,mBAEJ,CAGA,oBACI,0BACJ,CAEA,qBACI,0BAAyC,CACzC,aAAqB,CAArB,oBAAqB,CACrB,eACJ,CAGA,uBACI,YAAa,CAEb,YAGJ,CAEA,8CAJI,aAAc,CACd,gBAAkB,CAHlB,iBAYJ,CANA,uBACI,YAAa,CAEb,WAGJ,CAGA,gBAWI,kBAAmB,CAHnB,gBAAuB,CADvB,WAAY,CAMZ,oBAAsB,CAJtB,cAAe,CACf,YAAa,CAJb,WAAY,CAMZ,sBAAuB,CAXvB,iBAAkB,CAClB,WAAa,CACb,OAAQ,CACR,0BAA2B,CAU3B,uBAAyB,CATzB,UAUJ,CAEA,sBACI,0BACJ,CAEA,0BACI,yCACJ,CAEA,kBACI,eAAgB,CAChB,uBACJ,CAEA,4BACI,YAAa,CACb,SACJ,CAGA,6BACI,8BAA8C,CAC9C,uCAAyC,CAIzC,8BAA6C,CAH7C,uBAAyB,CACzB,yBAA2B,CAC3B,yBAEJ,CAGA,2BACI,8BAA+C,CAC/C,6BAA8B,CAE9B,iBAAkB,CADlB,2BAEJ,CAWA,2BACI,8BAA+C,CAC/C,6BAA8B,CAE9B,iBAAkB,CAClB,+BAA8C,CAC9C,uBACJ,CAzNA,wCAyNE,CAzNF,gBAyNE,CAzNF,6LAyNE,CAzNF,mDAyNE,CAzNF,oBAyNE,CAzNF,wDAyNE,CAzNF,mDAyNE,CAzNF,oBAyNE,CAzNF,wDAyNE,CAzNF,2CAyNE,CAzNF,wBAyNE,CAzNF,wDAyNE,CAzNF,2CAyNE,CAzNF,wBAyNE,CAzNF,wDAyNE,CAzNF,0CAyNE,CAzNF,wBAyNE,CAzNF,wDAyNE,CAzNF,2CAyNE,CAzNF,wBAyNE,CAzNF,sDAyNE,CAzNF,2CAyNE,CAzNF,wBAyNE,CAzNF,sDAyNE,CAzNF,2CAyNE,CAzNF,wBAyNE,CAzNF,wDAyNE,CAzNF,2CAyNE,CAzNF,wBAyNE,CAzNF,wDAyNE,CAzNF,2CAyNE,CAzNF,wBAyNE,CAzNF,wDAyNE,CAzNF,0CAyNE,CAzNF,wBAyNE,CAzNF,wDAyNE,CAzNF,2CAyNE,CAzNF,wBAyNE,CAzNF,qDAyNE,CAzNF,4CAyNE,CAzNF,wBAyNE,CAzNF,wDAyNE,CAzNF,4CAyNE,CAzNF,wBAyNE,CAzNF,wDAyNE,CAzNF,4CAyNE,CAzNF,wBAyNE,CAzNF,sDAyNE,CAzNF,4CAyNE,CAzNF,wBAyNE,CAzNF,sDAyNE,CAzNF,6CAyNE,CAzNF,wBAyNE,CAzNF,sDAyNE,CAzNF,4CAyNE,CAzNF,wBAyNE,CAzNF,wDAyNE,CAzNF,6CAyNE,CAzNF,wBAyNE,CAzNF,sDAyNE,CAzNF,6CAyNE,CAzNF,wBAyNE,CAzNF,wDAyNE,CAzNF,6CAyNE,CAzNF,wBAyNE,CAzNF,uDAyNE,CAzNF,0CAyNE,CAzNF,wBAyNE,CAzNF,wDAyNE,CAzNF,0CAyNE,CAzNF,wBAyNE,CAzNF,wDAyNE,CAzNF,0CAyNE,CAzNF,wBAyNE,CAzNF,sDAyNE,CAzNF,0CAyNE,CAzNF,wBAyNE,CAzNF,sDAyNE,CAzNF,4CAyNE,CAzNF,wBAyNE,CAzNF,wDAyNE,CAzNF,4CAyNE,CAzNF,wBAyNE,CAzNF,wDAyNE,CAzNF,2CAyNE,CAzNF,wBAyNE,CAzNF,wDAyNE,CAzNF,2CAyNE,CAzNF,wBAyNE,CAzNF,uDAyNE,CAzNF,wCAyNE,CAzNF,qBAyNE,CAzNF,wDAyNE,CAzNF,qDAyNE,CAzNF,6CAyNE,CAzNF,wBAyNE,CAzNF,wDAyNE,CAzNF,6CAyNE,CAzNF,wBAyNE,CAzNF,wDAyNE,CAzNF,6CAyNE,CAzNF,wBAyNE,CAzNF,sDAyNE,CAzNF,+CAyNE,CAzNF,aAyNE,CAzNF,6CAyNE,CAzNF,+CAyNE,CAzNF,aAyNE,CAzNF,6CAyNE,CAzNF,+CAyNE,CAzNF,aAyNE,CAzNF,6CAyNE,CAzNF,+CAyNE,CAzNF,aAyNE,CAzNF,4CAyNE,CAzNF,+CAyNE,CAzNF,aAyNE,CAzNF,4CAyNE,CAzNF,+CAyNE,CAzNF,aAyNE,CAzNF,4CAyNE,CAzNF,+CAyNE,CAzNF,aAyNE,CAzNF,4CAyNE,CAzNF,gDAyNE,CAzNF,aAyNE,CAzNF,6CAyNE,CAzNF,gDAyNE,CAzNF,aAyNE,CAzNF,6CAyNE,CAzNF,gDAyNE,CAzNF,aAyNE,CAzNF,6CAyNE,CAzNF,iDAyNE,CAzNF,aAyNE,CAzNF,8CAyNE,CAzNF,iDAyNE,CAzNF,aAyNE,CAzNF,6CAyNE,CAzNF,8CAyNE,CAzNF,aAyNE,CAzNF,6CAyNE,CAzNF,gDAyNE,CAzNF,aAyNE,CAzNF,4CAyNE,CAzNF,sDAyNE,CAzNF,qFAyNE,CAzNF,+FAyNE,CAzNF,+FAyNE,CAzNF,kGAyNE,CAzNF,yDAyNE,CAzNF,sDAyNE,CAzNF,mDAyNE,CAzNF,oBAyNE,CAzNF,wDAyNE,CAzNF,qDAyNE,CAzNF,oBAyNE,CAzNF,uDAyNE,CAzNF,mDAyNE,CAzNF,kDAyNE,CAzNF,kBAyNE,CAzNF,6HAyNE,CAzNF,wGAyNE,CAzNF,+GAyNE,CAzNF,wFAyNE,CAzNF,+HAyNE,CAzNF,wGAyNE,CAzNF,+CAyNE,CAzNF,yDAyNE,CAzNF,+CAyNE,CAzNF,wDAyNE,CAzNF,iDAyNE,CAzNF,wDAyNE,CAzNF,8CAyNE,CAzNF,uDAyNE,CAzNF,mDAyNE,CAzNF,sDAyNE,CAzNF,yDAyNE,CAzNF,iDAyNE,CAzNF,wBAyNE,CAzNF,wDAyNE,CAzNF,uCAyNE,CAzNF,yCAyNE,CAzNF,gDAyNE,CAzNF,sDAyNE,CAzNF,iBAyNE,CAzNF,sBAyNE,CAzNF,6BAyNE,CAzNF,8DAyNE,CAzNF,mCAyNE,CAzNF,uBAyNE,CAzNF,8BAyNE,CAzNF,oBAyNE,CAzNF,6BAyNE,CAzNF,oBAyNE,EAzNF,kEAyNE,CAzNF,8DAyNE,CAzNF,8DAyNE,CAzNF,8DAyNE,CAzNF,8DAyNE,EAzNF,wFAyNE,CAzNF,8DAyNE,CAzNF,8DAyNE,CAzNF,qBAyNE,CAzNF,2BAyNE,CAzNF,kBAyNE,EAzNF,wCAyNE,EAzNF,6DAyNE,CAzNF,+CAyNE,CCxNF,yBAEE,eAAiB,CADjB,WAAY,CAEZ,eACF,CAEA,wBAUE,eAAiB,CAPjB,WAAY,CAMZ,aAAc,CAHd,+FAAiH,CACjH,cAAe,CALf,WAAY,CAMZ,eAAgB,CAJhB,YAAa,CACb,iBAAkB,CAMlB,WAAY,CACZ,UAAW,CAXX,UAYF,CAEA,qCACE,aAAc,CACd,iBACF,CAEA,8BACE,YACF,CAGA,yBACE,wBAEE,cAAe,CADf,iBAEF,CACF,CAGA,2CACE,SACF,CAEA,iDACE,kBAAmB,CACnB,iBACF,CAEA,iDACE,kBAAmB,CACnB,iBACF,CAEA,uDACE,kBACF,CAGA,oBAEE,eAAiB,CADjB,WAAY,CAEZ,eACF,CAGA,8BAQE,WAAY,CAFZ,aAAc,CAHd,uEAAgF,CAChF,cAAe,CAHf,WAAY,CAIZ,eAAgB,CAEhB,YAAa,CAEb,eAAgB,CAPhB,iBAQF,CAGA,2CACE,YACF,CAGA,iCAME,+BAAgC,CAHhC,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,kBAAqB,CACrB,kBAAmB,CAEnB,sBACF,CAEA,iCAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAEhB,kBAAqB,CACrB,sBACF,CAEA,iCAGE,aAAc,CAFd,iBAAkB,CAClB,eAAgB,CAEhB,iBAAoB,CACpB,sBACF,CAGA,gCAEE,eAAgB,CADhB,aAEF,CAGA,qCAEE,aAAc,CADd,eAEF,CAEA,iCAEE,aAAc,CADd,iBAEF,CAGA,yCAEE,kBAAmB,CADnB,6BAA8B,CAK9B,yBAA0B,CAC1B,8BAAyC,CAFzC,iBAAkB,CADlB,aAAc,CADd,iBAKF,CAEA,2CAEE,aAAc,CADd,QAEF,CAGA,kEAEE,aAAc,CACd,iBACF,CAEA,iCAEE,eAAgB,CADhB,YAEF,CAEA,oCACE,oBACF,CAEA,oCACE,uBACF,CAGA,oCACE,wBAAyB,CAGzB,iBAAkB,CAElB,8BAAwC,CAHxC,aAAc,CAEd,eAAgB,CAHhB,UAKF,CAEA,kEAEE,wBAAyB,CACzB,iBAAkB,CAClB,eACF,CAEA,iCACE,kBAAmB,CAEnB,aAAc,CADd,eAEF,CAEA,qDACE,kBACF,CAEA,6CACE,kBACF,CAGA,mCACE,kBAAmB,CAMnB,wBAAyB,CAJzB,iBAAkB,CAGlB,aAAc,CAFd,8CAAwD,CACxD,cAAgB,CAHhB,eAMF,CAEA,kCACE,kBAAmB,CAMnB,wBAAyB,CAHzB,iBAAkB,CAFlB,aAAc,CAId,aAAc,CADd,eAAgB,CAFhB,iBAKF,CAEA,uCACE,gBAAuB,CAGvB,WAAY,CADZ,aAAc,CADd,SAGF,CAGA,iCAEE,WAA6B,CAE7B,iBAAkB,CAFlB,4BAA6B,CAC7B,aAEF,CAGA,mDACE,YACF,CAEA,0CACE,kBACF,CAGA,2CACE,aAAc,CAEd,iBAAkB,CADlB,mBAEF,CAGA,yBACE,8BAEE,cAAe,CADf,iBAEF,CAEA,iCACE,iBACF,CAEA,iCACE,kBACF,CAEA,iCACE,kBACF,CACF,CAGA,iDACE,SACF,CAEA,uDACE,kBAAmB,CACnB,iBACF,CAEA,uDACE,kBAAmB,CACnB,iBACF,CAEA,6DACE,kBACF,CC1RA,iBAeE,kBAAmB,CARnB,kDAAqD,CAErD,WAAY,CAHZ,iBAAkB,CAJlB,WAAY,CAQZ,+BAA8C,CAF9C,UAAY,CAGZ,cAAe,CAGf,YAAa,CAGb,cAAe,CAZf,WAAY,CAWZ,sBAAuB,CAGvB,YAAa,CAlBb,cAAe,CAEf,UAAW,CAUX,0CAAiD,CAKjD,wBAAiB,CAAjB,gBAAiB,CAdjB,UAAW,CAQX,YAQF,CAEA,uBAGE,kDAAqD,CADrD,+BAA8C,CAD9C,oBAGF,CAEA,wBAEE,8BAA6C,CAD7C,qBAEF,CAEA,uBACE,yBAA0B,CAC1B,kBACF,CAGA,sBACE,kDAAqD,CACrD,uBACF,CAEA,4BACE,kDAAqD,CACrD,kCACF,CAGA,sBACE,oBAAqB,CACrB,gDACF,CAEA,4CACE,wBACF,CAGA,yBACE,iBACE,WAAY,CAIZ,cAAe,CADf,WAAY,CAFZ,UAAW,CACX,UAGF,CACF,CAEA,yBACE,iBACE,WAAY,CAIZ,cAAe,CADf,WAAY,CAFZ,UAAW,CACX,UAGF,CACF,CAGA,+BACE,iBAEE,eAAmB,CADnB,qBAEF,CAEA,uBACE,kBACF,CACF,CAGA,uCACE,iBACE,eACF,CAEA,uBACE,cACF,CAEA,sBACE,eACF,CACF,CCzGA,kBAYE,6CAAoD,CARpD,eAAiB,CAGjB,0BAAqC,CAFrC,kBAAmB,CAHnB,WAAY,CAIZ,+BAA0C,CAK1C,eAAgB,CADhB,eAAgB,CAFhB,YAAa,CAPb,cAAe,CAEf,UAAW,CAUX,wBAAiB,CAAjB,gBAAiB,CAJjB,WAKF,CAEA,mBACE,GACE,SAAU,CACV,qCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAGA,iBACE,kBACF,CAEA,8BACE,eACF,CAEA,uBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAGhB,oBAAsB,CACtB,cAAiB,CACjB,SAAU,CAHV,wBAIF,CAGA,eACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAGA,cAEE,kBAAmB,CAInB,gBAAuB,CADvB,WAAY,CAEZ,iBAAkB,CAIlB,aAAc,CAHd,cAAe,CAPf,YAAa,CASb,cAAe,CAPf,OAAQ,CACR,gBAAiB,CAQjB,eAAgB,CAHhB,uBAAyB,CAIzB,UACF,CAEA,oBACE,kBAAmB,CACnB,aACF,CAEA,qBACE,kBAAmB,CACnB,oBACF,CAEA,oBACE,yBAA0B,CAC1B,mBACF,CAGA,mBAEE,kBAAmB,CADnB,YAAa,CAMb,aAAc,CADd,cAAe,CADf,WAAY,CAFZ,sBAAuB,CACvB,UAIF,CAGA,mBAGE,QAAO,CAFP,cAAe,CACf,eAEF,CAGA,eACE,4BAA6B,CAG7B,YAAa,CACb,sBAAuB,CAFvB,eAAgB,CADhB,gBAIF,CAEA,sBAQE,kBAAmB,CAJnB,kBAAmB,CADnB,WAAY,CAEZ,iBAAkB,CAMlB,aAAc,CALd,cAAe,CACf,YAAa,CAGb,cAAe,CARf,WAAY,CAOZ,sBAAuB,CAGvB,uBAAyB,CAXzB,UAYF,CAEA,4BACE,kBAAmB,CACnB,aACF,CAEA,6BACE,kBAAmB,CACnB,oBACF,CAEA,4BACE,yBAA0B,CAC1B,kBACF,CAGA,yBACE,kBACE,WAAY,CAEZ,SAAU,CAEV,cAAe,CADf,cAAe,CAFf,UAIF,CACF,CAEA,yBACE,kBACE,WAAY,CAEZ,SAAU,CACV,YAAa,CAFb,UAGF,CAEA,cAEE,cAAe,CADf,eAEF,CAEA,mBAGE,cAAe,CADf,WAAY,CADZ,UAGF,CAEA,mBACE,cACF,CACF,CAGA,mCACE,kBACE,kBAAmB,CACnB,oBAAqB,CACrB,+BACF,CAEA,uBACE,aACF,CAEA,cACE,aACF,CAEA,oBACE,kBAAmB,CACnB,aACF,CAEA,qBACE,kBACF,CAEA,eACE,oBACF,CAEA,sBACE,kBAAmB,CACnB,aACF,CAEA,4BACE,kBAAmB,CACnB,aACF,CACF,CAGA,+BACE,kBACE,qBAAsB,CACtB,+BACF,CAEA,cACE,sBACF,CAEA,oBAEE,kBAAmB,CADnB,iBAEF,CAEA,oBACE,iBAAqB,CACrB,sBACF,CACF,CAGA,uCACE,kBACE,cACF,CAMA,oCACE,eACF,CACF,CC3PA,iBASE,2CAA6C,CAH7C,gEAAqF,CAHrF,eAAgB,CADhB,eAAgB,CAQhB,2BACF,CAEA,+BACE,GACE,SAAU,CACV,qCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAGA,8BACE,yBAA0B,CAC1B,4BACF,CAGA,gCACE,eAAgB,CAChB,sBAAuB,CACvB,kBACF,CAEA,8BAGE,oBAAa,CAAb,YAAa,CADb,wBAAyB,CADzB,qBAGF,CAGA,+BAKE,oBAAqB,CACrB,2BAA4B,CAF5B,mBAAoB,CAHpB,eAAgB,CAChB,eAAgB,CAChB,eAIF,CAGA,yBACE,iBAEE,eAAgB,CADhB,eAEF,CACF,CAEA,yBACE,iBAEE,4BAA6B,CAD7B,4BAEF,CACF,CAGA,mCACE,iBAAkB,CAClB,4BACF,CAEA,yCACE,8BACF,CAGA,iCACE,kCACF,CAEA,yDACE,oBACF,CAGA,+BACE,iBAAkB,CAClB,4BACF,CAEA,qCACE,0BAAqC,CACrC,oBACF,CAGA,kDACE,SACF,CAEA,wDACE,kBAAmB,CACnB,iBACF,CAEA,wDACE,kBAAmB,CACnB,iBACF,CAEA,8DACE,kBACF,CCnHA,sBACE,uEAA8E,CAC9E,eACF,CAGA,iBACE,sBACF,CAEA,gBAEE,eAAgB,CADhB,kBAEF,CAEA,2BACE,eACF,CAGA,iBAME,eAAiB,CAFjB,wBAAyB,CACzB,mBAAqB,CAFrB,aAAc,CADd,eAKF,CAEA,mBAGE,iBAEF,CAEA,sBAEE,+BAAgC,CAChC,8BAA+B,CAF/B,cAOF,CAEA,oCACE,kBACF,CAEA,iCACE,iBACF,CAEA,oCACE,wBACF,CAEA,4BACE,wBACF,CAGA,kBACE,uBAA8B,CAC9B,iBAAkB,CAGlB,iBAAkB,CAFlB,WAAY,CACZ,uBAEF,CAEA,wBAEE,wBAAyB,CADzB,oBAEF,CAEA,wBAGE,wBAAyB,CADzB,oBAAqB,CAErB,8BAA6C,CAH7C,YAIF,CAEA,gBACE,uBAA8B,CAC9B,iBAAkB,CAElB,gBAAiB,CADjB,eAAgB,CAEhB,uBACF,CAEA,sBAEE,wBAAyB,CADzB,oBAEF,CAEA,sBAGE,wBAAyB,CADzB,oBAAqB,CAErB,8BAA6C,CAH7C,YAIF,CAGA,kCACE,wBAAyB,CACzB,aACF,CAGA,0BACE,mBACE,kBACF,CAEA,sBACE,eACF,CACF,CAEA,0BACE,mBACE,gBACF,CAEA,sBACE,aACF,CACF,CAEA,0BACE,mBACE,eACF,CAEA,sBACE,eACF,CACF,CAEA,0BACE,mBACE,gBACF,CAEA,sBACE,cACF,CACF,CAGA,iBACE,+BACF,CAEA,oBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAGA,2BAME,wCAAyC,CALzC,wBAAyB,CACzB,6BAA8B,CAG9B,oBAAsB,CADtB,iBAAkB,CADlB,iBAIF,CAEA,2BACE,MACE,wBACF,CACA,IACE,wBACF,CACF,CAGA,cACE,oBAAqB,CACrB,wBAAyB,CACzB,qBACF,CAGA,mBACE,wBAAyB,CAEzB,oBAAsB,CAEtB,8CAAwD,CADxD,gBAAkB,CAFlB,sBAIF,CAGA,kCACE,aAAc,CACd,mBACF,CAEA,iBACE,mBACF,CAGA,gBACE,aAAc,CACd,yBACF,CAEA,sBACE,aACF,CAGA,qBAEE,aAAc,CADd,eAEF,CAEA,iBAEE,aAAc,CADd,iBAEF,CClOA,mBACE,WAAY,CACZ,UACF,CAGA,uBAGE,qBAAuB,CACvB,8BAA+B,CAF/B,aAAc,CADd,WAIF,CAGA,wBAGE,wBAAyB,CACzB,6BAA8B,CAF9B,aAAc,CADd,WAIF,CAGA,oBAGE,qBAAuB,CAFvB,QAAO,CACP,eAAgB,CAEhB,cACF,CAGA,wCACE,uBAA8B,CAC9B,iBAAkB,CAClB,WAAY,CACZ,uBACF,CAEA,8CAEE,wBAAyB,CADzB,oBAEF,CAEA,8CAGE,wBAAyB,CADzB,oBAAqB,CAErB,8BAA6C,CAH7C,YAIF,CAGA,oBAKE,kBAAmB,CAEnB,qBAAuB,CALvB,+BAAgC,CAChC,YAAa,CAFb,WAAY,CAGZ,6BAA8B,CAE9B,gBAEF,CAGA,oBAGE,aAAe,CADf,kBAAmB,CAKnB,wBAAyB,CACzB,mBAAqB,CAPrB,YAAa,CAIb,iBAAmB,CACnB,eAAgB,CAFhB,kBAAoB,CAKpB,uBACF,CAEA,0BACE,wBACF,CAEA,2BACE,wBAAyB,CAEzB,oBAAqB,CADrB,UAEF,CAGA,oBACE,YACF,CAEA,cAEE,qBAAuB,CAGvB,oBAAsB,CAJtB,oBAAuB,CAGvB,uBAEF,CAEA,oBACE,wBACF,CAEA,qBACE,wBAAyB,CACzB,aAAc,CACd,eACF,CAEA,sBAEE,iBACF,CAEA,sBAEE,kBAAoB,CADpB,mBAEF,CAEA,sBAEE,gBAAkB,CADlB,oBAGF,CAGA,oBACE,YAAa,CACb,qBAAsB,CACtB,eACF,CAEA,qBAKE,kBAAmB,CAEnB,qBAAuB,CALvB,+BAAgC,CAChC,YAAa,CAFb,aAAc,CAGd,6BAA8B,CAE9B,cAEF,CAEA,sBACE,QAAO,CACP,eAAgB,CAChB,YACF,CAGA,SACE,qBAAuB,CACvB,wBAAyB,CACzB,mBAAqB,CAErB,kBAAmB,CADnB,YAAa,CAEb,uBACF,CAEA,eACE,mCACF,CAEA,kBACE,oBAAqB,CACrB,mCACF,CAGA,0BACE,uBACE,WACF,CAEA,wBACE,WACF,CACF,CAEA,0BACE,uBACE,WACF,CAEA,wBACE,WACF,CAEA,oBACE,YACF,CACF,CAEA,0BACE,uBACE,WACF,CAEA,wBACE,WACF,CAEA,oBACE,cACF,CACF,CAEA,0BACE,mBACE,qBAAsB,CACtB,WACF,CAEA,+CAGE,gBAAiB,CADjB,UAEF,CAEA,oBACE,YACF,CACF,CAGA,iBAGE,cAAgB,CADhB,kBAAmB,CADnB,YAGF,CAEA,cAKE,kBAAmB,CAFnB,iBAAkB,CAKlB,kBAAmB,CADnB,eAAgB,CALhB,aAAc,CADd,YAQF,CAEA,mBACE,wBAAyB,CACzB,aACF,CAEA,sBACE,wBAAyB,CACzB,aACF,CAEA,oBACE,wBAAyB,CACzB,aACF,CAGA,gBAGE,aAAe,CADf,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAGb,iBAEF,CAEA,uBACE,aACF,CAEA,sBACE,aACF,CAEA,sBACE,aACF,CAGA,kBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,SACE,6BACF,CAGA,uHAGE,SACF,CAEA,yIAGE,kBACF,CAEA,yIAGE,kBAAmB,CACnB,iBACF,CAEA,2JAGE,kBACF,CC/TA,0BAIE,eAAiB,CAFjB,wBAAyB,CACzB,iBAAkB,CAElB,eAAgB,CAJhB,iBAKF,CAEA,gCACE,oBACF,CAGA,yBAKE,kBAAmB,CAJnB,kBAAmB,CACnB,+BAAgC,CAEhC,YAAa,CAGb,cAAe,CADf,OAAQ,CAHR,gBAKF,CAEA,aAUE,kBAAmB,CANnB,iBAAkB,CAKlB,YAAa,CAEb,OAAQ,CAVR,eAWF,CAEA,mBACE,kBAEF,CAEA,oBACE,kBACF,CAQA,kBAIE,eAAiB,CAFjB,gBAAiB,CACjB,YAAa,CAFb,iBAIF,CAEA,2BACE,kBACF,CAGA,iBAQE,kBAAmB,CAFnB,gBAAoC,CADpC,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAQN,UACF,CAGA,eAEE,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAClB,WAAY,CAJZ,YAKF,CAEA,kBACE,aAAc,CACd,eAAgB,CAChB,cACF,CAEA,iBACE,aAAc,CACd,cAAe,CACf,gBACF,CAGA,oCAGE,gBAAuB,CADvB,WAAY,CADZ,YAGF,CAEA,4CAKE,aAAc,CADd,cAAe,CADf,eAAgB,CAFhB,gBAAiB,CACjB,SAIF,CAGA,0OAOE,eAAgB,CAChB,eAAgB,CAFhB,iBAGF,CAEA,uCAA0D,aAAc,CAA/B,cAAiC,CAC1E,uCAA0D,aAAc,CAA/B,cAAiC,CAC1E,uCAA0D,aAAc,CAA/B,cAAiC,CAC1E,uCAA0D,aAAc,CAA/B,cAAiC,CAC1E,uCAA0D,aAAc,CAA/B,cAAiC,CAC1E,uCAA0D,aAAc,CAA/B,cAAiC,CAG1E,sCAEE,eAAgB,CADhB,YAEF,CAGA,8EAIE,eAAgB,CAFhB,YAAa,CACb,iBAEF,CAEA,uCAEE,2BAA4B,CAD5B,oBAEF,CAEA,uCAEE,2BAA4B,CAD5B,uBAEF,CAEA,uCAGE,iBAAkB,CADlB,eAAgB,CADhB,YAGF,CAGA,0CACE,sBAAuB,CACvB,gBACF,CAEA,6CACE,sBACF,CAEA,0CACE,2BAA4B,CAC5B,gBACF,CAEA,6CACE,2BACF,CAGA,0CAIE,wBAAyB,CAFzB,wBAAyB,CAGzB,iBAAkB,CAFlB,aAAc,CAGd,eAAgB,CALhB,UAMF,CAEA,8EAIE,+BAAgC,CAChC,8BAA+B,CAH/B,gBAAiB,CACjB,eAGF,CAEA,uCACE,kBAAmB,CAEnB,aAAc,CADd,eAEF,CAEA,oGAEE,iBACF,CAEA,qDACE,kBACF,CAGA,yCACE,kBAAmB,CAEnB,iBAAkB,CAGlB,aAAc,CAFd,8CAAwD,CACxD,cAAe,CAHf,eAKF,CAEA,wCACE,kBAAmB,CAGnB,iBAAkB,CAFlB,aAAc,CAId,aAAc,CADd,eAAgB,CAFhB,YAIF,CAEA,6CACE,gBAAuB,CACvB,aAAc,CACd,SACF,CAGA,+CACE,6BAA8B,CAG9B,aAAc,CACd,iBAAkB,CAFlB,aAAc,CADd,iBAIF,CAGA,sCACE,aAAc,CACd,yBACF,CAEA,4CACE,aACF,CAGA,wCAGE,iBAAkB,CADlB,WAAY,CAEZ,YAAa,CAHb,cAIF,CAGA,uCAEE,WAA6B,CAA7B,4BAA6B,CAC7B,aACF,CAGA,iDACE,aAAc,CACd,iBACF,CAGA,gDACE,kBACF,CAGA,uCACE,oBAEF,CAQA,6FAEE,eAAgB,CADhB,YAEF,CAGA,yBACE,yBAEE,OAAQ,CADR,eAEF,CAEA,aAEE,cAAe,CADf,eAEF,CAEA,kBAEE,gBAAiB,CADjB,YAEF,CAEA,4CACE,cACF,CACF,CChUA,KAIE,wBAAyB,CADzB,uEAAgF,CADhF,YAIF,CAGA,cARE,YAAa,CAIb,eAYF,CARA,SAEE,eAAiB,CACjB,8BAA+B,CAI/B,8BAAwC,CAFxC,qBAAsB,CAJtB,WAOF,CAEA,gBAGE,kDAA6D,CAD7D,+BAAgC,CAEhC,UAAY,CAHZ,YAIF,CAEA,eAKE,kBAAmB,CADnB,YAAa,CAHb,cAAe,CACf,eAAgB,CAIhB,OAAQ,CAHR,QAIF,CAGA,eAEE,+BAAgC,CADhC,YAEF,CAEA,eAME,sBAA6B,CAJ7B,iBAAkB,CAClB,cAAe,CAEf,iBAAkB,CAJlB,iBAAkB,CAGlB,kBAGF,CAEA,qBACE,wBAAyB,CACzB,oBACF,CAEA,sBACE,wBAAyB,CACzB,kDAA6D,CAE7D,oBAAqB,CADrB,UAEF,CAEA,gBAEE,cAAe,CADf,eAAgB,CAEhB,iBACF,CAEA,eACE,cAAe,CACf,UACF,CAGA,iBACE,QAAO,CAEP,eAAgB,CADhB,YAEF,CAEA,eAME,kBAAmB,CAFnB,aAAc,CACd,YAAa,CAJb,cAAe,CACf,eAAgB,CAKhB,OAAQ,CAJR,kBAKF,CAEA,cACE,cACF,CAEA,cAEE,aAAc,CACd,cAAe,CAEf,eAAgB,CAJhB,aAAc,CAGd,oBAEF,CAEA,oBACE,aACF,CAEA,sBAEE,aAAc,CADd,eAEF,CAEA,sBAEE,aAAc,CADd,eAEF,CAEA,sBACE,aACF,CAEA,cACE,aAAc,CACd,aACF,CAEA,eACE,aAAc,CACd,iBAAkB,CAElB,cAAe,CADf,iBAEF,CAGA,SAEE,4BAA6B,CAC7B,YAAa,CAEb,cAAe,CADf,OAAQ,CAHR,YAKF,CAEA,aAIE,eAAiB,CADjB,wBAAyB,CAEzB,iBAAkB,CAKlB,aAAc,CAFd,cAAe,CAPf,QAAO,CAKP,cAAe,CACf,eAAgB,CALhB,gBAAiB,CAOjB,kBAEF,CAEA,mBACE,wBAAyB,CACzB,oBACF,CAEA,oBACE,wBAAyB,CACzB,oBAAqB,CACrB,UACF,CAEA,uBACE,wBAAyB,CACzB,oBACF,CAEA,sBAEE,kBAAmB,CADnB,UAEF,CAGA,kBAGE,wBAAyB,CADzB,4BAA6B,CAD7B,YAGF,CAEA,gBAIE,aAAc,CAHd,cAAe,CACf,eAAgB,CAChB,kBAEF,CAEA,cAGE,kBACF,CAEA,4BALE,YAAa,CACb,sBAcF,CAVA,cAME,kBAAmB,CAFnB,kDAA6D,CAD7D,iBAAkB,CAMlB,UAAY,CADZ,qBAAsB,CANtB,WAAY,CADZ,UASF,CAEA,cACE,cAAe,CACf,eACF,CAEA,aACE,cAAe,CACf,UACF,CAEA,gBACE,kBACF,CAEA,aACE,YAAa,CAGb,cAAe,CAFf,6BAA8B,CAC9B,aAEF,CAEA,cACE,aACF,CAEA,cAEE,aAAc,CADd,eAEF,CAEA,aACE,eACF,CAEA,mBAIE,aAAc,CAHd,cAAe,CACf,eAAgB,CAChB,iBAEF,CAEA,iBAEE,wBAAyB,CACzB,iBAAkB,CAGlB,aAAc,CAFd,cAAe,CAGf,eAAgB,CAFhB,iBAAkB,CAJlB,eAOF,CAGA,cAIE,eAAiB,CAFjB,YAAa,CADb,QAAO,CAEP,qBAAsB,CAEtB,eACF,CAEA,eAME,kBAAmB,CAHnB,eAAiB,CADjB,+BAAgC,CAEhC,YAAa,CACb,6BAA8B,CAJ9B,iBAMF,CAEA,uBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,QACF,CAEA,eAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,kBAGE,wBAAyB,CAEzB,iBAAkB,CAHlB,aAAc,CADd,cAAe,CAGf,eAEF,CAGA,UAEE,eAAiB,CACjB,6BAA8B,CAI9B,+BAAyC,CAHzC,YAAa,CACb,qBAAsB,CACtB,eAAgB,CALhB,WAOF,CAEA,WAGE,kDAA6D,CAD7D,+BAAgC,CAEhC,UAAY,CAHZ,YAIF,CAEA,UACE,cAAe,CACf,eAAgB,CAChB,cACF,CAEA,WACE,cAAe,CACf,UACF,CAGA,gBAEE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,eACF,CAEA,eACE,QAAO,CACP,eAAgB,CAChB,YACF,CAGA,iBAEE,iBAAkB,CADlB,iBAEF,CAEA,cACE,cAAe,CACf,kBACF,CAEA,iBAIE,aAAc,CAHd,cAAe,CACf,eAAgB,CAChB,kBAEF,CAEA,gBAEE,aAAc,CADd,cAAe,CAGf,eAAgB,CADhB,kBAEF,CAEA,iBAGE,aAAc,CADd,cAAe,CAEf,aAAc,CACd,iBAAkB,CAJlB,eAKF,CAEA,iBACE,iBACF,CAGA,SAGE,sBAAuB,CAFvB,YAAa,CAGb,QAAS,CAFT,kBAGF,CAEA,cACE,0BACF,CAEA,gBAKE,kBAAmB,CAFnB,iBAAkB,CAClB,YAAa,CAIb,aAAc,CADd,cAAe,CALf,WAAY,CAIZ,sBAAuB,CALvB,UAQF,CAEA,8BACE,wBAAyB,CACzB,UACF,CAEA,mCACE,wBAAyB,CACzB,UACF,CAEA,iBAME,oBAAqB,CAHrB,kBAAmB,CACnB,cAAe,CACf,eAAgB,CAJhB,aAAc,CACd,iBAKF,CAEA,+BACE,wBAAyB,CAEzB,8BAA+B,CAD/B,UAEF,CAEA,oCACE,wBAAyB,CAEzB,6BAA8B,CAD9B,aAEF,CAGA,kBAGE,kBAAmB,CAFnB,YAAa,CACb,OAEF,CAEA,uBAKE,8BAA+B,CAD/B,wBAAyB,CADzB,iBAAkB,CADlB,UAAW,CADX,SAKF,CAEA,oCACE,mBACF,CAEA,oCACE,mBACF,CAEA,kBACE,UAEE,UAAY,CADZ,uBAEF,CACA,IAEE,SAAU,CADV,2BAEF,CACF,CAGA,sBAGE,eAAiB,CADjB,4BAA6B,CAD7B,YAGF,CAEA,oBACE,YAAa,CACb,OAAQ,CACR,iBACF,CAEA,YAGE,wBAAyB,CACzB,kBAAmB,CAHnB,QAAO,CAIP,cAAe,CACf,YAAa,CAJb,iBAAkB,CAKlB,WAAY,CACZ,2BACF,CAEA,kBACE,oBACF,CAEA,qBACE,wBAAyB,CACzB,UACF,CAEA,aAWE,kBAAmB,CANnB,kDAA6D,CAD7D,WAAY,CADZ,iBAAkB,CAGlB,UAAY,CAEZ,cAAe,CAEf,YAAa,CAHb,cAAe,CALf,WAAY,CAUZ,sBAAuB,CAHvB,kBAAoB,CARpB,UAYF,CAEA,kCAEE,+BAA+C,CAD/C,qBAEF,CAEA,sBAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAEA,YAEE,aAAc,CADd,cAAe,CAEf,iBACF,CAGA,0BACE,UACE,WACF,CACF,CAEA,0BACE,SACE,WACF,CAEA,UACE,WACF,CACF,CAEA,yBACE,KACE,qBACF,CAEA,mBAGE,WAAY,CACZ,gBAAiB,CAFjB,UAGF,CAEA,cACE,QAAO,CACP,gBACF,CACF,CAGA,sEAEE,SACF,CAEA,kFAEE,kBAAmB,CACnB,iBACF,CAEA,kFAEE,kBAAmB,CACnB,iBACF,CAEA,8FAEE,kBACF,CAGA,cAEE,oBAGF,CAEA,4BAJE,2BAA4B,CAF5B,mBAAoB,CAGpB,eAQF,CALA,cAEE,oBAGF,CCxlBA,gBAME,uCAAwC,CALxC,kCAAoC,CACpC,kCAAoC,CACpC,2BAA6B,CAC7B,yBAA2B,CAC3B,iCAEF,CAEA,0BACE,GACE,wBAAyB,CACzB,4BACF,CACA,IACE,wBAAyB,CACzB,+BACF,CACA,GACE,wBAAyB,CACzB,4BACF,CACF,CAGA,sBACE,sCACF,CAEA,yBACE,GAEE,mCAA6C,CAD7C,kBAEF,CACA,IAEE,qCAAoD,CADpD,qBAEF,CACA,GAEE,mCAA6C,CAD7C,kBAEF,CACF,CAGA,2BACE,oCAAoD,CACpD,uCAAyC,CAGzC,2BAA6B,CAD7B,2BAA6B,CAD7B,2BAA6B,CAG7B,iCACF,CAEA,iBACE,8CACF,CAEA,iCACE,GACE,0BAAyC,CACzC,4BACF,CACA,IACE,0BAAyC,CACzC,8BACF,CACA,GACE,0BAAyC,CACzC,4BACF,CACF,CAGA,cACE,mFAA6F,CAC7F,eACF,CAEA,qBAGE,wBAAyB,CACzB,iBAAkB,CAHlB,mFAAkG,CAClG,cAAe,CAGf,eACF,CAGA,mBACE,WAAY,CACZ,UACF,CAGA,mBAGE,wBAAyB,CAFzB,kBAAmB,CACnB,UAEF,CAEA,sBACE,oBAAqB,CAGrB,oBAAa,CAAb,YAAa,CADb,sBAAuB,CAEvB,kBAAmB,CAHnB,qBAIF,CAGA,iBAEE,eAAgB,CADhB,UAEF,CAGA,0BAEE,uBACE,WACF,CAEA,wBACE,WACF,CACF,CAEA,0BACE,mBACE,gBACF,CAEA,sBACE,qBACF,CAGA,uBACE,WACF,CAEA,wBACE,WACF,CACF,CAEA,0BACE,mBACE,eACF,CAEA,sBACE,sBACF,CAGA,uBACE,WACF,CAEA,wBACE,WACF,CACF,CAGA,0BACE,mBACE,qBAAsB,CACtB,WACF,CAEA,+CAGE,gBAAiB,CADjB,UAEF,CACF", "sources": ["index.css", "components/Editor/editor.css", "components/Editor/FloatingButton.css", "components/Editor/FloatingToolbar.css", "components/Common/SmartCardMenu.css", "components/Common/PRDContentRenderer.css", "components/PRDEditor/PRDEditor.css", "components/Editor/EnhancedMilkdownEditor.css", "App.css", "components/PRDEvaluation/PRDReviewTab.css"], "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n/* 科技蓝主题自定义样式 */\n:root { \n    --primary: #2B6CB0; /* 科技蓝 */\n    --success: #38A169; /* 绿色 */\n    --warning: #D69E2E; /* 黄色 */\n    --highlight: #FFF9C4; \n}\n\n/* 平滑过渡动画 */\n.transition-all {\n    transition: all 0.3s ease;\n}\n\n/* 高亮脉冲动画 */\n@keyframes pulse { \n    0% { background-color: transparent; } \n    50% { background-color: var(--highlight); } \n    100% { background-color: transparent; } \n}\n\n.highlight-pulse { \n    animation: pulse 1.2s ease-in-out 2; \n}\n\n/* 滚动条样式 */\n.no-scrollbar::-webkit-scrollbar {\n    display: none;\n}\n\n.no-scrollbar {\n    -ms-overflow-style: none;  /* IE and Edge */\n    scrollbar-width: none;  /* Firefox */\n}\n\n/* 呼吸感间距 */\n.breathing-space {\n    padding: 1.5rem;\n}\n\n/* 文档样式增强 */\n.prose h1 { \n    scroll-margin-top: 5rem;\n    font-size: 2rem;\n    font-weight: 700;\n    color: var(--primary);\n    transition: background-color 0.3s ease;\n}\n\n.prose h2 { \n    scroll-margin-top: 5rem;\n    font-size: 1.5rem;\n    font-weight: 600;\n    color: var(--primary);\n    transition: background-color 0.3s ease;\n}\n\n.prose h3 { \n    scroll-margin-top: 5rem;\n    font-size: 1.25rem;\n    font-weight: 600;\n    color: var(--primary);\n    transition: background-color 0.3s ease;\n}\n\n/* 大纲导航样式 */\n.outline-item {\n    position: relative;\n    transition: all 0.2s ease;\n    cursor: pointer;\n}\n\n.outline-item::before {\n    content: '';\n    position: absolute;\n    left: 0;\n    top: 0;\n    bottom: 0;\n    width: 3px;\n    background: transparent;\n    transition: background 0.2s ease;\n    border-radius: 0 2px 2px 0;\n}\n\n.outline-item:hover::before {\n    background: var(--primary);\n}\n\n.outline-item.active::before {\n    background: var(--primary);\n}\n\n/* 大纲层级样式 - 调整左边距确保竖线不覆盖文字 */\n.outline-level-1 {\n    font-size: 1rem;\n    font-weight: 600;\n    color: var(--primary);\n    padding-left: 1rem; /* 增加左边距，为竖线留空间 */\n    margin-bottom: 0.25rem;\n}\n\n.outline-level-2 {\n    font-size: 0.875rem;\n    font-weight: 500;\n    color: #4B5563;\n    padding-left: 2.25rem; /* 增加左边距 */\n    margin-bottom: 0.125rem;\n}\n\n.outline-level-3 {\n    font-size: 0.75rem;\n    color: #6B7280;\n    padding-left: 3.5rem; /* 增加左边距 */\n    margin-bottom: 0.125rem;\n}\n\n/* 大纲项悬浮效果 */\n.outline-item:hover {\n    background-color: rgba(43, 108, 176, 0.05);\n}\n\n.outline-item.active {\n    background-color: rgba(43, 108, 176, 0.1);\n    color: var(--primary);\n    font-weight: 600;\n}\n\n/* 层级缩进指示器 - 调整位置避免与竖线冲突 */\n.outline-level-2::after {\n    content: '├─';\n    position: absolute;\n    left: 1.25rem; /* 调整位置，避免与竖线重叠 */\n    color: #CBD5E1;\n    font-size: 0.75rem;\n}\n\n.outline-level-3::after {\n    content: '└─';\n    position: absolute;\n    left: 2.5rem; /* 调整位置 */\n    color: #CBD5E1;\n    font-size: 0.75rem;\n}\n\n/* 折叠功能样式 */\n.outline-toggle {\n    position: absolute;\n    right: 0.5rem;\n    top: 50%;\n    transform: translateY(-50%);\n    width: 1rem;\n    height: 1rem;\n    border: none;\n    background: transparent;\n    cursor: pointer;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 0.25rem;\n    transition: all 0.2s ease;\n}\n\n.outline-toggle:hover {\n    background-color: rgba(43, 108, 176, 0.1);\n}\n\n.outline-toggle.collapsed {\n    transform: translateY(-50%) rotate(-90deg);\n}\n\n.outline-children {\n    overflow: hidden;\n    transition: all 0.3s ease;\n}\n\n.outline-children.collapsed {\n    max-height: 0;\n    opacity: 0;\n}\n\n/* 大纲点击高亮状态 */\n.outline-item.clicked-active {\n    background: rgba(249, 115, 22, 0.2) !important;\n    border-left: 4px solid #f97316 !important;\n    color: #ea580c !important;\n    font-weight: 700 !important;\n    transform: translateX(2px);\n    box-shadow: 0 2px 8px rgba(249, 115, 22, 0.2);\n}\n\n/* 内容区域被点击高亮 */\n.outline-clicked-highlight {\n    background: rgba(249, 115, 22, 0.15) !important;\n    border-left: 4px solid #f97316;\n    padding-left: 12px !important;\n    border-radius: 4px;\n}\n\n/* 多行文本截断 */\n.line-clamp-2 {\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n}\n\n/* 章节区域高亮（大纲点击后） */\n.outline-section-highlight {\n    background: rgba(249, 115, 22, 0.12) !important;\n    border-left: 6px solid #f97316;\n    padding-left: 16px !important;\n    border-radius: 6px;\n    box-shadow: 0 2px 12px rgba(249, 115, 22, 0.2);\n    transition: all 0.3s ease;\n} ", "/* 简单编辑器样式 */\n.simple-editor-container {\n  height: 100%;\n  background: white;\n  overflow: hidden;\n}\n\n.simple-editor-textarea {\n  width: 100%;\n  height: 100%;\n  border: none;\n  outline: none;\n  padding: 24px 32px;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Monaco', 'Men<PERSON>', 'Ubuntu Mono', monospace;\n  font-size: 16px;\n  line-height: 1.6;\n  color: #374151;\n  background: white;\n  resize: none;\n  tab-size: 2;\n}\n\n.simple-editor-textarea::placeholder {\n  color: #9ca3af;\n  font-style: italic;\n}\n\n.simple-editor-textarea:focus {\n  outline: none;\n}\n\n/* 响应式调整 */\n@media (max-width: 768px) {\n  .simple-editor-textarea {\n    padding: 16px 20px;\n    font-size: 15px;\n  }\n}\n\n/* 滚动条样式 */\n.simple-editor-textarea::-webkit-scrollbar {\n  width: 8px;\n}\n\n.simple-editor-textarea::-webkit-scrollbar-track {\n  background: #f1f5f9;\n  border-radius: 4px;\n}\n\n.simple-editor-textarea::-webkit-scrollbar-thumb {\n  background: #cbd5e1;\n  border-radius: 4px;\n}\n\n.simple-editor-textarea::-webkit-scrollbar-thumb:hover {\n  background: #94a3b8;\n}\n\n/* Milkdown编辑器容器 */\n.milkdown-container {\n  height: 100%;\n  background: white;\n  overflow: hidden;\n}\n\n/* 编辑器主体样式 */\n.milkdown-container .milkdown {\n  height: 100%;\n  padding: 24px 32px;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n  font-size: 16px;\n  line-height: 1.6;\n  color: #374151;\n  outline: none;\n  border: none;\n  overflow-y: auto;\n}\n\n/* 覆盖默认主题颜色 */\n.milkdown-container .milkdown .ProseMirror {\n  outline: none;\n}\n\n/* 标题样式 */\n.milkdown-container .milkdown h1 {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1f2937;\n  margin: 32px 0 16px 0;\n  padding-bottom: 8px;\n  border-bottom: 3px solid #ff6b35;\n  scroll-margin-top: 80px;\n}\n\n.milkdown-container .milkdown h2 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #374151;\n  margin: 24px 0 12px 0;\n  scroll-margin-top: 80px;\n}\n\n.milkdown-container .milkdown h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #4b5563;\n  margin: 20px 0 8px 0;\n  scroll-margin-top: 80px;\n}\n\n/* 段落样式 */\n.milkdown-container .milkdown p {\n  margin: 12px 0;\n  line-height: 1.7;\n}\n\n/* 强调样式 */\n.milkdown-container .milkdown strong {\n  font-weight: 700;\n  color: #1f2937;\n}\n\n.milkdown-container .milkdown em {\n  font-style: italic;\n  color: #6b7280;\n}\n\n/* 引用块样式 */\n.milkdown-container .milkdown blockquote {\n  border-left: 4px solid #ff6b35;\n  background: #fef3e2;\n  padding: 16px 20px;\n  margin: 20px 0;\n  font-style: italic;\n  border-radius: 0 6px 6px 0;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\n}\n\n.milkdown-container .milkdown blockquote p {\n  margin: 0;\n  color: #92400e;\n}\n\n/* 列表样式 */\n.milkdown-container .milkdown ul,\n.milkdown-container .milkdown ol {\n  margin: 16px 0;\n  padding-left: 24px;\n}\n\n.milkdown-container .milkdown li {\n  margin: 4px 0;\n  line-height: 1.6;\n}\n\n.milkdown-container .milkdown ul li {\n  list-style-type: disc;\n}\n\n.milkdown-container .milkdown ol li {\n  list-style-type: decimal;\n}\n\n/* 表格样式 */\n.milkdown-container .milkdown table {\n  border-collapse: collapse;\n  width: 100%;\n  margin: 20px 0;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.milkdown-container .milkdown th,\n.milkdown-container .milkdown td {\n  border: 1px solid #e5e7eb;\n  padding: 12px 16px;\n  text-align: left;\n}\n\n.milkdown-container .milkdown th {\n  background: #f9fafb;\n  font-weight: 600;\n  color: #374151;\n}\n\n.milkdown-container .milkdown tbody tr:nth-child(even) {\n  background: #f9fafb;\n}\n\n.milkdown-container .milkdown tbody tr:hover {\n  background: #f3f4f6;\n}\n\n/* 代码样式 */\n.milkdown-container .milkdown code {\n  background: #f3f4f6;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-size: 0.9em;\n  color: #d97706;\n  border: 1px solid #e5e7eb;\n}\n\n.milkdown-container .milkdown pre {\n  background: #1f2937;\n  color: #f9fafb;\n  padding: 16px 20px;\n  border-radius: 8px;\n  overflow-x: auto;\n  margin: 20px 0;\n  border: 1px solid #374151;\n}\n\n.milkdown-container .milkdown pre code {\n  background: transparent;\n  padding: 0;\n  color: inherit;\n  border: none;\n}\n\n/* 水平分割线 */\n.milkdown-container .milkdown hr {\n  border: none;\n  border-top: 2px solid #e5e7eb;\n  margin: 32px 0;\n  border-radius: 1px;\n}\n\n/* 光标和选择样式 */\n.milkdown-container .milkdown .ProseMirror-focused {\n  outline: none;\n}\n\n.milkdown-container .milkdown ::selection {\n  background: #fef3e2;\n}\n\n/* 占位符样式 */\n.milkdown-container .milkdown .placeholder {\n  color: #9ca3af;\n  pointer-events: none;\n  font-style: italic;\n}\n\n/* 响应式调整 */\n@media (max-width: 768px) {\n  .milkdown-container .milkdown {\n    padding: 16px 20px;\n    font-size: 15px;\n  }\n  \n  .milkdown-container .milkdown h1 {\n    font-size: 1.75rem;\n  }\n  \n  .milkdown-container .milkdown h2 {\n    font-size: 1.375rem;\n  }\n  \n  .milkdown-container .milkdown h3 {\n    font-size: 1.125rem;\n  }\n}\n\n/* 滚动条样式 */\n.milkdown-container .milkdown::-webkit-scrollbar {\n  width: 8px;\n}\n\n.milkdown-container .milkdown::-webkit-scrollbar-track {\n  background: #f1f5f9;\n  border-radius: 4px;\n}\n\n.milkdown-container .milkdown::-webkit-scrollbar-thumb {\n  background: #cbd5e1;\n  border-radius: 4px;\n}\n\n.milkdown-container .milkdown::-webkit-scrollbar-thumb:hover {\n  background: #94a3b8;\n} ", "/* 悬浮按钮样式 */\n.floating-button {\n  position: fixed;\n  bottom: 24px;\n  right: 24px;\n  width: 56px;\n  height: 56px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #3b82f6, #1d4ed8);\n  color: white;\n  border: none;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);\n  cursor: pointer;\n  z-index: 1000;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 18px;\n  user-select: none;\n  outline: none;\n}\n\n.floating-button:hover {\n  transform: scale(1.1);\n  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.5);\n  background: linear-gradient(135deg, #2563eb, #1e40af);\n}\n\n.floating-button:active {\n  transform: scale(1.05);\n  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.6);\n}\n\n.floating-button:focus {\n  outline: 2px solid #93c5fd;\n  outline-offset: 2px;\n}\n\n/* 打开状态的样式 */\n.floating-button.open {\n  background: linear-gradient(135deg, #ef4444, #dc2626);\n  transform: rotate(90deg);\n}\n\n.floating-button.open:hover {\n  background: linear-gradient(135deg, #dc2626, #b91c1c);\n  transform: rotate(90deg) scale(1.1);\n}\n\n/* 图标样式 */\n.floating-button-icon {\n  display: inline-block;\n  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.floating-button.open .floating-button-icon {\n  transform: rotate(-90deg);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .floating-button {\n    bottom: 16px;\n    right: 16px;\n    width: 48px;\n    height: 48px;\n    font-size: 16px;\n  }\n}\n\n@media (max-width: 480px) {\n  .floating-button {\n    bottom: 12px;\n    right: 12px;\n    width: 44px;\n    height: 44px;\n    font-size: 14px;\n  }\n}\n\n/* 高对比度模式支持 */\n@media (prefers-contrast: high) {\n  .floating-button {\n    border: 2px solid #000;\n    background: #0066cc;\n  }\n  \n  .floating-button:hover {\n    background: #0052a3;\n  }\n}\n\n/* 减少动画模式支持 */\n@media (prefers-reduced-motion: reduce) {\n  .floating-button {\n    transition: none;\n  }\n  \n  .floating-button:hover {\n    transform: none;\n  }\n  \n  .floating-button-icon {\n    transition: none;\n  }\n}\n", "/* 悬浮工具栏样式 */\n.floating-toolbar {\n  position: fixed;\n  bottom: 90px;\n  right: 24px;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);\n  border: 1px solid rgba(0, 0, 0, 0.08);\n  padding: 16px;\n  z-index: 999;\n  min-width: 220px;\n  max-width: 280px;\n  animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  user-select: none;\n}\n\n@keyframes slideUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n/* 工具栏区域 */\n.toolbar-section {\n  margin-bottom: 16px;\n}\n\n.toolbar-section:last-of-type {\n  margin-bottom: 0;\n}\n\n.toolbar-section-title {\n  font-size: 12px;\n  font-weight: 600;\n  color: #6b7280;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n  margin: 0 0 8px 0;\n  padding: 0;\n}\n\n/* 工具项容器 */\n.toolbar-items {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n/* 工具项样式 */\n.toolbar-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  border: none;\n  background: transparent;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 14px;\n  color: #374151;\n  text-align: left;\n  width: 100%;\n}\n\n.toolbar-item:hover {\n  background: #f3f4f6;\n  color: #1f2937;\n}\n\n.toolbar-item:active {\n  background: #e5e7eb;\n  transform: scale(0.98);\n}\n\n.toolbar-item:focus {\n  outline: 2px solid #3b82f6;\n  outline-offset: -2px;\n}\n\n/* 工具项图标 */\n.toolbar-item-icon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 20px;\n  height: 20px;\n  font-size: 16px;\n  flex-shrink: 0;\n}\n\n/* 工具项文本 */\n.toolbar-item-text {\n  font-size: 14px;\n  font-weight: 500;\n  flex: 1;\n}\n\n/* 关闭按钮区域 */\n.toolbar-close {\n  border-top: 1px solid #e5e7eb;\n  padding-top: 12px;\n  margin-top: 12px;\n  display: flex;\n  justify-content: center;\n}\n\n.toolbar-close-button {\n  width: 32px;\n  height: 32px;\n  border: none;\n  background: #f3f4f6;\n  border-radius: 50%;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: #6b7280;\n  transition: all 0.2s ease;\n}\n\n.toolbar-close-button:hover {\n  background: #e5e7eb;\n  color: #374151;\n}\n\n.toolbar-close-button:active {\n  background: #d1d5db;\n  transform: scale(0.95);\n}\n\n.toolbar-close-button:focus {\n  outline: 2px solid #3b82f6;\n  outline-offset: 2px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .floating-toolbar {\n    bottom: 70px;\n    right: 16px;\n    left: 16px;\n    min-width: auto;\n    max-width: none;\n  }\n}\n\n@media (max-width: 480px) {\n  .floating-toolbar {\n    bottom: 60px;\n    right: 12px;\n    left: 12px;\n    padding: 12px;\n  }\n  \n  .toolbar-item {\n    padding: 6px 8px;\n    font-size: 13px;\n  }\n  \n  .toolbar-item-icon {\n    width: 18px;\n    height: 18px;\n    font-size: 14px;\n  }\n  \n  .toolbar-item-text {\n    font-size: 13px;\n  }\n}\n\n/* 暗色主题支持 */\n@media (prefers-color-scheme: dark) {\n  .floating-toolbar {\n    background: #1f2937;\n    border-color: #374151;\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n  }\n  \n  .toolbar-section-title {\n    color: #9ca3af;\n  }\n  \n  .toolbar-item {\n    color: #e5e7eb;\n  }\n  \n  .toolbar-item:hover {\n    background: #374151;\n    color: #f9fafb;\n  }\n  \n  .toolbar-item:active {\n    background: #4b5563;\n  }\n  \n  .toolbar-close {\n    border-color: #374151;\n  }\n  \n  .toolbar-close-button {\n    background: #374151;\n    color: #9ca3af;\n  }\n  \n  .toolbar-close-button:hover {\n    background: #4b5563;\n    color: #e5e7eb;\n  }\n}\n\n/* 高对比度模式支持 */\n@media (prefers-contrast: high) {\n  .floating-toolbar {\n    border: 2px solid #000;\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);\n  }\n  \n  .toolbar-item {\n    border: 1px solid transparent;\n  }\n  \n  .toolbar-item:hover {\n    border-color: #000;\n    background: #f0f0f0;\n  }\n  \n  .toolbar-item:focus {\n    border-color: #0066cc;\n    outline: 2px solid #0066cc;\n  }\n}\n\n/* 减少动画模式支持 */\n@media (prefers-reduced-motion: reduce) {\n  .floating-toolbar {\n    animation: none;\n  }\n  \n  .toolbar-item {\n    transition: none;\n  }\n  \n  .toolbar-close-button {\n    transition: none;\n  }\n}\n", "/* SmartCardMenu 样式优化 */\n\n.smart-card-menu {\n  /* 确保菜单在所有情况下都有固定宽度 */\n  min-width: 320px;\n  max-width: 320px;\n  \n  /* 优化阴影效果 */\n  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  \n  /* 动画效果 */\n  animation: smartCardMenuFadeIn 0.15s ease-out;\n  transform-origin: top center;\n}\n\n@keyframes smartCardMenuFadeIn {\n  from {\n    opacity: 0;\n    transform: scale(0.95) translateY(-5px);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1) translateY(0);\n  }\n}\n\n/* 菜单项悬停效果优化 */\n.smart-card-menu button:hover {\n  transform: translateX(2px);\n  transition: all 0.15s ease-out;\n}\n\n/* 文本省略和换行控制 */\n.smart-card-menu .truncate-text {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.smart-card-menu .break-words {\n  word-break: break-word;\n  overflow-wrap: break-word;\n  hyphens: auto;\n}\n\n/* 选中文本预览区域优化 */\n.smart-card-menu .text-preview {\n  line-height: 1.4;\n  max-height: 4rem; /* 约3行文字 */\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n}\n\n/* 响应式设计 */\n@media (max-width: 640px) {\n  .smart-card-menu {\n    min-width: 280px;\n    max-width: 280px;\n  }\n}\n\n@media (max-width: 480px) {\n  .smart-card-menu {\n    min-width: calc(100vw - 40px);\n    max-width: calc(100vw - 40px);\n  }\n}\n\n/* 卡片类型按钮优化 */\n.smart-card-menu .card-type-button {\n  border-radius: 8px;\n  transition: all 0.15s ease-out;\n}\n\n.smart-card-menu .card-type-button:hover {\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n/* 图标动画 */\n.smart-card-menu .card-type-icon {\n  transition: transform 0.15s ease-out;\n}\n\n.smart-card-menu .card-type-button:hover .card-type-icon {\n  transform: scale(1.1);\n}\n\n/* 关闭按钮优化 */\n.smart-card-menu .close-button {\n  border-radius: 4px;\n  transition: all 0.15s ease-out;\n}\n\n.smart-card-menu .close-button:hover {\n  background-color: rgba(0, 0, 0, 0.05);\n  transform: scale(1.1);\n}\n\n/* 滚动条样式 */\n.smart-card-menu .text-preview::-webkit-scrollbar {\n  width: 4px;\n}\n\n.smart-card-menu .text-preview::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 2px;\n}\n\n.smart-card-menu .text-preview::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 2px;\n}\n\n.smart-card-menu .text-preview::-webkit-scrollbar-thumb:hover {\n  background: #a1a1a1;\n}\n", "/* PRD内容渲染器样式 - 与PRD智能评估保持一致 */\n\n.prd-content-renderer {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n  line-height: 1.6;\n}\n\n/* 章节内容样式 */\n.section-content {\n  scroll-margin-top: 80px;\n}\n\n.section-body p {\n  margin-bottom: 1rem;\n  line-height: 1.6;\n}\n\n.section-body p:last-child {\n  margin-bottom: 0;\n}\n\n/* 表格样式 - 与PRD智能评估完全一致 */\n.table-container {\n  width: 100%;\n  overflow-x: auto;\n  margin: 1rem 0;\n  border: 1px solid #e5e7eb;\n  border-radius: 0.5rem;\n  background: white;\n}\n\n.prd-content-table {\n  width: 100%;\n  border-collapse: collapse;\n  font-size: 0.875rem;\n  table-layout: fixed;\n}\n\n.prd-content-table td {\n  padding: 0.75rem;\n  border-bottom: 1px solid #f3f4f6;\n  border-right: 1px solid #f3f4f6;\n  vertical-align: top;\n  word-wrap: break-word;\n  word-break: break-word;\n  overflow-wrap: anywhere;\n}\n\n.prd-content-table tr:last-child td {\n  border-bottom: none;\n}\n\n.prd-content-table td:last-child {\n  border-right: none;\n}\n\n.prd-content-table tr:nth-child(even) {\n  background-color: #f9fafb;\n}\n\n.prd-content-table tr:hover {\n  background-color: #f3f4f6;\n}\n\n/* 编辑模式样式 */\n.editable-content {\n  border: 1px dashed transparent;\n  border-radius: 4px;\n  padding: 8px;\n  transition: all 0.2s ease;\n  min-height: 1.5rem;\n}\n\n.editable-content:hover {\n  border-color: #e5e7eb;\n  background-color: #f9fafb;\n}\n\n.editable-content:focus {\n  outline: none;\n  border-color: #3b82f6;\n  background-color: #eff6ff;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.editable-title {\n  border: 1px dashed transparent;\n  border-radius: 4px;\n  padding: 4px 8px;\n  margin: -4px -8px;\n  transition: all 0.2s ease;\n}\n\n.editable-title:hover {\n  border-color: #e5e7eb;\n  background-color: #f9fafb;\n}\n\n.editable-title:focus {\n  outline: none;\n  border-color: #3b82f6;\n  background-color: #eff6ff;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n/* 文本选中高亮 */\n.prd-content-renderer ::selection {\n  background-color: #fef3c7;\n  color: #92400e;\n}\n\n/* 响应式设计 - 与PRD智能评估保持一致 */\n@media (max-width: 1600px) {\n  .prd-content-table {\n    font-size: 0.8125rem;\n  }\n  \n  .prd-content-table td {\n    padding: 0.625rem;\n  }\n}\n\n@media (max-width: 1400px) {\n  .prd-content-table {\n    font-size: 0.75rem;\n  }\n  \n  .prd-content-table td {\n    padding: 0.5rem;\n  }\n}\n\n@media (max-width: 1200px) {\n  .prd-content-table {\n    font-size: 0.7rem;\n  }\n  \n  .prd-content-table td {\n    padding: 0.375rem;\n  }\n}\n\n@media (max-width: 1024px) {\n  .prd-content-table {\n    font-size: 0.65rem;\n  }\n  \n  .prd-content-table td {\n    padding: 0.25rem;\n  }\n}\n\n/* 智能卡片菜单动画 */\n.smart-card-menu {\n  animation: fadeInUp 0.2s ease-out;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 章节高亮效果 - 与PRD智能评估保持一致 */\n.section-content.highlight {\n  background-color: #fef3c7;\n  border-left: 4px solid #f59e0b;\n  padding-left: 1rem;\n  margin-left: -1rem;\n  border-radius: 0.25rem;\n  animation: highlight-pulse 2s ease-in-out;\n}\n\n@keyframes highlight-pulse {\n  0%, 100% {\n    background-color: #fef3c7;\n  }\n  50% {\n    background-color: #fde68a;\n  }\n}\n\n/* 确保长文本正确换行 */\n.section-body {\n  word-wrap: break-word;\n  overflow-wrap: break-word;\n  word-break: break-word;\n}\n\n/* 代码块样式（如果有的话） */\n.section-body code {\n  background-color: #f3f4f6;\n  padding: 0.125rem 0.25rem;\n  border-radius: 0.25rem;\n  font-size: 0.875em;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n}\n\n/* 列表样式 */\n.section-body ul, .section-body ol {\n  margin: 1rem 0;\n  padding-left: 1.5rem;\n}\n\n.section-body li {\n  margin-bottom: 0.5rem;\n}\n\n/* 链接样式 */\n.section-body a {\n  color: #3b82f6;\n  text-decoration: underline;\n}\n\n.section-body a:hover {\n  color: #1d4ed8;\n}\n\n/* 强调文本样式 */\n.section-body strong {\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.section-body em {\n  font-style: italic;\n  color: #4b5563;\n}\n", "/* PRD编辑器样式 - 与PRD智能评估保持一致 */\n\n/* 导入PRD智能评估的样式 */\n@import '../PRDEvaluation/PRDReviewTab.css';\n\n/* PRD编辑器特有样式 */\n.prd-editor-layout {\n  min-width: 0;\n  width: 100%;\n}\n\n/* 左侧面板样式 - 与PRD智能评估保持一致 */\n.prd-editor-left-panel {\n  width: 18rem; /* w-72 = 288px */\n  flex-shrink: 0;\n  background-color: white;\n  border-right: 1px solid #e5e7eb;\n}\n\n/* 右侧面板样式 - 与PRD智能评估保持一致 */\n.prd-editor-right-panel {\n  width: 24rem; /* w-96 = 384px */\n  flex-shrink: 0;\n  background-color: #f8fafc;\n  border-left: 1px solid #e5e7eb;\n}\n\n/* 中间内容区域 */\n.prd-editor-content {\n  flex: 1;\n  overflow-y: auto;\n  background-color: white;\n  padding: 1.5rem;\n}\n\n/* 编辑模式特殊样式 */\n.prd-editor-edit-mode .editable-content {\n  border: 1px dashed transparent;\n  border-radius: 4px;\n  padding: 8px;\n  transition: all 0.2s ease;\n}\n\n.prd-editor-edit-mode .editable-content:hover {\n  border-color: #e5e7eb;\n  background-color: #f9fafb;\n}\n\n.prd-editor-edit-mode .editable-content:focus {\n  outline: none;\n  border-color: #3b82f6;\n  background-color: #eff6ff;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n/* 工具栏样式 */\n.prd-editor-toolbar {\n  height: 4rem;\n  border-bottom: 1px solid #e5e7eb;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 1.5rem;\n  background-color: white;\n}\n\n/* 模式切换按钮样式 */\n.mode-toggle-button {\n  display: flex;\n  align-items: center;\n  space-x: 0.5rem;\n  padding: 0.5rem 1rem;\n  font-size: 0.875rem;\n  font-weight: 600;\n  border: 1px solid #d1d5db;\n  border-radius: 0.5rem;\n  transition: all 0.2s ease;\n}\n\n.mode-toggle-button:hover {\n  background-color: #f9fafb;\n}\n\n.mode-toggle-button.active {\n  background-color: #3b82f6;\n  color: white;\n  border-color: #3b82f6;\n}\n\n/* 大纲导航样式增强 */\n.outline-navigation {\n  padding: 1rem;\n}\n\n.outline-item {\n  padding: 0.5rem 0.75rem;\n  border-radius: 0.375rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  margin-bottom: 0.25rem;\n}\n\n.outline-item:hover {\n  background-color: #f3f4f6;\n}\n\n.outline-item.active {\n  background-color: #dbeafe;\n  color: #1d4ed8;\n  font-weight: 500;\n}\n\n.outline-item.level-1 {\n  font-weight: 600;\n  font-size: 0.875rem;\n}\n\n.outline-item.level-2 {\n  padding-left: 1.5rem;\n  font-size: 0.8125rem;\n}\n\n.outline-item.level-3 {\n  padding-left: 2.25rem;\n  font-size: 0.75rem;\n  color: #6b7280;\n}\n\n/* AI助手面板样式 */\n.ai-assistant-panel {\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.ai-assistant-header {\n  height: 3.5rem;\n  border-bottom: 1px solid #e5e7eb;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 1rem;\n  background-color: white;\n}\n\n.ai-assistant-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 1rem;\n}\n\n/* AI卡片样式 */\n.ai-card {\n  background-color: white;\n  border: 1px solid #e5e7eb;\n  border-radius: 0.5rem;\n  padding: 1rem;\n  margin-bottom: 1rem;\n  transition: all 0.2s ease;\n}\n\n.ai-card:hover {\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n}\n\n.ai-card.expanded {\n  border-color: #3b82f6;\n  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.1);\n}\n\n/* 响应式设计 - 与PRD智能评估保持一致 */\n@media (max-width: 1600px) {\n  .prd-editor-left-panel {\n    width: 16rem; /* w-64 */\n  }\n  \n  .prd-editor-right-panel {\n    width: 22rem; /* w-88 */\n  }\n}\n\n@media (max-width: 1400px) {\n  .prd-editor-left-panel {\n    width: 14rem; /* w-56 */\n  }\n  \n  .prd-editor-right-panel {\n    width: 20rem; /* w-80 */\n  }\n  \n  .prd-editor-content {\n    padding: 1rem;\n  }\n}\n\n@media (max-width: 1200px) {\n  .prd-editor-left-panel {\n    width: 12rem; /* w-48 */\n  }\n  \n  .prd-editor-right-panel {\n    width: 18rem; /* w-72 */\n  }\n  \n  .prd-editor-content {\n    padding: 0.75rem;\n  }\n}\n\n@media (max-width: 1024px) {\n  .prd-editor-layout {\n    flex-direction: column;\n    height: auto;\n  }\n  \n  .prd-editor-left-panel,\n  .prd-editor-right-panel {\n    width: 100%;\n    max-height: 300px;\n  }\n  \n  .prd-editor-content {\n    padding: 1rem;\n  }\n}\n\n/* 文档健康度指示器 */\n.document-health {\n  display: flex;\n  align-items: center;\n  space-x: 0.75rem;\n}\n\n.health-score {\n  width: 2.5rem;\n  height: 2.5rem;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 700;\n  font-size: 1.125rem;\n}\n\n.health-score.good {\n  background-color: #dcfce7;\n  color: #16a34a;\n}\n\n.health-score.warning {\n  background-color: #fef3c7;\n  color: #d97706;\n}\n\n.health-score.error {\n  background-color: #fee2e2;\n  color: #dc2626;\n}\n\n/* 保存指示器样式 */\n.save-indicator {\n  display: flex;\n  align-items: center;\n  space-x: 0.5rem;\n  font-size: 0.875rem;\n  color: #6b7280;\n}\n\n.save-indicator.saving {\n  color: #3b82f6;\n}\n\n.save-indicator.error {\n  color: #dc2626;\n}\n\n.save-indicator.saved {\n  color: #16a34a;\n}\n\n/* 动画效果 */\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.fade-in {\n  animation: fadeIn 0.3s ease-out;\n}\n\n/* 滚动条样式 */\n.prd-editor-content::-webkit-scrollbar,\n.ai-assistant-content::-webkit-scrollbar,\n.outline-navigation::-webkit-scrollbar {\n  width: 6px;\n}\n\n.prd-editor-content::-webkit-scrollbar-track,\n.ai-assistant-content::-webkit-scrollbar-track,\n.outline-navigation::-webkit-scrollbar-track {\n  background: #f1f5f9;\n}\n\n.prd-editor-content::-webkit-scrollbar-thumb,\n.ai-assistant-content::-webkit-scrollbar-thumb,\n.outline-navigation::-webkit-scrollbar-thumb {\n  background: #cbd5e1;\n  border-radius: 3px;\n}\n\n.prd-editor-content::-webkit-scrollbar-thumb:hover,\n.ai-assistant-content::-webkit-scrollbar-thumb:hover,\n.outline-navigation::-webkit-scrollbar-thumb:hover {\n  background: #94a3b8;\n}\n", "/* Enhanced Milkdown Editor Styles */\n.enhanced-milkdown-editor {\n  position: relative;\n  border: 1px solid #e2e8f0;\n  border-radius: 8px;\n  background: white;\n  overflow: hidden;\n}\n\n.enhanced-milkdown-editor.error {\n  border-color: #ef4444;\n}\n\n/* 工具栏样式 */\n.enhanced-editor-toolbar {\n  background: #f8fafc;\n  border-bottom: 1px solid #e2e8f0;\n  padding: 8px 12px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.toolbar-btn {\n  padding: 4px 8px;\n  font-size: 12px;\n  border: 1px solid #d1d5db;\n  border-radius: 4px;\n  background: white;\n  color: #374151;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.toolbar-btn:hover {\n  background: #f3f4f6;\n  border-color: #9ca3af;\n}\n\n.toolbar-btn:active {\n  background: #e5e7eb;\n}\n\n.toolbar-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* 编辑器容器样式 */\n.editor-container {\n  position: relative;\n  min-height: 300px;\n  padding: 16px;\n  background: white;\n}\n\n.editor-container.readonly {\n  background: #f9fafb;\n}\n\n/* 加载状态 */\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10;\n}\n\n/* 错误状态 */\n.error-message {\n  padding: 16px;\n  background: #fef2f2;\n  border: 1px solid #fecaca;\n  border-radius: 6px;\n  margin: 16px;\n}\n\n.error-message h3 {\n  color: #991b1b;\n  font-weight: 500;\n  margin: 0 0 4px 0;\n}\n\n.error-message p {\n  color: #dc2626;\n  font-size: 14px;\n  margin: 4px 0 8px 0;\n}\n\n/* Milkdown 编辑器内容样式覆盖 */\n.enhanced-milkdown-editor .milkdown {\n  outline: none;\n  border: none;\n  background: transparent;\n}\n\n.enhanced-milkdown-editor .milkdown .editor {\n  min-height: 250px;\n  padding: 0;\n  line-height: 1.6;\n  font-size: 14px;\n  color: #374151;\n}\n\n/* 标题样式 */\n.enhanced-milkdown-editor .milkdown h1,\n.enhanced-milkdown-editor .milkdown h2,\n.enhanced-milkdown-editor .milkdown h3,\n.enhanced-milkdown-editor .milkdown h4,\n.enhanced-milkdown-editor .milkdown h5,\n.enhanced-milkdown-editor .milkdown h6 {\n  margin: 16px 0 8px 0;\n  font-weight: 600;\n  line-height: 1.4;\n}\n\n.enhanced-milkdown-editor .milkdown h1 { font-size: 24px; color: #111827; }\n.enhanced-milkdown-editor .milkdown h2 { font-size: 20px; color: #1f2937; }\n.enhanced-milkdown-editor .milkdown h3 { font-size: 18px; color: #374151; }\n.enhanced-milkdown-editor .milkdown h4 { font-size: 16px; color: #4b5563; }\n.enhanced-milkdown-editor .milkdown h5 { font-size: 14px; color: #6b7280; }\n.enhanced-milkdown-editor .milkdown h6 { font-size: 12px; color: #9ca3af; }\n\n/* 段落样式 */\n.enhanced-milkdown-editor .milkdown p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n/* 列表样式 */\n.enhanced-milkdown-editor .milkdown ul,\n.enhanced-milkdown-editor .milkdown ol {\n  margin: 8px 0;\n  padding-left: 24px;\n  list-style: none;\n}\n\n.enhanced-milkdown-editor .milkdown ul {\n  list-style-type: disc;\n  list-style-position: outside;\n}\n\n.enhanced-milkdown-editor .milkdown ol {\n  list-style-type: decimal;\n  list-style-position: outside;\n}\n\n.enhanced-milkdown-editor .milkdown li {\n  margin: 4px 0;\n  line-height: 1.5;\n  display: list-item;\n}\n\n/* 确保嵌套列表正确显示 */\n.enhanced-milkdown-editor .milkdown ul ul {\n  list-style-type: circle;\n  margin-left: 16px;\n}\n\n.enhanced-milkdown-editor .milkdown ul ul ul {\n  list-style-type: square;\n}\n\n.enhanced-milkdown-editor .milkdown ol ol {\n  list-style-type: lower-alpha;\n  margin-left: 16px;\n}\n\n.enhanced-milkdown-editor .milkdown ol ol ol {\n  list-style-type: lower-roman;\n}\n\n/* 表格样式 */\n.enhanced-milkdown-editor .milkdown table {\n  width: 100%;\n  border-collapse: collapse;\n  margin: 16px 0;\n  border: 1px solid #e5e7eb;\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n.enhanced-milkdown-editor .milkdown th,\n.enhanced-milkdown-editor .milkdown td {\n  padding: 8px 12px;\n  text-align: left;\n  border-bottom: 1px solid #e5e7eb;\n  border-right: 1px solid #e5e7eb;\n}\n\n.enhanced-milkdown-editor .milkdown th {\n  background: #f9fafb;\n  font-weight: 600;\n  color: #374151;\n}\n\n.enhanced-milkdown-editor .milkdown td:last-child,\n.enhanced-milkdown-editor .milkdown th:last-child {\n  border-right: none;\n}\n\n.enhanced-milkdown-editor .milkdown tr:last-child td {\n  border-bottom: none;\n}\n\n/* 代码样式 */\n.enhanced-milkdown-editor .milkdown code {\n  background: #f3f4f6;\n  padding: 2px 4px;\n  border-radius: 3px;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-size: 13px;\n  color: #e11d48;\n}\n\n.enhanced-milkdown-editor .milkdown pre {\n  background: #1f2937;\n  color: #f9fafb;\n  padding: 16px;\n  border-radius: 6px;\n  overflow-x: auto;\n  margin: 16px 0;\n}\n\n.enhanced-milkdown-editor .milkdown pre code {\n  background: transparent;\n  color: inherit;\n  padding: 0;\n}\n\n/* 引用样式 */\n.enhanced-milkdown-editor .milkdown blockquote {\n  border-left: 4px solid #3b82f6;\n  padding-left: 16px;\n  margin: 16px 0;\n  color: #6b7280;\n  font-style: italic;\n}\n\n/* 链接样式 */\n.enhanced-milkdown-editor .milkdown a {\n  color: #3b82f6;\n  text-decoration: underline;\n}\n\n.enhanced-milkdown-editor .milkdown a:hover {\n  color: #1d4ed8;\n}\n\n/* 图片样式 */\n.enhanced-milkdown-editor .milkdown img {\n  max-width: 100%;\n  height: auto;\n  border-radius: 6px;\n  margin: 8px 0;\n}\n\n/* 分割线样式 */\n.enhanced-milkdown-editor .milkdown hr {\n  border: none;\n  border-top: 1px solid #e5e7eb;\n  margin: 24px 0;\n}\n\n/* 占位符样式 */\n.enhanced-milkdown-editor .milkdown .placeholder {\n  color: #9ca3af;\n  font-style: italic;\n}\n\n/* 选中状态 */\n.enhanced-milkdown-editor .milkdown ::selection {\n  background: #dbeafe;\n}\n\n/* 焦点状态 - 移除双重边框效果 */\n.enhanced-milkdown-editor:focus-within {\n  border-color: #3b82f6;\n  /* 移除box-shadow避免双重边框 */\n}\n\n/* 确保编辑器内部元素不会产生额外的focus样式 */\n.enhanced-milkdown-editor .milkdown *:focus {\n  outline: none;\n  box-shadow: none;\n}\n\n.enhanced-milkdown-editor .editor-container:focus {\n  outline: none;\n  box-shadow: none;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .enhanced-editor-toolbar {\n    padding: 6px 8px;\n    gap: 4px;\n  }\n  \n  .toolbar-btn {\n    padding: 3px 6px;\n    font-size: 11px;\n  }\n  \n  .editor-container {\n    padding: 12px;\n    min-height: 200px;\n  }\n  \n  .enhanced-milkdown-editor .milkdown .editor {\n    font-size: 13px;\n  }\n}\n", "/* App.css - PRD AI编辑器主样式文件 */\n\n.app {\n  display: flex;\n  height: 100vh;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n  background-color: #f8fafc;\n  overflow: hidden;\n}\n\n/* 左侧边栏样式 */\n.sidebar {\n  width: 320px;\n  background: white;\n  border-right: 1px solid #e2e8f0;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.sidebar-header {\n  padding: 20px;\n  border-bottom: 1px solid #e2e8f0;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.sidebar-title {\n  font-size: 18px;\n  font-weight: 700;\n  margin: 0;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n/* 文档列表样式 */\n.document-list {\n  padding: 16px;\n  border-bottom: 1px solid #e2e8f0;\n}\n\n.document-item {\n  padding: 12px 16px;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.2s;\n  margin-bottom: 8px;\n  border: 1px solid transparent;\n}\n\n.document-item:hover {\n  background-color: #f1f5f9;\n  border-color: #cbd5e1;\n}\n\n.document-item.active {\n  background-color: #ff6b35;\n  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);\n  color: white;\n  border-color: #ff6b35;\n}\n\n.document-title {\n  font-weight: 600;\n  font-size: 14px;\n  margin-bottom: 4px;\n}\n\n.document-info {\n  font-size: 12px;\n  opacity: 0.7;\n}\n\n/* 大纲部分样式 */\n.outline-section {\n  flex: 1;\n  padding: 16px;\n  overflow-y: auto;\n}\n\n.outline-title {\n  font-size: 14px;\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: #374151;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.outline-tree {\n  font-size: 13px;\n}\n\n.outline-item {\n  padding: 6px 0;\n  color: #6b7280;\n  cursor: pointer;\n  transition: color 0.2s;\n  line-height: 1.4;\n}\n\n.outline-item:hover {\n  color: #ff6b35;\n}\n\n.outline-item.level-1 {\n  font-weight: 600;\n  color: #374151;\n}\n\n.outline-item.level-2 {\n  font-weight: 500;\n  color: #4b5563;\n}\n\n.outline-item.level-3 {\n  color: #6b7280;\n}\n\n.outline-text {\n  display: block;\n  padding: 2px 0;\n}\n\n.outline-empty {\n  color: #9ca3af;\n  font-style: italic;\n  text-align: center;\n  padding: 20px 0;\n}\n\n/* 工具栏样式 */\n.toolbar {\n  padding: 16px;\n  border-top: 1px solid #e2e8f0;\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.toolbar-btn {\n  flex: 1;\n  padding: 8px 12px;\n  border: 1px solid #d1d5db;\n  background: white;\n  border-radius: 6px;\n  font-size: 12px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  color: #374151;\n}\n\n.toolbar-btn:hover {\n  background-color: #f9fafb;\n  border-color: #9ca3af;\n}\n\n.toolbar-btn.active {\n  background-color: #ff6b35;\n  border-color: #ff6b35;\n  color: white;\n}\n\n.toolbar-btn.secondary {\n  background-color: #f3f4f6;\n  border-color: #d1d5db;\n}\n\n.toolbar-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* 分析部分样式 */\n.analysis-section {\n  padding: 16px;\n  border-top: 1px solid #e2e8f0;\n  background-color: #fefefe;\n}\n\n.analysis-title {\n  font-size: 14px;\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: #374151;\n}\n\n.health-score {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 16px;\n}\n\n.score-circle {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-direction: column;\n  color: white;\n}\n\n.score-number {\n  font-size: 18px;\n  font-weight: 700;\n}\n\n.score-label {\n  font-size: 10px;\n  opacity: 0.9;\n}\n\n.health-details {\n  margin-bottom: 16px;\n}\n\n.health-item {\n  display: flex;\n  justify-content: space-between;\n  padding: 4px 0;\n  font-size: 12px;\n}\n\n.health-label {\n  color: #6b7280;\n}\n\n.health-value {\n  font-weight: 600;\n  color: #10b981;\n}\n\n.suggestions {\n  margin-top: 16px;\n}\n\n.suggestions-title {\n  font-size: 12px;\n  font-weight: 600;\n  margin-bottom: 8px;\n  color: #374151;\n}\n\n.suggestion-item {\n  padding: 6px 8px;\n  background-color: #fef3e2;\n  border-radius: 4px;\n  font-size: 11px;\n  margin-bottom: 4px;\n  color: #92400e;\n  line-height: 1.3;\n}\n\n/* 主内容区域样式 */\n.main-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  background: white;\n  overflow: hidden;\n}\n\n.editor-header {\n  padding: 20px 24px;\n  border-bottom: 1px solid #e2e8f0;\n  background: white;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.document-title-header {\n  font-size: 20px;\n  font-weight: 700;\n  color: #1f2937;\n  margin: 0;\n}\n\n.editor-status {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.auto-save-status {\n  font-size: 12px;\n  color: #10b981;\n  background-color: #ecfdf5;\n  padding: 4px 8px;\n  border-radius: 4px;\n}\n\n/* AI助手面板样式 */\n.ai-panel {\n  width: 380px;\n  background: white;\n  border-left: 1px solid #e2e8f0;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  box-shadow: -1px 0 3px rgba(0, 0, 0, 0.1);\n}\n\n.ai-header {\n  padding: 20px;\n  border-bottom: 1px solid #e2e8f0;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.ai-title {\n  font-size: 16px;\n  font-weight: 700;\n  margin: 0 0 6px 0;\n}\n\n.ai-status {\n  font-size: 12px;\n  opacity: 0.9;\n}\n\n/* 聊天容器样式 */\n.chat-container {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.chat-messages {\n  flex: 1;\n  overflow-y: auto;\n  padding: 16px;\n}\n\n/* 欢迎消息样式 */\n.welcome-message {\n  text-align: center;\n  padding: 32px 16px;\n}\n\n.welcome-icon {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.welcome-text h4 {\n  font-size: 16px;\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: #374151;\n}\n\n.welcome-text p {\n  font-size: 14px;\n  color: #6b7280;\n  margin-bottom: 12px;\n  line-height: 1.5;\n}\n\n.welcome-text ul {\n  text-align: left;\n  font-size: 13px;\n  color: #6b7280;\n  margin: 12px 0;\n  padding-left: 20px;\n}\n\n.welcome-text li {\n  margin-bottom: 4px;\n}\n\n/* 消息样式 */\n.message {\n  display: flex;\n  margin-bottom: 16px;\n  align-items: flex-start;\n  gap: 12px;\n}\n\n.message.user {\n  flex-direction: row-reverse;\n}\n\n.message-avatar {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  flex-shrink: 0;\n}\n\n.message.user .message-avatar {\n  background-color: #ff6b35;\n  color: white;\n}\n\n.message.assistant .message-avatar {\n  background-color: #667eea;\n  color: white;\n}\n\n.message-content {\n  max-width: 75%;\n  padding: 12px 16px;\n  border-radius: 16px;\n  font-size: 14px;\n  line-height: 1.5;\n  word-wrap: break-word;\n}\n\n.message.user .message-content {\n  background-color: #ff6b35;\n  color: white;\n  border-bottom-right-radius: 4px;\n}\n\n.message.assistant .message-content {\n  background-color: #f1f5f9;\n  color: #374151;\n  border-bottom-left-radius: 4px;\n}\n\n/* 输入指示器样式 */\n.typing-indicator {\n  display: flex;\n  gap: 4px;\n  align-items: center;\n}\n\n.typing-indicator span {\n  width: 6px;\n  height: 6px;\n  border-radius: 50%;\n  background-color: #9ca3af;\n  animation: typing 1.4s infinite;\n}\n\n.typing-indicator span:nth-child(2) {\n  animation-delay: 0.2s;\n}\n\n.typing-indicator span:nth-child(3) {\n  animation-delay: 0.4s;\n}\n\n@keyframes typing {\n  0%, 60%, 100% {\n    transform: translateY(0);\n    opacity: 0.5;\n  }\n  30% {\n    transform: translateY(-10px);\n    opacity: 1;\n  }\n}\n\n/* 聊天输入区域样式 */\n.chat-input-container {\n  padding: 16px;\n  border-top: 1px solid #e2e8f0;\n  background: white;\n}\n\n.chat-input-wrapper {\n  display: flex;\n  gap: 8px;\n  margin-bottom: 8px;\n}\n\n.chat-input {\n  flex: 1;\n  padding: 12px 16px;\n  border: 1px solid #d1d5db;\n  border-radius: 24px;\n  font-size: 14px;\n  outline: none;\n  resize: none;\n  transition: border-color 0.2s;\n}\n\n.chat-input:focus {\n  border-color: #667eea;\n}\n\n.chat-input:disabled {\n  background-color: #f9fafb;\n  opacity: 0.6;\n}\n\n.send-button {\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n  border: none;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  font-size: 16px;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.send-button:hover:not(:disabled) {\n  transform: scale(1.05);\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\n}\n\n.send-button:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.input-tips {\n  font-size: 11px;\n  color: #9ca3af;\n  text-align: center;\n}\n\n/* 响应式样式 */\n@media (max-width: 1200px) {\n  .ai-panel {\n    width: 320px;\n  }\n}\n\n@media (max-width: 1024px) {\n  .sidebar {\n    width: 280px;\n  }\n  \n  .ai-panel {\n    width: 300px;\n  }\n}\n\n@media (max-width: 768px) {\n  .app {\n    flex-direction: column;\n  }\n  \n  .sidebar,\n  .ai-panel {\n    width: 100%;\n    height: auto;\n    max-height: 200px;\n  }\n  \n  .main-content {\n    flex: 1;\n    min-height: 400px;\n  }\n}\n\n/* 滚动条样式 */\n.outline-section::-webkit-scrollbar,\n.chat-messages::-webkit-scrollbar {\n  width: 6px;\n}\n\n.outline-section::-webkit-scrollbar-track,\n.chat-messages::-webkit-scrollbar-track {\n  background: #f1f5f9;\n  border-radius: 3px;\n}\n\n.outline-section::-webkit-scrollbar-thumb,\n.chat-messages::-webkit-scrollbar-thumb {\n  background: #cbd5e1;\n  border-radius: 3px;\n}\n\n.outline-section::-webkit-scrollbar-thumb:hover,\n.chat-messages::-webkit-scrollbar-thumb:hover {\n  background: #94a3b8;\n}\n\n/* 文本截断样式 */\n.line-clamp-1 {\n  display: -webkit-box;\n  -webkit-line-clamp: 1;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.line-clamp-2 {\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}", "/* PRD评审确认页面样式 */\n\n/* 高亮文本动画效果 */\n.highlight-text {\n  background-color: #fef3c7 !important;\n  border: 2px solid #f59e0b !important;\n  border-radius: 4px !important;\n  padding: 2px 4px !important;\n  transition: all 0.3s ease !important;\n  animation: highlightPulse 3s ease-in-out;\n}\n\n@keyframes highlightPulse {\n  0% {\n    background-color: #fef3c7;\n    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);\n  }\n  50% {\n    background-color: #fde68a;\n    box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);\n  }\n  100% {\n    background-color: #fef3c7;\n    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);\n  }\n}\n\n/* 问题卡片高亮效果 */\n.issue-card-highlight {\n  animation: cardHighlight 2s ease-in-out;\n}\n\n@keyframes cardHighlight {\n  0% {\n    transform: scale(1);\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n  }\n  50% {\n    transform: scale(1.02);\n    box-shadow: 0 20px 25px -5px rgba(245, 158, 11, 0.3);\n  }\n  100% {\n    transform: scale(1);\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n  }\n}\n\n/* 章节高亮效果 */\n.outline-section-highlight {\n  background-color: rgba(59, 130, 246, 0.1) !important;\n  border-left: 4px solid #3b82f6 !important;\n  padding-left: 16px !important;\n  margin-left: -20px !important;\n  border-radius: 4px !important;\n  transition: all 0.3s ease !important;\n}\n\n.highlight-pulse {\n  animation: sectionHighlightPulse 2s ease-in-out;\n}\n\n@keyframes sectionHighlightPulse {\n  0% {\n    background-color: rgba(59, 130, 246, 0.1);\n    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);\n  }\n  50% {\n    background-color: rgba(59, 130, 246, 0.2);\n    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);\n  }\n  100% {\n    background-color: rgba(59, 130, 246, 0.1);\n    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);\n  }\n}\n\n/* 章节路径显示优化 */\n.section-path {\n  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n  font-weight: 500;\n}\n\n.target-text-preview {\n  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;\n  font-size: 11px;\n  background-color: #f3f4f6;\n  border-radius: 3px;\n  padding: 1px 4px;\n}\n\n/* 三栏布局优化 */\n.prd-review-layout {\n  min-width: 0;\n  width: 100%;\n}\n\n/* 表格样式优化 */\n.prd-content-table {\n  table-layout: fixed;\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.prd-content-table td {\n  word-wrap: break-word;\n  word-break: break-word;\n  overflow-wrap: anywhere;\n  hyphens: auto;\n  vertical-align: top;\n}\n\n/* 确保表格内容不会溢出 */\n.table-container {\n  width: 100%;\n  overflow: hidden;\n}\n\n/* 响应式设计 */\n@media (max-width: 1600px) {\n  /* 调整三栏宽度比例 */\n  .prd-review-left-panel {\n    width: 18rem; /* w-72 */\n  }\n\n  .prd-review-right-panel {\n    width: 22rem; /* w-88 */\n  }\n}\n\n@media (max-width: 1400px) {\n  .prd-content-table {\n    font-size: 0.75rem;\n  }\n\n  .prd-content-table td {\n    padding: 0.375rem 0.5rem;\n  }\n\n  /* 进一步调整三栏宽度 */\n  .prd-review-left-panel {\n    width: 16rem; /* w-64 */\n  }\n\n  .prd-review-right-panel {\n    width: 20rem; /* w-80 */\n  }\n}\n\n@media (max-width: 1200px) {\n  .prd-content-table {\n    font-size: 0.7rem;\n  }\n\n  .prd-content-table td {\n    padding: 0.25rem 0.375rem;\n  }\n\n  /* 小屏幕下调整布局 */\n  .prd-review-left-panel {\n    width: 14rem; /* w-56 */\n  }\n\n  .prd-review-right-panel {\n    width: 18rem; /* w-72 */\n  }\n}\n\n/* 超小屏幕下的布局调整 */\n@media (max-width: 1024px) {\n  .prd-review-layout {\n    flex-direction: column;\n    height: auto;\n  }\n\n  .prd-review-left-panel,\n  .prd-review-right-panel {\n    width: 100%;\n    max-height: 300px;\n  }\n}\n"], "names": [], "sourceRoot": ""}