# TipTap富文本编辑器技术总结

## 📋 项目概述

基于TipTap构建的现代化富文本编辑器，支持Markdown智能粘贴、表格编辑、工具栏切换等高级功能。

## 🏗️ 整体架构设计

### 核心设计理念
- **模块化组件架构**：每个功能独立封装，便于维护和扩展
- **智能Markdown处理**：自动识别并转换Markdown格式
- **用户体验优先**：直观的交互设计和视觉反馈
- **功能完整性**：覆盖富文本编辑的所有常用场景

### 技术选型

#### 核心框架
- **React 18+**：现代化前端框架
- **TipTap 2.x**：基于ProseMirror的富文本编辑器框架
- **ProseMirror**：底层文档模型和编辑引擎

#### 主要依赖
```json
{
  "@tiptap/react": "^2.x",
  "@tiptap/starter-kit": "^2.x",
  "@tiptap/extension-table": "^2.x",
  "@tiptap/extension-table-row": "^2.x",
  "@tiptap/extension-table-header": "^2.x",
  "@tiptap/extension-table-cell": "^2.x",
  "@tiptap/extension-image": "^2.x",
  "@tiptap/extension-link": "^2.x",
  "@tiptap/extension-placeholder": "^2.x",
  "@tiptap/extension-focus": "^2.x"
}
```

## 📁 组件结构

### 目录结构
```
src/components/TipTapEditor/
├── TipTapEditor.js              # 主编辑器组件
├── TipTapEditor.css             # 主样式文件
├── TipTapTableTest.js           # 测试页面组件
├── components/
│   ├── TableEditMenu.js         # 表格编辑菜单
│   └── TableEditMenu.css        # 表格编辑菜单样式
├── extensions/
│   ├── MarkdownPasteHandler.js  # Markdown粘贴处理扩展
│   └── TableWithEditButton.js   # 表格编辑按钮扩展
├── menus/
│   └── TableMenu.js             # 表格菜单组件
└── utils/
    ├── commands.js              # 编辑器命令工具
    ├── content.js               # 内容处理工具
    └── events.js                # 事件处理工具
```

### 组件关系图
```
TipTapEditor (主组件)
├── Toolbar (工具栏)
├── ToolbarToggle (工具栏切换按钮)
├── EditorContent (编辑器内容区)
├── TableEditMenu (表格编辑菜单)
└── Extensions (扩展系统)
    ├── MarkdownPasteHandler (Markdown处理)
    ├── Table (表格扩展)
    └── 其他TipTap扩展
```

## 🎯 核心功能实现

### 1. 富文本编辑基础功能
- **文本格式化**：粗体、斜体、删除线、内联代码
- **标题支持**：H1-H6多级标题
- **列表功能**：有序列表、无序列表，支持嵌套
- **引用块**：支持多行引用
- **代码块**：语法高亮代码块
- **链接插入**：URL链接和图片插入
- **水平分割线**：内容分隔

### 2. Markdown智能粘贴 ⭐
```javascript
// 核心实现：MarkdownPasteHandler.js
const MarkdownPasteHandler = Extension.create({
  name: 'markdownPasteHandler',
  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('markdownPasteHandler'),
        props: {
          handlePaste(view, event, slice) {
            // 检测Markdown格式并转换
            // 支持表格、列表、标题、代码块等
          }
        }
      })
    ];
  }
});
```

**支持的Markdown格式**：
- 表格：`| 列1 | 列2 |`
- 标题：`# ## ### ####`
- 列表：`- * + 1.`
- 代码块：` ``` `
- 引用：`>`
- 链接：`[text](url)`

### 3. 表格编辑系统 ⭐

#### 表格基础功能
- **表格创建**：工具栏插入表格
- **单元格编辑**：直接点击编辑
- **快捷键支持**：Ctrl+Enter添加新行

#### 表格编辑菜单
```javascript
// 表格右上角编辑按钮
const editButton = document.createElement('button');
editButton.className = 'table-edit-button';
editButton.innerHTML = '✏️';

// 编辑菜单功能
const commands = {
  addRowBefore: () => editor.chain().focus().addRowBefore().run(),
  addRowAfter: () => editor.chain().focus().addRowAfter().run(),
  deleteRow: () => editor.chain().focus().deleteRow().run(),
  addColumnBefore: () => editor.chain().focus().addColumnBefore().run(),
  addColumnAfter: () => editor.chain().focus().addColumnAfter().run(),
  deleteColumn: () => editor.chain().focus().deleteColumn().run(),
  deleteTable: () => editor.chain().focus().deleteTable().run(),
};
```

### 4. 工具栏切换系统 ⭐
```javascript
// 状态管理
const [isToolbarVisible, setIsToolbarVisible] = useState(true);

// 条件渲染
{isToolbarVisible && <Toolbar />}
{!isToolbarVisible && <ToolbarToggle />}

// CSS定位
.toolbar-toggle-button {
  position: absolute;
  top: 8px;
  right: 8px;
  /* 圆形悬浮按钮样式 */
}
```

## 🎨 样式设计

### 设计原则
- **现代化UI**：圆角、阴影、渐变效果
- **响应式设计**：适配不同屏幕尺寸
- **交互反馈**：悬浮、点击、焦点状态
- **视觉层次**：清晰的信息架构

### 关键样式特性
```css
/* 编辑器容器 */
.tiptap-editor-container {
  position: relative;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  background: #fff;
}

/* 工具栏 */
.tiptap-editor-toolbar {
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
}

/* 表格编辑按钮 */
.table-edit-button {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background: #007bff;
  border-radius: 50%;
  opacity: 0;
  transition: all 0.2s ease;
}

.table-wrapper:hover .table-edit-button {
  opacity: 1;
}
```

## 🧪 测试系统

### 测试页面组件
```javascript
// TipTapTableTest.js
const TipTapTableTest = () => {
  return (
    <div className="tiptap-table-test">
      <div className="test-controls">
        <button onClick={copyMarkdownToClipboard}>
          📋 复制Markdown测试内容
        </button>
        <button onClick={copyTableToClipboard}>
          📊 复制表格测试内容
        </button>
        <button onClick={clearLogs}>
          🗑️ 清空日志
        </button>
      </div>
      <TipTapEditor />
      <div className="test-logs">{/* 日志显示 */}</div>
    </div>
  );
};
```

### 测试内容
```markdown
# 完整的Markdown测试内容
## 二级标题
### 三级标题

**粗体文本** *斜体文本* ~~删除线~~ `内联代码`

- 无序列表项1
- 无序列表项2
  - 嵌套列表项

1. 有序列表项1
2. 有序列表项2

> 这是一个引用块
> 支持多行引用

| 产品名称 | 价格 | 库存 | 状态 |
|---------|------|------|------|
| iPhone 15 | ¥5999 | 50 | 在售 |
| MacBook Pro | ¥12999 | 20 | 在售 |
| AirPods | ¥1299 | 100 | 在售 |

```javascript
console.log('代码块测试');
```

[链接示例](https://example.com)
```

## 🚀 快速构建指南

### 1. 安装依赖
```bash
npm install @tiptap/react @tiptap/starter-kit @tiptap/extension-table @tiptap/extension-table-row @tiptap/extension-table-header @tiptap/extension-table-cell @tiptap/extension-image @tiptap/extension-link @tiptap/extension-placeholder @tiptap/extension-focus
```

### 2. 创建基础组件
```javascript
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Table } from '@tiptap/extension-table';
// ... 其他导入

const TipTapEditor = () => {
  const editor = useEditor({
    extensions: [
      StarterKit,
      Table.configure({
        resizable: true,
        handleWidth: 5,
        cellMinWidth: 100,
      }),
      // ... 其他扩展
    ],
    content: '<p>开始编辑...</p>',
  });

  return (
    <div className="tiptap-editor-container">
      <div className="tiptap-editor-toolbar">
        {/* 工具栏按钮 */}
      </div>
      <div className="tiptap-editor-content">
        <EditorContent editor={editor} />
      </div>
    </div>
  );
};
```

### 3. 添加Markdown粘贴支持
- 复制 `MarkdownPasteHandler.js` 扩展
- 在编辑器扩展中注册
- 配置支持的Markdown格式

### 4. 实现表格编辑功能
- 复制 `TableEditMenu.js` 组件
- 扩展Table组件添加自定义节点视图
- 添加表格编辑按钮和菜单

### 5. 添加工具栏切换
- 实现工具栏状态管理
- 添加切换按钮组件
- 配置CSS定位和样式

## ✅ 功能验证清单

### 基础功能
- [ ] 文本格式化（粗体、斜体、删除线、代码）
- [ ] 标题功能（H1-H6）
- [ ] 列表功能（有序、无序、嵌套）
- [ ] 引用块功能
- [ ] 代码块功能
- [ ] 链接和图片插入

### 高级功能
- [ ] Markdown智能粘贴
- [ ] 表格创建和编辑
- [ ] 表格右上角编辑按钮
- [ ] 表格编辑菜单（行列操作）
- [ ] 工具栏隐藏/显示切换
- [ ] 快捷键支持（Ctrl+Enter添加表格行）

### 用户体验
- [ ] 悬浮效果和动画
- [ ] 响应式设计
- [ ] 错误处理和边界情况
- [ ] 性能优化

## 🎯 扩展建议

### 可添加功能
1. **协作编辑**：多人实时编辑
2. **版本控制**：内容历史和回滚
3. **插件系统**：自定义扩展
4. **导出功能**：PDF、Word、HTML导出
5. **主题系统**：多种视觉主题
6. **快捷键自定义**：用户自定义快捷键

### 性能优化
1. **懒加载**：大文档分块加载
2. **虚拟滚动**：长文档性能优化
3. **缓存策略**：内容和状态缓存
4. **代码分割**：按需加载组件

## 📝 总结

这个TipTap编辑器实现了现代富文本编辑器的所有核心功能，特别是Markdown智能粘贴、表格编辑和工具栏切换等高级特性。通过模块化的架构设计，每个功能都可以独立开发、测试和维护，为后续扩展提供了良好的基础。

使用本文档作为指南，可以快速构建一个功能完整、用户体验优秀的富文本编辑器。

## 💻 完整代码示例

### 主编辑器组件核心代码
```javascript
import React, { useEffect, useState, useRef } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Table } from '@tiptap/extension-table';
import { TableRow } from '@tiptap/extension-table-row';
import { TableHeader } from '@tiptap/extension-table-header';
import { TableCell } from '@tiptap/extension-table-cell';
import { MarkdownPasteHandler } from './extensions/MarkdownPasteHandler';
import TableEditMenu from './components/TableEditMenu';

const TipTapEditor = ({ content = '', onChange, placeholder = '开始输入内容...' }) => {
  const [isToolbarVisible, setIsToolbarVisible] = useState(true);
  const [tableEditMenu, setTableEditMenu] = useState(null);
  const editorRef = useRef(null);

  // 配置编辑器扩展
  const extensions = [
    StarterKit.configure({ table: false }),
    Table.configure({
      resizable: true,
      handleWidth: 5,
      cellMinWidth: 100,
      allowTableNodeSelection: true,
    }).extend({
      addNodeView() {
        return ({ node, HTMLAttributes, getPos, editor }) => {
          const dom = document.createElement('div');
          dom.className = 'table-wrapper';

          const table = document.createElement('table');
          Object.entries(HTMLAttributes).forEach(([key, value]) => {
            table.setAttribute(key, value);
          });

          const editButton = document.createElement('button');
          editButton.className = 'table-edit-button';
          editButton.innerHTML = '✏️';
          editButton.title = '编辑表格';

          editButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            const rect = editButton.getBoundingClientRect();
            const position = {
              top: rect.bottom + 5,
              left: rect.left - 200,
            };

            setTableEditMenu({ node, getPos, editor, position });
          });

          dom.appendChild(table);
          dom.appendChild(editButton);

          return {
            dom,
            contentDOM: table,
            update: (updatedNode) => updatedNode.type === node.type,
            destroy: () => editButton.removeEventListener('click', () => {}),
          };
        };
      },
    }),
    TableRow,
    TableHeader,
    TableCell,
    MarkdownPasteHandler,
  ];

  // 创建编辑器实例
  const editor = useEditor({
    extensions,
    content,
    editorProps: {
      attributes: { class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none' },
    },
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      onChange && onChange({ html, editor });
    },
  });

  // 工具栏命令
  const commands = {
    toggleBold: () => editor?.chain().focus().toggleBold().run(),
    toggleItalic: () => editor?.chain().focus().toggleItalic().run(),
    toggleStrike: () => editor?.chain().focus().toggleStrike().run(),
    toggleCode: () => editor?.chain().focus().toggleCode().run(),
    setHeading: (level) => editor?.chain().focus().toggleHeading({ level }).run(),
    toggleBulletList: () => editor?.chain().focus().toggleBulletList().run(),
    toggleOrderedList: () => editor?.chain().focus().toggleOrderedList().run(),
    toggleBlockquote: () => editor?.chain().focus().toggleBlockquote().run(),
    toggleCodeBlock: () => editor?.chain().focus().toggleCodeBlock().run(),
    insertTable: () => editor?.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run(),
  };

  // 检查命令状态
  const isActive = {
    bold: () => editor?.isActive('bold') || false,
    italic: () => editor?.isActive('italic') || false,
    strike: () => editor?.isActive('strike') || false,
    code: () => editor?.isActive('code') || false,
    heading: (level) => editor?.isActive('heading', { level }) || false,
    bulletList: () => editor?.isActive('bulletList') || false,
    orderedList: () => editor?.isActive('orderedList') || false,
    blockquote: () => editor?.isActive('blockquote') || false,
    codeBlock: () => editor?.isActive('codeBlock') || false,
  };

  // 工具栏切换按钮组件
  const ToolbarToggle = () => (
    <button
      className="toolbar-toggle-button"
      onClick={() => setIsToolbarVisible(true)}
      title="显示工具栏"
    >
      🧰
    </button>
  );

  // 工具栏组件
  const Toolbar = () => (
    <div className="tiptap-editor-toolbar">
      {/* 文本格式化 */}
      <button
        className={`toolbar-button ${isActive.bold() ? 'is-active' : ''}`}
        onClick={commands.toggleBold}
        title="粗体"
      >
        <strong>B</strong>
      </button>
      <button
        className={`toolbar-button ${isActive.italic() ? 'is-active' : ''}`}
        onClick={commands.toggleItalic}
        title="斜体"
      >
        <em>I</em>
      </button>

      {/* 标题 */}
      <button
        className={`toolbar-button ${isActive.heading(1) ? 'is-active' : ''}`}
        onClick={() => commands.setHeading(1)}
        title="标题1"
      >
        H1
      </button>

      {/* 列表 */}
      <button
        className={`toolbar-button ${isActive.bulletList() ? 'is-active' : ''}`}
        onClick={commands.toggleBulletList}
        title="无序列表"
      >
        • 列表
      </button>

      {/* 表格 */}
      <button
        className="toolbar-button"
        onClick={commands.insertTable}
        title="插入表格"
      >
        📊 表格
      </button>

      {/* 工具栏切换按钮 */}
      <button
        className="toolbar-button"
        onClick={() => setIsToolbarVisible(false)}
        title="隐藏工具栏"
      >
        🧰
      </button>
    </div>
  );

  if (!editor) {
    return <div>编辑器加载中...</div>;
  }

  return (
    <div className="tiptap-editor-container" ref={editorRef}>
      {isToolbarVisible && <Toolbar />}
      {!isToolbarVisible && <ToolbarToggle />}

      <div className="tiptap-editor-content">
        <EditorContent editor={editor} />
      </div>

      {/* 表格编辑菜单 */}
      {tableEditMenu && (
        <TableEditMenu
          editor={tableEditMenu.editor}
          tableNode={tableEditMenu.node}
          position={tableEditMenu.position}
          onClose={() => setTableEditMenu(null)}
        />
      )}
    </div>
  );
};

export default TipTapEditor;
```

### Markdown粘贴处理扩展
```javascript
import { Extension } from '@tiptap/core';
import { Plugin, PluginKey } from 'prosemirror-state';

export const MarkdownPasteHandler = Extension.create({
  name: 'markdownPasteHandler',

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('markdownPasteHandler'),
        props: {
          handlePaste(view, event, slice) {
            const text = event.clipboardData?.getData('text/plain');
            if (!text) return false;

            // 检测是否为Markdown表格
            if (isMarkdownTable(text)) {
              const tableNode = parseMarkdownTable(text, view.state.schema);
              if (tableNode) {
                const tr = view.state.tr.replaceSelectionWith(tableNode);
                view.dispatch(tr);
                return true;
              }
            }

            // 处理其他Markdown格式
            const convertedContent = convertMarkdownToNodes(text, view.state.schema);
            if (convertedContent) {
              const tr = view.state.tr.replaceSelectionWith(convertedContent);
              view.dispatch(tr);
              return true;
            }

            return false;
          },
        },
      }),
    ];
  },
});

// 检测Markdown表格
function isMarkdownTable(text) {
  const lines = text.trim().split('\n');
  return lines.length >= 2 &&
         lines[0].includes('|') &&
         lines[1].includes('|') &&
         lines[1].includes('-');
}

// 解析Markdown表格
function parseMarkdownTable(text, schema) {
  const lines = text.trim().split('\n');
  const headerLine = lines[0];
  const separatorLine = lines[1];
  const dataLines = lines.slice(2);

  // 解析表头
  const headers = headerLine.split('|')
    .map(cell => cell.trim())
    .filter(cell => cell.length > 0);

  // 创建表格节点
  const headerRow = schema.nodes.tableRow.create(
    null,
    headers.map(header =>
      schema.nodes.tableHeader.create(
        null,
        schema.text(header)
      )
    )
  );

  const dataRows = dataLines.map(line => {
    const cells = line.split('|')
      .map(cell => cell.trim())
      .filter(cell => cell.length > 0);

    return schema.nodes.tableRow.create(
      null,
      cells.map(cell =>
        schema.nodes.tableCell.create(
          null,
          schema.text(cell)
        )
      )
    );
  });

  return schema.nodes.table.create(
    null,
    [headerRow, ...dataRows]
  );
}
```

### 表格编辑菜单组件
```javascript
import React, { useRef, useEffect } from 'react';

const TableEditMenu = ({ editor, tableNode, position, onClose }) => {
  const menuRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  const commands = {
    addRowBefore: () => { editor.chain().focus().addRowBefore().run(); onClose(); },
    addRowAfter: () => { editor.chain().focus().addRowAfter().run(); onClose(); },
    deleteRow: () => { editor.chain().focus().deleteRow().run(); onClose(); },
    addColumnBefore: () => { editor.chain().focus().addColumnBefore().run(); onClose(); },
    addColumnAfter: () => { editor.chain().focus().addColumnAfter().run(); onClose(); },
    deleteColumn: () => { editor.chain().focus().deleteColumn().run(); onClose(); },
    deleteTable: () => { editor.chain().focus().deleteTable().run(); onClose(); },
  };

  return (
    <div
      ref={menuRef}
      className="table-edit-menu"
      style={{
        position: 'absolute',
        top: position.top,
        left: position.left,
        zIndex: 1000,
      }}
    >
      <div className="table-edit-menu-header">
        <span>表格编辑</span>
        <button className="close-button" onClick={onClose}>×</button>
      </div>

      <div className="table-edit-menu-content">
        <div className="menu-section">
          <div className="menu-section-title">行操作</div>
          <button className="menu-item" onClick={commands.addRowBefore}>
            <span className="menu-icon">⬆️</span>在上方插入行
          </button>
          <button className="menu-item" onClick={commands.addRowAfter}>
            <span className="menu-icon">⬇️</span>在下方插入行
          </button>
          <button className="menu-item danger" onClick={commands.deleteRow}>
            <span className="menu-icon">🗑️</span>删除当前行
          </button>
        </div>

        <div className="menu-section">
          <div className="menu-section-title">列操作</div>
          <button className="menu-item" onClick={commands.addColumnBefore}>
            <span className="menu-icon">⬅️</span>在左侧插入列
          </button>
          <button className="menu-item" onClick={commands.addColumnAfter}>
            <span className="menu-icon">➡️</span>在右侧插入列
          </button>
          <button className="menu-item danger" onClick={commands.deleteColumn}>
            <span className="menu-icon">🗑️</span>删除当前列
          </button>
        </div>

        <div className="menu-section">
          <div className="menu-section-title">表格操作</div>
          <button className="menu-item danger" onClick={commands.deleteTable}>
            <span className="menu-icon">🗑️</span>删除整个表格
          </button>
        </div>
      </div>
    </div>
  );
};

export default TableEditMenu;
```

## 🎨 完整CSS样式

### 主样式文件
```css
/* TipTap编辑器基础样式 */
.tiptap-editor-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
}

.tiptap-editor-toolbar {
  padding: 12px 16px;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.toolbar-button {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.toolbar-button:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

.toolbar-button.is-active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

/* 工具栏切换按钮 */
.toolbar-toggle-button {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 36px;
  height: 36px;
  border: 2px solid #007bff;
  border-radius: 50%;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
  transition: all 0.2s ease;
  z-index: 100;
}

.toolbar-toggle-button:hover {
  background: #007bff;
  color: white;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.tiptap-editor-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* 编辑器内容样式 */
.ProseMirror {
  outline: none;
  min-height: 200px;
  line-height: 1.6;
  font-size: 14px;
  color: #333;
}

/* 表格样式 */
.table-wrapper {
  position: relative;
  display: inline-block;
  margin: 16px 0;
}

.table-wrapper:hover .table-edit-button {
  opacity: 1;
}

.table-edit-button {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background: #007bff;
  border: 2px solid white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
  transition: all 0.2s ease;
  z-index: 100;
  opacity: 0;
}

.table-edit-button:hover {
  background: #0056b3;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 16px 0;
  overflow: hidden;
  border: 1px solid #ddd;
}

.ProseMirror td,
.ProseMirror th {
  min-width: 1em;
  border: 1px solid #ddd;
  padding: 8px 12px;
  vertical-align: top;
  box-sizing: border-box;
  position: relative;
}

.ProseMirror th {
  font-weight: bold;
  text-align: left;
  background-color: #f8f9fa;
}

/* 表格编辑菜单样式 */
.table-edit-menu {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  max-width: 250px;
  font-size: 14px;
  user-select: none;
}

.table-edit-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
  font-weight: 600;
  color: #333;
}

.menu-item {
  width: 100%;
  padding: 8px 16px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #333;
  transition: all 0.2s ease;
}

.menu-item:hover {
  background: #f8f9fa;
}

.menu-item.danger {
  color: #dc3545;
}

.menu-item.danger:hover {
  background: #fff5f5;
  color: #c82333;
}
```

## 🚀 部署和使用

### 1. 基本使用
```javascript
import TipTapEditor from './components/TipTapEditor/TipTapEditor';

function App() {
  const [content, setContent] = useState('<p>Hello World!</p>');

  const handleChange = ({ html }) => {
    setContent(html);
  };

  return (
    <div className="App">
      <TipTapEditor
        content={content}
        onChange={handleChange}
        placeholder="开始编辑..."
      />
    </div>
  );
}
```

### 2. 高级配置
```javascript
<TipTapEditor
  content={initialContent}
  onChange={handleContentChange}
  onSelectionUpdate={handleSelectionUpdate}
  onFocus={handleFocus}
  onBlur={handleBlur}
  placeholder="请输入内容..."
  editable={true}
  className="custom-editor"
  style={{ height: '500px' }}
/>
```

这个完整的技术总结提供了构建TipTap富文本编辑器所需的所有信息，包括架构设计、核心代码、样式定义和使用指南。可以直接作为构建相同编辑器的完整参考文档。
