# 🔄 功能工作流程指南

## 📋 概述

本文档详细描述了智能PRD编辑器中各个核心功能的完整工作流程，包括用户操作、系统响应、数据流转和状态变化。

## 📊 表格功能工作流程

### 1. 新建表格完整流程

#### 用户操作流程
```
用户点击工具栏"📊 表格"按钮
    ↓
模板选择器弹出显示
    ↓
用户浏览并选择合适的模板
    ↓
表格编辑器打开（新建模式）
    ↓
用户编辑表格内容（添加/删除行列，编辑单元格）
    ↓
用户点击"插入表格"按钮
    ↓
表格插入到编辑器光标位置
    ↓
表格自动渲染为可交互的HTML表格
```

#### 系统内部流程
```
EnhancedMilkdownEditor.insertTable()
    ↓
setShowTemplateSelector(true)
    ↓
TableTemplateSelector.onTemplateSelect()
    ↓
setExternalEditData({ headers, data })
    ↓
TableInteractionManager.useEffect(externalEditData)
    ↓
setIsExternalEdit(true) + setShowTableEditor(true)
    ↓
TableEditor.render() [新建模式]
    ↓
用户编辑操作...
    ↓
handleTableSave() [外部编辑模式]
    ↓
onExternalEditComplete({ action: 'save', headers, data })
    ↓
TableConverter.toMarkdown(data, headers)
    ↓
insertTableToEditor(markdownTable)
    ↓
编辑器内容更新 + onChange触发
    ↓
TableRenderEnhancer.enhanceExistingTables()
    ↓
Markdown → HTML表格渲染完成
```

### 2. 编辑现有表格完整流程

#### 用户操作流程
```
用户悬停在表格上 → 显示编辑按钮
    ↓
用户点击编辑按钮 OR 双击表格
    ↓
表格编辑器打开（编辑模式）
    ↓
用户修改表格内容
    ↓
用户点击"保存更改"按钮
    ↓
表格在原位置更新
    ↓
表格重新渲染
```

#### 系统内部流程
```
TableInteractionManager.scanTables()
    ↓
表格元素识别 + 悬浮按钮绑定
    ↓
TableHoverButton.onClick() OR handleTableDoubleClick()
    ↓
parseTableData(tableElement)
    ↓
setEditingTable(table) + setTableData(data) + setTableHeaders(headers)
    ↓
setIsExternalEdit(false) + setShowTableEditor(true)
    ↓
TableEditor.render() [编辑模式]
    ↓
用户编辑操作...
    ↓
handleTableSave() [内部编辑模式]
    ↓
updateTableContent(tableElement, headers, data)
    ↓
TableConverter.toMarkdown() + 替换原表格内容
    ↓
TableRenderEnhancer.enhanceExistingTables()
    ↓
表格重新渲染完成
```

## 🤖 AI分析工作流程

### 1. 智能内容分析流程

#### 触发条件
- 用户停止输入超过2秒
- 内容长度达到阈值
- 用户主动触发分析

#### 分析流程
```
内容变化检测
    ↓
MultiAgentAnalyzer.analyze(content)
    ↓
并行分析任务:
├── RequirementAnalyzer.extractRequirements()
├── StructureAnalyzer.analyzeStructure()
├── QualityAnalyzer.checkQuality()
└── SuggestionGenerator.generateSuggestions()
    ↓
分析结果聚合
    ↓
SmartCardMenu.updateCards(analysisResults)
    ↓
智能卡片显示更新
```

### 2. 智能卡片生成流程

#### 卡片类型识别
```
内容分析 → 识别内容类型 → 选择卡片模板 → 生成卡片数据 → 渲染卡片
```

#### 支持的卡片类型
- 📋 **需求卡片** - 功能需求提取
- 🎯 **目标卡片** - 业务目标识别
- ⚠️ **风险卡片** - 潜在风险提示
- 💡 **建议卡片** - 优化建议
- 📊 **数据卡片** - 数据需求分析

## 📝 编辑器功能工作流程

### 1. 内容编辑流程

#### 基础编辑
```
用户输入 → Milkdown编辑器处理 → 内容状态更新 → UI重新渲染
```

#### 富文本编辑
```
工具栏操作 → 编辑器命令执行 → Markdown语法插入 → 实时预览更新
```

### 2. 内容同步流程

#### 编辑器 ↔ 外部状态同步
```
编辑器内容变化
    ↓
listenerCtx.markdownUpdated()
    ↓
onChange(markdown)
    ↓
父组件状态更新
    ↓
其他组件响应内容变化
```

## 🧭 导航功能工作流程

### 1. 大纲导航生成

#### 自动大纲提取
```
内容解析 → 标题识别 → 层级分析 → 导航树构建 → 导航面板更新
```

#### 导航交互
```
用户点击导航项 → 计算目标位置 → 平滑滚动 → 高亮当前章节
```

### 2. 文本选中功能

#### 选中检测
```
用户选中文本 → selection事件触发 → 选中范围计算 → 操作菜单显示
```

#### 选中操作
```
用户选择操作 → 执行相应命令 → 内容修改 → 选中状态清除
```

## 🎨 UI交互工作流程

### 1. 响应式布局

#### 屏幕尺寸适配
```
窗口大小变化 → 媒体查询触发 → 布局重新计算 → 组件重新排列
```

### 2. 主题切换

#### 主题变更流程
```
用户选择主题 → 主题配置加载 → CSS变量更新 → 全局样式重新应用
```

## 🔧 数据管理工作流程

### 1. 数据持久化

#### 自动保存
```
内容变化 → 防抖处理 → 数据序列化 → 本地存储 → 保存状态提示
```

#### 手动保存
```
用户触发保存 → 数据验证 → 格式化处理 → 存储执行 → 成功反馈
```

### 2. 数据恢复

#### 自动恢复
```
页面加载 → 检查本地存储 → 数据反序列化 → 内容恢复 → 状态同步
```

## 🚨 错误处理工作流程

### 1. 错误捕获

#### 组件级错误
```
组件错误 → Error Boundary捕获 → 错误信息记录 → 降级UI显示
```

#### 全局错误
```
未捕获错误 → 全局错误处理器 → 错误上报 → 用户友好提示
```

### 2. 错误恢复

#### 自动恢复
```
错误检测 → 重试机制 → 状态重置 → 功能恢复
```

#### 手动恢复
```
用户触发恢复 → 清除错误状态 → 重新初始化 → 正常功能恢复
```

## 📊 性能优化工作流程

### 1. 渲染优化

#### 虚拟化渲染
```
大量数据 → 可视区域计算 → 虚拟列表渲染 → 滚动更新
```

#### 组件懒加载
```
路由变化 → 动态导入 → 组件加载 → 渲染完成
```

### 2. 内存管理

#### 资源清理
```
组件卸载 → 事件监听器清理 → 定时器清除 → 内存释放
```

## 🧪 测试工作流程

### 1. 自动化测试

#### 单元测试
```
代码变更 → 测试触发 → 单元测试执行 → 结果报告
```

#### 集成测试
```
功能完成 → 集成测试启动 → 多组件协作测试 → 测试报告生成
```

### 2. 手动测试

#### 功能测试
```
测试计划 → 手动操作 → 结果记录 → 问题反馈
```

---

**文档版本**: v1.0  
**最后更新**: 2025-01-20  
**相关文档**: [架构总览](./ARCHITECTURE_OVERVIEW_2025.md), [表格系统指南](./TABLE_SYSTEM_GUIDE.md)
