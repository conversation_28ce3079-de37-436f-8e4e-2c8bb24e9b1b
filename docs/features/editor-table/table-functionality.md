# 📊 表格功能使用指南

## 🎯 功能概述

PRD智能评估系统现在提供了强大的表格编辑功能，包括：
- 🎨 可视化表格编辑器（类似Excel体验）
- 📋 7种专业PRD表格模板
- 🔄 CSV/Excel数据导入导出
- ✅ 表格数据验证和转换

## 🚀 快速开始

### 1. 访问表格功能
1. 打开PRD编辑器（自动使用增强编辑模式）
2. 在置顶工具栏中点击 **"📊 表格"** 按钮
3. 选择表格模板或创建空白表格
4. 表格将插入到当前光标位置

### 2. 编辑现有表格
- **双击表格** - 直接打开表格编辑器
- **悬浮编辑按钮** - 鼠标悬停在表格上时，右上角会出现编辑按钮
- **实时更新** - 编辑完成后表格立即更新

### 3. 使用表格模板
系统提供7种专业PRD表格模板：

#### 📋 需求管理模板
- **功能需求表** - 描述产品功能需求
- **用户故事表** - 记录用户故事和场景

#### ⚙️ 技术文档模板  
- **API接口表** - 描述系统API接口
- **数据字典表** - 定义数据库字段

#### 🧪 测试管理模板
- **测试用例表** - 描述测试用例

#### 📊 项目管理模板
- **风险评估表** - 评估项目风险
- **项目里程碑表** - 跟踪项目重要节点

## 📝 详细使用说明

### 新增交互功能 ⭐

#### 表格快速编辑
- **双击任意表格** - 立即打开表格编辑器
- **悬浮编辑按钮** - 鼠标悬停表格时显示编辑按钮
- **置顶工具栏** - 编辑器工具栏始终可见，方便操作
- **光标位置插入** - 新表格精确插入到光标位置

### 表格编辑器功能

#### 基本操作
- **单击单元格** - 选中单元格
- **双击单元格** - 进入编辑模式
- **Enter键** - 保存编辑
- **Escape键** - 取消编辑
- **方向键** - 在单元格间导航

#### 行列操作
- **添加行** - 点击"添加行"按钮
- **删除行** - 点击行号旁的"×"按钮
- **添加列** - 点击"添加列"按钮  
- **删除列** - 点击列标题旁的"×"按钮

#### 表头编辑
- 直接在表头输入框中修改列名
- 支持中文列名和特殊字符

#### 排序功能
- 点击列标题旁的排序按钮
- 支持升序/降序排列

### 表格模板详解

#### 1. 功能需求表
```
| 需求ID | 功能名称 | 优先级 | 描述 | 验收标准 | 负责人 | 状态 |
```
- **用途**: 管理产品功能需求
- **验证**: 需求ID格式、优先级枚举值
- **示例**: REQ-001, 用户注册, 高

#### 2. 用户故事表
```
| 故事ID | 作为 | 我想要 | 以便于 | 验收标准 | 故事点数 | 状态 |
```
- **用途**: 记录用户故事
- **验证**: 故事ID格式、故事点数为数字
- **示例**: US-001, 普通用户, 注册账号

#### 3. API接口表
```
| 接口名称 | 请求方法 | 请求路径 | 请求参数 | 响应格式 | 状态码 | 负责人 |
```
- **用途**: 文档化API接口
- **验证**: HTTP方法、路径格式
- **示例**: 用户注册, POST, /api/users/register

#### 4. 测试用例表
```
| 用例ID | 测试场景 | 前置条件 | 测试步骤 | 预期结果 | 优先级 | 执行状态 |
```
- **用途**: 管理测试用例
- **验证**: 用例ID格式、优先级和状态枚举
- **示例**: TC-001, 用户注册成功

#### 5. 数据字典表
```
| 表名 | 字段名 | 字段类型 | 长度 | 是否必填 | 默认值 | 说明 |
```
- **用途**: 定义数据库结构
- **验证**: 字段类型枚举、必填标识
- **示例**: users, email, VARCHAR, 255

#### 6. 风险评估表
```
| 风险ID | 风险描述 | 风险类型 | 发生概率 | 影响程度 | 风险等级 | 应对措施 | 负责人 |
```
- **用途**: 项目风险管理
- **验证**: 风险ID格式、概率和影响程度等级
- **示例**: RISK-001, 第三方API不稳定

#### 7. 项目里程碑表
```
| 里程碑 | 计划开始时间 | 计划结束时间 | 实际开始时间 | 实际结束时间 | 完成度 | 状态 | 备注 |
```
- **用途**: 跟踪项目进度
- **验证**: 日期格式、完成度百分比
- **示例**: 需求分析完成, 2024-01-01

## 🔧 高级功能

### 数据验证
每个模板都包含数据验证规则：
- **必填字段检查**
- **数据类型验证**（字符串、数字、邮箱、URL等）
- **长度限制**
- **正则表达式验证**
- **枚举值检查**

### 数据导入导出

#### CSV导出
1. 编辑完表格后点击"导出CSV"
2. 文件自动下载到本地
3. 支持Excel打开

#### Markdown转换
- 表格自动转换为Markdown格式
- 插入到PRD文档中
- 支持GitHub风格表格语法

### 表格验证工具

#### 验证规则示例
```javascript
{
  type: 'string',        // 数据类型
  required: true,        // 是否必填
  minLength: 2,          // 最小长度
  maxLength: 50,         // 最大长度
  pattern: '^REQ-\\d+$', // 正则表达式
  enum: ['高', '中', '低'] // 枚举值
}
```

#### 错误提示
- 实时验证反馈
- 详细错误信息
- 行列定位

## 💡 使用技巧

### 1. 快速填充
- 复制粘贴Excel数据
- 使用Tab键快速移动
- 批量编辑相似内容

### 2. 模板定制
- 基于现有模板修改
- 添加自定义列
- 调整验证规则

### 3. 数据管理
- 定期导出备份
- 版本控制管理
- 团队协作共享

### 4. 性能优化
- 大表格使用虚拟滚动
- 分页显示数据
- 按需加载内容

## 🐛 常见问题

### Q: 表格数据丢失怎么办？
A: 系统自动保存，可在浏览器本地存储中恢复

### Q: 如何处理大量数据？
A: 建议分批处理，单表不超过1000行

### Q: 表格格式不正确？
A: 检查验证规则，按照模板要求填写

### Q: 无法导出CSV？
A: 检查浏览器下载权限设置

## 🔄 更新日志

### v1.1 (2025-01-20) - 用户体验优化 ⭐
- ✅ **UI布局优化** - 解决横向滚动条问题，编辑区域自适应宽度
- ✅ **工具栏置顶** - 编辑器工具栏始终可见，提升操作便利性
- ✅ **精确插入** - 表格插入到光标位置，而非文档末尾
- ✅ **双击编辑** - 双击表格直接打开编辑器
- ✅ **悬浮按钮** - 表格悬浮编辑按钮，快速访问编辑功能
- ✅ **实时更新** - 表格编辑后立即在文档中更新

### v1.0 (2025-01-20)
- ✅ 实现可视化表格编辑器
- ✅ 添加7种PRD表格模板
- ✅ 集成数据验证功能
- ✅ 支持CSV导入导出
- ✅ 移除编辑器类型选择，固定使用增强编辑器

## 📞 技术支持

如遇问题请：
1. 查看浏览器控制台错误信息
2. 检查网络连接状态
3. 尝试刷新页面重新加载
4. 联系技术支持团队

---

**最后更新**: 2025-01-20  
**功能版本**: v1.0  
**兼容性**: Chrome 80+, Firefox 75+, Safari 13+
