# 📊 表格系统完整指南

## 🎯 系统概述

智能PRD编辑器的表格系统提供了统一、直观、功能丰富的表格创建和编辑体验。通过统一的架构设计，实现了从模板选择到编辑操作的完整工作流程。

## 🏗️ 架构设计

### 核心组件架构
```
┌─────────────────────────────────────────────────────────┐
│                   用户交互层                              │
├─────────────────────────────────────────────────────────┤
│  工具栏按钮  │  悬浮编辑按钮  │  双击编辑  │  模板选择器   │
├─────────────────────────────────────────────────────────┤
│                   管理协调层                              │
├─────────────────────────────────────────────────────────┤
│           TableInteractionManager (统一管理器)            │
├─────────────────────────────────────────────────────────┤
│                   功能实现层                              │
├─────────────────────────────────────────────────────────┤
│  TableEditor  │  TableConverter  │  TableRenderEnhancer │
├─────────────────────────────────────────────────────────┤
│                   数据支持层                              │
└─────────────────────────────────────────────────────────┘
│  TableTemplates  │  TableUtils  │  HoverButton         │
└─────────────────────────────────────────────────────────┘
```

### 统一管理原则
- **单一入口** - 所有表格操作通过TableInteractionManager
- **双模式支持** - 新建表格和编辑现有表格的统一处理
- **状态管理** - 集中的表格状态和生命周期管理

## 🔧 核心组件详解

### 1. TableInteractionManager (核心管理器)
**职责**: 统一管理所有表格交互和编辑操作

**主要功能**:
- 扫描和识别页面中的表格
- 管理表格的悬浮按钮显示
- 处理表格双击编辑事件
- 协调新建表格和编辑现有表格的流程
- 管理表格编辑器的显示和隐藏

**关键特性**:
```javascript
// 双模式支持
const [isExternalEdit, setIsExternalEdit] = useState(false);

// 外部编辑数据处理
useEffect(() => {
  if (externalEditData) {
    setTableHeaders(externalEditData.headers);
    setTableData(externalEditData.data);
    setIsExternalEdit(true);
    setShowTableEditor(true);
  }
}, [externalEditData]);
```

### 2. TableEditor (表格编辑器)
**职责**: 提供完整的表格编辑界面和功能

**主要功能**:
- 表格数据的可视化编辑
- 行列的添加、删除、移动
- 单元格内容的编辑
- 表格头部的管理
- 数据验证和格式化

**操作支持**:
- ✅ 添加/删除行
- ✅ 添加/删除列
- ✅ 单元格编辑
- ✅ 拖拽排序（规划中）
- ✅ 批量操作（规划中）

### 3. TableTemplateSelector (模板选择器)
**职责**: 提供丰富的表格模板选择

**模板类型**:
- 📋 **基础表格** - 简单的数据表格
- 📊 **数据分析** - 带统计功能的表格
- 📝 **需求矩阵** - PRD专用的需求表格
- 🎯 **功能清单** - 功能点管理表格
- 📈 **进度跟踪** - 项目进度表格

### 4. TableRenderEnhancer (渲染增强器)
**职责**: 将Markdown表格转换为可交互的HTML表格

**核心功能**:
- Markdown表格语法识别
- HTML表格生成和样式应用
- 交互功能的绑定
- 动态渲染和更新

### 5. TableConverter (格式转换器)
**职责**: 处理表格数据的格式转换

**转换支持**:
- 数组数据 ↔ Markdown语法
- HTML表格 ↔ 数据结构
- 导入/导出格式支持

## 🔄 工作流程

### 新建表格流程
```
1. 用户点击工具栏"📊 表格"按钮
   ↓
2. 显示TableTemplateSelector模板选择器
   ↓
3. 用户选择模板
   ↓
4. 通过externalEditData传递数据给TableInteractionManager
   ↓
5. TableInteractionManager打开TableEditor (新建模式)
   ↓
6. 用户编辑表格内容
   ↓
7. 点击"插入表格"按钮
   ↓
8. TableConverter转换为Markdown
   ↓
9. 插入到编辑器光标位置
   ↓
10. TableRenderEnhancer渲染为HTML表格
```

### 编辑现有表格流程
```
1. 用户悬停表格 → 显示编辑按钮 OR 双击表格
   ↓
2. TableInteractionManager解析表格数据
   ↓
3. 打开TableEditor (编辑模式)
   ↓
4. 用户编辑表格内容
   ↓
5. 点击"保存更改"按钮
   ↓
6. TableConverter转换为Markdown
   ↓
7. 更新原表格位置的内容
   ↓
8. TableRenderEnhancer重新渲染
```

## 🎨 用户界面设计

### 工具栏集成
- **位置**: 编辑器顶部工具栏
- **样式**: 统一的按钮设计
- **交互**: 悬停效果和点击反馈

### 悬浮编辑按钮
- **触发**: 鼠标悬停在表格上
- **位置**: 表格右上角
- **样式**: 半透明背景，清晰图标
- **功能**: 快速进入编辑模式

### 表格编辑器界面
- **布局**: 模态对话框形式
- **尺寸**: 响应式设计，最大化利用屏幕空间
- **操作区**: 顶部工具栏，底部操作按钮
- **编辑区**: 中央表格编辑区域

### 模板选择器界面
- **布局**: 网格式模板展示
- **预览**: 每个模板的缩略图预览
- **分类**: 按功能类型分组显示
- **搜索**: 模板搜索和筛选功能

## 📊 数据结构

### 表格数据格式
```javascript
// 标准表格数据结构
{
  headers: ['列1', '列2', '列3'],
  data: [
    ['行1列1', '行1列2', '行1列3'],
    ['行2列1', '行2列2', '行2列3']
  ]
}

// 扩展表格数据结构（规划中）
{
  headers: [
    { name: '列1', type: 'text', width: 100 },
    { name: '列2', type: 'number', width: 80 }
  ],
  data: [
    { id: 1, values: ['值1', '值2'], meta: {} }
  ],
  config: {
    sortable: true,
    filterable: true,
    pagination: false
  }
}
```

### 模板数据格式
```javascript
{
  id: 'basic-table',
  name: '基础表格',
  category: 'basic',
  description: '简单的数据表格',
  headers: ['项目', '描述', '状态'],
  data: [
    ['示例项目', '项目描述', '进行中'],
    ['', '', '']
  ],
  preview: 'base64-image-data'
}
```

## 🔧 技术实现

### 状态管理
```javascript
// TableInteractionManager中的状态
const [tables, setTables] = useState([]);           // 页面表格列表
const [editingTable, setEditingTable] = useState(null);  // 当前编辑表格
const [tableData, setTableData] = useState([]);     // 表格数据
const [tableHeaders, setTableHeaders] = useState([]); // 表格头部
const [showTableEditor, setShowTableEditor] = useState(false); // 编辑器显示
const [isExternalEdit, setIsExternalEdit] = useState(false);   // 外部编辑模式
```

### 事件处理
```javascript
// 表格扫描和事件绑定
const scanTables = useCallback(() => {
  const tableElements = editorContainer.querySelectorAll('table');
  const tableList = Array.from(tableElements).map((element, index) => ({
    id: `table-${index}`,
    element,
    rect: element.getBoundingClientRect()
  }));
  setTables(tableList);
}, [editorContainer]);

// 双击编辑处理
const handleTableDoubleClick = (e) => {
  const tableElement = e.target.closest('table');
  if (tableElement) {
    const table = tables.find(t => t.element === tableElement);
    if (table) {
      handleTableEdit(table);
    }
  }
};
```

### 渲染优化
```javascript
// 表格渲染增强
const enhanceExistingTables = () => {
  const walker = document.createTreeWalker(
    editorContainer,
    NodeFilter.SHOW_TEXT,
    null,
    false
  );

  const textNodes = [];
  let node;
  while (node = walker.nextNode()) {
    if (containsTableMarkdown(node.textContent)) {
      textNodes.push(node);
    }
  }

  textNodes.forEach(textNode => {
    convertMarkdownTableToHTML(textNode);
  });
};
```

## 🎯 最佳实践

### 开发建议
1. **组件复用** - 充分利用现有的表格组件
2. **状态管理** - 通过TableInteractionManager统一管理
3. **性能优化** - 避免不必要的重新渲染
4. **错误处理** - 完善的错误边界和降级处理

### 用户体验
1. **操作一致性** - 所有表格操作保持一致的交互模式
2. **视觉反馈** - 及时的操作反馈和状态提示
3. **键盘支持** - 完整的键盘导航和快捷键
4. **无障碍访问** - 符合WCAG标准的无障碍设计

### 扩展指南
1. **新增模板** - 在tableTemplates.js中添加新模板
2. **自定义功能** - 通过TableEditor扩展编辑功能
3. **样式定制** - 通过CSS变量定制表格样式
4. **插件开发** - 基于现有架构开发表格插件

---

**文档版本**: v1.0  
**最后更新**: 2025-01-20  
**相关文档**: [架构总览](./ARCHITECTURE_OVERVIEW_2025.md), [功能矩阵](./FEATURE_MATRIX.md)
