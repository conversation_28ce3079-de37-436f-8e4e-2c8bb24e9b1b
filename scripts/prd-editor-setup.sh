#!/bin/bash

# PRD编辑器统一渲染实施脚本
# 用于快速执行各个阶段的任务

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "PRD编辑器统一渲染实施脚本"
    echo ""
    echo "用法: $0 [选项] [阶段]"
    echo ""
    echo "阶段:"
    echo "  stage1    执行阶段1: PDF内容提取与处理"
    echo "  stage2    执行阶段2: 统一内容渲染系统"
    echo "  stage3    执行阶段3: PRD编辑器集成改造"
    echo "  stage4    执行阶段4: 数据同步与状态管理"
    echo "  all       执行所有阶段"
    echo "  status    查看当前状态"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -v, --verbose  详细输出"
    echo "  --dry-run      仅显示将要执行的操作，不实际执行"
    echo ""
    echo "示例:"
    echo "  $0 stage1              # 执行阶段1"
    echo "  $0 --verbose stage2    # 详细执行阶段2"
    echo "  $0 --dry-run all       # 预览所有操作"
}

# 检查环境
check_environment() {
    log_info "检查环境依赖..."
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    # 检查BSV PDF文件
    if [ ! -f "test_data/BSV特性需求文档-pdf.pdf" ]; then
        log_error "BSV PDF文件不存在: test_data/BSV特性需求文档-pdf.pdf"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 更新进度文档
update_progress() {
    local stage=$1
    local task=$2
    local status=$3
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    log_info "更新进度: 阶段${stage} - ${task} - ${status}"
    
    # 这里可以添加实际的进度更新逻辑
    # 比如更新markdown文件或者数据库
}

# 阶段1: PDF内容提取与处理
execute_stage1() {
    log_info "开始执行阶段1: PDF内容提取与处理"
    
    # 任务1.1: PDF内容提取
    log_info "任务1.1: 提取BSV PDF内容"
    update_progress "1" "PDF内容提取" "进行中"
    
    if [ "$DRY_RUN" = "true" ]; then
        log_info "[DRY RUN] 将执行: cd tools/prd-loader && python3 simple_pdf_extract.py"
    else
        cd tools/prd-loader
        python3 simple_pdf_extract.py
        cd "$PROJECT_ROOT"
        
        # 验证输出文件
        if [ -f "tools/prd-loader/bsv_full_content.txt" ]; then
            log_success "PDF内容提取完成"
            update_progress "1" "PDF内容提取" "完成"
        else
            log_error "PDF内容提取失败"
            exit 1
        fi
    fi
    
    # 任务1.2: 数据结构标准化
    log_info "任务1.2: 数据结构标准化"
    update_progress "1" "数据结构标准化" "进行中"
    
    if [ "$DRY_RUN" = "true" ]; then
        log_info "[DRY RUN] 将执行: cd tools/prd-loader && python3 process_bsv_content.py"
    else
        cd tools/prd-loader
        python3 process_bsv_content.py
        cd "$PROJECT_ROOT"
        
        # 验证输出文件
        if [ -f "src/data/realPRDContent.js" ]; then
            log_success "数据结构标准化完成"
            update_progress "1" "数据结构标准化" "完成"
        else
            log_error "数据结构标准化失败"
            exit 1
        fi
    fi
    
    log_success "阶段1完成"
}

# 阶段2: 统一内容渲染系统
execute_stage2() {
    log_info "开始执行阶段2: 统一内容渲染系统"
    
    log_warning "阶段2需要手动创建React组件"
    log_info "请按照以下步骤执行:"
    log_info "1. 创建 src/components/Common/PRDContentRenderer.js"
    log_info "2. 创建 src/components/Common/SmartCardMenu.js"
    log_info "3. 参考方案文档中的代码实现"
    
    if [ "$DRY_RUN" = "true" ]; then
        log_info "[DRY RUN] 将创建React组件文件"
    else
        # 创建目录
        mkdir -p src/components/Common
        
        # 检查文件是否已存在
        if [ ! -f "src/components/Common/PRDContentRenderer.js" ]; then
            log_warning "需要手动创建 PRDContentRenderer.js"
        fi
        
        if [ ! -f "src/components/Common/SmartCardMenu.js" ]; then
            log_warning "需要手动创建 SmartCardMenu.js"
        fi
    fi
    
    log_info "阶段2需要手动完成组件开发"
}

# 阶段3: PRD编辑器集成改造
execute_stage3() {
    log_info "开始执行阶段3: PRD编辑器集成改造"
    
    log_warning "阶段3需要手动修改现有组件"
    log_info "请按照以下步骤执行:"
    log_info "1. 修改 src/components/PRDEditor/PRDEditor.js"
    log_info "2. 创建 src/components/PRDEditor/PRDEditor.css"
    log_info "3. 集成统一渲染组件"
    
    log_info "阶段3需要手动完成组件集成"
}

# 阶段4: 数据同步与状态管理
execute_stage4() {
    log_info "开始执行阶段4: 数据同步与状态管理"
    
    log_warning "阶段4需要手动创建工具函数和修改hooks"
    log_info "请按照以下步骤执行:"
    log_info "1. 创建 src/utils/contentSync.js"
    log_info "2. 修改 src/hooks/useContentState.js"
    log_info "3. 实现数据同步逻辑"
    
    log_info "阶段4需要手动完成数据同步开发"
}

# 查看状态
show_status() {
    log_info "检查项目状态..."
    
    echo ""
    echo "=== 文件状态检查 ==="
    
    # 检查关键文件
    files=(
        "test_data/BSV特性需求文档-pdf.pdf:BSV PDF源文件"
        "tools/prd-loader/bsv_full_content.txt:提取的PDF内容"
        "src/data/realPRDContent.js:结构化PRD数据"
        "src/components/Common/PRDContentRenderer.js:统一内容渲染器"
        "src/components/Common/SmartCardMenu.js:智能卡片菜单"
        "src/components/PRDEditor/PRDEditor.js:PRD编辑器主组件"
        "src/utils/contentSync.js:数据同步工具"
        "docs/PRD编辑器统一渲染方案.md:方案文档"
        "docs/PRD编辑器实施进度.md:进度文档"
    )
    
    for file_info in "${files[@]}"; do
        IFS=':' read -r file desc <<< "$file_info"
        if [ -f "$file" ]; then
            echo -e "✅ ${desc}: ${GREEN}存在${NC}"
        else
            echo -e "❌ ${desc}: ${RED}不存在${NC}"
        fi
    done
    
    echo ""
    echo "=== 环境状态检查 ==="
    check_environment
}

# 主函数
main() {
    local VERBOSE=false
    local DRY_RUN=false
    local STAGE=""
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            stage1|stage2|stage3|stage4|all|status)
                STAGE=$1
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定阶段，显示帮助
    if [ -z "$STAGE" ]; then
        show_help
        exit 1
    fi
    
    # 设置详细输出
    if [ "$VERBOSE" = "true" ]; then
        set -x
    fi
    
    # 显示dry run提示
    if [ "$DRY_RUN" = "true" ]; then
        log_warning "DRY RUN 模式 - 仅显示操作，不实际执行"
    fi
    
    # 执行对应阶段
    case $STAGE in
        stage1)
            check_environment
            execute_stage1
            ;;
        stage2)
            execute_stage2
            ;;
        stage3)
            execute_stage3
            ;;
        stage4)
            execute_stage4
            ;;
        all)
            check_environment
            execute_stage1
            execute_stage2
            execute_stage3
            execute_stage4
            ;;
        status)
            show_status
            ;;
    esac
}

# 执行主函数
main "$@"
