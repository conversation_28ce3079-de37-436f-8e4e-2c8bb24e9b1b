# ========================================
# REQ AI Edit Agent Backend - 环境配置
# ========================================

# 服务器配置
NODE_ENV=development
PORT=8000
APP_VERSION=1.0.0

# 前端地址
FRONTEND_URL=http://localhost:3000

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/req-ai-agent
REDIS_URL=redis://localhost:6379

# Agent 配置
AGENT_DISCOVERY_INTERVAL=30000
AGENT_HEALTH_CHECK_INTERVAL=10000
AGENT_LOAD_BALANCE_STRATEGY=round-robin

# AI 服务配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1
CLAUDE_API_KEY=your-claude-api-key

# 认证配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=12

# 安全配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
CORS_ORIGIN=http://localhost:3000

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# WebSocket配置
WS_HEARTBEAT_INTERVAL=30000
WS_MAX_CONNECTIONS=1000

# Agent 端点配置（多Agent部署）
ANALYSIS_AGENT_ENDPOINT=http://localhost:8001
KNOWLEDGE_AGENT_ENDPOINT=http://localhost:8002
CASES_AGENT_ENDPOINT=http://localhost:8003
INSPIRATION_AGENT_ENDPOINT=http://localhost:8004

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_PATH=/health

# 开发配置
ENABLE_DEBUG=true
ENABLE_MOCK_AGENTS=true
ENABLE_API_DOCS=true 