2025-06-14 00:30:12:3012 [[32minfo[39m]: [32mAgentOrchestrator创建成功[39m {
  "service": "req-ai-backend",
  "version": "1.0.0"
}
2025-06-14 00:30:12:3012 [[32minfo[39m]: [32m健康检查已启动[39m {
  "service": "req-ai-backend",
  "version": "1.0.0"
}
2025-06-14 00:30:12:3012 [[32minfo[39m]: [32mAgent注册成功[39m {
  "service": "req-ai-backend",
  "version": "1.0.0",
  "agentId": "req-analyzer",
  "name": "需求分析Agent",
  "capabilities": [
    "requirement_analysis",
    "text_parsing",
    "structure_analysis"
  ]
}
2025-06-14 00:30:12:3012 [[32minfo[39m]: [32mAgent注册成功[39m {
  "service": "req-ai-backend",
  "version": "1.0.0",
  "agentId": "content-optimizer",
  "name": "内容优化Agent",
  "capabilities": [
    "content_optimization",
    "language_improvement",
    "clarity_enhancement"
  ]
}
2025-06-14 00:30:12:3012 [[32minfo[39m]: [32mAgent注册成功[39m {
  "service": "req-ai-backend",
  "version": "1.0.0",
  "agentId": "validation-agent",
  "name": "验证Agent",
  "capabilities": [
    "validation",
    "consistency_check",
    "quality_assurance"
  ]
}
2025-06-14 00:30:12:3012 [[32minfo[39m]: [32mAgentOrchestrator初始化完成[39m {
  "service": "req-ai-backend",
  "version": "1.0.0"
}
2025-06-14 00:30:12:3012 [[32minfo[39m]: [32mAgent orchestrator initialized[39m {
  "service": "req-ai-backend",
  "version": "1.0.0"
}
2025-06-14 00:30:12:3012 [[32minfo[39m]: [32m🚀 服务器启动成功[39m {
  "service": "req-ai-backend",
  "version": "1.0.0",
  "port": 8000,
  "environment": "development",
  "pid": 76508,
  "endpoints": {
    "api": "http://localhost:8000/api",
    "health": "http://localhost:8000/health",
    "websocket": "ws://localhost:8000/ws"
  }
}
2025-06-14 00:30:20:3020 [[32minfo[39m]: [32m::1 - - [13/Jun/2025:16:30:20 +0000] "GET /health HTTP/1.1" 200 158 "-" "curl/8.7.1"[39m {
  "service": "req-ai-backend",
  "version": "1.0.0"
}
2025-06-14 00:30:26:3026 [[32minfo[39m]: [32m::1 - - [13/Jun/2025:16:30:26 +0000] "GET / HTTP/1.1" 200 302 "-" "curl/8.7.1"[39m {
  "service": "req-ai-backend",
  "version": "1.0.0"
}
2025-06-14 00:30:31:3031 [[31merror[39m]: [31mError occurred[39m {
  "service": "req-ai-backend",
  "version": "1.0.0",
  "requestId": "req-1749832231405-8nhgh26a1",
  "error": {
    "name": "AppError",
    "message": "路径 /api/agents 不存在",
    "code": "NOT_FOUND",
    "statusCode": 404,
    "stack": "AppError: 路径 /api/agents 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:47:12)"
  },
  "request": {
    "method": "GET",
    "url": "/api/agents",
    "ip": "::1",
    "userAgent": "curl/8.7.1"
  }
}
2025-06-14 00:30:31:3031 [[32minfo[39m]: [32m::1 - - [13/Jun/2025:16:30:31 +0000] "GET /api/agents HTTP/1.1" 404 168 "-" "curl/8.7.1"[39m {
  "service": "req-ai-backend",
  "version": "1.0.0"
}
2025-06-14 00:30:49:3049 [[32minfo[39m]: [32mAgent discovery requested[39m {
  "service": "req-ai-backend",
  "version": "1.0.0",
  "requestId": "req-1749832249185-td00j08yz",
  "agentCount": 3,
  "activeCount": 3
}
2025-06-14 00:30:49:3049 [[32minfo[39m]: [32m::1 - - [13/Jun/2025:16:30:49 +0000] "GET /api/agents/discovery HTTP/1.1" 200 829 "-" "curl/8.7.1"[39m {
  "service": "req-ai-backend",
  "version": "1.0.0"
}
2025-06-14 00:31:01:311 [[33mwarn[39m]: [33mTask creation validation failed[39m {
  "service": "req-ai-backend",
  "version": "1.0.0",
  "requestId": "req-1749832261495-5lt6aj6zr",
  "error": "\"type\" must be one of [analysis-agent, knowledge-agent, cases-agent, inspiration-agent, orchestrator-agent]",
  "body": {
    "type": "requirement_analysis",
    "content": "分析这个需求文档",
    "priority": 1
  }
}
2025-06-14 00:31:01:311 [[32minfo[39m]: [32m::1 - - [13/Jun/2025:16:31:01 +0000] "POST /api/tasks HTTP/1.1" 400 232 "-" "curl/8.7.1"[39m {
  "service": "req-ai-backend",
  "version": "1.0.0"
}
2025-06-14 00:46:47:4647 [[32minfo[39m]: [32m::1 - - [13/Jun/2025:16:46:47 +0000] "GET /health HTTP/1.1" 200 160 "-" "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36"[39m {
  "service": "req-ai-backend",
  "version": "1.0.0"
}
