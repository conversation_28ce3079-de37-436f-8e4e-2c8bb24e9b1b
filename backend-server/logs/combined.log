{"level":"info","message":"AgentOrchestrator创建成功","service":"req-ai-backend","timestamp":"2025-06-13T16:30:00.064Z","version":"1.0.0"}
{"level":"info","message":"健康检查已启动","service":"req-ai-backend","timestamp":"2025-06-13T16:30:00.068Z","version":"1.0.0"}
{"agentId":"req-analyzer","capabilities":["requirement_analysis","text_parsing","structure_analysis"],"level":"info","message":"Agent注册成功","name":"需求分析Agent","service":"req-ai-backend","timestamp":"2025-06-13T16:30:00.069Z","version":"1.0.0"}
{"agentId":"content-optimizer","capabilities":["content_optimization","language_improvement","clarity_enhancement"],"level":"info","message":"Agent注册成功","name":"内容优化Agent","service":"req-ai-backend","timestamp":"2025-06-13T16:30:00.069Z","version":"1.0.0"}
{"agentId":"validation-agent","capabilities":["validation","consistency_check","quality_assurance"],"level":"info","message":"Agent注册成功","name":"验证Agent","service":"req-ai-backend","timestamp":"2025-06-13T16:30:00.070Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator初始化完成","service":"req-ai-backend","timestamp":"2025-06-13T16:30:00.070Z","version":"1.0.0"}
{"level":"info","message":"Agent orchestrator initialized","service":"req-ai-backend","timestamp":"2025-06-13T16:30:00.070Z","version":"1.0.0"}
{"endpoints":{"api":"http://localhost:8000/api","health":"http://localhost:8000/health","websocket":"ws://localhost:8000/ws"},"environment":"development","level":"info","message":"🚀 服务器启动成功","pid":76180,"port":8000,"service":"req-ai-backend","timestamp":"2025-06-13T16:30:00.071Z","version":"1.0.0"}
{"level":"info","message":"收到 SIGTERM 信号，开始优雅关闭...","service":"req-ai-backend","timestamp":"2025-06-13T16:30:04.717Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator创建成功","service":"req-ai-backend","timestamp":"2025-06-13T16:30:12.879Z","version":"1.0.0"}
{"level":"info","message":"健康检查已启动","service":"req-ai-backend","timestamp":"2025-06-13T16:30:12.881Z","version":"1.0.0"}
{"agentId":"req-analyzer","capabilities":["requirement_analysis","text_parsing","structure_analysis"],"level":"info","message":"Agent注册成功","name":"需求分析Agent","service":"req-ai-backend","timestamp":"2025-06-13T16:30:12.881Z","version":"1.0.0"}
{"agentId":"content-optimizer","capabilities":["content_optimization","language_improvement","clarity_enhancement"],"level":"info","message":"Agent注册成功","name":"内容优化Agent","service":"req-ai-backend","timestamp":"2025-06-13T16:30:12.882Z","version":"1.0.0"}
{"agentId":"validation-agent","capabilities":["validation","consistency_check","quality_assurance"],"level":"info","message":"Agent注册成功","name":"验证Agent","service":"req-ai-backend","timestamp":"2025-06-13T16:30:12.882Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator初始化完成","service":"req-ai-backend","timestamp":"2025-06-13T16:30:12.882Z","version":"1.0.0"}
{"level":"info","message":"Agent orchestrator initialized","service":"req-ai-backend","timestamp":"2025-06-13T16:30:12.882Z","version":"1.0.0"}
{"endpoints":{"api":"http://localhost:8000/api","health":"http://localhost:8000/health","websocket":"ws://localhost:8000/ws"},"environment":"development","level":"info","message":"🚀 服务器启动成功","pid":76508,"port":8000,"service":"req-ai-backend","timestamp":"2025-06-13T16:30:12.884Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [13/Jun/2025:16:30:20 +0000] \"GET /health HTTP/1.1\" 200 158 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-13T16:30:20.813Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [13/Jun/2025:16:30:26 +0000] \"GET / HTTP/1.1\" 200 302 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-13T16:30:26.518Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /api/agents 不存在","name":"AppError","stack":"AppError: 路径 /api/agents 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:47:12)","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/api/agents","userAgent":"curl/8.7.1"},"requestId":"req-1749832231405-8nhgh26a1","service":"req-ai-backend","timestamp":"2025-06-13T16:30:31.406Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [13/Jun/2025:16:30:31 +0000] \"GET /api/agents HTTP/1.1\" 404 168 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-13T16:30:31.407Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"req-1749832249185-td00j08yz","service":"req-ai-backend","timestamp":"2025-06-13T16:30:49.190Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [13/Jun/2025:16:30:49 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 829 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-13T16:30:49.191Z","version":"1.0.0"}
{"body":{"content":"分析这个需求文档","priority":1,"type":"requirement_analysis"},"error":"\"type\" must be one of [analysis-agent, knowledge-agent, cases-agent, inspiration-agent, orchestrator-agent]","level":"warn","message":"Task creation validation failed","requestId":"req-1749832261495-5lt6aj6zr","service":"req-ai-backend","timestamp":"2025-06-13T16:31:01.499Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [13/Jun/2025:16:31:01 +0000] \"POST /api/tasks HTTP/1.1\" 400 232 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-13T16:31:01.501Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [13/Jun/2025:16:46:47 +0000] \"GET /health HTTP/1.1\" 200 160 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-06-13T16:46:47.298Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator创建成功","service":"req-ai-backend","timestamp":"2025-06-14T16:06:24.204Z","version":"1.0.0"}
{"level":"info","message":"健康检查已启动","service":"req-ai-backend","timestamp":"2025-06-14T16:06:24.206Z","version":"1.0.0"}
{"agentId":"req-analyzer","capabilities":["requirement_analysis","text_parsing","structure_analysis"],"level":"info","message":"Agent注册成功","name":"需求分析Agent","service":"req-ai-backend","timestamp":"2025-06-14T16:06:24.207Z","version":"1.0.0"}
{"agentId":"content-optimizer","capabilities":["content_optimization","language_improvement","clarity_enhancement"],"level":"info","message":"Agent注册成功","name":"内容优化Agent","service":"req-ai-backend","timestamp":"2025-06-14T16:06:24.207Z","version":"1.0.0"}
{"agentId":"validation-agent","capabilities":["validation","consistency_check","quality_assurance"],"level":"info","message":"Agent注册成功","name":"验证Agent","service":"req-ai-backend","timestamp":"2025-06-14T16:06:24.207Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator初始化完成","service":"req-ai-backend","timestamp":"2025-06-14T16:06:24.207Z","version":"1.0.0"}
{"level":"info","message":"Agent orchestrator initialized","service":"req-ai-backend","timestamp":"2025-06-14T16:06:24.207Z","version":"1.0.0"}
{"endpoints":{"api":"http://localhost:8000/api","health":"http://localhost:8000/health","websocket":"ws://localhost:8000/ws"},"environment":"development","level":"info","message":"🚀 服务器启动成功","pid":23589,"port":8000,"service":"req-ai-backend","timestamp":"2025-06-14T16:06:24.209Z","version":"1.0.0"}
{"activeCount":2,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"req-1749917239185-h4u9fbx2y","service":"req-ai-backend","timestamp":"2025-06-14T16:07:19.200Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [14/Jun/2025:16:07:19 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 831 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-14T16:07:19.207Z","version":"1.0.0"}
{"body":{"agentId":"req-analyzer","analysisType":"requirement_analysis","text":"这是一个产品需求文档测试"},"error":"\"selectedText\" is required","level":"warn","message":"Analysis request validation failed","requestId":"req-1749917256469-we0vrl333","service":"req-ai-backend","timestamp":"2025-06-14T16:07:36.473Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [14/Jun/2025:16:07:36 +0000] \"POST /api/analysis HTTP/1.1\" 400 159 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-14T16:07:36.474Z","version":"1.0.0"}
{"body":{"agentId":"req-analyzer","analysisType":"requirement_analysis","selectedText":"这是一个产品需求文档测试"},"error":"\"analysisType\" must be one of [evaluate, inspect, supplement, reference, examples, cases, inspire, creative]","level":"warn","message":"Analysis request validation failed","requestId":"req-1749917277503-pxupwqos1","service":"req-ai-backend","timestamp":"2025-06-14T16:07:57.508Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [14/Jun/2025:16:07:57 +0000] \"POST /api/analysis HTTP/1.1\" 400 241 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-14T16:07:57.518Z","version":"1.0.0"}
{"body":{"agentId":"req-analyzer","analysisType":"evaluate","selectedText":"这是一个产品需求文档测试"},"error":"\"agentId\" is not allowed","level":"warn","message":"Analysis request validation failed","requestId":"req-1749917290628-cbiveex5l","service":"req-ai-backend","timestamp":"2025-06-14T16:08:10.642Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [14/Jun/2025:16:08:10 +0000] \"POST /api/analysis HTTP/1.1\" 400 152 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-14T16:08:10.645Z","version":"1.0.0"}
{"level":"info","message":"任务创建成功","priority":1,"service":"req-ai-backend","taskId":"1adbc9bf-05af-40c0-a311-519bb7131512","timestamp":"2025-06-14T16:08:27.797Z","type":"analysis-agent","version":"1.0.0"}
{"level":"info","message":"任务开始执行","service":"req-ai-backend","taskId":"1adbc9bf-05af-40c0-a311-519bb7131512","timestamp":"2025-06-14T16:08:27.797Z","version":"1.0.0"}
{"level":"warn","message":"任务重试","retryCount":1,"service":"req-ai-backend","taskId":"1adbc9bf-05af-40c0-a311-519bb7131512","timestamp":"2025-06-14T16:08:27.798Z","version":"1.0.0"}
{"error":{"message":"orchestrator.waitForTaskCompletion is not a function","name":"TypeError","stack":"TypeError: orchestrator.waitForTaskCompletion is not a function\n    at file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/routes/analysis.js:69:35\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"POST","url":"/api/analysis","userAgent":"curl/8.7.1"},"requestId":"req-1749917307795-lvkdwr4ot","service":"req-ai-backend","timestamp":"2025-06-14T16:08:27.798Z","version":"1.0.0"}
{"level":"info","message":"任务开始执行","service":"req-ai-backend","taskId":"1adbc9bf-05af-40c0-a311-519bb7131512","timestamp":"2025-06-14T16:08:27.799Z","version":"1.0.0"}
{"level":"warn","message":"任务重试","retryCount":2,"service":"req-ai-backend","taskId":"1adbc9bf-05af-40c0-a311-519bb7131512","timestamp":"2025-06-14T16:08:27.799Z","version":"1.0.0"}
{"level":"info","message":"任务开始执行","service":"req-ai-backend","taskId":"1adbc9bf-05af-40c0-a311-519bb7131512","timestamp":"2025-06-14T16:08:27.800Z","version":"1.0.0"}
{"level":"warn","message":"任务重试","retryCount":3,"service":"req-ai-backend","taskId":"1adbc9bf-05af-40c0-a311-519bb7131512","timestamp":"2025-06-14T16:08:27.800Z","version":"1.0.0"}
{"level":"info","message":"任务开始执行","service":"req-ai-backend","taskId":"1adbc9bf-05af-40c0-a311-519bb7131512","timestamp":"2025-06-14T16:08:27.800Z","version":"1.0.0"}
{"error":"没有找到支持 analysis-agent 能力的Agent","level":"error","message":"任务最终失败","service":"req-ai-backend","taskId":"1adbc9bf-05af-40c0-a311-519bb7131512","timestamp":"2025-06-14T16:08:27.800Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [14/Jun/2025:16:08:27 +0000] \"POST /api/analysis HTTP/1.1\" 500 197 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-14T16:08:27.800Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /api/v1/agents/discovery 不存在","name":"AppError","stack":"AppError: 路径 /api/v1/agents/discovery 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/server.js:78:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/api/v1/agents/discovery","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"1749917616277-tkije57mm","service":"req-ai-backend","timestamp":"2025-06-14T16:13:36.319Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [14/Jun/2025:16:13:36 +0000] \"GET /api/v1/agents/discovery HTTP/1.1\" 404 177 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-06-14T16:13:36.364Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [15/Jun/2025:14:59:26 +0000] \"GET /health HTTP/1.1\" 200 162 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-06-15T14:59:26.387Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /api/v1/agents/discovery 不存在","name":"AppError","stack":"AppError: 路径 /api/v1/agents/discovery 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/server.js:78:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/api/v1/agents/discovery","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"1749999943340-vdakx4lng","service":"req-ai-backend","timestamp":"2025-06-15T15:05:43.397Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [15/Jun/2025:15:05:43 +0000] \"GET /api/v1/agents/discovery HTTP/1.1\" 404 177 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-06-15T15:05:43.414Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /api/v1/agents/discovery 不存在","name":"AppError","stack":"AppError: 路径 /api/v1/agents/discovery 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/server.js:78:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/api/v1/agents/discovery","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"1750002188069-6uf2a4i0m","service":"req-ai-backend","timestamp":"2025-06-15T15:43:08.183Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [15/Jun/2025:15:43:08 +0000] \"GET /api/v1/agents/discovery HTTP/1.1\" 404 177 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-06-15T15:43:08.204Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator创建成功","service":"req-ai-backend","timestamp":"2025-06-23T15:18:04.696Z","version":"1.0.0"}
{"level":"info","message":"健康检查已启动","service":"req-ai-backend","timestamp":"2025-06-23T15:18:04.698Z","version":"1.0.0"}
{"agentId":"req-analyzer","capabilities":["requirement_analysis","text_parsing","structure_analysis"],"level":"info","message":"Agent注册成功","name":"需求分析Agent","service":"req-ai-backend","timestamp":"2025-06-23T15:18:04.699Z","version":"1.0.0"}
{"agentId":"content-optimizer","capabilities":["content_optimization","language_improvement","clarity_enhancement"],"level":"info","message":"Agent注册成功","name":"内容优化Agent","service":"req-ai-backend","timestamp":"2025-06-23T15:18:04.699Z","version":"1.0.0"}
{"agentId":"validation-agent","capabilities":["validation","consistency_check","quality_assurance"],"level":"info","message":"Agent注册成功","name":"验证Agent","service":"req-ai-backend","timestamp":"2025-06-23T15:18:04.699Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator初始化完成","service":"req-ai-backend","timestamp":"2025-06-23T15:18:04.699Z","version":"1.0.0"}
{"level":"info","message":"Agent orchestrator initialized","service":"req-ai-backend","timestamp":"2025-06-23T15:18:04.699Z","version":"1.0.0"}
{"endpoints":{"api":"http://localhost:8000/api","health":"http://localhost:8000/health","websocket":"ws://localhost:8000/ws"},"environment":"development","level":"info","message":"🚀 服务器启动成功","pid":14032,"port":8000,"service":"req-ai-backend","timestamp":"2025-06-23T15:18:04.701Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /api/v1/agents/discovery 不存在","name":"AppError","stack":"AppError: 路径 /api/v1/agents/discovery 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/server.js:78:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/api/v1/agents/discovery","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"1750691898797-8svmsh6c0","service":"req-ai-backend","timestamp":"2025-06-23T15:18:18.842Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [23/Jun/2025:15:18:18 +0000] \"GET /api/v1/agents/discovery HTTP/1.1\" 404 177 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-06-23T15:18:18.862Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /api/v1/agents/discovery 不存在","name":"AppError","stack":"AppError: 路径 /api/v1/agents/discovery 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/server.js:78:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/api/v1/agents/discovery","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"1750692431990-us6x0m13y","service":"req-ai-backend","timestamp":"2025-06-23T15:27:12.110Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [23/Jun/2025:15:27:12 +0000] \"GET /api/v1/agents/discovery HTTP/1.1\" 404 177 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-06-23T15:27:12.127Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [23/Jun/2025:15:31:29 +0000] \"GET /health HTTP/1.1\" 200 160 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-23T15:31:29.317Z","version":"1.0.0"}
{"activeCount":2,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1750692834555-8adc38sh0","service":"req-ai-backend","timestamp":"2025-06-23T15:33:54.677Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [23/Jun/2025:15:33:54 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 827 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-06-23T15:33:54.680Z","version":"1.0.0"}
{"activeCount":2,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1750692835762-hqexfdhcx","service":"req-ai-backend","timestamp":"2025-06-23T15:33:55.844Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [23/Jun/2025:15:33:55 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 827 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-06-23T15:33:55.845Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1750692928412-thpppwbjk","service":"req-ai-backend","timestamp":"2025-06-23T15:35:28.442Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [23/Jun/2025:15:35:28 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 825 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-06-23T15:35:28.444Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1750692960798-13w6wdo1j","service":"req-ai-backend","timestamp":"2025-06-23T15:36:01.020Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [23/Jun/2025:15:36:01 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 825 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-06-23T15:36:01.021Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1750692960803-65ed3x880","service":"req-ai-backend","timestamp":"2025-06-23T15:36:01.022Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [23/Jun/2025:15:36:01 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 825 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-06-23T15:36:01.023Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1750692964497-b8ixq7437","service":"req-ai-backend","timestamp":"2025-06-23T15:36:04.572Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [23/Jun/2025:15:36:04 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 825 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-06-23T15:36:04.573Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1750693581242-6qsays258","service":"req-ai-backend","timestamp":"2025-06-23T15:46:21.525Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [23/Jun/2025:15:46:21 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 825 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-06-23T15:46:21.528Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1750734188069-4tsr7oga5","service":"req-ai-backend","timestamp":"2025-06-24T03:03:08.398Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [24/Jun/2025:03:03:08 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 825 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-06-24T03:03:08.405Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [25/Jun/2025:04:06:40 +0000] \"GET /health HTTP/1.1\" 200 163 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-25T04:06:40.026Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"req-1750824406886-5g3i4aznk","service":"req-ai-backend","timestamp":"2025-06-25T04:06:46.888Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [25/Jun/2025:04:06:46 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 829 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-25T04:06:46.889Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1750828114354-th33zjqb3","service":"req-ai-backend","timestamp":"2025-06-25T05:08:34.521Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [25/Jun/2025:05:08:34 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 825 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-06-25T05:08:34.540Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1750829336582-4x7k4fjdh","service":"req-ai-backend","timestamp":"2025-06-25T05:28:57.091Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [25/Jun/2025:05:28:57 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 825 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-06-25T05:28:57.099Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1750842299980-f46c6i8q0","service":"req-ai-backend","timestamp":"2025-06-25T09:05:00.100Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [25/Jun/2025:09:05:00 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 825 \"http://localhost:3000/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-06-25T09:05:00.120Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [25/Jun/2025:09:44:52 +0000] \"GET /health HTTP/1.1\" 200 163 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-25T09:44:52.945Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [25/Jun/2025:10:49:21 +0000] \"GET /health HTTP/1.1\" 200 163 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-25T10:49:21.309Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"req-1750848592601-spzx3dut9","service":"req-ai-backend","timestamp":"2025-06-25T10:49:52.607Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [25/Jun/2025:10:49:52 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 829 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-25T10:49:52.608Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [25/Jun/2025:15:24:40 +0000] \"GET /health HTTP/1.1\" 200 163 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-25T15:24:40.990Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"req-1750865102974-mk64olws1","service":"req-ai-backend","timestamp":"2025-06-25T15:25:02.975Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [25/Jun/2025:15:25:02 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 829 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-25T15:25:02.976Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [25/Jun/2025:15:48:17 +0000] \"GET /health HTTP/1.1\" 200 163 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-25T15:48:17.460Z","version":"1.0.0"}
{"level":"info","message":"收到 SIGTERM 信号，开始优雅关闭...","service":"req-ai-backend","timestamp":"2025-06-25T16:02:05.902Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator创建成功","service":"req-ai-backend","timestamp":"2025-06-25T16:02:17.531Z","version":"1.0.0"}
{"level":"info","message":"健康检查已启动","service":"req-ai-backend","timestamp":"2025-06-25T16:02:17.533Z","version":"1.0.0"}
{"agentId":"req-analyzer","capabilities":["requirement_analysis","text_parsing","structure_analysis"],"level":"info","message":"Agent注册成功","name":"需求分析Agent","service":"req-ai-backend","timestamp":"2025-06-25T16:02:17.533Z","version":"1.0.0"}
{"agentId":"content-optimizer","capabilities":["content_optimization","language_improvement","clarity_enhancement"],"level":"info","message":"Agent注册成功","name":"内容优化Agent","service":"req-ai-backend","timestamp":"2025-06-25T16:02:17.534Z","version":"1.0.0"}
{"agentId":"validation-agent","capabilities":["validation","consistency_check","quality_assurance"],"level":"info","message":"Agent注册成功","name":"验证Agent","service":"req-ai-backend","timestamp":"2025-06-25T16:02:17.534Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator初始化完成","service":"req-ai-backend","timestamp":"2025-06-25T16:02:17.534Z","version":"1.0.0"}
{"level":"info","message":"Agent orchestrator initialized","service":"req-ai-backend","timestamp":"2025-06-25T16:02:17.534Z","version":"1.0.0"}
{"endpoints":{"api":"http://localhost:8000/api","health":"http://localhost:8000/health","websocket":"ws://localhost:8000/ws"},"environment":"development","level":"info","message":"🚀 服务器启动成功","pid":44553,"port":8000,"service":"req-ai-backend","timestamp":"2025-06-25T16:02:17.536Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [25/Jun/2025:16:02:41 +0000] \"GET /health HTTP/1.1\" 200 158 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-25T16:02:41.888Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"req-1750867956229-ggiqo4p52","service":"req-ai-backend","timestamp":"2025-06-25T16:12:36.234Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [25/Jun/2025:16:12:36 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 829 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-25T16:12:36.237Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [25/Jun/2025:16:25:58 +0000] \"GET /health HTTP/1.1\" 200 161 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-25T16:25:58.480Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [25/Jun/2025:16:31:41 +0000] \"GET /health HTTP/1.1\" 200 161 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-25T16:31:41.759Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"req-1750869972401-b12g1ae74","service":"req-ai-backend","timestamp":"2025-06-25T16:46:12.412Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [25/Jun/2025:16:46:12 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 829 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-25T16:46:12.414Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [25/Jun/2025:16:56:37 +0000] \"GET /health HTTP/1.1\" 200 161 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-25T16:56:37.822Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"req-1750870605512-cvotkos05","service":"req-ai-backend","timestamp":"2025-06-25T16:56:45.513Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [25/Jun/2025:16:56:45 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 829 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-25T16:56:45.514Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [25/Jun/2025:17:13:17 +0000] \"GET /health HTTP/1.1\" 200 160 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-25T17:13:17.221Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [26/Jun/2025:06:10:07 +0000] \"GET /health HTTP/1.1\" 200 162 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-06-26T06:10:07.116Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [02/Jul/2025:05:34:28 +0000] \"GET /health HTTP/1.1\" 200 163 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-07-02T05:34:28.132Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator创建成功","service":"req-ai-backend","timestamp":"2025-07-02T06:13:49.423Z","version":"1.0.0"}
{"level":"info","message":"健康检查已启动","service":"req-ai-backend","timestamp":"2025-07-02T06:13:49.426Z","version":"1.0.0"}
{"agentId":"req-analyzer","capabilities":["requirement_analysis","text_parsing","structure_analysis"],"level":"info","message":"Agent注册成功","name":"需求分析Agent","service":"req-ai-backend","timestamp":"2025-07-02T06:13:49.426Z","version":"1.0.0"}
{"agentId":"content-optimizer","capabilities":["content_optimization","language_improvement","clarity_enhancement"],"level":"info","message":"Agent注册成功","name":"内容优化Agent","service":"req-ai-backend","timestamp":"2025-07-02T06:13:49.426Z","version":"1.0.0"}
{"agentId":"validation-agent","capabilities":["validation","consistency_check","quality_assurance"],"level":"info","message":"Agent注册成功","name":"验证Agent","service":"req-ai-backend","timestamp":"2025-07-02T06:13:49.426Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator初始化完成","service":"req-ai-backend","timestamp":"2025-07-02T06:13:49.427Z","version":"1.0.0"}
{"level":"info","message":"Agent orchestrator initialized","service":"req-ai-backend","timestamp":"2025-07-02T06:13:49.427Z","version":"1.0.0"}
{"endpoints":{"api":"http://localhost:8000/api","health":"http://localhost:8000/health","websocket":"ws://localhost:8000/ws"},"environment":"development","level":"info","message":"🚀 服务器启动成功","pid":76543,"port":8000,"service":"req-ai-backend","timestamp":"2025-07-02T06:13:49.429Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator创建成功","service":"req-ai-backend","timestamp":"2025-07-06T06:21:15.253Z","version":"1.0.0"}
{"level":"info","message":"健康检查已启动","service":"req-ai-backend","timestamp":"2025-07-06T06:21:15.255Z","version":"1.0.0"}
{"agentId":"req-analyzer","capabilities":["requirement_analysis","text_parsing","structure_analysis"],"level":"info","message":"Agent注册成功","name":"需求分析Agent","service":"req-ai-backend","timestamp":"2025-07-06T06:21:15.255Z","version":"1.0.0"}
{"agentId":"content-optimizer","capabilities":["content_optimization","language_improvement","clarity_enhancement"],"level":"info","message":"Agent注册成功","name":"内容优化Agent","service":"req-ai-backend","timestamp":"2025-07-06T06:21:15.255Z","version":"1.0.0"}
{"agentId":"validation-agent","capabilities":["validation","consistency_check","quality_assurance"],"level":"info","message":"Agent注册成功","name":"验证Agent","service":"req-ai-backend","timestamp":"2025-07-06T06:21:15.256Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator初始化完成","service":"req-ai-backend","timestamp":"2025-07-06T06:21:15.256Z","version":"1.0.0"}
{"level":"info","message":"Agent orchestrator initialized","service":"req-ai-backend","timestamp":"2025-07-06T06:21:15.256Z","version":"1.0.0"}
{"endpoints":{"api":"http://localhost:8000/api","health":"http://localhost:8000/health","websocket":"ws://localhost:8000/ws"},"environment":"development","level":"info","message":"🚀 服务器启动成功","pid":61247,"port":8000,"service":"req-ai-backend","timestamp":"2025-07-06T06:21:15.258Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [06/Jul/2025:06:22:12 +0000] \"GET /health HTTP/1.1\" 200 159 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-07-06T06:22:12.774Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1751782955883-ku9cn2j5d","service":"req-ai-backend","timestamp":"2025-07-06T06:22:35.970Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [06/Jul/2025:06:22:35 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 825 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-07-06T06:22:35.971Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [06/Jul/2025:06:37:26 +0000] \"GET /health HTTP/1.1\" 200 160 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-07-06T06:37:26.922Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [06/Jul/2025:06:42:24 +0000] \"GET / HTTP/1.1\" 200 302 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-07-06T06:42:24.948Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /favicon.ico 不存在","name":"AppError","stack":"AppError: 路径 /favicon.ico 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/server.js:85:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"req-1751784148018-yuvhumhcy","service":"req-ai-backend","timestamp":"2025-07-06T06:42:28.107Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [06/Jul/2025:06:42:28 +0000] \"GET /favicon.ico HTTP/1.1\" 404 169 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-07-06T06:42:28.120Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1751784155475-7nk2fbluv","service":"req-ai-backend","timestamp":"2025-07-06T06:42:35.499Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [06/Jul/2025:06:42:35 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 825 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-07-06T06:42:35.506Z","version":"1.0.0"}
{"activeCount":2,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1751784172307-bfhc0tlg2","service":"req-ai-backend","timestamp":"2025-07-06T06:42:52.516Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [06/Jul/2025:06:42:52 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 827 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-07-06T06:42:52.517Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1751784328244-y1jpflk3v","service":"req-ai-backend","timestamp":"2025-07-06T06:45:28.308Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [06/Jul/2025:06:45:28 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 825 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-07-06T06:45:28.312Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1751784328246-kg1c65b83","service":"req-ai-backend","timestamp":"2025-07-06T06:45:28.316Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [06/Jul/2025:06:45:28 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 825 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-07-06T06:45:28.316Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator创建成功","service":"req-ai-backend","timestamp":"2025-07-07T03:39:00.379Z","version":"1.0.0"}
{"level":"info","message":"健康检查已启动","service":"req-ai-backend","timestamp":"2025-07-07T03:39:00.381Z","version":"1.0.0"}
{"agentId":"req-analyzer","capabilities":["requirement_analysis","text_parsing","structure_analysis"],"level":"info","message":"Agent注册成功","name":"需求分析Agent","service":"req-ai-backend","timestamp":"2025-07-07T03:39:00.381Z","version":"1.0.0"}
{"agentId":"content-optimizer","capabilities":["content_optimization","language_improvement","clarity_enhancement"],"level":"info","message":"Agent注册成功","name":"内容优化Agent","service":"req-ai-backend","timestamp":"2025-07-07T03:39:00.381Z","version":"1.0.0"}
{"agentId":"validation-agent","capabilities":["validation","consistency_check","quality_assurance"],"level":"info","message":"Agent注册成功","name":"验证Agent","service":"req-ai-backend","timestamp":"2025-07-07T03:39:00.382Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator初始化完成","service":"req-ai-backend","timestamp":"2025-07-07T03:39:00.382Z","version":"1.0.0"}
{"level":"info","message":"Agent orchestrator initialized","service":"req-ai-backend","timestamp":"2025-07-07T03:39:00.382Z","version":"1.0.0"}
{"endpoints":{"api":"http://localhost:8000/api","health":"http://localhost:8000/health","websocket":"ws://localhost:8000/ws"},"environment":"development","level":"info","message":"🚀 服务器启动成功","pid":67574,"port":8000,"service":"req-ai-backend","timestamp":"2025-07-07T03:39:00.383Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [07/Jul/2025:03:39:06 +0000] \"GET /health HTTP/1.1\" 200 158 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-07-07T03:39:06.581Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"req-1751859582523-82hjos8my","service":"req-ai-backend","timestamp":"2025-07-07T03:39:42.533Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [07/Jul/2025:03:39:42 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 829 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-07-07T03:39:42.545Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator创建成功","service":"req-ai-backend","timestamp":"2025-07-07T03:43:30.208Z","version":"1.0.0"}
{"level":"info","message":"健康检查已启动","service":"req-ai-backend","timestamp":"2025-07-07T03:43:30.210Z","version":"1.0.0"}
{"agentId":"req-analyzer","capabilities":["requirement_analysis","text_parsing","structure_analysis"],"level":"info","message":"Agent注册成功","name":"需求分析Agent","service":"req-ai-backend","timestamp":"2025-07-07T03:43:30.210Z","version":"1.0.0"}
{"agentId":"content-optimizer","capabilities":["content_optimization","language_improvement","clarity_enhancement"],"level":"info","message":"Agent注册成功","name":"内容优化Agent","service":"req-ai-backend","timestamp":"2025-07-07T03:43:30.210Z","version":"1.0.0"}
{"agentId":"validation-agent","capabilities":["validation","consistency_check","quality_assurance"],"level":"info","message":"Agent注册成功","name":"验证Agent","service":"req-ai-backend","timestamp":"2025-07-07T03:43:30.210Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator初始化完成","service":"req-ai-backend","timestamp":"2025-07-07T03:43:30.211Z","version":"1.0.0"}
{"level":"info","message":"Agent orchestrator initialized","service":"req-ai-backend","timestamp":"2025-07-07T03:43:30.211Z","version":"1.0.0"}
{"endpoints":{"api":"http://localhost:8888/api","health":"http://localhost:8888/health","websocket":"ws://localhost:8888/ws"},"environment":"development","level":"info","message":"🚀 服务器启动成功","pid":76934,"port":8888,"service":"req-ai-backend","timestamp":"2025-07-07T03:43:30.212Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [07/Jul/2025:03:43:39 +0000] \"GET /health HTTP/1.1\" 200 158 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-07-07T03:43:39.529Z","version":"1.0.0"}
{"activeCount":2,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1751859842797-86x1w5e61","service":"req-ai-backend","timestamp":"2025-07-07T03:44:03.555Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [07/Jul/2025:03:44:03 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 827 \"http://localhost:3003/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-07-07T03:44:03.559Z","version":"1.0.0"}
{"activeCount":2,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"req-1751859864062-gabq1osgf","service":"req-ai-backend","timestamp":"2025-07-07T03:44:24.067Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [07/Jul/2025:03:44:24 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 831 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-07-07T03:44:24.069Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1751860864941-xibyt4xdi","service":"req-ai-backend","timestamp":"2025-07-07T04:01:05.286Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [07/Jul/2025:04:01:05 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 825 \"http://localhost:3003/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-07-07T04:01:05.297Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator创建成功","service":"req-ai-backend","timestamp":"2025-07-07T04:04:54.444Z","version":"1.0.0"}
{"level":"info","message":"健康检查已启动","service":"req-ai-backend","timestamp":"2025-07-07T04:04:54.446Z","version":"1.0.0"}
{"agentId":"req-analyzer","capabilities":["requirement_analysis","text_parsing","structure_analysis"],"level":"info","message":"Agent注册成功","name":"需求分析Agent","service":"req-ai-backend","timestamp":"2025-07-07T04:04:54.447Z","version":"1.0.0"}
{"agentId":"content-optimizer","capabilities":["content_optimization","language_improvement","clarity_enhancement"],"level":"info","message":"Agent注册成功","name":"内容优化Agent","service":"req-ai-backend","timestamp":"2025-07-07T04:04:54.447Z","version":"1.0.0"}
{"agentId":"validation-agent","capabilities":["validation","consistency_check","quality_assurance"],"level":"info","message":"Agent注册成功","name":"验证Agent","service":"req-ai-backend","timestamp":"2025-07-07T04:04:54.447Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator初始化完成","service":"req-ai-backend","timestamp":"2025-07-07T04:04:54.447Z","version":"1.0.0"}
{"level":"info","message":"Agent orchestrator initialized","service":"req-ai-backend","timestamp":"2025-07-07T04:04:54.447Z","version":"1.0.0"}
{"endpoints":{"api":"http://localhost:8000/api","health":"http://localhost:8000/health","websocket":"ws://localhost:8000/ws"},"environment":"development","level":"info","message":"🚀 服务器启动成功","pid":17083,"port":8000,"service":"req-ai-backend","timestamp":"2025-07-07T04:04:54.448Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [07/Jul/2025:04:06:09 +0000] \"GET /health HTTP/1.1\" 200 159 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-07-07T04:06:09.418Z","version":"1.0.0"}
{"level":"info","message":"收到 SIGTERM 信号，开始优雅关闭...","service":"req-ai-backend","timestamp":"2025-07-07T04:15:48.920Z","version":"1.0.0"}
{"level":"info","message":"收到 SIGTERM 信号，开始优雅关闭...","service":"req-ai-backend","timestamp":"2025-07-07T04:15:49.080Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator创建成功","service":"req-ai-backend","timestamp":"2025-07-07T04:16:29.247Z","version":"1.0.0"}
{"level":"info","message":"健康检查已启动","service":"req-ai-backend","timestamp":"2025-07-07T04:16:29.249Z","version":"1.0.0"}
{"agentId":"req-analyzer","capabilities":["requirement_analysis","text_parsing","structure_analysis"],"level":"info","message":"Agent注册成功","name":"需求分析Agent","service":"req-ai-backend","timestamp":"2025-07-07T04:16:29.249Z","version":"1.0.0"}
{"agentId":"content-optimizer","capabilities":["content_optimization","language_improvement","clarity_enhancement"],"level":"info","message":"Agent注册成功","name":"内容优化Agent","service":"req-ai-backend","timestamp":"2025-07-07T04:16:29.249Z","version":"1.0.0"}
{"agentId":"validation-agent","capabilities":["validation","consistency_check","quality_assurance"],"level":"info","message":"Agent注册成功","name":"验证Agent","service":"req-ai-backend","timestamp":"2025-07-07T04:16:29.250Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator初始化完成","service":"req-ai-backend","timestamp":"2025-07-07T04:16:29.250Z","version":"1.0.0"}
{"level":"info","message":"Agent orchestrator initialized","service":"req-ai-backend","timestamp":"2025-07-07T04:16:29.251Z","version":"1.0.0"}
{"endpoints":{"api":"http://localhost:8000/api","health":"http://localhost:8000/health","websocket":"ws://localhost:8000/ws"},"environment":"development","level":"info","message":"🚀 服务器启动成功","pid":40265,"port":8000,"service":"req-ai-backend","timestamp":"2025-07-07T04:16:29.252Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [07/Jul/2025:04:17:17 +0000] \"GET /health HTTP/1.1\" 200 159 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-07-07T04:17:17.319Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator创建成功","service":"req-ai-backend","timestamp":"2025-07-07T04:18:46.817Z","version":"1.0.0"}
{"level":"info","message":"健康检查已启动","service":"req-ai-backend","timestamp":"2025-07-07T04:18:46.825Z","version":"1.0.0"}
{"agentId":"req-analyzer","capabilities":["requirement_analysis","text_parsing","structure_analysis"],"level":"info","message":"Agent注册成功","name":"需求分析Agent","service":"req-ai-backend","timestamp":"2025-07-07T04:18:46.827Z","version":"1.0.0"}
{"agentId":"content-optimizer","capabilities":["content_optimization","language_improvement","clarity_enhancement"],"level":"info","message":"Agent注册成功","name":"内容优化Agent","service":"req-ai-backend","timestamp":"2025-07-07T04:18:46.828Z","version":"1.0.0"}
{"agentId":"validation-agent","capabilities":["validation","consistency_check","quality_assurance"],"level":"info","message":"Agent注册成功","name":"验证Agent","service":"req-ai-backend","timestamp":"2025-07-07T04:18:46.828Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator初始化完成","service":"req-ai-backend","timestamp":"2025-07-07T04:18:46.829Z","version":"1.0.0"}
{"level":"info","message":"Agent orchestrator initialized","service":"req-ai-backend","timestamp":"2025-07-07T04:18:46.833Z","version":"1.0.0"}
{"endpoints":{"api":"http://localhost:8000/api","health":"http://localhost:8000/health","websocket":"ws://localhost:8000/ws"},"environment":"development","level":"info","message":"🚀 服务器启动成功","pid":44945,"port":8000,"service":"req-ai-backend","timestamp":"2025-07-07T04:18:46.834Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1751862117692-gycqibg9u","service":"req-ai-backend","timestamp":"2025-07-07T04:21:57.836Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [07/Jul/2025:04:21:57 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 825 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-07-07T04:21:57.844Z","version":"1.0.0"}
{"activeCount":3,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"req-1751862331186-kfer2tzyf","service":"req-ai-backend","timestamp":"2025-07-07T04:25:31.207Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [07/Jul/2025:04:25:31 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 829 \"-\" \"curl/8.7.1\"","service":"req-ai-backend","timestamp":"2025-07-07T04:25:31.213Z","version":"1.0.0"}
{"activeCount":2,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1751862354810-y19p15nsq","service":"req-ai-backend","timestamp":"2025-07-07T04:25:55.255Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [07/Jul/2025:04:25:55 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 827 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-07-07T04:25:55.259Z","version":"1.0.0"}
{"activeCount":2,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1751862507878-jiudfnf42","service":"req-ai-backend","timestamp":"2025-07-07T04:28:28.054Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [07/Jul/2025:04:28:28 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 827 \"http://localhost:3001/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-07-07T04:28:28.063Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator创建成功","service":"req-ai-backend","timestamp":"2025-07-07T05:33:34.710Z","version":"1.0.0"}
{"level":"info","message":"健康检查已启动","service":"req-ai-backend","timestamp":"2025-07-07T05:33:34.739Z","version":"1.0.0"}
{"agentId":"req-analyzer","capabilities":["requirement_analysis","text_parsing","structure_analysis"],"level":"info","message":"Agent注册成功","name":"需求分析Agent","service":"req-ai-backend","timestamp":"2025-07-07T05:33:34.740Z","version":"1.0.0"}
{"agentId":"content-optimizer","capabilities":["content_optimization","language_improvement","clarity_enhancement"],"level":"info","message":"Agent注册成功","name":"内容优化Agent","service":"req-ai-backend","timestamp":"2025-07-07T05:33:34.741Z","version":"1.0.0"}
{"agentId":"validation-agent","capabilities":["validation","consistency_check","quality_assurance"],"level":"info","message":"Agent注册成功","name":"验证Agent","service":"req-ai-backend","timestamp":"2025-07-07T05:33:34.742Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator初始化完成","service":"req-ai-backend","timestamp":"2025-07-07T05:33:34.742Z","version":"1.0.0"}
{"level":"info","message":"Agent orchestrator initialized","service":"req-ai-backend","timestamp":"2025-07-07T05:33:34.742Z","version":"1.0.0"}
{"endpoints":{"api":"http://localhost:8000/api","health":"http://localhost:8000/health","websocket":"ws://localhost:8000/ws"},"environment":"development","level":"info","message":"🚀 服务器启动成功","pid":82247,"port":8000,"service":"req-ai-backend","timestamp":"2025-07-07T05:33:34.745Z","version":"1.0.0"}
{"activeCount":2,"agentCount":3,"level":"info","message":"Agent discovery requested","requestId":"1751867095085-g6c49xmu2","service":"req-ai-backend","timestamp":"2025-07-07T05:44:55.190Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [07/Jul/2025:05:44:55 +0000] \"GET /api/agents/discovery HTTP/1.1\" 200 827 \"http://localhost:3002/\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-07-07T05:44:55.192Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator创建成功","service":"req-ai-backend","timestamp":"2025-07-07T13:49:55.444Z","version":"1.0.0"}
{"level":"info","message":"健康检查已启动","service":"req-ai-backend","timestamp":"2025-07-07T13:49:55.456Z","version":"1.0.0"}
{"agentId":"req-analyzer","capabilities":["requirement_analysis","text_parsing","structure_analysis"],"level":"info","message":"Agent注册成功","name":"需求分析Agent","service":"req-ai-backend","timestamp":"2025-07-07T13:49:55.456Z","version":"1.0.0"}
{"agentId":"content-optimizer","capabilities":["content_optimization","language_improvement","clarity_enhancement"],"level":"info","message":"Agent注册成功","name":"内容优化Agent","service":"req-ai-backend","timestamp":"2025-07-07T13:49:55.456Z","version":"1.0.0"}
{"agentId":"validation-agent","capabilities":["validation","consistency_check","quality_assurance"],"level":"info","message":"Agent注册成功","name":"验证Agent","service":"req-ai-backend","timestamp":"2025-07-07T13:49:55.456Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator初始化完成","service":"req-ai-backend","timestamp":"2025-07-07T13:49:55.457Z","version":"1.0.0"}
{"level":"info","message":"Agent orchestrator initialized","service":"req-ai-backend","timestamp":"2025-07-07T13:49:55.457Z","version":"1.0.0"}
{"endpoints":{"api":"http://localhost:9000/api","health":"http://localhost:9000/health","websocket":"ws://localhost:9000/ws"},"environment":"development","level":"info","message":"🚀 服务器启动成功","pid":3543,"port":9000,"service":"req-ai-backend","timestamp":"2025-07-07T13:49:55.458Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator创建成功","service":"req-ai-backend","timestamp":"2025-07-08T03:05:55.591Z","version":"1.0.0"}
{"level":"info","message":"健康检查已启动","service":"req-ai-backend","timestamp":"2025-07-08T03:05:55.593Z","version":"1.0.0"}
{"agentId":"req-analyzer","capabilities":["requirement_analysis","text_parsing","structure_analysis"],"level":"info","message":"Agent注册成功","name":"需求分析Agent","service":"req-ai-backend","timestamp":"2025-07-08T03:05:55.594Z","version":"1.0.0"}
{"agentId":"content-optimizer","capabilities":["content_optimization","language_improvement","clarity_enhancement"],"level":"info","message":"Agent注册成功","name":"内容优化Agent","service":"req-ai-backend","timestamp":"2025-07-08T03:05:55.594Z","version":"1.0.0"}
{"agentId":"validation-agent","capabilities":["validation","consistency_check","quality_assurance"],"level":"info","message":"Agent注册成功","name":"验证Agent","service":"req-ai-backend","timestamp":"2025-07-08T03:05:55.594Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator初始化完成","service":"req-ai-backend","timestamp":"2025-07-08T03:05:55.594Z","version":"1.0.0"}
{"level":"info","message":"Agent orchestrator initialized","service":"req-ai-backend","timestamp":"2025-07-08T03:05:55.594Z","version":"1.0.0"}
{"endpoints":{"api":"http://localhost:3550/api","health":"http://localhost:3550/health","websocket":"ws://localhost:3550/ws"},"environment":"development","level":"info","message":"🚀 服务器启动成功","pid":21408,"port":"3550","service":"req-ai-backend","timestamp":"2025-07-08T03:05:55.595Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator创建成功","service":"req-ai-backend","timestamp":"2025-07-20T23:30:35.213Z","version":"1.0.0"}
{"level":"info","message":"健康检查已启动","service":"req-ai-backend","timestamp":"2025-07-20T23:30:35.216Z","version":"1.0.0"}
{"agentId":"req-analyzer","capabilities":["requirement_analysis","text_parsing","structure_analysis"],"level":"info","message":"Agent注册成功","name":"需求分析Agent","service":"req-ai-backend","timestamp":"2025-07-20T23:30:35.216Z","version":"1.0.0"}
{"agentId":"content-optimizer","capabilities":["content_optimization","language_improvement","clarity_enhancement"],"level":"info","message":"Agent注册成功","name":"内容优化Agent","service":"req-ai-backend","timestamp":"2025-07-20T23:30:35.217Z","version":"1.0.0"}
{"agentId":"validation-agent","capabilities":["validation","consistency_check","quality_assurance"],"level":"info","message":"Agent注册成功","name":"验证Agent","service":"req-ai-backend","timestamp":"2025-07-20T23:30:35.217Z","version":"1.0.0"}
{"level":"info","message":"AgentOrchestrator初始化完成","service":"req-ai-backend","timestamp":"2025-07-20T23:30:35.217Z","version":"1.0.0"}
{"level":"info","message":"Agent orchestrator initialized","service":"req-ai-backend","timestamp":"2025-07-20T23:30:35.217Z","version":"1.0.0"}
{"endpoints":{"api":"http://localhost:3810/api","health":"http://localhost:3810/health","websocket":"ws://localhost:3810/ws"},"environment":"development","level":"info","message":"🚀 服务器启动成功","pid":71292,"port":"3810","service":"req-ai-backend","timestamp":"2025-07-20T23:30:35.219Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /requirement-analysis 不存在","name":"AppError","stack":"AppError: 路径 /requirement-analysis 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/src/server.js:82:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/requirement-analysis","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"req-1753054274277-shmlplsip","service":"req-ai-backend","timestamp":"2025-07-20T23:31:14.278Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [20/Jul/2025:23:31:14 +0000] \"GET /requirement-analysis HTTP/1.1\" 404 193 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-07-20T23:31:14.283Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /favicon.ico 不存在","name":"AppError","stack":"AppError: 路径 /favicon.ico 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/src/server.js:82:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"req-1753054275081-q6l76guc8","service":"req-ai-backend","timestamp":"2025-07-20T23:31:15.082Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [20/Jul/2025:23:31:15 +0000] \"GET /favicon.ico HTTP/1.1\" 404 184 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-07-20T23:31:15.107Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [20/Jul/2025:23:31:59 +0000] \"GET / HTTP/1.1\" 200 302 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-07-20T23:31:59.746Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /requirement-analysis 不存在","name":"AppError","stack":"AppError: 路径 /requirement-analysis 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/src/server.js:82:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/requirement-analysis","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"req-1753069212456-p1v3ejypu","service":"req-ai-backend","timestamp":"2025-07-21T03:40:12.469Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [21/Jul/2025:03:40:12 +0000] \"GET /requirement-analysis HTTP/1.1\" 404 193 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-07-21T03:40:12.512Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /milkdown-table-test 不存在","name":"AppError","stack":"AppError: 路径 /milkdown-table-test 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/src/server.js:82:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/milkdown-table-test","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"req-1753157358138-u18f35ngu","service":"req-ai-backend","timestamp":"2025-07-22T04:09:18.147Z","version":"1.0.0"}
{"level":"info","message":"::1 - - [22/Jul/2025:04:09:18 +0000] \"GET /milkdown-table-test HTTP/1.1\" 404 192 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"req-ai-backend","timestamp":"2025-07-22T04:09:18.201Z","version":"1.0.0"}
