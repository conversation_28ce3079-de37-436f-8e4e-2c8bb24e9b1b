{"error":{"code":"NOT_FOUND","message":"路径 /api/agents 不存在","name":"AppError","stack":"AppError: 路径 /api/agents 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:646:15\n    at next (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:265:14)\n    at Function.handle (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:47:12)","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/api/agents","userAgent":"curl/8.7.1"},"requestId":"req-1749832231405-8nhgh26a1","service":"req-ai-backend","timestamp":"2025-06-13T16:30:31.406Z","version":"1.0.0"}
{"error":{"message":"orchestrator.waitForTaskCompletion is not a function","name":"TypeError","stack":"TypeError: orchestrator.waitForTaskCompletion is not a function\n    at file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/routes/analysis.js:69:35\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"POST","url":"/api/analysis","userAgent":"curl/8.7.1"},"requestId":"req-1749917307795-lvkdwr4ot","service":"req-ai-backend","timestamp":"2025-06-14T16:08:27.798Z","version":"1.0.0"}
{"error":"没有找到支持 analysis-agent 能力的Agent","level":"error","message":"任务最终失败","service":"req-ai-backend","taskId":"1adbc9bf-05af-40c0-a311-519bb7131512","timestamp":"2025-06-14T16:08:27.800Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /api/v1/agents/discovery 不存在","name":"AppError","stack":"AppError: 路径 /api/v1/agents/discovery 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/server.js:78:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/api/v1/agents/discovery","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"1749917616277-tkije57mm","service":"req-ai-backend","timestamp":"2025-06-14T16:13:36.319Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /api/v1/agents/discovery 不存在","name":"AppError","stack":"AppError: 路径 /api/v1/agents/discovery 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/server.js:78:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/api/v1/agents/discovery","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"1749999943340-vdakx4lng","service":"req-ai-backend","timestamp":"2025-06-15T15:05:43.397Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /api/v1/agents/discovery 不存在","name":"AppError","stack":"AppError: 路径 /api/v1/agents/discovery 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/server.js:78:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/api/v1/agents/discovery","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"1750002188069-6uf2a4i0m","service":"req-ai-backend","timestamp":"2025-06-15T15:43:08.183Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /api/v1/agents/discovery 不存在","name":"AppError","stack":"AppError: 路径 /api/v1/agents/discovery 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/server.js:78:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/api/v1/agents/discovery","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"1750691898797-8svmsh6c0","service":"req-ai-backend","timestamp":"2025-06-23T15:18:18.842Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /api/v1/agents/discovery 不存在","name":"AppError","stack":"AppError: 路径 /api/v1/agents/discovery 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/server.js:78:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/api/v1/agents/discovery","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"1750692431990-us6x0m13y","service":"req-ai-backend","timestamp":"2025-06-23T15:27:12.110Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /favicon.ico 不存在","name":"AppError","stack":"AppError: 路径 /favicon.ico 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/src/server.js:85:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/temp-repos/req-ai-edit-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"req-1751784148018-yuvhumhcy","service":"req-ai-backend","timestamp":"2025-07-06T06:42:28.107Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /requirement-analysis 不存在","name":"AppError","stack":"AppError: 路径 /requirement-analysis 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/src/server.js:82:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/requirement-analysis","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"req-1753054274277-shmlplsip","service":"req-ai-backend","timestamp":"2025-07-20T23:31:14.278Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /favicon.ico 不存在","name":"AppError","stack":"AppError: 路径 /favicon.ico 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/src/server.js:82:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"req-1753054275081-q6l76guc8","service":"req-ai-backend","timestamp":"2025-07-20T23:31:15.082Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /requirement-analysis 不存在","name":"AppError","stack":"AppError: 路径 /requirement-analysis 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/src/server.js:82:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/requirement-analysis","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"req-1753069212456-p1v3ejypu","service":"req-ai-backend","timestamp":"2025-07-21T03:40:12.469Z","version":"1.0.0"}
{"error":{"code":"NOT_FOUND","message":"路径 /milkdown-table-test 不存在","name":"AppError","stack":"AppError: 路径 /milkdown-table-test 不存在\n    at notFoundHandler (file:///Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/src/middleware/errorHandler.js:146:17)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:280:10)\n    at file:///Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/src/server.js:82:3\n    at Layer.handle [as handle_request] (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspace/LLM-Res/team-evolve-designer/intelli-req-assess-feature-dev/Intelli-SD-agent/backend-server/node_modules/express/lib/router/index.js:286:9","statusCode":404},"level":"error","message":"Error occurred","request":{"ip":"::1","method":"GET","url":"/milkdown-table-test","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},"requestId":"req-1753157358138-u18f35ngu","service":"req-ai-backend","timestamp":"2025-07-22T04:09:18.147Z","version":"1.0.0"}
