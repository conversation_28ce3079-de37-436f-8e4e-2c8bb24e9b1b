import winston from 'winston';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 日志级别配置
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
};

// 日志颜色配置
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white'
};

winston.addColors(colors);

// 自定义日志格式
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf((info) => {
    const { timestamp, level, message, ...args } = info;
    
    // 格式化额外的元数据
    const metaStr = Object.keys(args).length ? JSON.stringify(args, null, 2) : '';
    
    return `${timestamp} [${level}]: ${message} ${metaStr}`;
  })
);

// 生产环境日志格式（JSON）
const prodFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// 日志传输器配置
const transports = [
  // 控制台输出
  new winston.transports.Console({
    format: process.env.NODE_ENV === 'production' ? prodFormat : format
  }),
  
  // 错误日志文件
  new winston.transports.File({
    filename: path.join(__dirname, '../../logs/error.log'),
    level: 'error',
    format: prodFormat,
    maxsize: 5242880, // 5MB
    maxFiles: 5
  }),
  
  // 组合日志文件
  new winston.transports.File({
    filename: path.join(__dirname, '../../logs/combined.log'),
    format: prodFormat,
    maxsize: 5242880, // 5MB
    maxFiles: 5
  })
];

// 创建logger实例
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || (process.env.NODE_ENV === 'development' ? 'debug' : 'info'),
  levels,
  format: prodFormat,
  defaultMeta: {
    service: 'req-ai-backend',
    version: process.env.APP_VERSION || '1.0.0'
  },
  transports,
  // 处理异常和Promise拒绝
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(__dirname, '../../logs/exceptions.log'),
      format: prodFormat
    })
  ],
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(__dirname, '../../logs/rejections.log'),
      format: prodFormat
    })
  ]
});

// 添加性能监控
logger.addPerformanceTimer = (label) => {
  const start = Date.now();
  return () => {
    const duration = Date.now() - start;
    logger.info(`Performance: ${label}`, { duration: `${duration}ms` });
    return duration;
  };
};

// 添加请求日志记录器
logger.logRequest = (req, res, duration) => {
  const logData = {
    requestId: req.requestId,
    method: req.method,
    url: req.originalUrl,
    statusCode: res.statusCode,
    duration: `${duration}ms`,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    contentLength: res.get('Content-Length') || 0
  };
  
  if (res.statusCode >= 400) {
    logger.error('Request failed', logData);
  } else if (res.statusCode >= 300) {
    logger.warn('Request redirected', logData);
  } else {
    logger.info('Request completed', logData);
  }
};

// 添加Agent操作日志记录器
logger.logAgentOperation = (operation, agentId, details = {}) => {
  logger.info(`Agent operation: ${operation}`, {
    operation,
    agentId,
    timestamp: Date.now(),
    ...details
  });
};

// 添加任务日志记录器
logger.logTask = (taskId, status, details = {}) => {
  const logLevel = status === 'failed' ? 'error' : 'info';
  logger[logLevel](`Task ${status}`, {
    taskId,
    status,
    timestamp: Date.now(),
    ...details
  });
};

// 添加分析日志记录器
logger.logAnalysis = (analysisId, type, result, details = {}) => {
  logger.info('Analysis completed', {
    analysisId,
    analysisType: type,
    confidence: result.confidence,
    duration: result.duration,
    timestamp: Date.now(),
    ...details
  });
};

// 添加系统状态日志记录器
logger.logSystemStatus = (component, status, metrics = {}) => {
  const logLevel = status === 'healthy' ? 'info' : 'warn';
  logger[logLevel](`System status: ${component}`, {
    component,
    status,
    metrics,
    timestamp: Date.now()
  });
};

// 添加安全事件日志记录器
logger.logSecurityEvent = (event, severity, details = {}) => {
  const logLevel = severity === 'high' ? 'error' : severity === 'medium' ? 'warn' : 'info';
  logger[logLevel](`Security event: ${event}`, {
    event,
    severity,
    timestamp: Date.now(),
    ...details
  });
};

// 创建子logger的工厂函数
logger.createChildLogger = (component) => {
  return {
    error: (message, meta = {}) => logger.error(message, { component, ...meta }),
    warn: (message, meta = {}) => logger.warn(message, { component, ...meta }),
    info: (message, meta = {}) => logger.info(message, { component, ...meta }),
    http: (message, meta = {}) => logger.http(message, { component, ...meta }),
    debug: (message, meta = {}) => logger.debug(message, { component, ...meta })
  };
};

// 确保日志目录存在
import fs from 'fs';
const logDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 监控日志文件大小和轮转
if (process.env.NODE_ENV === 'production') {
  // 设置文件大小监控
  const logRotation = setInterval(() => {
    const logFiles = ['error.log', 'combined.log', 'exceptions.log', 'rejections.log'];
    
    logFiles.forEach(fileName => {
      const filePath = path.join(logDir, fileName);
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        const fileSizeMB = stats.size / (1024 * 1024);
        
        if (fileSizeMB > 10) { // 超过10MB时警告
          logger.warn('Log file size warning', {
            file: fileName,
            size: `${fileSizeMB.toFixed(2)}MB`
          });
        }
      }
    });
  }, 60000); // 每分钟检查一次
  
  // 清理定时器
  process.on('SIGINT', () => clearInterval(logRotation));
  process.on('SIGTERM', () => clearInterval(logRotation));
}

export { logger };
export default logger; 