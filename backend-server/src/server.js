import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import dotenv from 'dotenv';

// 导入路由和中间件
import agentRoutes from './routes/agents.js';
import taskRoutes from './routes/tasks.js';
import analysisRoutes from './routes/analysis.js';
import { errorHandler, notFoundHandler } from './middleware/errorHandler.js';
import { logger } from './utils/logger.js';
import { AgentOrchestrator } from './services/AgentOrchestrator.js';

// 加载环境变量
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3550;

// ============= 中间件配置 =============

// 安全头
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// CORS配置
app.use(cors({
  origin: [
    // 9000-9100端口范围支持
    ...Array.from({length: 101}, (_, i) => `http://localhost:${9000 + i}`),
    process.env.FRONTEND_URL
  ].filter(Boolean), // 过滤掉undefined值
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID', 'X-Timestamp', 'X-Client-Version']
}));

// 请求解析
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
app.use(compression());

// 日志记录
app.use(morgan('combined', {
  stream: {
    write: (message) => logger.info(message.trim())
  }
}));

// 速率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 限制每个IP 1000个请求
  message: {
    error: '请求过于频繁，请稍后再试',
    retryAfter: '15分钟'
  },
  standardHeaders: true,
  legacyHeaders: false,
});
app.use('/api', limiter);

// 请求ID中间件
app.use((req, res, next) => {
  req.requestId = req.headers['x-request-id'] || 
                  `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  res.setHeader('X-Request-ID', req.requestId);
  next();
});

// ============= 初始化服务 =============

// 初始化Agent编排器
const orchestrator = new AgentOrchestrator();
app.set('orchestrator', orchestrator);

// ============= 路由配置 =============

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.APP_VERSION || '1.0.0',
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    agents: {
      total: orchestrator.getAvailableAgents().length,
      active: orchestrator.getAvailableAgents().filter(a => a.status === 'active').length
    }
  });
});

// API路由
app.use('/api/agents', agentRoutes);
app.use('/api/tasks', taskRoutes);
app.use('/api/analysis', analysisRoutes);

// 根路径
app.get('/', (req, res) => {
  res.json({
    name: 'REQ AI Edit Agent Backend',
    version: '1.0.0',
    description: 'AI智能需求编辑助手后端服务 - 前后端分离架构',
    endpoints: {
      health: '/health',
      agents: '/api/agents',
      tasks: '/api/tasks',
      analysis: '/api/analysis',
      websocket: '/ws'
    },
    documentation: '/api/docs',
    status: 'running'
  });
});

// 404处理
app.use(notFoundHandler);

// 错误处理中间件
app.use(errorHandler);

// ============= WebSocket配置 =============

const server = createServer(app);

// WebSocket服务器
const wss = new WebSocketServer({ 
  server,
  path: '/ws'
});

// 简单的WebSocket处理
wss.on('connection', (ws, req) => {
  const clientId = `client-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  logger.info('WebSocket client connected', { clientId, ip: req.socket.remoteAddress });
  
  // 发送欢迎消息
  ws.send(JSON.stringify({
    type: 'welcome',
    clientId,
    timestamp: Date.now(),
    message: '连接成功'
  }));
  
  // 处理消息
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data.toString());
      logger.debug('WebSocket message received', { clientId, message });
      
      // 心跳响应
      if (message.type === 'ping') {
        ws.send(JSON.stringify({
          type: 'pong',
          timestamp: Date.now()
        }));
      }
      
      // 其他消息处理...
      
    } catch (error) {
      logger.error('WebSocket message parsing error', { clientId, error: error.message });
    }
  });
  
  // 连接关闭
  ws.on('close', () => {
    logger.info('WebSocket client disconnected', { clientId });
  });
  
  // 错误处理
  ws.on('error', (error) => {
    logger.error('WebSocket error', { clientId, error: error.message });
  });
});

// ============= 服务器启动 =============

async function startServer() {
  try {
    // 初始化Agent编排器
    await orchestrator.initialize();
    logger.info('Agent orchestrator initialized');
    
    // 启动服务器
    server.listen(PORT, () => {
      logger.info(`🚀 服务器启动成功`, {
        port: PORT,
        environment: process.env.NODE_ENV || 'development',
        pid: process.pid,
        endpoints: {
          api: `http://localhost:${PORT}/api`,
          health: `http://localhost:${PORT}/health`,
          websocket: `ws://localhost:${PORT}/ws`
        }
      });
    });
    
  } catch (error) {
    logger.error('服务器启动失败', { error: error.message });
    process.exit(1);
  }
}

// 优雅关闭
const gracefulShutdown = async (signal) => {
  logger.info(`收到 ${signal} 信号，开始优雅关闭...`);
  
  server.close(async () => {
    try {
      // 关闭WebSocket连接
      wss.clients.forEach((ws) => {
        ws.terminate();
      });
      
      // 关闭Agent编排器
      await orchestrator.shutdown();
      
      logger.info('服务器已优雅关闭');
      process.exit(0);
    } catch (error) {
      logger.error('关闭过程中出错', { error: error.message });
      process.exit(1);
    }
  });
  
  // 强制关闭超时
  setTimeout(() => {
    logger.error('强制关闭服务器');
    process.exit(1);
  }, 10000);
};

// 信号处理
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常', { error: error.message, stack: error.stack });
  gracefulShutdown('uncaughtException');
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝', { reason, promise });
  gracefulShutdown('unhandledRejection');
});

// 启动服务器
startServer();

export default app; 