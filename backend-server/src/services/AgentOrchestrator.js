import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger.js';

/**
 * AI Agent智能编排服务
 * 负责Agent发现、健康监控、任务分配和协作编排
 */
export class AgentOrchestrator extends EventEmitter {
  constructor() {
    super();
    
    // Agent注册表
    this.agents = new Map();
    
    // 任务队列
    this.taskQueue = [];
    this.activeTasks = new Map();
    
    // 配置
    this.config = {
      maxConcurrentTasks: 10,
      taskTimeout: 300000, // 5分钟
      healthCheckInterval: 30000, // 30秒
      maxRetries: 3
    };
    
    // 状态
    this.isInitialized = false;
    this.healthCheckTimer = null;
    
    logger.info('AgentOrchestrator创建成功');
  }

  /**
   * 初始化编排器
   */
  async initialize() {
    try {
      // 启动健康检查
      this.startHealthCheck();
      
      // 注册默认Agent
      await this.registerDefaultAgents();
      
      this.isInitialized = true;
      logger.info('AgentOrchestrator初始化完成');
      
      this.emit('initialized');
    } catch (error) {
      logger.error('AgentOrchestrator初始化失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 注册默认Agent
   */
  async registerDefaultAgents() {
    const defaultAgents = [
      {
        id: 'req-analyzer',
        name: '需求分析Agent',
        type: 'analyzer',
        endpoint: 'http://localhost:8001',
        capabilities: ['requirement_analysis', 'text_parsing', 'structure_analysis'],
        status: 'inactive'
      },
      {
        id: 'content-optimizer',
        name: '内容优化Agent',
        type: 'optimizer',
        endpoint: 'http://localhost:8002',
        capabilities: ['content_optimization', 'language_improvement', 'clarity_enhancement'],
        status: 'inactive'
      },
      {
        id: 'validation-agent',
        name: '验证Agent',
        type: 'validator',
        endpoint: 'http://localhost:8003',
        capabilities: ['validation', 'consistency_check', 'quality_assurance'],
        status: 'inactive'
      }
    ];

    for (const agentConfig of defaultAgents) {
      await this.registerAgent(agentConfig);
    }
  }

  /**
   * 注册Agent
   */
  async registerAgent(agentConfig) {
    const agent = {
      ...agentConfig,
      id: agentConfig.id || uuidv4(),
      registeredAt: new Date(),
      lastHeartbeat: new Date(),
      taskCount: 0,
      successCount: 0,
      failureCount: 0,
      averageResponseTime: 0
    };

    this.agents.set(agent.id, agent);
    
    logger.info('Agent注册成功', { 
      agentId: agent.id, 
      name: agent.name,
      capabilities: agent.capabilities 
    });
    
    this.emit('agentRegistered', agent);
    return agent;
  }

  /**
   * 注销Agent
   */
  async unregisterAgent(agentId) {
    const agent = this.agents.get(agentId);
    if (!agent) {
      throw new Error(`Agent ${agentId} 不存在`);
    }

    this.agents.delete(agentId);
    
    logger.info('Agent注销成功', { agentId, name: agent.name });
    this.emit('agentUnregistered', agent);
  }

  /**
   * 获取可用Agent列表
   */
  getAvailableAgents() {
    return Array.from(this.agents.values());
  }

  /**
   * 获取活跃Agent列表
   */
  getActiveAgents() {
    return Array.from(this.agents.values()).filter(agent => agent.status === 'active');
  }

  /**
   * 根据能力查找Agent
   */
  findAgentsByCapability(capability) {
    return Array.from(this.agents.values()).filter(agent => 
      agent.capabilities.includes(capability) && agent.status === 'active'
    );
  }

  /**
   * 创建任务
   */
  async createTask(taskConfig) {
    const task = {
      id: uuidv4(),
      ...taskConfig,
      status: 'pending',
      createdAt: new Date(),
      retryCount: 0,
      results: {},
      errors: []
    };

    this.taskQueue.push(task);
    
    logger.info('任务创建成功', { 
      taskId: task.id, 
      type: task.type,
      priority: task.priority 
    });
    
    this.emit('taskCreated', task);
    
    // 尝试执行任务
    this.processTaskQueue();
    
    return task;
  }

  /**
   * 处理任务队列
   */
  async processTaskQueue() {
    if (this.activeTasks.size >= this.config.maxConcurrentTasks) {
      return;
    }

    // 按优先级排序
    this.taskQueue.sort((a, b) => (b.priority || 0) - (a.priority || 0));

    const task = this.taskQueue.shift();
    if (!task) {
      return;
    }

    try {
      await this.executeTask(task);
    } catch (error) {
      logger.error('任务执行失败', { 
        taskId: task.id, 
        error: error.message 
      });
    }
  }

  /**
   * 执行任务
   */
  async executeTask(task) {
    this.activeTasks.set(task.id, task);
    task.status = 'running';
    task.startedAt = new Date();
    
    logger.info('任务开始执行', { taskId: task.id });
    this.emit('taskStarted', task);

    try {
      let result;
      
      if (task.mode === 'collaboration') {
        result = await this.executeCollaborativeTask(task);
      } else {
        result = await this.executeSingleAgentTask(task);
      }
      
      task.status = 'completed';
      task.completedAt = new Date();
      task.results = result;
      
      logger.info('任务执行完成', { 
        taskId: task.id,
        duration: task.completedAt - task.startedAt 
      });
      
      this.emit('taskCompleted', task);
      
    } catch (error) {
      await this.handleTaskError(task, error);
    } finally {
      this.activeTasks.delete(task.id);
      // 继续处理队列
      this.processTaskQueue();
    }
  }

  /**
   * 执行单Agent任务
   */
  async executeSingleAgentTask(task) {
    const requiredCapability = task.requiredCapability || task.type;
    const agents = this.findAgentsByCapability(requiredCapability);
    
    if (agents.length === 0) {
      throw new Error(`没有找到支持 ${requiredCapability} 能力的Agent`);
    }

    // 选择最佳Agent（简单负载均衡）
    const agent = agents.reduce((best, current) => 
      current.taskCount < best.taskCount ? current : best
    );

    return await this.sendTaskToAgent(agent, task);
  }

  /**
   * 执行协作任务
   */
  async executeCollaborativeTask(task) {
    const { agents: agentIds, executionMode = 'sequential' } = task;
    const results = {};
    
    if (executionMode === 'parallel') {
      // 并行执行
      const promises = agentIds.map(async (agentId) => {
        const agent = this.agents.get(agentId);
        if (!agent) throw new Error(`Agent ${agentId} 不存在`);
        
        const result = await this.sendTaskToAgent(agent, task);
        return { agentId, result };
      });
      
      const parallelResults = await Promise.all(promises);
      parallelResults.forEach(({ agentId, result }) => {
        results[agentId] = result;
      });
      
    } else {
      // 串行执行
      for (const agentId of agentIds) {
        const agent = this.agents.get(agentId);
        if (!agent) throw new Error(`Agent ${agentId} 不存在`);
        
        // 将前一个Agent的结果作为输入
        const taskWithContext = {
          ...task,
          previousResults: results
        };
        
        results[agentId] = await this.sendTaskToAgent(agent, taskWithContext);
      }
    }
    
    return results;
  }

  /**
   * 向Agent发送任务
   */
  async sendTaskToAgent(agent, task) {
    const startTime = Date.now();
    
    try {
      agent.taskCount++;
      
      // 模拟发送任务到Agent（实际应该是HTTP请求）
      logger.info('向Agent发送任务', { 
        agentId: agent.id, 
        taskId: task.id 
      });
      
      // 模拟处理时间
      await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));
      
      const mockResult = {
        success: true,
        data: `${agent.name}处理结果`,
        metadata: {
          agentId: agent.id,
          taskId: task.id,
          processedAt: new Date()
        }
      };
      
      agent.successCount++;
      const responseTime = Date.now() - startTime;
      agent.averageResponseTime = (agent.averageResponseTime + responseTime) / 2;
      
      return mockResult;
      
    } catch (error) {
      agent.failureCount++;
      throw error;
    }
  }

  /**
   * 处理任务错误
   */
  async handleTaskError(task, error) {
    task.errors.push({
      message: error.message,
      timestamp: new Date(),
      retryCount: task.retryCount
    });

    if (task.retryCount < this.config.maxRetries) {
      task.retryCount++;
      task.status = 'pending';
      this.taskQueue.unshift(task); // 重新加入队列头部
      
      logger.warn('任务重试', { 
        taskId: task.id, 
        retryCount: task.retryCount 
      });
      
    } else {
      task.status = 'failed';
      task.failedAt = new Date();
      
      logger.error('任务最终失败', { 
        taskId: task.id, 
        error: error.message 
      });
      
      this.emit('taskFailed', task);
    }
  }

  /**
   * 获取任务状态
   */
  getTaskStatus(taskId) {
    // 查找活跃任务
    const activeTask = this.activeTasks.get(taskId);
    if (activeTask) return activeTask;
    
    // 查找队列中的任务
    return this.taskQueue.find(task => task.id === taskId);
  }

  /**
   * 等待任务完成
   * @param {string} taskId - 任务ID
   * @param {number} timeout - 超时时间（毫秒）
   * @returns {Promise} 任务结果
   */
  async waitForTaskCompletion(taskId, timeout = 30000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      // 检查任务是否已经完成
      const task = this.getTaskStatus(taskId);
      if (!task) {
        return reject(new Error(`任务 ${taskId} 不存在`));
      }
      
      if (task.status === 'completed') {
        return resolve(task.results);
      }
      
      if (task.status === 'failed') {
        return reject(new Error(`任务 ${taskId} 执行失败: ${task.errors[task.errors.length - 1]?.message}`));
      }
      
      // 设置超时
      const timeoutId = setTimeout(() => {
        this.off('taskCompleted', taskCompletedHandler);
        this.off('taskFailed', taskFailedHandler);
        reject(new Error(`任务 ${taskId} 执行超时`));
      }, timeout);
      
      // 监听任务完成事件
      const taskCompletedHandler = (completedTask) => {
        if (completedTask.id === taskId) {
          clearTimeout(timeoutId);
          this.off('taskCompleted', taskCompletedHandler);
          this.off('taskFailed', taskFailedHandler);
          resolve(completedTask.results);
        }
      };
      
      // 监听任务失败事件
      const taskFailedHandler = (failedTask) => {
        if (failedTask.id === taskId) {
          clearTimeout(timeoutId);
          this.off('taskCompleted', taskCompletedHandler);
          this.off('taskFailed', taskFailedHandler);
          reject(new Error(`任务 ${taskId} 执行失败: ${failedTask.errors[failedTask.errors.length - 1]?.message}`));
        }
      };
      
      // 注册事件监听器
      this.on('taskCompleted', taskCompletedHandler);
      this.on('taskFailed', taskFailedHandler);
      
      // 定期检查任务状态（防止事件丢失）
      const checkInterval = setInterval(() => {
        const currentTask = this.getTaskStatus(taskId);
        if (!currentTask) {
          clearInterval(checkInterval);
          clearTimeout(timeoutId);
          this.off('taskCompleted', taskCompletedHandler);
          this.off('taskFailed', taskFailedHandler);
          reject(new Error(`任务 ${taskId} 不存在`));
          return;
        }
        
        if (currentTask.status === 'completed') {
          clearInterval(checkInterval);
          clearTimeout(timeoutId);
          this.off('taskCompleted', taskCompletedHandler);
          this.off('taskFailed', taskFailedHandler);
          resolve(currentTask.results);
        } else if (currentTask.status === 'failed') {
          clearInterval(checkInterval);
          clearTimeout(timeoutId);
          this.off('taskCompleted', taskCompletedHandler);
          this.off('taskFailed', taskFailedHandler);
          reject(new Error(`任务 ${taskId} 执行失败: ${currentTask.errors[currentTask.errors.length - 1]?.message}`));
        }
        
        // 检查是否超时
        if (Date.now() - startTime > timeout) {
          clearInterval(checkInterval);
          clearTimeout(timeoutId);
          this.off('taskCompleted', taskCompletedHandler);
          this.off('taskFailed', taskFailedHandler);
          reject(new Error(`任务 ${taskId} 执行超时`));
        }
      }, 1000); // 每秒检查一次
    });
  }

  /**
   * 取消任务
   */
  async cancelTask(taskId) {
    // 从队列中移除
    const queueIndex = this.taskQueue.findIndex(task => task.id === taskId);
    if (queueIndex !== -1) {
      const task = this.taskQueue.splice(queueIndex, 1)[0];
      task.status = 'cancelled';
      task.cancelledAt = new Date();
      
      logger.info('任务已取消', { taskId });
      this.emit('taskCancelled', task);
      return task;
    }
    
    // 取消活跃任务（实际实现中需要通知Agent）
    const activeTask = this.activeTasks.get(taskId);
    if (activeTask) {
      activeTask.status = 'cancelled';
      activeTask.cancelledAt = new Date();
      this.activeTasks.delete(taskId);
      
      logger.info('活跃任务已取消', { taskId });
      this.emit('taskCancelled', activeTask);
      return activeTask;
    }
    
    throw new Error(`任务 ${taskId} 不存在`);
  }

  /**
   * 启动健康检查
   */
  startHealthCheck() {
    this.healthCheckTimer = setInterval(async () => {
      await this.performHealthCheck();
    }, this.config.healthCheckInterval);
    
    logger.info('健康检查已启动');
  }

  /**
   * 执行健康检查
   */
  async performHealthCheck() {
    const now = new Date();
    
    for (const [agentId, agent] of this.agents) {
      try {
        // 模拟健康检查（实际应该是HTTP请求）
        const isHealthy = Math.random() > 0.1; // 90%概率健康
        
        if (isHealthy) {
          agent.status = 'active';
          agent.lastHeartbeat = now;
        } else {
          agent.status = 'inactive';
        }
        
      } catch (error) {
        agent.status = 'error';
        logger.warn('Agent健康检查失败', { 
          agentId, 
          error: error.message 
        });
      }
    }
  }

  /**
   * 获取统计信息
   */
  getStatistics() {
    const agents = this.getAvailableAgents();
    const activeTasks = Array.from(this.activeTasks.values());
    
    return {
      agents: {
        total: agents.length,
        active: agents.filter(a => a.status === 'active').length,
        inactive: agents.filter(a => a.status === 'inactive').length,
        error: agents.filter(a => a.status === 'error').length
      },
      tasks: {
        queue: this.taskQueue.length,
        active: activeTasks.length,
        completed: agents.reduce((sum, a) => sum + a.successCount, 0),
        failed: agents.reduce((sum, a) => sum + a.failureCount, 0)
      },
      performance: {
        averageResponseTime: agents.length > 0 
          ? agents.reduce((sum, a) => sum + a.averageResponseTime, 0) / agents.length 
          : 0,
        successRate: this.calculateSuccessRate()
      }
    };
  }

  /**
   * 计算成功率
   */
  calculateSuccessRate() {
    const agents = this.getAvailableAgents();
    const totalTasks = agents.reduce((sum, a) => sum + a.taskCount, 0);
    const successfulTasks = agents.reduce((sum, a) => sum + a.successCount, 0);
    
    return totalTasks > 0 ? (successfulTasks / totalTasks * 100) : 0;
  }

  /**
   * 关闭编排器
   */
  async shutdown() {
    try {
      // 停止健康检查
      if (this.healthCheckTimer) {
        clearInterval(this.healthCheckTimer);
        this.healthCheckTimer = null;
      }
      
      // 等待活跃任务完成
      while (this.activeTasks.size > 0) {
        logger.info('等待任务完成...', { activeTaskCount: this.activeTasks.size });
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      // 清理资源
      this.agents.clear();
      this.taskQueue.length = 0;
      this.activeTasks.clear();
      
      this.isInitialized = false;
      
      logger.info('AgentOrchestrator已关闭');
      this.emit('shutdown');
      
    } catch (error) {
      logger.error('AgentOrchestrator关闭失败', { error: error.message });
      throw error;
    }
  }
}

export default AgentOrchestrator; 