import express from 'express';
import { validateTaskCreation } from '../middleware/validation.js';
import { logger } from '../utils/logger.js';

const router = express.Router();

// 创建新任务
router.post('/', validateTaskCreation, async (req, res, next) => {
  try {
    const orchestrator = req.app.get('orchestrator');
    const { type, input, context, priority = 1, timeout = 30000 } = req.body;
    
    const taskConfig = {
      type,
      input,
      context: {
        ...context,
        requestId: req.requestId,
        clientIP: req.ip,
        userAgent: req.get('User-Agent')
      },
      priority,
      timeout
    };
    
    const task = await orchestrator.createTask(taskConfig);
    
    res.status(201).json({
      success: true,
      data: {
        taskId: task.id,
        status: task.status,
        type: task.type,
        createdAt: task.createdAt,
        estimatedDuration: task.estimatedDuration,
        assignedAgent: task.assignedAgent
      },
      requestId: req.requestId
    });
    
    logger.info('Task created successfully', {
      requestId: req.requestId,
      taskId: task.id,
      taskType: type,
      priority,
      assignedAgent: task.assignedAgent?.id
    });
    
  } catch (error) {
    next(error);
  }
});

// 获取任务状态
router.get('/:taskId', async (req, res, next) => {
  try {
    const orchestrator = req.app.get('orchestrator');
    const { taskId } = req.params;
    
    const task = orchestrator.getTask(taskId);
    if (!task) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'TASK_NOT_FOUND',
          message: `任务 ${taskId} 不存在`
        },
        requestId: req.requestId
      });
    }
    
    res.json({
      success: true,
      data: {
        taskId: task.id,
        status: task.status,
        type: task.type,
        input: task.input,
        result: task.result,
        error: task.error,
        progress: task.progress,
        createdAt: task.createdAt,
        startedAt: task.startedAt,
        completedAt: task.completedAt,
        duration: task.duration,
        assignedAgent: task.assignedAgent ? {
          id: task.assignedAgent.id,
          type: task.assignedAgent.type,
          endpoint: task.assignedAgent.endpoint
        } : null
      },
      requestId: req.requestId
    });
    
  } catch (error) {
    next(error);
  }
});

// 取消任务
router.delete('/:taskId', async (req, res, next) => {
  try {
    const orchestrator = req.app.get('orchestrator');
    const { taskId } = req.params;
    
    const result = await orchestrator.cancelTask(taskId);
    
    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: {
          code: result.code || 'TASK_CANCEL_FAILED',
          message: result.message || `无法取消任务 ${taskId}`
        },
        requestId: req.requestId
      });
    }
    
    res.json({
      success: true,
      data: {
        taskId,
        message: '任务已成功取消',
        cancelledAt: result.cancelledAt
      },
      requestId: req.requestId
    });
    
    logger.info('Task cancelled', {
      requestId: req.requestId,
      taskId,
      cancelledAt: result.cancelledAt
    });
    
  } catch (error) {
    next(error);
  }
});

// 获取任务列表
router.get('/', async (req, res, next) => {
  try {
    const orchestrator = req.app.get('orchestrator');
    const { 
      status, 
      type, 
      limit = 50, 
      offset = 0,
      sort = 'createdAt',
      order = 'desc'
    } = req.query;
    
    const filters = {};
    if (status) filters.status = status;
    if (type) filters.type = type;
    
    const tasks = orchestrator.getTasks({
      filters,
      limit: parseInt(limit),
      offset: parseInt(offset),
      sort,
      order
    });
    
    res.json({
      success: true,
      data: {
        tasks: tasks.items.map(task => ({
          taskId: task.id,
          status: task.status,
          type: task.type,
          createdAt: task.createdAt,
          duration: task.duration,
          assignedAgent: task.assignedAgent?.id
        })),
        pagination: {
          total: tasks.total,
          limit: parseInt(limit),
          offset: parseInt(offset),
          hasMore: tasks.hasMore
        }
      },
      requestId: req.requestId
    });
    
  } catch (error) {
    next(error);
  }
});

// 重试失败任务
router.post('/:taskId/retry', async (req, res, next) => {
  try {
    const orchestrator = req.app.get('orchestrator');
    const { taskId } = req.params;
    
    const result = await orchestrator.retryTask(taskId);
    
    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: result.code || 'TASK_RETRY_FAILED',
          message: result.message || `无法重试任务 ${taskId}`
        },
        requestId: req.requestId
      });
    }
    
    res.json({
      success: true,
      data: {
        taskId,
        newTaskId: result.newTaskId,
        message: '任务重试已启动',
        retriedAt: result.retriedAt
      },
      requestId: req.requestId
    });
    
    logger.info('Task retried', {
      requestId: req.requestId,
      originalTaskId: taskId,
      newTaskId: result.newTaskId,
      retriedAt: result.retriedAt
    });
    
  } catch (error) {
    next(error);
  }
});

// 批量操作任务
router.post('/batch', async (req, res, next) => {
  try {
    const orchestrator = req.app.get('orchestrator');
    const { action, taskIds } = req.body;
    
    if (!action || !Array.isArray(taskIds) || taskIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_BATCH_REQUEST',
          message: '批量操作需要指定action和taskIds'
        },
        requestId: req.requestId
      });
    }
    
    const results = await orchestrator.batchOperation(action, taskIds);
    
    res.json({
      success: true,
      data: {
        action,
        taskIds,
        results: results.map(result => ({
          taskId: result.taskId,
          success: result.success,
          message: result.message,
          error: result.error
        })),
        summary: {
          total: taskIds.length,
          successful: results.filter(r => r.success).length,
          failed: results.filter(r => !r.success).length
        }
      },
      requestId: req.requestId
    });
    
    logger.info('Batch operation completed', {
      requestId: req.requestId,
      action,
      taskCount: taskIds.length,
      successCount: results.filter(r => r.success).length
    });
    
  } catch (error) {
    next(error);
  }
});

export default router; 