import express from 'express';
import { validateAnalysisRequest } from '../middleware/validation.js';
import { logger } from '../utils/logger.js';

const router = express.Router();

// 执行AI分析
router.post('/', validateAnalysisRequest, async (req, res, next) => {
  try {
    const orchestrator = req.app.get('orchestrator');
    const { 
      selectedText, 
      analysisType, 
      context = {},
      multiAgent = false,
      priority = 1 
    } = req.body;
    
    let result;
    
    if (multiAgent) {
      // 多Agent协同分析
      const tasks = [
        {
          type: 'analysis-agent',
          input: { selectedText },
          context: { analysisType: 'evaluate', ...context }
        },
        {
          type: 'knowledge-agent',
          input: { selectedText },
          context: { analysisType: 'supplement', ...context }
        },
        {
          type: 'cases-agent',
          input: { selectedText },
          context: { analysisType: 'examples', ...context }
        }
      ];
      
      result = await orchestrator.executeCollaboration({
        tasks,
        mode: 'parallel',
        context: {
          ...context,
          requestId: req.requestId,
          analysisType: 'multi-agent'
        }
      });
      
    } else {
      // 单Agent分析
      const agentType = getAgentTypeByAnalysis(analysisType);
      
      const taskConfig = {
        type: agentType,
        input: { selectedText },
        context: {
          analysisType,
          ...context,
          requestId: req.requestId
        },
        priority
      };
      
      const task = await orchestrator.createTask(taskConfig);
      
      // 等待任务完成
      result = await orchestrator.waitForTaskCompletion(task.id, 45000);
    }
    
    res.json({
      success: true,
      data: {
        analysisId: result.id,
        type: multiAgent ? 'multi-agent' : analysisType,
        selectedText,
        result: result.result || result.results,
        confidence: result.confidence,
        duration: result.duration,
        agentInfo: result.agentInfo,
        metadata: {
          multiAgent,
          analysisType,
          createdAt: result.createdAt || Date.now()
        }
      },
      requestId: req.requestId
    });
    
    logger.info('Analysis completed', {
      requestId: req.requestId,
      analysisId: result.id,
      analysisType,
      multiAgent,
      duration: result.duration,
      textLength: selectedText.length
    });
    
  } catch (error) {
    next(error);
  }
});

// 获取分析历史
router.get('/history', async (req, res, next) => {
  try {
    const orchestrator = req.app.get('orchestrator');
    const { 
      limit = 20, 
      offset = 0,
      type,
      dateFrom,
      dateTo,
      sort = 'createdAt',
      order = 'desc'
    } = req.query;
    
    const filters = {};
    if (type) filters.analysisType = type;
    if (dateFrom) filters.dateFrom = new Date(dateFrom);
    if (dateTo) filters.dateTo = new Date(dateTo);
    
    const history = await orchestrator.getAnalysisHistory({
      filters,
      limit: parseInt(limit),
      offset: parseInt(offset),
      sort,
      order
    });
    
    res.json({
      success: true,
      data: {
        analyses: history.items.map(analysis => ({
          analysisId: analysis.id,
          type: analysis.type,
          selectedText: analysis.selectedText.substring(0, 100) + '...',
          createdAt: analysis.createdAt,
          duration: analysis.duration,
          confidence: analysis.confidence,
          status: analysis.status
        })),
        pagination: {
          total: history.total,
          limit: parseInt(limit),
          offset: parseInt(offset),
          hasMore: history.hasMore
        },
        statistics: {
          totalAnalyses: history.total,
          averageDuration: history.averageDuration,
          averageConfidence: history.averageConfidence,
          typeDistribution: history.typeDistribution
        }
      },
      requestId: req.requestId
    });
    
  } catch (error) {
    next(error);
  }
});

// 获取单个分析详情
router.get('/:analysisId', async (req, res, next) => {
  try {
    const orchestrator = req.app.get('orchestrator');
    const { analysisId } = req.params;
    
    const analysis = await orchestrator.getAnalysis(analysisId);
    
    if (!analysis) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'ANALYSIS_NOT_FOUND',
          message: `分析 ${analysisId} 不存在`
        },
        requestId: req.requestId
      });
    }
    
    res.json({
      success: true,
      data: {
        analysisId: analysis.id,
        type: analysis.type,
        selectedText: analysis.selectedText,
        result: analysis.result,
        confidence: analysis.confidence,
        duration: analysis.duration,
        createdAt: analysis.createdAt,
        completedAt: analysis.completedAt,
        agentInfo: analysis.agentInfo,
        metadata: analysis.metadata,
        context: analysis.context
      },
      requestId: req.requestId
    });
    
  } catch (error) {
    next(error);
  }
});

// 重新执行分析
router.post('/:analysisId/rerun', async (req, res, next) => {
  try {
    const orchestrator = req.app.get('orchestrator');
    const { analysisId } = req.params;
    const { context = {} } = req.body;
    
    const originalAnalysis = await orchestrator.getAnalysis(analysisId);
    
    if (!originalAnalysis) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'ANALYSIS_NOT_FOUND',
          message: `原始分析 ${analysisId} 不存在`
        },
        requestId: req.requestId
      });
    }
    
    const newTaskConfig = {
      type: originalAnalysis.agentInfo.type,
      input: { selectedText: originalAnalysis.selectedText },
      context: {
        ...originalAnalysis.context,
        ...context,
        rerunOf: analysisId,
        requestId: req.requestId
      }
    };
    
    const task = await orchestrator.createTask(newTaskConfig);
    const result = await orchestrator.waitForTaskCompletion(task.id, 45000);
    
    res.json({
      success: true,
      data: {
        newAnalysisId: result.id,
        originalAnalysisId: analysisId,
        result: result.result,
        confidence: result.confidence,
        duration: result.duration,
        improvements: await orchestrator.compareAnalyses(analysisId, result.id)
      },
      requestId: req.requestId
    });
    
    logger.info('Analysis rerun completed', {
      requestId: req.requestId,
      originalAnalysisId: analysisId,
      newAnalysisId: result.id,
      duration: result.duration
    });
    
  } catch (error) {
    next(error);
  }
});

// 分析质量反馈
router.post('/:analysisId/feedback', async (req, res, next) => {
  try {
    const orchestrator = req.app.get('orchestrator');
    const { analysisId } = req.params;
    const { rating, feedback, tags = [] } = req.body;
    
    if (!rating || rating < 1 || rating > 5) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_RATING',
          message: '评分必须在1-5之间'
        },
        requestId: req.requestId
      });
    }
    
    const result = await orchestrator.submitAnalysisFeedback(analysisId, {
      rating,
      feedback,
      tags,
      submittedAt: Date.now(),
      requestId: req.requestId
    });
    
    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'ANALYSIS_NOT_FOUND',
          message: `分析 ${analysisId} 不存在`
        },
        requestId: req.requestId
      });
    }
    
    res.json({
      success: true,
      data: {
        analysisId,
        message: '反馈提交成功',
        feedbackId: result.feedbackId,
        submittedAt: result.submittedAt
      },
      requestId: req.requestId
    });
    
    logger.info('Analysis feedback submitted', {
      requestId: req.requestId,
      analysisId,
      rating,
      feedbackId: result.feedbackId
    });
    
  } catch (error) {
    next(error);
  }
});

// 分析统计报告
router.get('/stats/report', async (req, res, next) => {
  try {
    const orchestrator = req.app.get('orchestrator');
    const { 
      period = '7d', // 7d, 30d, 90d
      agentType 
    } = req.query;
    
    const stats = await orchestrator.getAnalysisStatistics({
      period,
      agentType,
      includeComparisons: true
    });
    
    res.json({
      success: true,
      data: {
        period,
        agentType,
        summary: {
          totalAnalyses: stats.totalAnalyses,
          averageDuration: stats.averageDuration,
          averageConfidence: stats.averageConfidence,
          averageRating: stats.averageRating,
          successRate: stats.successRate
        },
        trends: {
          daily: stats.dailyTrends,
          hourly: stats.hourlyTrends
        },
        distribution: {
          byType: stats.typeDistribution,
          byAgent: stats.agentDistribution,
          byConfidence: stats.confidenceDistribution
        },
        performance: {
          fastestAnalyses: stats.fastestAnalyses,
          slowestAnalyses: stats.slowestAnalyses,
          highestRated: stats.highestRated,
          mostImproved: stats.mostImproved
        }
      },
      requestId: req.requestId
    });
    
  } catch (error) {
    next(error);
  }
});

// 工具函数：根据分析类型获取Agent类型
function getAgentTypeByAnalysis(analysisType) {
  const mapping = {
    'evaluate': 'analysis-agent',
    'inspect': 'analysis-agent',
    'supplement': 'knowledge-agent',
    'reference': 'knowledge-agent',
    'examples': 'cases-agent',
    'cases': 'cases-agent',
    'inspire': 'inspiration-agent',
    'creative': 'inspiration-agent'
  };
  return mapping[analysisType] || 'analysis-agent';
}

export default router; 