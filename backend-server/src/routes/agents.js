import express from 'express';
import { AgentOrchestrator } from '../services/AgentOrchestrator.js';
import { validateAgentRegistration, validateTaskExecution } from '../middleware/validation.js';
import { logger } from '../utils/logger.js';

const router = express.Router();

// Agent发现端点
router.get('/discovery', async (req, res, next) => {
  try {
    const orchestrator = req.app.get('orchestrator');
    const agents = orchestrator.getAvailableAgents();
    
    res.json({
      success: true,
      data: {
        agents: agents.map(agent => ({
          id: agent.id,
          type: agent.type,
          endpoint: agent.endpoint,
          capabilities: agent.capabilities,
          status: agent.status,
          metadata: agent.metadata,
          lastHeartbeat: agent.lastHeartbeat,
          taskCount: agent.taskCount,
          successRate: agent.successRate
        })),
        timestamp: Date.now(),
        totalAgents: agents.length,
        activeAgents: agents.filter(a => a.status === 'active').length
      },
      requestId: req.requestId
    });
    
    logger.info('Agent discovery requested', {
      requestId: req.requestId,
      agentCount: agents.length,
      activeCount: agents.filter(a => a.status === 'active').length
    });
    
  } catch (error) {
    next(error);
  }
});

// Agent注册端点
router.post('/register', validateAgentRegistration, async (req, res, next) => {
  try {
    const orchestrator = req.app.get('orchestrator');
    const { id, type, endpoint, capabilities, metadata } = req.body;
    
    const agentInfo = {
      id,
      type,
      endpoint,
      capabilities: capabilities || [],
      metadata: metadata || {}
    };
    
    const registeredAgent = await orchestrator.registerAgent(agentInfo);
    
    res.status(201).json({
      success: true,
      data: {
        agent: registeredAgent,
        message: 'Agent注册成功'
      },
      requestId: req.requestId
    });
    
    logger.info('Agent registered successfully', {
      requestId: req.requestId,
      agentId: id,
      agentType: type,
      endpoint
    });
    
  } catch (error) {
    next(error);
  }
});

// Agent健康检查
router.get('/:agentId/health', async (req, res, next) => {
  try {
    const orchestrator = req.app.get('orchestrator');
    const { agentId } = req.params;
    
    const agent = orchestrator.getAgent(agentId);
    if (!agent) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'AGENT_NOT_FOUND',
          message: `Agent ${agentId} 不存在`
        },
        requestId: req.requestId
      });
    }
    
    const healthInfo = await orchestrator.checkAgentHealth(agentId);
    
    res.json({
      success: true,
      data: {
        agentId,
        status: healthInfo.status,
        lastHeartbeat: healthInfo.lastHeartbeat,
        responseTime: healthInfo.responseTime,
        uptime: healthInfo.uptime,
        taskCount: agent.taskCount,
        successRate: agent.successRate
      },
      requestId: req.requestId
    });
    
  } catch (error) {
    next(error);
  }
});

// 多Agent协同执行
router.post('/collaborate', validateTaskExecution, async (req, res, next) => {
  try {
    const orchestrator = req.app.get('orchestrator');
    const { tasks, mode = 'parallel', context = {} } = req.body;
    
    const collaborationResult = await orchestrator.executeCollaboration({
      tasks,
      mode, // 'parallel' | 'sequential' 
      context,
      requestId: req.requestId
    });
    
    res.json({
      success: true,
      data: {
        collaborationId: collaborationResult.id,
        mode,
        tasks: collaborationResult.tasks,
        results: collaborationResult.results,
        summary: collaborationResult.summary,
        startTime: collaborationResult.startTime,
        endTime: collaborationResult.endTime,
        duration: collaborationResult.duration
      },
      requestId: req.requestId
    });
    
    logger.info('Multi-agent collaboration completed', {
      requestId: req.requestId,
      collaborationId: collaborationResult.id,
      taskCount: tasks.length,
      mode,
      duration: collaborationResult.duration
    });
    
  } catch (error) {
    next(error);
  }
});

// Agent状态统计
router.get('/stats', async (req, res, next) => {
  try {
    const orchestrator = req.app.get('orchestrator');
    const stats = orchestrator.getSystemStats();
    
    res.json({
      success: true,
      data: {
        agents: {
          total: stats.totalAgents,
          active: stats.activeAgents,
          inactive: stats.inactiveAgents,
          byType: stats.agentsByType
        },
        tasks: {
          total: stats.totalTasks,
          pending: stats.pendingTasks,
          running: stats.runningTasks,
          completed: stats.completedTasks,
          failed: stats.failedTasks
        },
        performance: {
          averageResponseTime: stats.averageResponseTime,
          systemLoad: stats.systemLoad,
          uptime: process.uptime()
        }
      },
      requestId: req.requestId
    });
    
  } catch (error) {
    next(error);
  }
});

// Agent取消注册
router.delete('/:agentId', async (req, res, next) => {
  try {
    const orchestrator = req.app.get('orchestrator');
    const { agentId } = req.params;
    
    const result = await orchestrator.unregisterAgent(agentId);
    
    if (!result.success) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'AGENT_NOT_FOUND',
          message: `Agent ${agentId} 不存在或已注销`
        },
        requestId: req.requestId
      });
    }
    
    res.json({
      success: true,
      data: {
        message: `Agent ${agentId} 已成功注销`,
        agentId
      },
      requestId: req.requestId
    });
    
    logger.info('Agent unregistered', {
      requestId: req.requestId,
      agentId
    });
    
  } catch (error) {
    next(error);
  }
});

export default router; 