import { logger } from '../utils/logger.js';

// 应用错误类
export class AppError extends Error {
  constructor(message, statusCode = 500, code = 'INTERNAL_ERROR', details = null) {
    super(message);
    this.name = 'AppError';
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

// Agent相关错误
export class AgentError extends AppError {
  constructor(message, code = 'AGENT_ERROR', statusCode = 400) {
    super(message, statusCode, code);
    this.name = 'AgentError';
  }
}

// 任务相关错误
export class TaskError extends AppError {
  constructor(message, code = 'TASK_ERROR', statusCode = 400) {
    super(message, statusCode, code);
    this.name = 'TaskError';
  }
}

// 验证错误
export class ValidationError extends AppError {
  constructor(message, field = null, value = null) {
    super(message, 400, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
    this.field = field;
    this.value = value;
  }
}

// 统一错误处理中间件
export const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // 记录错误日志
  logger.error('Error occurred', {
    requestId: req.requestId,
    error: {
      name: err.name,
      message: err.message,
      code: err.code,
      statusCode: err.statusCode,
      stack: err.stack
    },
    request: {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    }
  });

  // MongoDB重复键错误
  if (err.code === 11000) {
    const message = '资源已存在';
    error = new AppError(message, 409, 'DUPLICATE_RESOURCE');
  }

  // MongoDB验证错误
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message).join(', ');
    error = new ValidationError(message);
  }

  // MongoDB转换错误
  if (err.name === 'CastError') {
    const message = '无效的资源ID';
    error = new AppError(message, 400, 'INVALID_ID');
  }

  // JWT错误
  if (err.name === 'JsonWebTokenError') {
    const message = '无效的认证令牌';
    error = new AppError(message, 401, 'INVALID_TOKEN');
  }

  // JWT过期错误
  if (err.name === 'TokenExpiredError') {
    const message = '认证令牌已过期';
    error = new AppError(message, 401, 'TOKEN_EXPIRED');
  }

  // 网络超时错误
  if (err.code === 'ECONNABORTED' || err.code === 'ETIMEDOUT') {
    const message = '请求超时';
    error = new AppError(message, 408, 'REQUEST_TIMEOUT');
  }

  // Agent连接错误
  if (err.code === 'ECONNREFUSED' || err.code === 'ENOTFOUND') {
    const message = 'Agent服务不可用';
    error = new AgentError(message, 'AGENT_UNAVAILABLE', 503);
  }

  // 系统资源不足
  if (err.code === 'EMFILE' || err.code === 'ENFILE') {
    const message = '系统资源不足';
    error = new AppError(message, 503, 'RESOURCE_EXHAUSTED');
  }

  // 准备响应
  const response = {
    success: false,
    error: {
      code: error.code || 'INTERNAL_ERROR',
      message: error.message || '服务器内部错误'
    },
    timestamp: new Date().toISOString(),
    requestId: req.requestId
  };

  // 在开发环境中包含更多错误信息
  if (process.env.NODE_ENV === 'development') {
    response.error.stack = error.stack;
    response.error.details = error.details;
    
    if (error.field !== undefined) {
      response.error.field = error.field;
    }
    
    if (error.value !== undefined) {
      response.error.value = error.value;
    }
  }

  // 发送错误响应
  res.status(error.statusCode || 500).json(response);
};

// 404处理中间件
export const notFoundHandler = (req, res, next) => {
  const message = `路径 ${req.originalUrl} 不存在`;
  const error = new AppError(message, 404, 'NOT_FOUND');
  next(error);
};

// 异步错误捕获装饰器
export const catchAsync = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 全局错误处理器
export const handleGlobalErrors = () => {
  // 处理未捕获的异常
  process.on('uncaughtException', (err) => {
    logger.error('Uncaught Exception - Shutting down...', {
      error: {
        name: err.name,
        message: err.message,
        stack: err.stack
      }
    });
    
    process.exit(1);
  });

  // 处理未处理的Promise拒绝
  process.on('unhandledRejection', (err, promise) => {
    logger.error('Unhandled Rejection - Shutting down...', {
      error: {
        name: err.name,
        message: err.message,
        stack: err.stack
      },
      promise: promise.toString()
    });
    
    process.exit(1);
  });

  // 处理SIGTERM信号
  process.on('SIGTERM', () => {
    logger.info('SIGTERM received. Starting graceful shutdown...');
    // 这里会在server.js中处理具体的关闭逻辑
  });

  // 处理SIGINT信号 (Ctrl+C)
  process.on('SIGINT', () => {
    logger.info('SIGINT received. Starting graceful shutdown...');
    // 这里会在server.js中处理具体的关闭逻辑
  });
};

// 错误响应格式化工具
export const formatErrorResponse = (error, requestId) => {
  const baseResponse = {
    success: false,
    error: {
      code: error.code || 'UNKNOWN_ERROR',
      message: error.message || '未知错误'
    },
    timestamp: new Date().toISOString(),
    requestId
  };

  // 根据错误类型添加额外信息
  if (error instanceof ValidationError) {
    baseResponse.error.field = error.field;
    baseResponse.error.value = error.value;
  }

  if (error instanceof AgentError || error instanceof TaskError) {
    baseResponse.error.details = error.details;
  }

  return baseResponse;
};

// 健康检查错误
export class HealthCheckError extends AppError {
  constructor(service, message) {
    super(`健康检查失败: ${service} - ${message}`, 503, 'HEALTH_CHECK_FAILED');
    this.name = 'HealthCheckError';
    this.service = service;
  }
}

// 协同执行错误
export class CollaborationError extends AppError {
  constructor(message, failedTasks = []) {
    super(message, 500, 'COLLABORATION_ERROR');
    this.name = 'CollaborationError';
    this.failedTasks = failedTasks;
  }
} 