import Joi from 'joi';
import { logger } from '../utils/logger.js';

// Agent注册验证
export const validateAgentRegistration = (req, res, next) => {
  const schema = Joi.object({
    id: Joi.string().required().min(3).max(50),
    type: Joi.string().required().valid(
      'analysis-agent', 
      'knowledge-agent', 
      'cases-agent', 
      'inspiration-agent',
      'orchestrator-agent'
    ),
    endpoint: Joi.string().uri().required(),
    capabilities: Joi.array().items(Joi.string()).default([]),
    metadata: Joi.object().default({})
  });

  const { error, value } = schema.validate(req.body);
  
  if (error) {
    logger.warn('Agent registration validation failed', {
      requestId: req.requestId,
      error: error.details[0].message,
      body: req.body
    });
    
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: error.details[0].message,
        field: error.details[0].path.join('.')
      },
      requestId: req.requestId
    });
  }

  req.body = value;
  next();
};

// 任务创建验证
export const validateTaskCreation = (req, res, next) => {
  const schema = Joi.object({
    type: Joi.string().required().valid(
      'analysis-agent',
      'knowledge-agent', 
      'cases-agent',
      'inspiration-agent',
      'orchestrator-agent'
    ),
    input: Joi.object({
      selectedText: Joi.string().required().min(1).max(10000),
      context: Joi.string().optional().max(50000)
    }).required(),
    context: Joi.object().default({}),
    priority: Joi.number().integer().min(1).max(10).default(1),
    timeout: Joi.number().integer().min(1000).max(300000).default(30000)
  });

  const { error, value } = schema.validate(req.body);
  
  if (error) {
    logger.warn('Task creation validation failed', {
      requestId: req.requestId,
      error: error.details[0].message,
      body: req.body
    });
    
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: error.details[0].message,
        field: error.details[0].path.join('.')
      },
      requestId: req.requestId
    });
  }

  req.body = value;
  next();
};

// 任务执行验证
export const validateTaskExecution = (req, res, next) => {
  const schema = Joi.object({
    tasks: Joi.array().items(
      Joi.object({
        type: Joi.string().required().valid(
          'analysis-agent',
          'knowledge-agent',
          'cases-agent', 
          'inspiration-agent'
        ),
        input: Joi.object().required(),
        context: Joi.object().default({})
      })
    ).min(1).max(10).required(),
    mode: Joi.string().valid('parallel', 'sequential').default('parallel'),
    context: Joi.object().default({})
  });

  const { error, value } = schema.validate(req.body);
  
  if (error) {
    logger.warn('Task execution validation failed', {
      requestId: req.requestId,
      error: error.details[0].message,
      body: req.body
    });
    
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: error.details[0].message,
        field: error.details[0].path.join('.')
      },
      requestId: req.requestId
    });
  }

  req.body = value;
  next();
};

// 分析请求验证
export const validateAnalysisRequest = (req, res, next) => {
  const schema = Joi.object({
    selectedText: Joi.string().required().min(1).max(10000),
    analysisType: Joi.string().required().valid(
      'evaluate', 'inspect', 'supplement', 'reference', 
      'examples', 'cases', 'inspire', 'creative'
    ),
    context: Joi.object().default({}),
    multiAgent: Joi.boolean().default(false),
    priority: Joi.number().integer().min(1).max(10).default(1)
  });

  const { error, value } = schema.validate(req.body);
  
  if (error) {
    logger.warn('Analysis request validation failed', {
      requestId: req.requestId,
      error: error.details[0].message,
      body: req.body
    });
    
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: error.details[0].message,
        field: error.details[0].path.join('.')
      },
      requestId: req.requestId
    });
  }

  req.body = value;
  next();
};

// 分页参数验证
export const validatePagination = (req, res, next) => {
  const schema = Joi.object({
    limit: Joi.number().integer().min(1).max(100).default(20),
    offset: Joi.number().integer().min(0).default(0),
    sort: Joi.string().valid('createdAt', 'updatedAt', 'type', 'status').default('createdAt'),
    order: Joi.string().valid('asc', 'desc').default('desc')
  });

  const { error, value } = schema.validate(req.query);
  
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: error.details[0].message,
        field: error.details[0].path.join('.')
      },
      requestId: req.requestId
    });
  }

  req.query = { ...req.query, ...value };
  next();
};

// 反馈验证
export const validateFeedback = (req, res, next) => {
  const schema = Joi.object({
    rating: Joi.number().integer().min(1).max(5).required(),
    feedback: Joi.string().max(1000).optional(),
    tags: Joi.array().items(Joi.string().max(50)).max(10).default([])
  });

  const { error, value } = schema.validate(req.body);
  
  if (error) {
    logger.warn('Feedback validation failed', {
      requestId: req.requestId,
      error: error.details[0].message,
      body: req.body
    });
    
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: error.details[0].message,
        field: error.details[0].path.join('.')
      },
      requestId: req.requestId
    });
  }

  req.body = value;
  next();
};

// WebSocket消息验证
export const validateWebSocketMessage = (message) => {
  const schema = Joi.object({
    type: Joi.string().required().valid(
      'subscribe', 'unsubscribe', 'task_status', 'agent_status', 'ping'
    ),
    data: Joi.object().default({}),
    requestId: Joi.string().optional(),
    timestamp: Joi.number().optional()
  });

  const { error, value } = schema.validate(message);
  
  if (error) {
    return {
      valid: false,
      error: error.details[0].message
    };
  }

  return {
    valid: true,
    data: value
  };
};

// ID参数验证
export const validateId = (paramName = 'id') => {
  return (req, res, next) => {
    const id = req.params[paramName];
    
    if (!id || typeof id !== 'string' || id.length < 3 || id.length > 100) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_ID',
          message: `无效的${paramName}参数`
        },
        requestId: req.requestId
      });
    }

    next();
  };
};

// 请求大小限制验证
export const validateRequestSize = (maxSize = 1024 * 1024) => {
  return (req, res, next) => {
    const contentLength = parseInt(req.get('Content-Length') || '0');
    
    if (contentLength > maxSize) {
      logger.warn('Request size exceeded limit', {
        requestId: req.requestId,
        contentLength,
        maxSize,
        url: req.originalUrl
      });
      
      return res.status(413).json({
        success: false,
        error: {
          code: 'REQUEST_TOO_LARGE',
          message: `请求大小超过限制 (${Math.round(maxSize / 1024)}KB)`
        },
        requestId: req.requestId
      });
    }

    next();
  };
};

// 通用错误处理
export const handleValidationError = (error, req, res, next) => {
  if (error.name === 'ValidationError') {
    logger.warn('Validation error', {
      requestId: req.requestId,
      error: error.message,
      url: req.originalUrl
    });
    
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: error.message
      },
      requestId: req.requestId
    });
  }

  next(error);
}; 