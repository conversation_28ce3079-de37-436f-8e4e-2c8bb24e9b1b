{"name": "req-ai-backend-server", "version": "1.0.0", "description": "AI需求编辑系统后端服务 - 前后端分离架构", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "health": "node scripts/health-check.js"}, "keywords": ["ai", "requirements", "analysis", "backend", "microservices", "agent-orchestration"], "author": "AI Requirements Editor Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "compression": "^1.7.4", "express-rate-limit": "^6.8.1", "joi": "^17.9.2", "winston": "^3.10.0", "ws": "^8.13.0", "axios": "^1.4.0", "uuid": "^9.0.0", "dotenv": "^16.3.1", "express-validator": "^7.0.1", "morgan": "^1.10.0", "http-status-codes": "^2.2.0", "node-cron": "^3.0.2", "express-async-errors": "^3.1.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.1", "supertest": "^6.3.3", "eslint": "^8.45.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/req-ai-edit-agent.git"}, "bugs": {"url": "https://github.com/your-org/req-ai-edit-agent/issues"}, "homepage": "https://github.com/your-org/req-ai-edit-agent#readme"}