// BSV特性需求文档数据
// 自动生成于 BSV PRD加载工具

export const realPRDContent = {
  title: "BSV 特性需求文档",
  sections: [
    {
      id: "section-0-1",
      title: "1.4		⽂档关系	1.4.1		当前功能相关⽂档		 UI/UE	⽂档	 FR	⽂档	 DR	⽂档	 跨域关联PRD	⽂档",
      level: 2,
      content: `1.4		⽂档关系	1.4.1		当前功能相关⽂档		 UI/UE	⽂档	 FR	⽂档	 DR	⽂档	 跨域关联PRD	⽂档`
    },
    {
      id: "section-0-1-1",
      title: "1.4.2	政策法规⽂件	以下法规仅⽤于功能开发参考。	区域	法规	 法规相关内容	 与本功能相关性",
      level: 3,
      content: `1.4.2	政策法规⽂件	以下法规仅⽤于功能开发参考。	区域	法规	 法规相关内容	 与本功能相关性`
    },
    {
      id: "section-0-1-1",
      title: "1.4.3	⾏业规范⽂件		暂⽆相关⾏业规范。	⼆、 产品概述	2.1	产品场景及概要说明	l 产品场景说明：在⾏驶过程中（时速⾼于30km/h）， 当 驾 驶 员 发 出 转 向 信 号 时 ，CSD和HUD即显示对应侧盲区实时视频流，为⽤户提供视野⽀持，提升驾驶安全性。	变道/汇⼊主路/汇出主路场景	 转弯场景",
      level: 3,
      content: `1.4.3	⾏业规范⽂件		暂⽆相关⾏业规范。	⼆、 产品概述	2.1	产品场景及概要说明	l 产品场景说明：在⾏驶过程中（时速⾼于30km/h）， 当 驾 驶 员 发 出 转 向 信 号 时 ，CSD和HUD即显示对应侧盲区实时视频流，为⽤户提供视野⽀持，提升驾驶安全性。	变道/汇⼊主路/汇出主路场景	 转弯场景`
    },
    {
      id: "section-0-2",
      title: "2.5	关键状态流转",
      level: 2,
      content: `2.5	关键状态流转`
    },
    {
      id: "section-1",
      title: "四、⾮功能说明	4.1	功能指标要求	功能指标	 详细描述	 关联use	case	id	视场⻆	 ⽔平60°	/垂直37°	 /	⻋⾝占⽐	 10%-15%	 /	帧率	 ⼤于50	 /	激活延迟	 ＜300ms	 /	显示延迟	 ＜100ms	 /	显示尺⼨	 ＞6⼨	 /	摄像头分辨率	 ＞100万	 /	视频流分辨率	 1280×720	 /	4.2		数据指标需求	序号	 类别	 指标项	指标名称	指标逻辑	 备注",
      level: 1,
      content: `四、⾮功能说明	4.1	功能指标要求	功能指标	 详细描述	 关联use	case	id	视场⻆	 ⽔平60°	/垂直37°	 /	⻋⾝占⽐	 10%-15%	 /	帧率	 ⼤于50	 /	激活延迟	 ＜300ms	 /	显示延迟	 ＜100ms	 /	显示尺⼨	 ＞6⼨	 /	摄像头分辨率	 ＞100万	 /	视频流分辨率	 1280×720	 /	4.2		数据指标需求	序号	 类别	 指标项	指标名称	指标逻辑	 备注`
    },
    {
      id: "section-1-1",
      title: "4.3	补充说明	4.3.1	⽂⾔提示汇总	序号	场景	 ⽂⾔	 备注	1	 左侧摄像头、通信故障前/后激活功能	 左侧视盲区辅助功能受限	5S后⽂⾔和影像退出	（激活前故障每个点⽕周期触发⼀次，激活后故障⽆相关要求）	2	 右侧摄像头、通信故障前/后激活功能	 右侧视盲区辅助功能受限	5S后⽂⾔和影像退出	（激活前故障每个点⽕周期触发⼀次，激活后故障⽆相关要求）	3	 域控、双侧摄像头、通信故障前/后激活功能	 侧视盲区辅助功能受限	 5S后⽂⾔toast退出	（激活前故障每个点⽕周期触发⼀次，激活后故障⽆相关要求）",
      level: 2,
      content: `4.3	补充说明	4.3.1	⽂⾔提示汇总	序号	场景	 ⽂⾔	 备注	1	 左侧摄像头、通信故障前/后激活功能	 左侧视盲区辅助功能受限	5S后⽂⾔和影像退出	（激活前故障每个点⽕周期触发⼀次，激活后故障⽆相关要求）	2	 右侧摄像头、通信故障前/后激活功能	 右侧视盲区辅助功能受限	5S后⽂⾔和影像退出	（激活前故障每个点⽕周期触发⼀次，激活后故障⽆相关要求）	3	 域控、双侧摄像头、通信故障前/后激活功能	 侧视盲区辅助功能受限	 5S后⽂⾔toast退出	（激活前故障每个点⽕周期触发⼀次，激活后故障⽆相关要求）`
    },
  ]
};

// BSV评审问题数据
export const mockIssues = [
  {
    id: "P001",
    section: "section-1",
    type: "error",
    priority: "high",
    title: "BSV架构设计缺失",
    description: "BSV功能的架构设计不完整，缺少关键组件说明。",
    suggestion: "建议优化BSV功能的相关设计和文档。",
    aiRecommendation: "基于BSV功能特点，建议进行相应改进。",
    confidence: 80,
    rule: "BSV设计规范",
    ruleId: "BSV-001",
    sectionPath: ["BSV\u76f8\u5173\u7ae0\u8282"],
    targetText: "BSV相关内容",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P002",
    section: "section-2",
    type: "warning",
    priority: "medium",
    title: "BSV文档关联不规范",
    description: "BSV相关文档的关联关系不够清晰，影响可追溯性。",
    suggestion: "建议优化BSV功能的相关设计和文档。",
    aiRecommendation: "基于BSV功能特点，建议进行相应改进。",
    confidence: 81,
    rule: "BSV设计规范",
    ruleId: "BSV-002",
    sectionPath: ["BSV\u76f8\u5173\u7ae0\u8282"],
    targetText: "BSV相关内容",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P003",
    section: "section-3",
    type: "suggestion",
    priority: "low",
    title: "BSV功能描述可优化",
    description: "BSV功能的描述可以更加详细和具体。",
    suggestion: "建议优化BSV功能的相关设计和文档。",
    aiRecommendation: "基于BSV功能特点，建议进行相应改进。",
    confidence: 82,
    rule: "BSV设计规范",
    ruleId: "BSV-003",
    sectionPath: ["BSV\u76f8\u5173\u7ae0\u8282"],
    targetText: "BSV相关内容",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P004",
    section: "section-1",
    type: "error",
    priority: "high",
    title: "BSV架构设计缺失",
    description: "BSV功能的架构设计不完整，缺少关键组件说明。",
    suggestion: "建议优化BSV功能的相关设计和文档。",
    aiRecommendation: "基于BSV功能特点，建议进行相应改进。",
    confidence: 83,
    rule: "BSV设计规范",
    ruleId: "BSV-004",
    sectionPath: ["BSV\u76f8\u5173\u7ae0\u8282"],
    targetText: "BSV相关内容",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P005",
    section: "section-2",
    type: "warning",
    priority: "medium",
    title: "BSV文档关联不规范",
    description: "BSV相关文档的关联关系不够清晰，影响可追溯性。",
    suggestion: "建议优化BSV功能的相关设计和文档。",
    aiRecommendation: "基于BSV功能特点，建议进行相应改进。",
    confidence: 84,
    rule: "BSV设计规范",
    ruleId: "BSV-005",
    sectionPath: ["BSV\u76f8\u5173\u7ae0\u8282"],
    targetText: "BSV相关内容",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P006",
    section: "section-3",
    type: "suggestion",
    priority: "low",
    title: "BSV功能描述可优化",
    description: "BSV功能的描述可以更加详细和具体。",
    suggestion: "建议优化BSV功能的相关设计和文档。",
    aiRecommendation: "基于BSV功能特点，建议进行相应改进。",
    confidence: 85,
    rule: "BSV设计规范",
    ruleId: "BSV-006",
    sectionPath: ["BSV\u76f8\u5173\u7ae0\u8282"],
    targetText: "BSV相关内容",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P007",
    section: "section-1",
    type: "error",
    priority: "high",
    title: "BSV架构设计缺失",
    description: "BSV功能的架构设计不完整，缺少关键组件说明。",
    suggestion: "建议优化BSV功能的相关设计和文档。",
    aiRecommendation: "基于BSV功能特点，建议进行相应改进。",
    confidence: 86,
    rule: "BSV设计规范",
    ruleId: "BSV-007",
    sectionPath: ["BSV\u76f8\u5173\u7ae0\u8282"],
    targetText: "BSV相关内容",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P008",
    section: "section-2",
    type: "warning",
    priority: "medium",
    title: "BSV文档关联不规范",
    description: "BSV相关文档的关联关系不够清晰，影响可追溯性。",
    suggestion: "建议优化BSV功能的相关设计和文档。",
    aiRecommendation: "基于BSV功能特点，建议进行相应改进。",
    confidence: 87,
    rule: "BSV设计规范",
    ruleId: "BSV-008",
    sectionPath: ["BSV\u76f8\u5173\u7ae0\u8282"],
    targetText: "BSV相关内容",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P009",
    section: "section-3",
    type: "suggestion",
    priority: "low",
    title: "BSV功能描述可优化",
    description: "BSV功能的描述可以更加详细和具体。",
    suggestion: "建议优化BSV功能的相关设计和文档。",
    aiRecommendation: "基于BSV功能特点，建议进行相应改进。",
    confidence: 88,
    rule: "BSV设计规范",
    ruleId: "BSV-009",
    sectionPath: ["BSV\u76f8\u5173\u7ae0\u8282"],
    targetText: "BSV相关内容",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P010",
    section: "section-1",
    type: "error",
    priority: "high",
    title: "BSV架构设计缺失",
    description: "BSV功能的架构设计不完整，缺少关键组件说明。",
    suggestion: "建议优化BSV功能的相关设计和文档。",
    aiRecommendation: "基于BSV功能特点，建议进行相应改进。",
    confidence: 89,
    rule: "BSV设计规范",
    ruleId: "BSV-010",
    sectionPath: ["BSV\u76f8\u5173\u7ae0\u8282"],
    targetText: "BSV相关内容",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
];
