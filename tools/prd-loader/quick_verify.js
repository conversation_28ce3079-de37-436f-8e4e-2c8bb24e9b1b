#!/usr/bin/env node
// 快速验证应用功能

const fs = require('fs');
const path = require('path');

function quickVerify() {
    console.log('🚀 快速验证应用功能\n');
    
    let allPassed = true;
    
    try {
        // 1. 检查文件是否存在
        const filePath = path.join(__dirname, 'src/data/realPRDContent.js');
        if (!fs.existsSync(filePath)) {
            console.log('❌ realPRDContent.js 文件不存在');
            allPassed = false;
            return;
        }
        console.log('✅ realPRDContent.js 文件存在');
        
        // 2. 检查文件语法
        try {
            require(filePath);
            console.log('✅ 文件语法正确，可以正常加载');
        } catch (error) {
            console.log(`❌ 文件语法错误: ${error.message}`);
            allPassed = false;
        }
        
        // 3. 检查文件内容
        const content = fs.readFileSync(filePath, 'utf8');
        
        const checks = [
            { name: 'BSV标题', pattern: /BSV特性需求文档/, required: true },
            { name: 'sections数组', pattern: /sections:\s*\[/, required: true },
            { name: 'mockIssues导出', pattern: /export const mockIssues/, required: true },
            { name: '问题P001', pattern: /"P001"/, required: true },
            { name: '问题P010', pattern: /"P010"/, required: true },
            { name: '编制/变更日志', pattern: /编制\/变更日志/, required: true },
            { name: '一、文档综述', pattern: /一、文档综述/, required: true },
            { name: '二、产品概述', pattern: /二、产品概述/, required: true }
        ];
        
        console.log('\n📊 内容检查:');
        checks.forEach(check => {
            const found = check.pattern.test(content);
            const status = found ? '✅' : '❌';
            console.log(`${status} ${check.name}: ${found ? '存在' : '缺失'}`);
            if (check.required && !found) {
                allPassed = false;
            }
        });
        
        // 4. 统计问题数量
        const issueMatches = content.match(/id:\s*"P\d{3}"/g);
        const issueCount = issueMatches ? issueMatches.length : 0;
        console.log(`\n📈 统计信息:`);
        console.log(`✅ 问题数量: ${issueCount} 个`);
        
        if (issueCount !== 10) {
            console.log(`⚠️  预期10个问题，实际${issueCount}个`);
        }
        
        // 5. 检查章节数量
        const sectionMatches = content.match(/id:\s*"section-[^"]+"/g);
        const sectionCount = sectionMatches ? sectionMatches.length : 0;
        console.log(`✅ 章节数量: ${sectionCount} 个`);
        
    } catch (error) {
        console.log(`❌ 验证过程出错: ${error.message}`);
        allPassed = false;
    }
    
    console.log(`\n🎯 验证结果: ${allPassed ? '✅ 通过' : '❌ 失败'}`);
    
    if (allPassed) {
        console.log('\n🎉 应用功能验证通过！');
        console.log('📱 应用地址: http://localhost:3810');
        console.log('🔧 修复完成: JavaScript语法错误已解决');
    }
    
    return allPassed;
}

if (require.main === module) {
    quickVerify();
}

module.exports = { quickVerify };
