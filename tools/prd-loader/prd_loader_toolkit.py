#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PRD文档动态加载工具包
基于BSV文档加载经验，提供标准化的PRD文档处理工具
"""

import os
import json
import re
from pathlib import Path

try:
    from pypdf import PdfReader
except ImportError:
    try:
        import PyPDF2
        from PyPDF2 import PdfReader
    except ImportError:
        print("请安装PDF处理库: pip3 install pypdf")
        exit(1)

class PRDLoader:
    """PRD文档加载器"""
    
    def __init__(self, function_name):
        self.function_name = function_name
        self.function_code = function_name.upper()
        self.base_dir = Path(".")
        self.project_root = self.base_dir / "../.."
        self.test_data_dir = self.project_root / "test_data"
        self.output_dir = self.base_dir
        
    def extract_pdf_content(self, pdf_path):
        """提取PDF内容"""
        print(f"📄 提取PDF内容: {pdf_path}")
        
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PdfReader(file)
                full_text = ""
                
                print(f"PDF总页数: {len(pdf_reader.pages)}")
                
                for page_num, page in enumerate(pdf_reader.pages):
                    page_text = page.extract_text()
                    full_text += f"\n=== 第{page_num + 1}页 ===\n"
                    full_text += page_text
                    full_text += "\n"
                
                # 保存提取的内容
                output_file = self.output_dir / f"{self.function_code}_full_content.txt"
                with open(output_file, "w", encoding="utf-8") as f:
                    f.write(full_text)
                
                print(f"✅ 内容已保存到: {output_file}")
                return full_text
                
        except Exception as e:
            print(f"❌ 读取PDF文件时出错: {e}")
            return None
    
    def analyze_document_structure(self, content):
        """分析文档结构"""
        print("🔍 分析文档结构...")
        
        sections = []
        lines = content.split('\n')
        
        # 基础章节结构模板
        section_patterns = [
            (r'^(.+特性需求文档)$', 0, 'cover'),
            (r'^([一二三四五六七八九十]+、.+)$', 1, 'section'),
            (r'^(\d+\.\d+\s+.+)$', 2, 'subsection'),
            (r'^(\d+\.\d+\.\d+\s+.+)$', 3, 'subsubsection')
        ]
        
        section_counter = {'section': 0, 'subsection': {}, 'subsubsection': {}}
        
        for line in lines:
            line = line.strip()
            if not line or '===' in line:
                continue
                
            for pattern, level, section_type in section_patterns:
                if re.match(pattern, line):
                    if section_type == 'cover':
                        section_id = 'cover'
                    elif section_type == 'section':
                        section_counter['section'] += 1
                        section_id = f"section-{section_counter['section']}"
                    elif section_type == 'subsection':
                        parent = section_counter['section']
                        if parent not in section_counter['subsection']:
                            section_counter['subsection'][parent] = 0
                        section_counter['subsection'][parent] += 1
                        section_id = f"section-{parent}-{section_counter['subsection'][parent]}"
                    elif section_type == 'subsubsection':
                        # 处理三级标题
                        section_id = f"section-{section_counter['section']}-{section_counter['subsection'].get(section_counter['section'], 1)}-1"
                    
                    sections.append({
                        'id': section_id,
                        'title': line,
                        'level': level,
                        'content': line
                    })
                    break
        
        print(f"✅ 识别到 {len(sections)} 个章节")
        return sections
    
    def generate_mock_issues(self, num_issues=10):
        """生成模拟问题数据"""
        print(f"🔧 生成 {num_issues} 个模拟问题...")
        
        issue_templates = [
            {
                "type": "error",
                "priority": "high",
                "title_template": "{function}架构设计缺失",
                "description_template": "{function}功能的架构设计不完整，缺少关键组件说明。"
            },
            {
                "type": "warning", 
                "priority": "medium",
                "title_template": "{function}文档关联不规范",
                "description_template": "{function}相关文档的关联关系不够清晰，影响可追溯性。"
            },
            {
                "type": "suggestion",
                "priority": "low", 
                "title_template": "{function}功能描述可优化",
                "description_template": "{function}功能的描述可以更加详细和具体。"
            }
        ]
        
        issues = []
        for i in range(num_issues):
            template = issue_templates[i % len(issue_templates)]
            issue_id = f"P{i+1:03d}"
            
            issue = {
                "id": issue_id,
                "section": f"section-{(i % 3) + 1}",
                "type": template["type"],
                "priority": template["priority"],
                "title": template["title_template"].format(function=self.function_name),
                "description": template["description_template"].format(function=self.function_name),
                "suggestion": f"建议优化{self.function_name}功能的相关设计和文档。",
                "aiRecommendation": f"基于{self.function_name}功能特点，建议进行相应改进。",
                "confidence": 80 + (i % 20),
                "rule": f"{self.function_name}设计规范",
                "ruleId": f"{self.function_code}-{i+1:03d}",
                "sectionPath": [f"{self.function_name}相关章节"],
                "targetText": f"{self.function_name}相关内容",
                "paragraphIndex": 1,
                "startOffset": 0,
                "endOffset": 1
            }
            issues.append(issue)
        
        # 保存问题数据
        output_file = self.output_dir / f"{self.function_code}_issues.json"
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(issues, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 问题数据已保存到: {output_file}")
        return issues
    
    def generate_realPRDContent_js(self, sections, issues):
        """生成realPRDContent.js文件内容"""
        print("📝 生成realPRDContent.js文件...")
        
        # 生成sections的JavaScript代码
        sections_js = "[\n"
        for section in sections:
            sections_js += f"""    {{
      id: "{section['id']}",
      title: "{section['title']}",
      level: {section['level']},
      content: `{section['content']}`
    }},\n"""
        sections_js += "  ]"
        
        # 生成issues的JavaScript代码
        issues_js = "[\n"
        for issue in issues:
            issues_js += f"""  {{
    id: "{issue['id']}",
    section: "{issue['section']}",
    type: "{issue['type']}",
    priority: "{issue['priority']}",
    title: "{issue['title']}",
    description: "{issue['description']}",
    suggestion: "{issue['suggestion']}",
    aiRecommendation: "{issue['aiRecommendation']}",
    confidence: {issue['confidence']},
    rule: "{issue['rule']}",
    ruleId: "{issue['ruleId']}",
    sectionPath: {json.dumps(issue['sectionPath'])},
    targetText: "{issue['targetText']}",
    paragraphIndex: {issue['paragraphIndex']},
    startOffset: {issue['startOffset']},
    endOffset: {issue['endOffset']}
  }},\n"""
        issues_js += "]"
        
        # 生成完整的JavaScript文件内容
        js_content = f'''// {self.function_name}特性需求文档数据
// 自动生成于 {self.function_name} PRD加载工具

export const realPRDContent = {{
  title: "{self.function_name} 特性需求文档",
  sections: {sections_js}
}};

// {self.function_name}评审问题数据
export const mockIssues = {issues_js};
'''
        
        # 保存JavaScript文件
        output_file = self.output_dir / f"{self.function_code}_realPRDContent.js"
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(js_content)
        
        print(f"✅ JavaScript文件已保存到: {output_file}")
        return js_content
    
    def generate_validator_script(self, expected_sections, expected_issues):
        """生成验证脚本"""
        print("🧪 生成验证脚本...")
        
        validator_content = f'''#!/usr/bin/env node
// {self.function_name}文档验证脚本

const fs = require('fs');
const path = require('path');

function validate{self.function_code}Data() {{
    console.log('🚀 验证{self.function_name}文档数据');
    
    const expectedSections = {expected_sections};
    const expectedIssues = {expected_issues};
    
    try {{
        // 检查文件是否存在
        const filePath = path.join(__dirname, 'src/data/realPRDContent.js');
        if (!fs.existsSync(filePath)) {{
            console.log('❌ realPRDContent.js 文件不存在');
            return false;
        }}
        console.log('✅ realPRDContent.js 文件存在');
        
        // 检查文件内容
        const content = fs.readFileSync(filePath, 'utf8');
        
        const checks = [
            {{ name: '{self.function_name}标题', pattern: /{self.function_name}特性需求文档/, required: true }},
            {{ name: 'sections数组', pattern: /sections:\\s*\\[/, required: true }},
            {{ name: 'mockIssues导出', pattern: /export const mockIssues/, required: true }}
        ];
        
        console.log('\\n📊 内容检查:');
        let allPassed = true;
        checks.forEach(check => {{
            const found = check.pattern.test(content);
            const status = found ? '✅' : '❌';
            console.log(`${{status}} ${{check.name}}: ${{found ? '存在' : '缺失'}}`);
            if (check.required && !found) {{
                allPassed = false;
            }}
        }});
        
        console.log(`\\n🎯 验证结果: ${{allPassed ? '✅ 通过' : '❌ 失败'}}`);
        return allPassed;
        
    }} catch (error) {{
        console.log(`❌ 验证过程出错: ${{error.message}}`);
        return false;
    }}
}}

if (require.main === module) {{
    validate{self.function_code}Data();
}}

module.exports = {{ validate{self.function_code}Data }};
'''
        
        output_file = self.output_dir / f"validate_{self.function_code.lower()}_data.js"
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(validator_content)
        
        print(f"✅ 验证脚本已保存到: {output_file}")
        return validator_content
    
    def process_prd_document(self, pdf_filename=None):
        """处理PRD文档的完整流程"""
        print(f"🚀 开始处理{self.function_name}特性需求文档")
        
        # 1. 确定PDF文件路径
        if pdf_filename is None:
            pdf_filename = f"{self.function_name}特性需求文档-pdf.pdf"
        
        pdf_path = self.test_data_dir / pdf_filename
        
        if not pdf_path.exists():
            print(f"❌ PDF文件不存在: {pdf_path}")
            print(f"请将PDF文件放置在: {pdf_path}")
            return False
        
        # 2. 提取PDF内容
        content = self.extract_pdf_content(pdf_path)
        if not content:
            return False
        
        # 3. 分析文档结构
        sections = self.analyze_document_structure(content)
        
        # 4. 生成问题数据
        issues = self.generate_mock_issues()
        
        # 5. 生成JavaScript文件
        js_content = self.generate_realPRDContent_js(sections, issues)
        
        # 6. 生成验证脚本
        validator = self.generate_validator_script(len(sections), len(issues))
        
        print(f"\\n🎉 {self.function_name}文档处理完成!")
        print(f"📊 统计信息:")
        print(f"   - 章节数量: {len(sections)}")
        print(f"   - 问题数量: {len(issues)}")
        print(f"   - 生成文件: {self.function_code}_realPRDContent.js")
        print(f"   - 验证脚本: validate_{self.function_code.lower()}_data.js")
        
        return True

def main():
    """主函数 - 命令行接口"""
    import sys
    
    if len(sys.argv) < 2:
        print("使用方法: python3 prd_loader_toolkit.py <功能名称> [PDF文件名]")
        print("示例: python3 prd_loader_toolkit.py LCA")
        print("示例: python3 prd_loader_toolkit.py AEB AEB特性需求文档.pdf")
        return
    
    function_name = sys.argv[1]
    pdf_filename = sys.argv[2] if len(sys.argv) > 2 else None
    
    loader = PRDLoader(function_name)
    success = loader.process_prd_document(pdf_filename)
    
    if success:
        print("\\n✅ 处理成功! 接下来请:")
        print("1. 检查生成的文件内容")
        print("2. 运行验证脚本测试")
        print("3. 更新src/data/realPRDContent.js")
        print("4. 测试应用功能")
    else:
        print("\\n❌ 处理失败，请检查错误信息")

if __name__ == "__main__":
    main()
