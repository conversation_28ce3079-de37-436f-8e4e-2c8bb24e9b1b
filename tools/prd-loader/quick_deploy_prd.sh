#!/bin/bash
# PRD文档快速部署脚本
# 用于将生成的PRD数据快速集成到应用中

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查参数
if [ $# -lt 1 ]; then
    print_error "使用方法: $0 <功能名称> [选项]"
    echo "示例: $0 LCA"
    echo "选项:"
    echo "  --backup    创建备份文件"
    echo "  --validate  运行验证测试"
    echo "  --commit    自动提交到git"
    exit 1
fi

FUNCTION_NAME=$1
FUNCTION_CODE=$(echo "$FUNCTION_NAME" | tr '[:lower:]' '[:upper:]')

# 解析选项
BACKUP=false
VALIDATE=false
COMMIT=false

for arg in "$@"; do
    case $arg in
        --backup)
            BACKUP=true
            ;;
        --validate)
            VALIDATE=true
            ;;
        --commit)
            COMMIT=true
            ;;
    esac
done

print_info "开始部署 ${FUNCTION_NAME} 特性需求文档"

# 检查必要文件是否存在
GENERATED_JS="${FUNCTION_CODE}_realPRDContent.js"
TARGET_JS="../../src/data/realPRDContent.js"

if [ ! -f "$GENERATED_JS" ]; then
    print_error "生成的JavaScript文件不存在: $GENERATED_JS"
    print_info "请先运行: python3 prd_loader_toolkit.py $FUNCTION_NAME"
    exit 1
fi

print_success "找到生成的文件: $GENERATED_JS"

# 创建备份
if [ "$BACKUP" = true ] || [ -f "$TARGET_JS" ]; then
    BACKUP_FILE="${TARGET_JS}.backup.$(date +%Y%m%d_%H%M%S)"
    if [ -f "$TARGET_JS" ]; then
        cp "$TARGET_JS" "$BACKUP_FILE"
        print_success "已创建备份文件: $BACKUP_FILE"
    fi
fi

# 检查JavaScript语法
print_info "检查JavaScript语法..."
if node -c "$GENERATED_JS"; then
    print_success "JavaScript语法检查通过"
else
    print_error "JavaScript语法检查失败"
    exit 1
fi

# 复制文件到目标位置
print_info "部署文件到应用中..."
cp "$GENERATED_JS" "$TARGET_JS"
print_success "文件已部署到: $TARGET_JS"

# 验证应用编译
print_info "检查应用编译状态..."
cd ../..
if npm run build > /dev/null 2>&1; then
    print_success "应用编译成功"
else
    print_warning "应用编译可能有问题，请检查"
fi
cd tools/prd-loader

# 运行验证测试
if [ "$VALIDATE" = true ]; then
    VALIDATOR_SCRIPT="validate_${FUNCTION_CODE,,}_data.js"
    if [ -f "$VALIDATOR_SCRIPT" ]; then
        print_info "运行验证测试..."
        if node "$VALIDATOR_SCRIPT"; then
            print_success "验证测试通过"
        else
            print_warning "验证测试失败，请检查"
        fi
    else
        print_warning "验证脚本不存在: $VALIDATOR_SCRIPT"
    fi
fi

# 生成部署报告
REPORT_FILE="${FUNCTION_NAME}_deployment_report.md"
cat > "$REPORT_FILE" << EOF
# ${FUNCTION_NAME} 特性需求文档部署报告

## 📋 部署信息

- **功能名称**: ${FUNCTION_NAME}
- **部署时间**: $(date '+%Y-%m-%d %H:%M:%S')
- **源文件**: ${GENERATED_JS}
- **目标文件**: ${TARGET_JS}
- **备份文件**: ${BACKUP_FILE:-"未创建"}

## ✅ 部署步骤

1. ✅ 检查源文件存在
2. ✅ JavaScript语法验证
3. ✅ 创建备份文件
4. ✅ 部署到目标位置
5. ✅ 应用编译检查
$([ "$VALIDATE" = true ] && echo "6. ✅ 验证测试执行" || echo "6. ⏭️  验证测试跳过")

## 🔧 后续步骤

1. 启动应用: \`npm start\`
2. 访问应用: http://localhost:3810
3. 测试功能完整性
4. 提交代码变更

## 📊 文件统计

\`\`\`bash
# 查看文件大小
ls -lh ${TARGET_JS}

# 检查内容
grep -c "section-" ${TARGET_JS}
grep -c "P[0-9]" ${TARGET_JS}
\`\`\`

## 🚀 快速验证命令

\`\`\`bash
# 检查应用状态
curl -s -I http://localhost:3810

# 运行验证脚本
node validate_${FUNCTION_CODE,,}_data.js

# 查看git状态
git status
\`\`\`
EOF

print_success "部署报告已生成: $REPORT_FILE"

# Git提交
if [ "$COMMIT" = true ]; then
    print_info "提交到Git仓库..."
    
    # 添加文件到暂存区
    git add "$TARGET_JS" "$REPORT_FILE"
    
    # 如果有备份文件也添加
    if [ -n "$BACKUP_FILE" ] && [ -f "$BACKUP_FILE" ]; then
        git add "$BACKUP_FILE"
    fi
    
    # 提交
    COMMIT_MESSAGE="feat: 部署${FUNCTION_NAME}特性需求文档数据

- 更新realPRDContent.js为${FUNCTION_NAME}文档内容
- 自动生成的章节和问题数据
- 通过语法检查和编译验证"

    if git commit -m "$COMMIT_MESSAGE"; then
        print_success "已提交到Git仓库"
    else
        print_warning "Git提交失败或无变更"
    fi
fi

# 显示完成信息
echo ""
print_success "🎉 ${FUNCTION_NAME} 特性需求文档部署完成!"
echo ""
print_info "📱 应用访问地址: http://localhost:3810"
print_info "📄 部署报告: $REPORT_FILE"
echo ""
print_info "🔧 接下来请:"
echo "   1. 启动应用: npm start"
echo "   2. 测试功能: 浏览器访问应用"
echo "   3. 验证数据: 检查章节和问题显示"
echo "   4. 提交代码: git add && git commit"
echo ""

# 询问是否启动应用
read -p "是否现在启动应用? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_info "启动应用..."
    npm start
fi
