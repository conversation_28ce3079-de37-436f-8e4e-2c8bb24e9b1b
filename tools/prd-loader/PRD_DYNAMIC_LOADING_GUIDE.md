# PRD文档动态加载流程和规则指南

## 📋 概述

本指南基于BSV特性需求文档的成功加载经验，制定了一套标准化的流程和规则，用于快速实现其他PRD文档的动态加载。

## 🔄 标准化流程

### 阶段1：文档准备和内容提取

#### 1.1 PDF内容提取
```bash
# 使用Python脚本提取PDF内容
python3 extract_pdf_content.py [PDF文件路径]
```

**核心脚本模板**：
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

try:
    from pypdf import PdfReader
except ImportError:
    import PyPDF2
    from PyPDF2 import PdfReader

def extract_pdf_content(pdf_path):
    """提取PDF的完整内容"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PdfReader(file)
            full_text = ""
            
            for page_num, page in enumerate(pdf_reader.pages):
                page_text = page.extract_text()
                full_text += f"\n=== 第{page_num + 1}页 ===\n"
                full_text += page_text
                full_text += "\n"
            
            return full_text
    except Exception as e:
        print(f"读取PDF文件时出错: {e}")
        return None
```

#### 1.2 章节结构分析
- 识别文档的层级结构（一级、二级、三级标题）
- 提取章节ID和标题映射关系
- 生成章节内容片段

### 阶段2：评审问题数据提取

#### 2.1 从评审报告提取问题
```python
def extract_issues_from_report(report_path):
    """从评审报告中提取问题数据"""
    issues = []
    # 解析HTML或其他格式的评审报告
    # 提取问题ID、标题、描述、建议等信息
    return issues
```

#### 2.2 问题数据标准化
每个问题必须包含以下字段：
```json
{
  "id": "P001",
  "section": "section-x-x",
  "type": "error|warning|suggestion",
  "priority": "high|medium|low",
  "title": "问题标题",
  "description": "问题描述",
  "suggestion": "改进建议",
  "aiRecommendation": "AI推荐",
  "confidence": 85,
  "rule": "规则名称",
  "ruleId": "RULE-001",
  "sectionPath": ["章节路径"],
  "targetText": "目标文本",
  "paragraphIndex": 1,
  "startOffset": 0,
  "endOffset": 1
}
```

### 阶段3：数据结构生成

#### 3.1 章节数据结构
```javascript
const sections = [
  {
    id: "cover",
    title: "文档标题",
    level: 0,
    content: `文档内容...`
  },
  {
    id: "section-1",
    title: "一、章节标题",
    level: 1,
    content: `章节内容...`
  }
  // ... 更多章节
];
```

#### 3.2 完整数据结构
```javascript
export const realPRDContent = {
  title: "PRD文档标题",
  sections: sections
};

export const mockIssues = [
  // 问题数组
];
```

### 阶段4：集成和验证

#### 4.1 文件更新
- 更新 `src/data/realPRDContent.js`
- 保留原文件备份
- 验证JavaScript语法

#### 4.2 功能测试
```javascript
// 验证脚本模板
function validatePRDData() {
  // 检查章节数量和结构
  // 验证问题数据完整性
  // 测试应用功能
}
```

## 📏 标准化规则

### 规则1：文件命名规范
```
原始文件：[功能名]特性需求文档-pdf.pdf
提取内容：[功能名]_full_content.txt
问题数据：[功能名]_issues.json
章节数据：[功能名]_sections_content.json
```

### 规则2：章节ID生成规则
```
封面：cover
一级章节：section-1, section-2, section-3...
二级章节：section-1-1, section-1-2, section-1-3...
三级章节：section-1-1-1, section-1-1-2...
```

### 规则3：问题ID生成规则
```
格式：P + 三位数字（P001, P002, P003...）
优先级：high > medium > low
类型：error > warning > suggestion
```

### 规则4：内容处理规则
- 保留原始格式和结构
- 处理特殊字符和编码
- 保持表格和列表格式
- 添加图片占位符

## 🛠️ 快速实施工具包

### 工具1：PDF提取脚本生成器
```python
def generate_pdf_extractor(function_name):
    """生成特定功能的PDF提取脚本"""
    template = f"""
#!/usr/bin/env python3
# 提取{function_name}特性需求文档内容

def extract_{function_name.lower()}_content(pdf_path):
    # 使用标准提取逻辑
    pass

if __name__ == "__main__":
    extract_{function_name.lower()}_content("test_data/{function_name}特性需求文档-pdf.pdf")
"""
    return template
```

### 工具2：数据结构生成器
```python
def generate_data_structure(function_name, sections, issues):
    """生成realPRDContent.js文件内容"""
    template = f"""
// {function_name}特性需求文档数据
export const realPRDContent = {{
  title: "{function_name} 特性需求文档",
  sections: {json.dumps(sections, ensure_ascii=False, indent=2)}
}};

export const mockIssues = {json.dumps(issues, ensure_ascii=False, indent=2)};
"""
    return template
```

### 工具3：验证脚本生成器
```javascript
function generateValidator(functionName, expectedSections, expectedIssues) {
  return `
// ${functionName}文档验证脚本
function validate${functionName}Data() {
  console.log('验证${functionName}文档数据...');
  
  // 验证章节数量
  const expectedSectionCount = ${expectedSections};
  const expectedIssueCount = ${expectedIssues};
  
  // 执行验证逻辑
  return validateStructure(expectedSectionCount, expectedIssueCount);
}
`;
}
```

## 🚀 快速实施步骤

### 步骤1：准备阶段（5分钟）
1. 将新的PDF文档放入 `test_data/` 目录
2. 确认文档命名符合规范
3. 准备评审报告文件

### 步骤2：内容提取（10分钟）
1. 运行PDF提取脚本
2. 分析章节结构
3. 提取评审问题

### 步骤3：数据生成（10分钟）
1. 生成章节数据结构
2. 生成问题数据结构
3. 合并为完整数据

### 步骤4：集成更新（5分钟）
1. 备份现有文件
2. 更新 `realPRDContent.js`
3. 验证语法正确性

### 步骤5：测试验证（10分钟）
1. 运行验证脚本
2. 测试应用功能
3. 确认数据完整性

## 📊 质量检查清单

### ✅ 内容完整性
- [ ] PDF内容完全提取
- [ ] 章节结构正确
- [ ] 表格格式保留
- [ ] 特殊字符处理

### ✅ 数据结构
- [ ] 章节ID符合规范
- [ ] 问题ID连续编号
- [ ] 字段完整无缺失
- [ ] 关联关系正确

### ✅ 功能验证
- [ ] JavaScript语法正确
- [ ] 应用正常编译
- [ ] 页面正常显示
- [ ] 交互功能正常

### ✅ 文档质量
- [ ] 备份文件创建
- [ ] 更新日志记录
- [ ] 测试报告生成
- [ ] Git提交完成

## 🔧 故障排除指南

### 常见问题1：PDF提取失败
**解决方案**：
- 检查PDF文件完整性
- 尝试不同的PDF库
- 手动处理特殊格式

### 常见问题2：JavaScript语法错误
**解决方案**：
- 检查模板字符串边界
- 处理特殊字符转义
- 验证JSON格式正确性

### 常见问题3：章节关联错误
**解决方案**：
- 重新分析章节结构
- 检查ID生成逻辑
- 验证问题章节映射

## 📈 优化建议

### 自动化改进
1. 开发自动化脚本，一键完成整个流程
2. 集成CI/CD流程，自动验证和部署
3. 建立文档模板库，支持不同类型PRD

### 扩展功能
1. 支持多种文档格式（Word, Markdown等）
2. 增加智能章节识别算法
3. 集成AI辅助问题提取功能

通过遵循这套标准化流程和规则，可以将新PRD文档的加载时间从数小时缩短到30-40分钟，大大提高开发效率。

## 🎯 使用示例

### 示例1：加载LCA（车道居中辅助）文档

```bash
# 1. 准备PDF文件
cp "LCA特性需求文档-pdf.pdf" test_data/

# 2. 运行自动化工具
python3 prd_loader_toolkit.py LCA

# 3. 快速部署
./quick_deploy_prd.sh LCA --backup --validate --commit

# 4. 启动应用测试
npm start
```

### 示例2：加载AEB（自动紧急制动）文档

```bash
# 1. 使用自定义PDF文件名
python3 prd_loader_toolkit.py AEB "AEB自动紧急制动需求文档.pdf"

# 2. 手动验证和部署
node validate_aeb_data.js
./quick_deploy_prd.sh AEB --backup

# 3. 测试应用
curl -s http://localhost:3810 | grep "AEB"
```

## 🏆 最佳实践

### 1. 文档准备最佳实践
- 确保PDF文档结构清晰，章节标题规范
- 提前准备评审报告，包含具体问题描述
- 检查文档编码，避免特殊字符问题

### 2. 数据质量最佳实践
- 验证章节层级关系正确
- 确保问题与章节的关联准确
- 保持数据结构的一致性

### 3. 部署安全最佳实践
- 始终创建备份文件
- 运行完整的验证测试
- 分步骤部署，及时发现问题

### 4. 版本控制最佳实践
- 使用描述性的提交信息
- 包含变更的详细说明
- 标记重要的里程碑版本

## 📚 工具包文件说明

| 文件名 | 用途 | 说明 |
|--------|------|------|
| `PRD_DYNAMIC_LOADING_GUIDE.md` | 流程指南 | 完整的流程和规则文档 |
| `prd_loader_toolkit.py` | 自动化工具 | Python工具包，处理PDF和生成数据 |
| `quick_deploy_prd.sh` | 部署脚本 | Bash脚本，快速部署到应用 |
| `validate_*_data.js` | 验证脚本 | 自动生成的验证工具 |

## 🔄 持续改进

### 版本1.0（当前）
- 基础PDF提取和数据生成
- 标准化的章节和问题结构
- 自动化部署流程

### 版本2.0（规划中）
- 智能章节识别算法
- 多格式文档支持（Word, Markdown）
- AI辅助问题提取
- 可视化配置界面

### 版本3.0（未来）
- 实时协作编辑
- 版本差异对比
- 自动化测试集成
- 云端文档同步

通过这套完整的工具包和流程，团队可以快速、安全、高效地加载任何新的PRD文档，大大提升开发效率和文档质量。
