{"version": "1.0.0", "name": "PRD文档动态加载工具包", "description": "基于BSV文档成功加载经验的标准化PRD处理工具", "paths": {"project_root": "../..", "test_data": "../../test_data", "src_data": "../../src/data", "target_file": "../../src/data/realPRDContent.js", "backup_dir": "../../backups", "output_dir": "./output", "examples_dir": "./examples", "templates_dir": "./templates"}, "file_patterns": {"pdf_input": "{function_name}特性需求文档-pdf.pdf", "content_output": "{function_code}_full_content.txt", "issues_output": "{function_code}_issues.json", "js_output": "{function_code}_realPRDContent.js", "validator_output": "validate_{function_code}_data.js", "backup_pattern": "realPRDContent.js.backup.{timestamp}", "report_output": "{function_name}_deployment_report.md"}, "section_patterns": {"cover": "^(.+特性需求文档)$", "level1": "^([一二三四五六七八九十]+、.+)$", "level2": "^(\\d+\\.\\d+\\s+.+)$", "level3": "^(\\d+\\.\\d+\\.\\d+\\s+.+)$"}, "section_id_rules": {"cover": "cover", "level1_prefix": "section-", "level2_separator": "-", "level3_separator": "-"}, "issue_config": {"default_count": 10, "id_prefix": "P", "id_format": "{prefix}{number:03d}", "types": ["error", "warning", "suggestion"], "priorities": ["high", "medium", "low"], "confidence_range": [60, 95]}, "issue_templates": [{"type": "error", "priority": "high", "title_template": "{function}架构设计缺失", "description_template": "{function}功能的架构设计不完整，缺少关键组件说明。", "suggestion_template": "建议补充完整的{function}架构设计文档。", "rule_template": "{function}架构完整性要求", "rule_id_template": "ARCH-001"}, {"type": "warning", "priority": "medium", "title_template": "{function}文档关联不规范", "description_template": "{function}相关文档的关联关系不够清晰，影响可追溯性。", "suggestion_template": "建议重新整理{function}相关文档的关联关系。", "rule_template": "{function}文档可追溯性规范", "rule_id_template": "DOC-001"}, {"type": "suggestion", "priority": "low", "title_template": "{function}功能描述可优化", "description_template": "{function}功能的描述可以更加详细和具体。", "suggestion_template": "建议优化{function}功能的描述内容。", "rule_template": "{function}功能描述规范", "rule_id_template": "FUNC-001"}], "validation_rules": {"required_sections": ["cover", "section-1", "section-2"], "min_sections": 5, "max_sections": 50, "min_issues": 5, "max_issues": 20, "required_fields": ["id", "section", "type", "priority", "title", "description", "suggestion", "aiRecommendation"]}, "deployment_config": {"backup_enabled": true, "syntax_check": true, "validation_required": true, "auto_commit": false, "commit_message_template": "feat: 部署{function_name}特性需求文档数据\n\n- 更新realPRDContent.js为{function_name}文档内容\n- 自动生成的章节和问题数据\n- 通过语法检查和编译验证"}, "pdf_processing": {"libraries": ["pypdf", "PyPDF2"], "encoding": "utf-8", "page_separator": "\n=== 第{page_num}页 ===\n", "max_pages": 100, "timeout": 300}, "javascript_generation": {"indent": 2, "quote_style": "double", "trailing_comma": true, "template_literal": true}, "logging": {"level": "INFO", "format": "[{timestamp}] {level}: {message}", "file_enabled": false, "console_enabled": true}, "supported_functions": [{"code": "BSV", "name": "侧视盲区辅助", "description": "Blind Spot Visualization", "status": "completed"}, {"code": "LCA", "name": "车道居中辅助", "description": "Lane Centering Assist", "status": "planned"}, {"code": "AEB", "name": "自动紧急制动", "description": "Autonomous Emergency Braking", "status": "planned"}, {"code": "ACC", "name": "自适应巡航控制", "description": "Adaptive Cruise Control", "status": "planned"}, {"code": "ICC", "name": "智能巡航控制", "description": "Intelligent Cruise Control", "status": "planned"}], "quality_metrics": {"processing_time_target": 40, "error_rate_target": 0.05, "automation_rate_target": 0.9, "consistency_score_target": 0.95}, "environment": {"python_version": ">=3.7", "node_version": ">=14.0", "required_packages": ["pypdf", "pathlib", "json", "re"]}}