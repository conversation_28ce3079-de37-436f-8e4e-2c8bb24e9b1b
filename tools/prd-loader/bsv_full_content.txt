
=== 第1页 ===
BSV特性需求⽂档	编制/变更⽇志	序号	版本	PRD ID	修改章节及内容	 修改人	审核人	批准人	修改日期	1	 V1.0.0	S-123-66	初次创建	 蔡⻜	/	 /	 2023-12-05	2	 V1.0.1	S-123-66	/	 蔡⻜	/	 /	 2024-03-08	3	 V1.0.2	S-123-66	1.增加关联系统故障相关⽂⾔提示相关usecase：3.4.1、3.5.1、3.5.2	2.增加与⾏⻋功能的相互抑制usecase：3.2.1、3.3.1、3.1.1、3.1.2	3.增加与BSD联动的usecase:3.3.2	
张梓煜于芷涵	郑旭	郑福⺠	周希希	2024-10-29	
⼀、 ⽂档综述	1.1	⽂档背景	本⽂应⽤于吉利集团的侧视盲区辅助功能需求⽂档，详细描述侧视盲区辅助的场景分析，应⽤流程，详细策略设计等。		项⽬实现过程中，请以详细设计为基础进⾏需求实现，如遇到歧义或变更，请及时沟通。	1.2	⽂档范围	本⽂档介绍了侧视盲区辅助（Blind	Spot	Visualization,	BSV）的产品概述和具体的场景设计。产品概述中介绍了产品的定义、使⽤场景、⻋型配置说明、功能架构、功能流程。	1.3	术语解释		序号	 术语	 定义	1	 ODD	 Operational	Design	Domain	2	 HMI	 Human-Machine	Interface	3	 AVM	 Around	view	Monitor	4	 CSD	 Centerstack	Display	5	 HUD	 Heads	Up	Display	6	 BSD	 Blind	Spot	Detection	7	 DOW	 Door	Opening	Warning	
批注 [1]: ⽂⾔不是⼀个单独的use	case，⽐昂更⽇志需要体现增量的条⽬	

=== 第2页 ===
1.4		⽂档关系	1.4.1		当前功能相关⽂档		 UI/UE	⽂档	 FR	⽂档	 DR	⽂档	 跨域关联PRD	⽂档	
v1.0.1	L946_BSV_UEV1.0.2.pdf	
BSV-EADP-视频流.pdfBSV-HMI-Error	Messae.pdfBSV-ASM-Ative	to	Failure.pdfBSV-HMI-Exit.pdfBSV-ASM-Active.pdfBSV-HMI-Ative.pdfBSV-UserInput-拨杆.pdfBSV-BrakeControl-⻋速.pdfBSV-ASM-Exit.pdfBSV-Propulsion-挡位.pdf	
BSV-DR(ASM)-拨杆激活.pdfBSV-DR(ASM)-拨杆退出.pdfBSV-DR(ASM)-摄像头故障.pdfBSV-DR(ASM)-跳转到故障.pdfBSV-DR(ASM)-系统故障.pdf	
DOW：FD-S-120-62	Door	Open	Warning		开⻔预警	V1.0.7(1).pdf	AVM：VPA-Basic	FDR.pdf	BSD：FD-S-123-07	Lane	Change	Assist	变道辅助	V1.0.8.pdf	
V1.0.2	/	 url:swap://10.24.24.227:1770/x0400000019C3AD93	url:swap://10.24.24.227:1770/x0400000019C3E534	 /	 /	
1.4.2	政策法规⽂件	以下法规仅⽤于功能开发参考。	区域	法规	 法规相关内容	 与本功能相关性	
中国	 GB/T	39265（2020）	 道路⻋辆盲区监测系统性能要求及试验⽅法，对BSD系统的性能要求及测试步骤做了介绍。	
中国暂⽆直接对侧视盲区辅助功能的相关规定。	BSD联动报警时，BSV视图会有所体现。	5.1.1讲述了⻋辆盲区及参测范围	
欧洲	 ECE	R166	 《关于就⻋辆前部和侧⾯附近弱势道路使⽤者对驾驶员的提醒功能⽽批准装置和机动⻋辆的统⼀规定》 ，是联合国制定的⼀项旨在提⾼道路交通安全性的重要法规，适⽤于需要安装提醒驾驶员注意⻋辆前部和侧⾯附近弱势道路使⽤者的装置的机动⻋辆及其相关设备。	
这些设备可能包括雷达、摄像头、传感器等，它们能够实时监测⻋辆周围的环境，并向驾驶员提供警示信息。	15.2讲述了⻋辆盲区及参测范围	
批注 [2]: 跟DOW	BSD	PO打合	

=== 第3页 ===
1.4.3	⾏业规范⽂件		暂⽆相关⾏业规范。	⼆、 产品概述	2.1	产品场景及概要说明	l 产品场景说明：在⾏驶过程中（时速⾼于30km/h）， 当 驾 驶 员 发 出 转 向 信 号 时 ，CSD和HUD即显示对应侧盲区实时视频流，为⽤户提供视野⽀持，提升驾驶安全性。	变道/汇⼊主路/汇出主路场景	 转弯场景	
	 	侧后⽅视野，观察侧后⽅来⻋等，预防事故	 扩展侧后⽅视野，观察侧后⽅直⾏⻋辆等	l 产品可⽤地点：满⾜⼀定光照条件的路段	l 产品使⽤⽅式：设置项开启后，通过转向拨杆激活功能，通过拨杆或换档退出功能	l 产品关键参数：	¡ 盲区显示视频流尺⼨	¡ 视频流清晰度	¡ 延迟	2.2	产品架构/系统架构	简单功能不需要架构分解。	2.3	产品梯度配置说明	平台	 舱泊	 6v	 10v	 11v	功能配置	 -	 -	 -	 l ⽤户D档拨动转向拨杆，显示对应侧盲区视图	
批注 [3]: 和⾃研打合⼀版产品架构	批注 [4]: 扩充系统架构的标准	

=== 第4页 ===
l 挂⻋模式下挂R挡，显示双侧盲区视图	l 后⻔开启激活DOW报警，⼆排屏显示对应侧盲区视图	2.4	功能流程图	正常模式下：	
	挂⻋模式下：	
	DOW模式下：	


=== 第5页 ===
	2.5	关键状态流转	
	T1：OFF	to	Initialize		VMM模式满⾜	T2：Initialize	to	Standby		系统⽆故障and两颗侧后摄像头之⼀⽆故障andVMM模式=Driving/Active/Convenience	T3：Standby	to	Active		正常模式下转向拨杆激活or挂⻋模式下R档激活orDOW模式下开⻔触发⼆级报警	T4：Active	to	Standby		同时满⾜以下条件：		（1）正常模式下软开关退出or拨杆回正退出or关联系统故障退出or泊⻋功能开启退出or⾏⻋辅助功能开启退出or切换档位退出or设置项关闭退出【没有后排屏】		（2）拖⻋模式下软开关退出or拨杆回正退出or关联系统故障退出or切换档位退出orR切⾮R⻋速抄15退出or设置项关闭退出【没有后排屏】		（3）DOW模式下软开关退出or⻋⻔关闭退出or前后排屏都关闭or设置项关闭退出
批注 [5]: 需要跟⾏⻋确认挂⻋模式下是否能开启ICC/ACC等功能	

=== 第6页 ===
andVMM满⾜条件【有后排屏】	T5：Initialize	to	Failure		关联系统故障or双侧摄像头故障andVMM满⾜条件	T6：Failure	to	Standby		关联系统故障回复and摄像头恢复andVMM满⾜条件	T7：Standby	to	Failure		关联系统故障or双侧摄像头故障andVMM满⾜条件	T8：Failure	to	Active		⼀颗以上摄像头5s内恢复and系统故障5s内恢复andVMM满⾜条件and满⾜T3条件	T9：Active	to	Failure		关联系统故障or两颗摄像头故障andVMM满⾜条件	T10：On	to	Off		VMM不满⾜	2.6	配置	2.6.1	硬件配置	配置⻋型必须配有侧后视摄像头	ADCU与DHU之间须有千兆以太⽹作为视频流传输通道	2.6.2	EBOC	E100	BLIND	SPOT	VISSUAL	盲区可视	E101	N	BSV																									⽆盲区可视	E102	BSV																													盲区可视	2.6.3	CCP	当参数CCP	#781	==	01时，⽆需实现功能。	当参数CCP	#781	==	02时，需要实现功能。	2.7	功能列表	⼀级功能	 ⼆级功能	三级功能	侧视盲区辅助	设置项	 增加侧视盲区辅助功能设置项开关	⽤户查看侧视盲区辅助⼩i⽂⾔	

=== 第7页 ===
打开侧视盲区辅助设置项	关闭侧视盲区辅助设置项	侧视盲区辅助设置项下电记忆	功能激活	转向拨杆激活左侧侧视盲区辅助	转向拨杆激活右侧侧视盲区辅助	
功能退出	
⽤户回正转向拨杆退出侧视盲区辅助	拨杆⾃动回正退出侧视盲区辅助	影像软开关退出侧视盲区辅助	R档/P档/N档退出侧视盲区辅助	APA开启/HPA开启/AVM开启（包括RCTA激活和PDC触发）退出侧视盲区辅助	故障退出左侧视盲区辅助	故障退出右侧视盲区辅助	设置项开关退出侧视盲区辅助	ICC/NOA/CNOA/ALCA/HWA功能开启退出侧视盲区辅助	视图位置调整	 视图位置调整	视图位置下电记忆	与其他功能的联动逻辑	 ⾏⻋辅助功能抑制侧视盲区辅助功能	BSV视图上增加BSD报警视觉提示	故障提示	左侧视盲区辅助激活前发⽣故障	右侧视盲区辅助激活前发⽣故障	⼯作电源模式	 ⼯作电源模式	
挂⻋模式	
挂⻋模式下转向拨杆激活双侧视盲区辅助拼接视图	挂⻋模式下R挡激活侧视盲区辅助拼接视图	挂⻋模式下软开关退出侧视盲区辅助拼接视图	挂⻋模式下切换挡位退出侧视盲区辅助拼接视图	挂⻋模式下抑制AVM	挂⻋模式下双侧视盲区辅助激活前发⽣故障	

=== 第8页 ===
挂⻋模式下双侧视盲区辅助激活后发⽣故障	挂⻋模式下单侧视盲区辅助激活发⽣故障	挂⻋模式下设置项开关退出侧视盲区辅助	挂⻋模式下⽤户回正转向拨杆退出侧视盲区辅助	挂⻋模式下拨杆⾃动回正退出侧视盲区辅助	
DOW模式	
后排DOW⼆级报警激活后排侧视盲区辅助视图	后排关⻔退出后排侧视盲区辅助视图	软开关退出后排侧视盲区辅助视图	后排侧视盲区辅助激活前发⽣故障	后排侧视盲区辅助激活后发⽣故障	前后排视图显示逻辑	后排DOW模式下设置项开关退出侧视盲区辅助	三、功能场景设计	3.1		功能激活	3.1.1	转向拨杆激活左侧侧视盲区辅助		 内容说明	 原型	
前置条件	
1. ⻋辆具备侧视盲区辅助功能	2. CSD屏幕及其他显示单元正常	3. 设置项侧视盲区辅助开关开启	4. ⾮⾏⻋辅助功能控⻋状态，D档下速度超过30km/h,最⾼⻋速⽆限制	5. 侧视盲区辅助功能关联的域控制器、左侧后摄像头、通信均正常	6. 监测未启⽤拖⻋钩	 包含原型图、负一屏、文言、软开关、语音及泛化等 触发事件	⽤户通过转向杆激活左转向灯；	
主流程	 1. ⽤户通过转向杆激活左转向灯；	2. 在转向拨杆拨下的300ms内中控特定区域显示左侧后⽅影像（影像尺⼨、具体显示位置在第四节定义） ；	3. case结束	
批注 [6]: 25R1节点规划功能	

=== 第9页 ===
可选流程1	
同时满⾜以下条件时：	1. ⻋辆已激活右侧侧视盲区辅助	2. ⽤户将转向杆直接由最上拨到最下⽅（左侧） ，拨杆中间位停留不超过200ms	执⾏以下操作：	视频框不退出，由右侧后⽅影像，切换为左侧后⽅影像	
可选流程2	
同时满⾜以下条件时：	1. ⻋辆已激活左侧侧视盲区辅助	2. ⽤户将转向杆回正后，迅速拨到最下⽅（左侧） ，拨杆中间位停留不超过200ms	执⾏以下操作：	视图框不退出，持续显示左侧后⽅影像	备注说明	/	3.1.2	转向拨杆激活右侧侧视盲区辅助		 内容说明	 原型	
前置条件	
1. ⻋辆具备侧视盲区辅助功能	2. CSD屏幕及其他显示单元正常	3. 设置项侧视盲区辅助开关开启	4. ⾮⾏⻋辅助功能控⻋状态，D档下速度超过30km/h，最⾼⻋速⽆限制	5. 侧视盲区辅助功能：域控制器、右侧后摄像头、通信均正常	6. 检测未启⽤拖⻋钩	 包含原型图、负一屏、文言、软开关、语音及泛化等 触发事件	⽤户通过转向杆激活右转向灯；	
主流程	 1. ⽤户通过转向杆激活右转向灯；	2. 在转向拨杆拨下的300ms内中控特定区域显示右侧后⽅影像（影像尺⼨、具体显示位置在第四节定义） ；	3. case结束	
可选流程1	
同时满⾜以下条件时：	1. ⻋辆已激活左侧侧视盲区辅助	2. ⽤户将转向杆直接由最下拨到最上⽅（右侧） ，拨杆中间位停留不超过200ms	执⾏以下操作：	

=== 第10页 ===
视频框不退出，由左侧后⽅影像，切换为右侧后⽅影像	
可选流程2	
同时满⾜以下条件时：	1. ⻋辆已激活左侧侧视盲区辅助	2. ⽤户将转向杆回正后，迅速拨到最上⽅（左侧） ，拨杆中间位停留不超过200ms	执⾏以下操作：	视图框不退出，持续显示右侧后⽅影像	备注说明	/	3.2		功能退出	3.2.1	ICC/NOA/CNOA/ALCA/HWA功能开启退出侧视盲区辅助		 内容说明	 原型	
前置条件	1. ⻋辆具备侧视盲区辅助功能	2. CSD屏幕及其他显示单元正常	3. 侧视盲区辅助功能：域控制器、侧后摄像头、通信均正常	4. 侧后盲区辅助功能已激活	 包含原型图、负一屏、文言、软开关、语音及泛化等 触发事件	⽤户激活⾏⻋辅助功能（ICC/NOA/CNOA/ALCA/HWA）	
主流程	 1. ⽤户激活⾏⻋辅助功能（ICC\NOA\CNOA\ALCA\HWA）	2. CSD侧后盲区辅助视图退出，进⼊⾏⻋辅助功能控⻋界⾯，⽆⽂⾔提示	3. case结束	备注说明	/	3.3		与其他功能的联动逻辑	3.3.1	⾏⻋辅助功能抑制侧视盲区辅助功能		 内容说明	 原型	
前置条件	
1. ⻋辆具备侧视盲区辅助功能	2. CSD屏幕及其他显示单元正常	3. 设置项侧视盲区辅助开关开启	4. 侧视盲区辅助功能关联的域控制器、侧后摄像头、通信均正常	5. D档下速度超过30km/h	6. ⾏⻋辅助功能（ICC\NOA\CNOA\ALCA\HWA）已激活并处于控
包含原型图、负一屏、文言、软开关、语音及泛化等 

=== 第11页 ===
⻋状态	触发事件	转向拨杆激活左/右转向灯	主流程	 1. 转向拨杆激活左/右转向灯	2. 侧后盲区辅助功能不被激活，CSD保持当前界⾯，⽆⽂⾔提示	3. case结束	备注说明	/	3.3.2	BSV视图上增加BSD报警视觉提示		 内容说明	 原型	
前置条件	
1. ⻋辆具备侧视盲区辅助和BSD功能	2. CSD屏幕和其他显示单元正常	3. 设置项侧视盲区辅助、BSD开关开启	4. 侧视盲区辅助功能关联的域控制器、侧后摄像头、通信均正常	5. ⾮⾏⻋辅助功能控⻋状态，D档下速度超过30km/h	6. 侧后盲区辅助功能已激活	
包含原型图、负一屏、文言、软开关、语音及泛化等 
触发事件	侧后盲区内有⻋辆接近⾃⻋，激活BSD⼆级报警	
主流程	
1. 侧后盲区内有⻋辆接近⾃⻋，激活BSD⼆级报警	2. BSV视图的来⻋侧对应边缘有红⾊动效闪烁，闪烁频率和BSD⼆级报警频率保持⼀致，延时：<=120ms，闪烁频率：4hz，125ms	on/125ms	off	3. 红⾊动效随BSD⼆级报警消失⽽退出	4. case结束	
	备注说明	/	3.4	挂⻋模式	3.4.1	挂⻋模式下单侧视盲区辅助激活发⽣故障		 内容说明	 原型	前置条件	1. ⻋辆具备侧视盲区辅助功能	2. CSD屏幕显示正常	 包含原型图、负一屏、文言、软开


=== 第12页 ===
3. 设置项侧视盲区辅助开关开启	4. 侧视盲区辅助功能：单侧视摄像头故障或单侧通信故障或关联系统故障	5. 检测到⾃⻋同房⻋连接（不包括机械拖⻋钩直接拖⻋）	
关、语音及泛化等 
触发事件	⽤户通过转向杆/R挡侧视盲区辅助拼接视图；	
主流程	
1. 侧视盲区辅助功能：左侧视摄像头故障或左侧通信故障	2. ⽤户通过转向杆/R挡侧视盲区辅助拼接视图；	3. 中控显示侧视盲区拼接视图，左侧视图显示⽂⾔：左侧视盲区辅助功能受限，功能恢复前⽂⾔在视图上常显，⽂⾔跟影像⼀起退出	4. case结束	
可选流程1	
1. 侧视盲区辅助功能：右侧视摄像头故障或右侧通信故障	2. ⽤户通过转向杆/R挡侧视盲区辅助拼接视图；	3. 中控显示侧视盲区拼接视图，右侧视图显示⽂⾔：右侧视盲区辅助功能受限，功能恢复前⽂⾔在视图上常显，⽂⾔跟影像⼀起退出	4. case结束	
可选流程2	
1. 侧视盲区辅助拼接视图已激活	2. 侧视盲区辅助功能：左侧视摄像头故障或左侧通信故障	3. 侧视盲区拼接视图不退出，左侧视图显示⽂⾔：左侧视盲区辅助功能受限，功能恢复前⽂⾔在视图上常显，⽂⾔跟影像⼀起退出	4. case结束	
可选流程3	
1. 侧视盲区辅助拼接视图已激活	2. 侧视盲区辅助功能：右侧视摄像头故障或右侧通信故障	3. 侧视盲区拼接视图不退出，右侧视图显示⽂⾔：右侧视盲区辅助功能受限，功能恢复前⽂⾔在视图上常显，⽂⾔跟影像⼀起退出	4. case结束	
可选流程4	
1. 侧视盲区辅助功能：关联系统故障	2. ⽤户拨动转向杆/R档	3. 中控显示⽂⾔toast：侧视盲区辅助功能受限，5S后⽂⾔退出，⼀个点⽕周期⽂⾔只提示⼀次。⽂⾔显示过程中，⽤户回正拨杆/切⼊P档/R档切⼊D/N档且速度超15km/h，⽂⾔不

=== 第13页 ===
退出。	4. case结束	
可选流程5	
1. 侧视盲区辅助拼接视图已激活	2. 侧视盲区辅助功能：关联系统故障	3. 中控显示⽂⾔toast：侧视盲区辅助功能受限，5s后⽂⾔退出。⽂⾔显示过程中，⽤户回正拨杆/切⼊P档/R档切⼊D/N档且速度超15km/h，⽂⾔不退出。	4. case结束	备注说明	/	3.5	后排DOW模式	3.5.1	后排侧视盲区辅助激活前发⽣故障		 内容说明	 原型	
前置条件	1. ⻋辆具备侧视盲区辅助功能和DOW功能	2. 后排CSD屏幕显示正常	3. 设置项开⻔预警开关开启	4. 侧视盲区辅助功能关联的域控制器、对应侧后摄像头或通信之⼀发⽣故障	
包含原型图、负一屏、文言、软开关、语音及泛化等 
触发事件	后排⻋⻔打开触发DOW⼆级报警	
主流程	
1. 后排侧视盲区辅助未激活	2. 侧视盲区辅助功能单侧后摄像头或通信之⼀发⽣故障	3. 触发后排侧视盲区辅助；显示置⿊影像，并提示⽂⾔：左/右侧视盲区辅助功能受限，影像窗⼝不退出，警示标识提示不变，故障后退出逻辑遵循故障前的退出逻辑	4. case结束	
可选流程1	
1. 后排侧视盲区辅助未激活	2. 侧视盲区辅助功能双侧后摄像头或通信之⼀发⽣故障	3. 触发后排侧视盲区辅助；显示置⿊影像，并提示⽂⾔：侧视盲区辅助功能受限，影像窗⼝不退出，警示标识提示不变，故障后退出逻辑遵循故障前的退出逻辑	4. case结束	可选流程2	 1. 后排侧视盲区辅助未激活	2. 侧视盲区辅助功能关联系统故障	

=== 第14页 ===
3. ⽤户打开后⻋⻔触发DOW⼆级报警	4. 后排显示⽂⾔toast：侧视盲区辅助功能受限，5s后⽂⾔退出。⼀个点⽕周期内，⽂⾔只显示⼀次。⽂⾔显示过程中，⽤户关闭⻋⻔，⽂⾔不退出。	5. case结束	备注说明	/	3.5.2	后排侧视盲区辅助激活后发⽣故障		 内容说明	 原型	
前置条件	1. ⻋辆具备侧视盲区辅助功能和DOW功能	2. 后排CSD屏幕显示正常	3. 设置项开⻔预警开关开启	4. 侧视盲区辅助功能关联的域控制器、侧后摄像头、通信均正常	
包含原型图、负一屏、文言、软开关、语音及泛化等 
触发事件	后排侧视盲区辅助激活后发⽣故障	
主流程	
1. 后排侧视盲区辅助已激活	2. 侧视盲区辅助功能对应单侧后摄像头或通信发⽣故障	3. 影像置⿊，并提示⽂⾔：左/右侧视盲区辅助功能受限，影像窗⼝不退出，警示标识提示不变，故障后退出逻辑遵循故障前的退出逻辑	4. case结束	
可选流程1	
1. 后排侧视盲区辅助已激活	2. 侧视盲区辅助功能双侧后摄像头或通信发⽣故障	3. 影像置⿊，并提示⽂⾔：侧视盲区辅助功能受限，影像窗⼝不退出，警示标识提示不变，故障后退出逻辑遵循故障前的退出逻辑	4. case结束	
可选流程2	
1. 后排侧视盲区辅助已激活	2. 侧视盲区辅助功能关联系统故障	3. 后排显示⽂⾔toast：侧视盲区辅助功能受限，5s后⽂⾔退出。⽂⾔显示过程中，⽤户关闭⻋⻔，⽂⾔不退出。	4. case结束	备注说明	/	

=== 第15页 ===
四、⾮功能说明	4.1	功能指标要求	功能指标	 详细描述	 关联use	case	id	视场⻆	 ⽔平60°	/垂直37°	 /	⻋⾝占⽐	 10%-15%	 /	帧率	 ⼤于50	 /	激活延迟	 ＜300ms	 /	显示延迟	 ＜100ms	 /	显示尺⼨	 ＞6⼨	 /	摄像头分辨率	 ＞100万	 /	视频流分辨率	 1280×720	 /	4.2		数据指标需求	序号	 类别	 指标项	指标名称	指标逻辑	 备注	
1	
运营类	BSV活跃率	
BSV⽇活跃率	
计算⽅式=【每⽇激活BSV⻋辆数】/【每⽇启动的⻋辆数】	【每⽇激活BSV⻋辆数】:在⼀个⾃然⽇周期内，激活BSV的⻋辆，即可计数	【每⽇启动的⻋辆数】 ：在⼀个⾃然⽇周期内，⻋辆上电或⻋辆发动机启动，即可计数	
指定按⽇筛选或指定时间段筛选	
2	 BSV⽉活跃率	
计算⽅式=【每⽉主动激活BSV⻋辆数】/【每⽉启动的⻋辆数】	【每⽉主动激活BSV⻋辆数】:在⼀个⾃然⽉周期内，激活BSV的⻋辆，即可计数	【每⽉启动的⻋辆数】 ：在⼀个⾃然⽉周期内，⻋辆上电或⻋辆发动机启动，即可计数	
指定按⽉筛选或指定时间段筛选	
3	 产品类		 BSV设置项使⽤频率	BSV设置项使⽤频率	 计算⽅式=【该设置项开启或关闭成功次数】/【指定时间段】	【该设置项开启或关闭成功次数】:在指定时间段内，该设置项被开启或关闭成功，即	

=== 第16页 ===
可计数	【指定时间段】:根据需求选择的具体时间段	
4	 BSV设置项状态占⽐	BSV设置项状态占⽐	
计算⽅式=【该设置项保持开启的⻋辆数】/【⻋辆总数】	【该设置项保持开启的⻋辆数】:在指定时间段，该设置项处于开启状态，即可计数	【⻋辆总数】 ：在指定时间段内，选定的⻋辆总数	
	
5	 故障	 BSV不同故障提示⽂⾔的频率	
BSV不同故障提示⽂⾔的频率	
计算⽅式=【该故障触发⽂⾔的次数】/【指定时间段】	【该故障触发⽂⾔的次数】:在指定时间段内，该故障触发⽂⾔，即可计数	故障触发分类包括：触发“侧视摄像头可能被遮挡，请清理”/触发“侧视盲区辅助功能受限”	【指定时间段】:根据需求选择的具体时间段	
	
4.3	补充说明	4.3.1	⽂⾔提示汇总	序号	场景	 ⽂⾔	 备注	1	 左侧摄像头、通信故障前/后激活功能	 左侧视盲区辅助功能受限	5S后⽂⾔和影像退出	（激活前故障每个点⽕周期触发⼀次，激活后故障⽆相关要求）	2	 右侧摄像头、通信故障前/后激活功能	 右侧视盲区辅助功能受限	5S后⽂⾔和影像退出	（激活前故障每个点⽕周期触发⼀次，激活后故障⽆相关要求）	3	 域控、双侧摄像头、通信故障前/后激活功能	 侧视盲区辅助功能受限	 5S后⽂⾔toast退出	（激活前故障每个点⽕周期触发⼀次，激活后故障⽆相关要求）		
批注 [7]: ⽂⾔提示汇总建议放进单独章节	
