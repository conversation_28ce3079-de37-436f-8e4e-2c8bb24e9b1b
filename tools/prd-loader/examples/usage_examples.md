# PRD加载工具使用示例

## 📋 基础使用示例

### 示例1: 加载LCA（车道居中辅助）文档

```bash
# 1. 进入工具目录
cd tools/prd-loader

# 2. 准备PDF文档
cp "LCA特性需求文档-pdf.pdf" ../../test_data/

# 3. 运行处理工具
python3 prd_loader_toolkit.py LCA

# 4. 快速部署
./quick_deploy_prd.sh LCA --backup --validate --commit

# 5. 验证结果
cd ../.. && npm start
```

**预期输出:**
```
🚀 开始处理LCA特性需求文档
📄 提取PDF内容: ../../test_data/LCA特性需求文档-pdf.pdf
🔍 分析文档结构...
✅ 识别到 8 个章节
🔧 生成 10 个模拟问题...
📝 生成realPRDContent.js文件...
🧪 生成验证脚本...
🎉 LCA文档处理完成!
```

### 示例2: 加载AEB（自动紧急制动）文档

```bash
# 使用自定义PDF文件名
python3 prd_loader_toolkit.py AEB "AEB自动紧急制动需求文档.pdf"

# 分步骤部署
./quick_deploy_prd.sh AEB --backup
node validate_aeb_data.js
```

**生成的文件:**
- `AEB_realPRDContent.js` - 主数据文件
- `AEB_issues.json` - 问题数据
- `AEB_full_content.txt` - PDF提取内容
- `validate_aeb_data.js` - 验证脚本

### 示例3: 批量处理多个文档

```bash
#!/bin/bash
# batch_process.sh

functions=("LCA" "AEB" "ACC" "ICC")

for func in "${functions[@]}"; do
    echo "处理 $func 文档..."
    python3 prd_loader_toolkit.py $func
    
    if [ $? -eq 0 ]; then
        echo "✅ $func 处理成功"
        ./quick_deploy_prd.sh $func --backup
    else
        echo "❌ $func 处理失败"
    fi
    
    echo "---"
done
```

## 🔧 高级使用示例

### 示例4: 自定义问题数量

```python
# 修改prd_loader_toolkit.py中的问题生成
loader = PRDLoader("LCA")
issues = loader.generate_mock_issues(15)  # 生成15个问题
```

### 示例5: 使用配置文件

```python
import json
from prd_loader_toolkit import PRDLoader

# 加载配置
with open('config.json', 'r', encoding='utf-8') as f:
    config = json.load(f)

# 使用配置创建加载器
loader = PRDLoader("LCA")
loader.config = config

# 根据配置处理文档
success = loader.process_prd_document()
```

### 示例6: 自定义章节模式

```python
# 在config.json中修改章节识别模式
{
  "section_patterns": {
    "cover": "^(.+特性需求文档)$",
    "level1": "^([一二三四五六七八九十]+、.+)$",
    "level2": "^(\\d+\\.\\d+\\s+.+)$",
    "level3": "^(\\d+\\.\\d+\\.\\d+\\s+.+)$",
    "custom": "^(附录[A-Z]\\s+.+)$"  # 自定义模式
  }
}
```

## 📊 验证和测试示例

### 示例7: 完整验证流程

```bash
# 1. 语法检查
node -c LCA_realPRDContent.js

# 2. 数据结构验证
node validate_lca_data.js

# 3. 应用编译测试
cd ../.. && npm run build

# 4. 功能测试
npm start &
sleep 10
curl -s http://localhost:3810 | grep "LCA"
```

### 示例8: 质量检查

```bash
# 检查生成文件的质量
echo "章节数量: $(grep -c 'id:' LCA_realPRDContent.js)"
echo "问题数量: $(grep -c '"id": "P' LCA_realPRDContent.js)"
echo "文件大小: $(ls -lh LCA_realPRDContent.js | awk '{print $5}')"
```

## 🛠️ 故障排除示例

### 示例9: PDF提取失败处理

```bash
# 检查PDF文件
file ../../test_data/LCA特性需求文档-pdf.pdf

# 尝试不同的PDF库
pip3 install PyPDF2
python3 -c "
import PyPDF2
print('PyPDF2可用')
"

# 手动提取测试
python3 simple_pdf_extract.py
```

### 示例10: JavaScript语法错误修复

```bash
# 检查语法错误
node -c LCA_realPRDContent.js

# 查看详细错误
node LCA_realPRDContent.js

# 检查特殊字符
grep -n "[^\x00-\x7F]" LCA_realPRDContent.js
```

## 📈 性能优化示例

### 示例11: 大文档处理优化

```python
# 对于大型PDF文档的优化处理
class OptimizedPRDLoader(PRDLoader):
    def extract_pdf_content(self, pdf_path, max_pages=50):
        """限制处理页数以提高性能"""
        # 实现优化的PDF提取逻辑
        pass
    
    def analyze_document_structure(self, content, chunk_size=1000):
        """分块处理大文档"""
        # 实现分块分析逻辑
        pass
```

### 示例12: 并行处理

```bash
# 并行处理多个文档
parallel -j 4 python3 prd_loader_toolkit.py {} ::: LCA AEB ACC ICC
```

## 🔄 集成示例

### 示例13: CI/CD集成

```yaml
# .github/workflows/prd-update.yml
name: PRD文档更新

on:
  push:
    paths:
      - 'test_data/*.pdf'

jobs:
  update-prd:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: 设置Python环境
        uses: actions/setup-python@v2
        with:
          python-version: '3.8'
          
      - name: 安装依赖
        run: |
          cd tools/prd-loader
          pip install pypdf
          
      - name: 处理PRD文档
        run: |
          cd tools/prd-loader
          python3 prd_loader_toolkit.py ${{ env.FUNCTION_NAME }}
          
      - name: 部署更新
        run: |
          cd tools/prd-loader
          ./quick_deploy_prd.sh ${{ env.FUNCTION_NAME }} --backup --commit
```

### 示例14: 自动化脚本

```bash
#!/bin/bash
# auto_update_prd.sh

# 监控test_data目录的PDF文件变化
inotifywait -m ../../test_data -e create -e modify --format '%f' |
while read file; do
    if [[ $file == *"特性需求文档-pdf.pdf" ]]; then
        # 提取功能名
        func_name=$(echo $file | sed 's/特性需求文档-pdf.pdf//')
        
        echo "检测到新的PRD文档: $file"
        echo "开始处理 $func_name 功能..."
        
        # 自动处理
        python3 prd_loader_toolkit.py $func_name
        ./quick_deploy_prd.sh $func_name --backup --validate
        
        echo "$func_name 处理完成"
    fi
done
```

## 📚 扩展示例

### 示例15: 自定义问题模板

```json
// custom_issue_templates.json
{
  "templates": [
    {
      "type": "security",
      "priority": "critical",
      "title_template": "{function}安全风险评估",
      "description_template": "{function}功能存在潜在的安全风险。",
      "suggestion_template": "建议进行{function}安全风险评估和加固。"
    }
  ]
}
```

### 示例16: 多语言支持

```python
# 支持英文PRD文档
class MultiLanguagePRDLoader(PRDLoader):
    def __init__(self, function_name, language='zh'):
        super().__init__(function_name)
        self.language = language
        
    def get_section_patterns(self):
        if self.language == 'en':
            return {
                "cover": r"^(.+Requirements Document)$",
                "level1": r"^(\d+\.\s+.+)$",
                "level2": r"^(\d+\.\d+\s+.+)$"
            }
        else:
            return super().get_section_patterns()
```

这些示例涵盖了从基础使用到高级定制的各种场景，帮助用户快速掌握PRD加载工具的使用方法。
