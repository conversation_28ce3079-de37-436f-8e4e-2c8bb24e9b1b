#!/bin/bash
# PRD文档动态加载工具包安装脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
    echo "$(printf '=%.0s' {1..60})"
}

# 检查系统要求
check_requirements() {
    print_header "🔍 检查系统要求"
    
    # 检查Python版本
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        print_success "Python版本: $PYTHON_VERSION"
    else
        print_error "Python3未安装，请先安装Python3"
        exit 1
    fi
    
    # 检查Node.js版本
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js版本: $NODE_VERSION"
    else
        print_warning "Node.js未安装，某些功能可能无法使用"
    fi
    
    # 检查npm
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        print_success "npm版本: $NPM_VERSION"
    else
        print_warning "npm未安装，无法运行应用验证"
    fi
    
    # 检查git
    if command -v git &> /dev/null; then
        GIT_VERSION=$(git --version)
        print_success "Git版本: $GIT_VERSION"
    else
        print_warning "Git未安装，无法使用自动提交功能"
    fi
}

# 安装Python依赖
install_python_deps() {
    print_header "📦 安装Python依赖"
    
    # 检查pip
    if ! command -v pip3 &> /dev/null; then
        print_error "pip3未安装，请先安装pip3"
        exit 1
    fi
    
    # 安装pypdf
    print_info "安装pypdf库..."
    if pip3 install pypdf; then
        print_success "pypdf安装成功"
    else
        print_warning "pypdf安装失败，尝试安装PyPDF2..."
        if pip3 install PyPDF2; then
            print_success "PyPDF2安装成功"
        else
            print_error "PDF处理库安装失败"
            exit 1
        fi
    fi
    
    # 验证安装
    if python3 -c "import pypdf" 2>/dev/null || python3 -c "import PyPDF2" 2>/dev/null; then
        print_success "PDF处理库验证成功"
    else
        print_error "PDF处理库验证失败"
        exit 1
    fi
}

# 创建必要的目录
create_directories() {
    print_header "📁 创建目录结构"
    
    # 获取项目根目录
    PROJECT_ROOT="$(cd ../.. && pwd)"
    print_info "项目根目录: $PROJECT_ROOT"
    
    # 创建必要目录
    directories=(
        "$PROJECT_ROOT/test_data"
        "$PROJECT_ROOT/backups"
        "$(pwd)/output"
        "$(pwd)/examples"
        "$(pwd)/templates"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_success "创建目录: $dir"
        else
            print_info "目录已存在: $dir"
        fi
    done
}

# 设置脚本权限
set_permissions() {
    print_header "🔐 设置文件权限"
    
    # 设置脚本可执行权限
    scripts=(
        "quick_deploy_prd.sh"
        "install.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [ -f "$script" ]; then
            chmod +x "$script"
            print_success "设置可执行权限: $script"
        fi
    done
}

# 验证安装
verify_installation() {
    print_header "✅ 验证安装"
    
    # 验证Python脚本
    if python3 -c "
import sys
sys.path.append('.')
from prd_loader_toolkit import PRDLoader
print('PRD加载工具导入成功')
"; then
        print_success "Python工具验证成功"
    else
        print_error "Python工具验证失败"
        return 1
    fi
    
    # 验证配置文件
    if python3 -c "
import json
with open('config.json', 'r', encoding='utf-8') as f:
    config = json.load(f)
print(f'配置文件加载成功，版本: {config[\"version\"]}')
"; then
        print_success "配置文件验证成功"
    else
        print_error "配置文件验证失败"
        return 1
    fi
    
    # 验证部署脚本
    if [ -x "quick_deploy_prd.sh" ]; then
        print_success "部署脚本验证成功"
    else
        print_error "部署脚本验证失败"
        return 1
    fi
    
    return 0
}

# 显示使用说明
show_usage() {
    print_header "📚 使用说明"
    
    echo "工具包安装完成！接下来您可以："
    echo ""
    echo "1. 📄 准备PDF文档:"
    echo "   cp '您的PRD文档.pdf' ../../test_data/"
    echo ""
    echo "2. 🔧 运行处理工具:"
    echo "   python3 prd_loader_toolkit.py 功能名"
    echo ""
    echo "3. 🚀 快速部署:"
    echo "   ./quick_deploy_prd.sh 功能名 --backup --validate"
    echo ""
    echo "4. 📖 查看文档:"
    echo "   - README.md - 工具包总览"
    echo "   - PRD_LOADER_README.md - 快速入门"
    echo "   - PRD_DYNAMIC_LOADING_GUIDE.md - 完整指南"
    echo ""
    echo "5. 🎯 运行演示:"
    echo "   python3 demo_prd_loading.py"
    echo ""
    echo "6. ✅ 验证功能:"
    echo "   python3 quick_verify.js"
    echo ""
}

# 主安装函数
main() {
    print_header "🚀 PRD文档动态加载工具包安装程序"
    echo "版本: 1.0.0"
    echo "基于BSV文档成功加载经验开发"
    echo ""
    
    # 检查是否在正确的目录
    if [ ! -f "prd_loader_toolkit.py" ]; then
        print_error "请在tools/prd-loader目录下运行此脚本"
        exit 1
    fi
    
    # 执行安装步骤
    check_requirements
    echo ""
    
    install_python_deps
    echo ""
    
    create_directories
    echo ""
    
    set_permissions
    echo ""
    
    if verify_installation; then
        echo ""
        print_success "🎉 安装完成！"
        echo ""
        show_usage
    else
        echo ""
        print_error "❌ 安装验证失败，请检查错误信息"
        exit 1
    fi
}

# 运行安装
main "$@"
