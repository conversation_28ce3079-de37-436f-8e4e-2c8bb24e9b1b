// {function_name}特性需求文档数据
// 自动生成于 {timestamp}
// 使用PRD动态加载工具包生成

export const realPRDContent = {
  title: "{function_name} 特性需求文档",
  sections: [
    {
      id: "cover",
      title: "{function_name}特性需求文档",
      level: 0,
      content: `{function_name}特性需求文档

编制/变更日志
版本号: V1.0
编制日期: {date}
编制人: 自动生成

本文档定义了{function_name}功能的产品需求规格说明。`
    },
    {
      id: "section-1",
      title: "一、文档综述",
      level: 1,
      content: `一、文档综述

1.1 文档背景
本文档用于定义{function_name}功能的产品需求。

1.2 文档范围  
本文档介绍了{function_name}的产品概述和具体的场景设计。

1.3 术语解释
{function_name}: {function_description}
ADAS: Advanced Driver Assistance Systems 高级驾驶辅助系统`
    },
    {
      id: "section-2", 
      title: "二、产品概述",
      level: 1,
      content: `二、产品概述

2.1 产品场景及概要说明
{function_name}系统通过传感器感知环境信息，为驾驶员提供辅助功能。

2.2 产品架构/系统架构
{function_name}系统包含感知模块、决策模块和执行模块。

2.3 功能特性
- 环境感知
- 智能决策
- 精确执行`
    },
    {
      id: "section-3",
      title: "三、功能场景设计", 
      level: 1,
      content: `三、功能场景设计

3.1 主要功能场景
{function_name}主要应用于以下场景：
- 高速公路行驶
- 城市道路行驶
- 停车场景

3.2 功能边界
明确{function_name}功能的适用范围和限制条件。

3.3 异常处理
定义各种异常情况下的系统行为。`
    }
    // 更多章节将根据实际PDF内容动态生成
  ]
};

// {function_name}评审问题数据
export const mockIssues = [
  {
    id: "P001",
    section: "section-1",
    type: "error",
    priority: "high", 
    title: "{function_name}架构设计缺失",
    description: "{function_name}功能的架构设计不完整，缺少关键组件说明。",
    suggestion: "建议补充完整的{function_name}架构设计文档，包括各模块的详细说明。",
    aiRecommendation: "基于{function_name}功能特点，建议采用分层架构设计，明确各层职责。",
    confidence: 85,
    rule: "{function_name}架构完整性要求",
    ruleId: "ARCH-001",
    sectionPath: ["文档综述"],
    targetText: "产品架构/系统架构",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P002",
    section: "section-2",
    type: "warning",
    priority: "medium",
    title: "{function_name}文档关联不规范", 
    description: "{function_name}相关文档的关联关系不够清晰，影响可追溯性。",
    suggestion: "建议重新整理{function_name}相关文档的关联关系，建立清晰的文档树。",
    aiRecommendation: "建议使用标准的文档管理工具，确保{function_name}文档的版本控制和关联管理。",
    confidence: 78,
    rule: "{function_name}文档可追溯性规范",
    ruleId: "DOC-001", 
    sectionPath: ["产品概述"],
    targetText: "相关文档",
    paragraphIndex: 2,
    startOffset: 0,
    endOffset: 1
  },
  {
    id: "P003",
    section: "section-3",
    type: "suggestion",
    priority: "low",
    title: "{function_name}功能描述可优化",
    description: "{function_name}功能的描述可以更加详细和具体。",
    suggestion: "建议优化{function_name}功能的描述内容，增加具体的技术参数和性能指标。",
    aiRecommendation: "建议参考行业标准，完善{function_name}功能的技术规格说明。",
    confidence: 72,
    rule: "{function_name}功能描述规范",
    ruleId: "FUNC-001",
    sectionPath: ["功能场景设计"],
    targetText: "功能描述",
    paragraphIndex: 1,
    startOffset: 0,
    endOffset: 1
  }
  // 更多问题将根据配置动态生成
];

// 导出配置信息
export const documentConfig = {
  functionName: "{function_name}",
  functionCode: "{function_code}",
  version: "1.0.0",
  generatedAt: "{timestamp}",
  totalSections: "{total_sections}",
  totalIssues: "{total_issues}",
  processingTime: "{processing_time}",
  toolVersion: "PRD Loader Toolkit v1.0.0"
};
