#!/usr/bin/env node
// BSV文档验证脚本

const fs = require('fs');
const path = require('path');

function validateBSVData() {
    console.log('🚀 验证BSV文档数据');
    
    const expectedSections = 6;
    const expectedIssues = 10;
    
    try {
        // 检查文件是否存在
        const filePath = path.join(__dirname, 'src/data/realPRDContent.js');
        if (!fs.existsSync(filePath)) {
            console.log('❌ realPRDContent.js 文件不存在');
            return false;
        }
        console.log('✅ realPRDContent.js 文件存在');
        
        // 检查文件内容
        const content = fs.readFileSync(filePath, 'utf8');
        
        const checks = [
            { name: 'BSV标题', pattern: /BSV特性需求文档/, required: true },
            { name: 'sections数组', pattern: /sections:\s*\[/, required: true },
            { name: 'mockIssues导出', pattern: /export const mockIssues/, required: true }
        ];
        
        console.log('\n📊 内容检查:');
        let allPassed = true;
        checks.forEach(check => {
            const found = check.pattern.test(content);
            const status = found ? '✅' : '❌';
            console.log(`${status} ${check.name}: ${found ? '存在' : '缺失'}`);
            if (check.required && !found) {
                allPassed = false;
            }
        });
        
        console.log(`\n🎯 验证结果: ${allPassed ? '✅ 通过' : '❌ 失败'}`);
        return allPassed;
        
    } catch (error) {
        console.log(`❌ 验证过程出错: ${error.message}`);
        return false;
    }
}

if (require.main === module) {
    validateBSVData();
}

module.exports = { validateBSVData };
