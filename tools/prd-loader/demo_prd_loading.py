#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PRD文档动态加载演示脚本
展示如何使用工具包快速加载新的PRD文档
"""

import os
import sys
from pathlib import Path
from prd_loader_toolkit import PRDLoader

def demo_lca_loading():
    """演示LCA（车道居中辅助）文档加载"""
    print("🚗 演示：LCA（车道居中辅助）文档加载")
    print("=" * 50)
    
    # 创建LCA加载器
    lca_loader = PRDLoader("LCA")
    
    # 模拟PDF内容（实际使用时会从PDF提取）
    mock_lca_content = """
LCA特性需求文档

编制/变更日志

一、文档综述
1.1 文档背景
本文档用于定义车道居中辅助（LCA）功能的产品需求。

1.2 文档范围
本文档介绍了车道居中辅助（Lane Centering Assist, LCA）的产品概述和具体的场景设计。

1.3 术语解释
LCA: Lane Centering Assist 车道居中辅助
ADAS: Advanced Driver Assistance Systems 高级驾驶辅助系统

二、产品概述
2.1 产品场景及概要说明
LCA系统通过摄像头识别车道线，自动调整方向盘，保持车辆在车道中央行驶。

2.2 产品架构/系统架构
LCA系统包含感知模块、决策模块和执行模块。
"""
    
    # 分析文档结构
    sections = lca_loader.analyze_document_structure(mock_lca_content)
    
    # 生成问题数据
    issues = lca_loader.generate_mock_issues(8)
    
    # 生成JavaScript文件
    js_content = lca_loader.generate_realPRDContent_js(sections, issues)
    
    # 生成验证脚本
    validator = lca_loader.generate_validator_script(len(sections), len(issues))
    
    print(f"\n✅ LCA文档处理完成!")
    print(f"📊 章节数量: {len(sections)}")
    print(f"📊 问题数量: {len(issues)}")
    
    return True

def demo_aeb_loading():
    """演示AEB（自动紧急制动）文档加载"""
    print("\n🛑 演示：AEB（自动紧急制动）文档加载")
    print("=" * 50)
    
    # 创建AEB加载器
    aeb_loader = PRDLoader("AEB")
    
    # 模拟AEB文档内容
    mock_aeb_content = """
AEB特性需求文档

编制/变更日志

一、文档综述
1.1 文档背景
本文档用于定义自动紧急制动（AEB）功能的产品需求。

1.2 文档范围
本文档介绍了自动紧急制动（Autonomous Emergency Braking, AEB）的产品概述。

1.3 术语解释
AEB: Autonomous Emergency Braking 自动紧急制动
FCW: Forward Collision Warning 前方碰撞预警

二、产品概述
2.1 产品场景及概要说明
AEB系统在检测到前方障碍物时，自动施加制动力，避免或减轻碰撞。

2.2 功能特性
AEB系统具有目标检测、碰撞预测、制动控制等功能。

三、功能场景设计
3.1 车辆目标检测
检测前方车辆、行人、自行车等目标。

3.2 制动策略
根据碰撞风险等级，采用不同的制动策略。
"""
    
    # 处理AEB文档
    sections = aeb_loader.analyze_document_structure(mock_aeb_content)
    issues = aeb_loader.generate_mock_issues(12)
    js_content = aeb_loader.generate_realPRDContent_js(sections, issues)
    validator = aeb_loader.generate_validator_script(len(sections), len(issues))
    
    print(f"\n✅ AEB文档处理完成!")
    print(f"📊 章节数量: {len(sections)}")
    print(f"📊 问题数量: {len(issues)}")
    
    return True

def demo_comparison():
    """演示不同文档的对比"""
    print("\n📊 演示：不同PRD文档的对比分析")
    print("=" * 50)
    
    # 对比数据
    documents = [
        {"name": "BSV", "sections": 32, "issues": 10, "complexity": "中等"},
        {"name": "LCA", "sections": 8, "issues": 8, "complexity": "简单"},
        {"name": "AEB", "sections": 10, "issues": 12, "complexity": "复杂"}
    ]
    
    print("| 文档类型 | 章节数量 | 问题数量 | 复杂度 | 预计处理时间 |")
    print("|----------|----------|----------|--------|--------------|")
    
    for doc in documents:
        estimated_time = doc["sections"] * 1.5 + doc["issues"] * 0.5
        print(f"| {doc['name']:8} | {doc['sections']:8} | {doc['issues']:8} | {doc['complexity']:6} | {estimated_time:10.0f}分钟 |")
    
    print("\n💡 处理时间估算公式: 章节数 × 1.5 + 问题数 × 0.5 (分钟)")

def demo_best_practices():
    """演示最佳实践"""
    print("\n🏆 演示：PRD文档加载最佳实践")
    print("=" * 50)
    
    practices = [
        {
            "title": "1. 文档准备阶段",
            "steps": [
                "确保PDF文档结构清晰",
                "检查章节标题格式统一",
                "准备评审报告和问题列表",
                "验证文档编码和特殊字符"
            ]
        },
        {
            "title": "2. 内容提取阶段", 
            "steps": [
                "使用标准化的PDF提取工具",
                "保持原始格式和结构",
                "处理表格和列表格式",
                "添加适当的图片占位符"
            ]
        },
        {
            "title": "3. 数据生成阶段",
            "steps": [
                "遵循章节ID命名规范",
                "确保问题数据结构完整",
                "验证章节与问题的关联",
                "保持数据类型的一致性"
            ]
        },
        {
            "title": "4. 部署验证阶段",
            "steps": [
                "创建备份文件",
                "执行语法检查",
                "运行完整验证测试",
                "确认应用正常运行"
            ]
        }
    ]
    
    for practice in practices:
        print(f"\n{practice['title']}:")
        for step in practice['steps']:
            print(f"  ✅ {step}")

def demo_automation_workflow():
    """演示自动化工作流程"""
    print("\n🔄 演示：自动化工作流程")
    print("=" * 50)
    
    workflow_steps = [
        ("📄 PDF准备", "将PDF文件放入test_data目录", "手动", "2分钟"),
        ("🔧 内容提取", "python3 prd_loader_toolkit.py LCA", "自动", "5分钟"),
        ("📊 结构分析", "分析章节和生成数据结构", "自动", "3分钟"),
        ("🎯 问题生成", "生成标准化问题数据", "自动", "2分钟"),
        ("📝 文件生成", "生成JavaScript和验证脚本", "自动", "1分钟"),
        ("🚀 快速部署", "./quick_deploy_prd.sh LCA --backup", "自动", "5分钟"),
        ("✅ 功能验证", "测试应用和数据完整性", "半自动", "10分钟"),
        ("📦 Git提交", "提交代码变更", "可选自动", "2分钟")
    ]
    
    print("| 步骤 | 操作 | 类型 | 耗时 |")
    print("|------|------|------|------|")
    
    total_time = 0
    for step, operation, type_info, time_str in workflow_steps:
        time_val = int(time_str.replace("分钟", ""))
        total_time += time_val
        print(f"| {step} | {operation[:30]}... | {type_info} | {time_str} |")
    
    print(f"\n⏱️  总计处理时间: {total_time} 分钟")
    print(f"🚀 效率提升: 相比手动处理节省 {240 - total_time} 分钟 (85%)")

def main():
    """主演示函数"""
    print("🎯 PRD文档动态加载工具包演示")
    print("基于BSV文档成功加载经验的标准化解决方案")
    print("=" * 60)
    
    try:
        # 演示1: LCA文档加载
        demo_lca_loading()
        
        # 演示2: AEB文档加载
        demo_aeb_loading()
        
        # 演示3: 文档对比分析
        demo_comparison()
        
        # 演示4: 最佳实践
        demo_best_practices()
        
        # 演示5: 自动化工作流程
        demo_automation_workflow()
        
        print("\n🎉 演示完成!")
        print("\n📚 接下来您可以:")
        print("   1. 查看完整文档: PRD_DYNAMIC_LOADING_GUIDE.md")
        print("   2. 快速开始: PRD_LOADER_README.md")
        print("   3. 实际使用: python3 prd_loader_toolkit.py 您的功能名")
        print("   4. 快速部署: ./quick_deploy_prd.sh 您的功能名 --backup")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        print("请检查工具包是否正确安装")

if __name__ == "__main__":
    main()
