#!/usr/bin/env python3
# -*- coding: utf-8 -*-

try:
    from pypdf import PdfReader
except ImportError:
    try:
        import PyPDF2
        from PyPDF2 import PdfReader
    except ImportError:
        print("请安装PDF处理库: pip3 install pypdf")
        exit(1)

def extract_pdf_simple(pdf_path):
    """简单提取PDF内容"""
    try:
        print(f"正在读取PDF文件: {pdf_path}")
        with open(pdf_path, 'rb') as file:
            pdf_reader = PdfReader(file)
            print(f"PDF总页数: {len(pdf_reader.pages)}")
            
            # 提取所有页面
            text_content = ""
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                page_text = page.extract_text()
                text_content += f"\n=== 第{page_num + 1}页 ===\n"
                text_content += page_text
                text_content += "\n"
                print(f"已提取第{page_num + 1}页，字符数: {len(page_text)}")
            
            return text_content
    except Exception as e:
        print(f"读取PDF文件时出错: {e}")
        return None

def main():
    pdf_path = "../../test_data/BSV特性需求文档-pdf.pdf"
    
    print("=== 简单PDF内容提取测试 ===")
    
    # 提取PDF文本
    text = extract_pdf_simple(pdf_path)
    if text:
        print(f"\n提取成功，总字符数: {len(text)}")
        
        # 保存到文件
        with open("bsv_full_content.txt", "w", encoding="utf-8") as f:
            f.write(text)
        print("内容已保存到: bsv_full_content.txt")
        
        # 显示前500个字符
        print(f"\n内容预览:\n{text[:500]}...")
    else:
        print("提取失败")

if __name__ == "__main__":
    main()
